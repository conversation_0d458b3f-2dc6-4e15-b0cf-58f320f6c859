// web/src/lib/utils/r2DocumentUpload.ts
// Updated document upload utility that uses Cloudflare R2 instead of local storage

import { uploadFile, FileType } from '../../../../cron/lib/storage/r2Storage';
import { logger } from '../../../../cron/utils/logger';

export interface R2UploadResult {
  success: boolean;
  originalFileName: string;
  filename: string;
  filePath: string; // R2 file key
  publicPath: string; // R2 public URL
  fileSize: number;
  contentType: string;
  error?: string;
}

/**
 * Document type to R2 file type mapping
 */
const DOCUMENT_TYPE_TO_R2_TYPE: Record<string, FileType> = {
  resume: 'resumes',
  'cover-letter': 'userDocuments',
  portfolio: 'userDocuments',
  transcript: 'userDocuments',
  certificate: 'userDocuments',
  default: 'userDocuments',
};

/**
 * Upload a document to Cloudflare R2
 */
export async function uploadDocumentToR2(
  file: File,
  documentType: string = 'default',
  identifier?: string
): Promise<R2UploadResult> {
  try {
    logger.info(`📤 Uploading document to R2: ${file.name} (type: ${documentType})`);

    // Validate file
    if (!file || file.size === 0) {
      throw new Error('No file provided or file is empty');
    }

    // Check file size (10MB limit)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      throw new Error(`File size ${file.size} exceeds maximum allowed size of ${maxSize} bytes`);
    }

    // Validate file type
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ];

    if (!allowedTypes.includes(file.type)) {
      throw new Error(
        `File type ${file.type} is not supported. Please upload a PDF or Word document.`
      );
    }

    // Convert file to buffer
    const buffer = Buffer.from(await file.arrayBuffer());

    // Get R2 file type
    const r2FileType = DOCUMENT_TYPE_TO_R2_TYPE[documentType] || DOCUMENT_TYPE_TO_R2_TYPE.default;

    // Upload to R2
    const uploadResult = await uploadFile(buffer, file.name, file.type, r2FileType, identifier);

    if (!uploadResult.success) {
      throw new Error(uploadResult.error || 'Upload failed');
    }

    logger.info(`✅ Document uploaded successfully to R2: ${uploadResult.publicUrl}`);

    return {
      success: true,
      originalFileName: file.name,
      filename: uploadResult.fileKey?.split('/').pop() || file.name,
      filePath: uploadResult.fileKey!, // R2 file key
      publicPath: uploadResult.publicUrl!, // R2 public URL
      fileSize: uploadResult.fileSize!,
      contentType: uploadResult.contentType!,
    };
  } catch (error) {
    logger.error(`❌ Failed to upload document to R2:`, error);

    return {
      success: false,
      originalFileName: file.name,
      filename: '',
      filePath: '',
      publicPath: '',
      fileSize: 0,
      contentType: file.type,
      error: error instanceof Error ? error.message : 'Unknown upload error',
    };
  }
}

/**
 * Download a document from R2 (for resume parsing workers)
 */
export async function downloadDocumentFromR2(
  fileKey: string,
  bucketType?: 'resumes' | 'userDocuments'
): Promise<{ success: boolean; buffer?: Buffer; error?: string }> {
  try {
    logger.info(`📥 Downloading document from R2: ${fileKey}`);

    // Import the download function dynamically to avoid circular dependencies
    const { downloadFile, BucketType } = await import('../../../../cron/lib/storage/r2Storage');

    const r2BucketType = bucketType === 'resumes' ? BucketType.RESUMES : BucketType.USER;

    const downloadResult = await downloadFile(fileKey, r2BucketType);

    if (!downloadResult.success) {
      throw new Error(downloadResult.error || 'Download failed');
    }

    logger.info(`✅ Document downloaded successfully from R2: ${fileKey}`);

    return {
      success: true,
      buffer: downloadResult.buffer,
    };
  } catch (error) {
    logger.error(`❌ Failed to download document from R2:`, error);

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown download error',
    };
  }
}

/**
 * Check if a document exists in R2
 */
export async function checkDocumentExistsInR2(
  fileKey: string,
  bucketType?: 'resumes' | 'userDocuments'
): Promise<boolean> {
  try {
    const downloadResult = await downloadDocumentFromR2(fileKey, bucketType);
    return downloadResult.success;
  } catch {
    return false;
  }
}

/**
 * Get the public URL for a document in R2
 */
export function getDocumentPublicUrl(
  fileKey: string,
  bucketType: 'resumes' | 'userDocuments' = 'userDocuments'
): string {
  const R2_CUSTOM_DOMAIN = process.env.R2_CUSTOM_DOMAIN || '';
  const R2_ACCOUNT_ID = process.env.R2_ACCOUNT_ID || '';

  if (R2_CUSTOM_DOMAIN) {
    return `https://${R2_CUSTOM_DOMAIN}/${fileKey}`;
  } else {
    // Use R2 Public Development URL - files are accessible directly from the root
    // The actual structure is: https://pub-{accountId}.r2.dev/filename.ext
    return `https://pub-${R2_ACCOUNT_ID}.r2.dev/${fileKey}`;
  }
}

/**
 * Migrate a local file to R2 (utility function)
 */
export async function migrateLocalFileToR2(
  localFilePath: string,
  originalFileName: string,
  contentType: string,
  documentType: string,
  userId?: string
): Promise<R2UploadResult> {
  try {
    logger.info(`🔄 Migrating local file to R2: ${localFilePath}`);

    // Read the local file
    const fs = await import('fs/promises');
    const buffer = await fs.readFile(localFilePath);

    // Create a mock File object
    const file = new File([buffer], originalFileName, { type: contentType });

    // Upload to R2
    return await uploadDocumentToR2(file, documentType, userId);
  } catch (error) {
    logger.error(`❌ Failed to migrate local file to R2:`, error);

    return {
      success: false,
      originalFileName,
      filename: '',
      filePath: '',
      publicPath: '',
      fileSize: 0,
      contentType,
      error: error instanceof Error ? error.message : 'Migration failed',
    };
  }
}

export default uploadDocumentToR2;
