{"version": 3, "file": "_server.ts-Dj9hwjC3.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/worker/health/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { g as getRedisClient } from \"../../../../../chunks/redis.js\";\nconst WORKER_TYPES = [\n  \"resume-parsing\",\n  \"resume-optimization\",\n  \"search\",\n  \"ats-analysis\",\n  \"job-specific-analysis\",\n  \"email\",\n  \"automation\"\n];\nconst WORKER_STATUS_TYPES = [\"healthy\", \"degraded\", \"unhealthy\", \"unknown\"];\nfunction validateWorkerHealth(body) {\n  const { workerType, status, metrics } = body;\n  if (!workerType || !metrics) {\n    return {\n      valid: false,\n      error: \"Missing required fields: workerType and metrics are required\"\n    };\n  }\n  if (!WORKER_TYPES.includes(workerType)) {\n    return {\n      valid: false,\n      error: `Invalid worker type: ${workerType}`\n    };\n  }\n  const workerStatus = status ?? \"healthy\";\n  if (!WORKER_STATUS_TYPES.includes(workerStatus)) {\n    return {\n      valid: false,\n      error: `Invalid status: ${status}`\n    };\n  }\n  const requiredMetricFields = [\n    \"cpu\",\n    \"memory\",\n    \"queueSize\",\n    \"processingCount\",\n    \"responseTime\",\n    \"errorRate\",\n    \"successRate\",\n    \"capacity\"\n  ];\n  for (const field of requiredMetricFields) {\n    if (metrics[field] === void 0) {\n      return {\n        valid: false,\n        error: `Missing required metric: ${field}`\n      };\n    }\n  }\n  return { valid: true };\n}\nasync function updateWorkerHealthInRedis(redis, workerType, status, metrics) {\n  try {\n    const healthy = metrics.cpu < 90 && metrics.memory < 90 && metrics.errorRate < 10 && metrics.capacity > 20;\n    const healthData = {\n      status,\n      healthy,\n      lastHeartbeat: (/* @__PURE__ */ new Date()).toISOString()\n    };\n    await redis.hset(\"worker:health\", workerType, JSON.stringify(healthData));\n    await redis.hset(\"worker:metrics\", workerType, JSON.stringify(metrics));\n    return true;\n  } catch (error) {\n    console.error(`Error updating health for worker ${workerType}:`, error);\n    return false;\n  }\n}\nconst POST = async ({ request, locals }) => {\n  try {\n    const user = locals.user;\n    const isProduction = process.env.NODE_ENV === \"production\";\n    if (isProduction && !user) {\n      return new Response(\"Unauthorized\", { status: 401 });\n    }\n    const body = await request.json();\n    const validation = validateWorkerHealth(body);\n    if (!validation.valid) {\n      return json(\n        {\n          success: false,\n          error: validation.error\n        },\n        { status: 400 }\n      );\n    }\n    const redis = await getRedisClient();\n    if (!redis) {\n      return json(\n        {\n          success: false,\n          error: \"Redis client not available\"\n        },\n        { status: 500 }\n      );\n    }\n    const { workerType, status = \"healthy\", metrics } = body;\n    const result = await updateWorkerHealthInRedis(redis, workerType, status, metrics);\n    if (!result) {\n      return json(\n        {\n          success: false,\n          error: \"Failed to update worker health\"\n        },\n        { status: 500 }\n      );\n    }\n    return json({\n      success: true,\n      message: `Health status updated for worker: ${workerType}`\n    });\n  } catch (error) {\n    console.error(\"Error updating worker health:\", error);\n    return json(\n      {\n        success: false,\n        error: \"Failed to update worker health\",\n        details: String(error)\n      },\n      { status: 500 }\n    );\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;AAEA,MAAM,YAAY,GAAG;AACrB,EAAE,gBAAgB;AAClB,EAAE,qBAAqB;AACvB,EAAE,QAAQ;AACV,EAAE,cAAc;AAChB,EAAE,uBAAuB;AACzB,EAAE,OAAO;AACT,EAAE;AACF,CAAC;AACD,MAAM,mBAAmB,GAAG,CAAC,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,CAAC;AAC3E,SAAS,oBAAoB,CAAC,IAAI,EAAE;AACpC,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI;AAC9C,EAAE,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,EAAE;AAC/B,IAAI,OAAO;AACX,MAAM,KAAK,EAAE,KAAK;AAClB,MAAM,KAAK,EAAE;AACb,KAAK;AACL;AACA,EAAE,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;AAC1C,IAAI,OAAO;AACX,MAAM,KAAK,EAAE,KAAK;AAClB,MAAM,KAAK,EAAE,CAAC,qBAAqB,EAAE,UAAU,CAAC;AAChD,KAAK;AACL;AACA,EAAE,MAAM,YAAY,GAAG,MAAM,IAAI,SAAS;AAC1C,EAAE,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;AACnD,IAAI,OAAO;AACX,MAAM,KAAK,EAAE,KAAK;AAClB,MAAM,KAAK,EAAE,CAAC,gBAAgB,EAAE,MAAM,CAAC;AACvC,KAAK;AACL;AACA,EAAE,MAAM,oBAAoB,GAAG;AAC/B,IAAI,KAAK;AACT,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,IAAI,iBAAiB;AACrB,IAAI,cAAc;AAClB,IAAI,WAAW;AACf,IAAI,aAAa;AACjB,IAAI;AACJ,GAAG;AACH,EAAE,KAAK,MAAM,KAAK,IAAI,oBAAoB,EAAE;AAC5C,IAAI,IAAI,OAAO,CAAC,KAAK,CAAC,KAAK,MAAM,EAAE;AACnC,MAAM,OAAO;AACb,QAAQ,KAAK,EAAE,KAAK;AACpB,QAAQ,KAAK,EAAE,CAAC,yBAAyB,EAAE,KAAK,CAAC;AACjD,OAAO;AACP;AACA;AACA,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE;AACxB;AACA,eAAe,yBAAyB,CAAC,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE;AAC7E,EAAE,IAAI;AACN,IAAI,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,GAAG,EAAE,IAAI,OAAO,CAAC,MAAM,GAAG,EAAE,IAAI,OAAO,CAAC,SAAS,GAAG,EAAE,IAAI,OAAO,CAAC,QAAQ,GAAG,EAAE;AAC9G,IAAI,MAAM,UAAU,GAAG;AACvB,MAAM,MAAM;AACZ,MAAM,OAAO;AACb,MAAM,aAAa,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC7D,KAAK;AACL,IAAI,MAAM,KAAK,CAAC,IAAI,CAAC,eAAe,EAAE,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;AAC7E,IAAI,MAAM,KAAK,CAAC,IAAI,CAAC,gBAAgB,EAAE,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;AAC3E,IAAI,OAAO,IAAI;AACf,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,iCAAiC,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;AAC3E,IAAI,OAAO,KAAK;AAChB;AACA;AACK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AAC5C,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC5B,IAAI,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;AAC9D,IAAI,IAAI,YAAY,IAAI,CAAC,IAAI,EAAE;AAC/B,MAAM,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1D;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,MAAM,UAAU,GAAG,oBAAoB,CAAC,IAAI,CAAC;AACjD,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE;AAC3B,MAAM,OAAO,IAAI;AACjB,QAAQ;AACR,UAAU,OAAO,EAAE,KAAK;AACxB,UAAU,KAAK,EAAE,UAAU,CAAC;AAC5B,SAAS;AACT,QAAQ,EAAE,MAAM,EAAE,GAAG;AACrB,OAAO;AACP;AACA,IAAI,MAAM,KAAK,GAAG,MAAM,cAAc,EAAE;AACxC,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,OAAO,IAAI;AACjB,QAAQ;AACR,UAAU,OAAO,EAAE,KAAK;AACxB,UAAU,KAAK,EAAE;AACjB,SAAS;AACT,QAAQ,EAAE,MAAM,EAAE,GAAG;AACrB,OAAO;AACP;AACA,IAAI,MAAM,EAAE,UAAU,EAAE,MAAM,GAAG,SAAS,EAAE,OAAO,EAAE,GAAG,IAAI;AAC5D,IAAI,MAAM,MAAM,GAAG,MAAM,yBAAyB,CAAC,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC;AACtF,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,OAAO,IAAI;AACjB,QAAQ;AACR,UAAU,OAAO,EAAE,KAAK;AACxB,UAAU,KAAK,EAAE;AACjB,SAAS;AACT,QAAQ,EAAE,MAAM,EAAE,GAAG;AACrB,OAAO;AACP;AACA,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,OAAO,EAAE,CAAC,kCAAkC,EAAE,UAAU,CAAC;AAC/D,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC;AACzD,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,KAAK,EAAE,gCAAgC;AAC/C,QAAQ,OAAO,EAAE,MAAM,CAAC,KAAK;AAC7B,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;;;;"}