{"version": 3, "file": "_server.ts-BGakHp2j.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/email/analytics/stats/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../../chunks/prisma.js\";\nimport { l as logger } from \"../../../../../../chunks/logger.js\";\nasync function GET({ url }) {\n  try {\n    const timeRange = url.searchParams.get(\"timeRange\") || \"7d\";\n    const template = url.searchParams.get(\"template\") || \"all\";\n    const startDateParam = url.searchParams.get(\"startDate\");\n    const endDateParam = url.searchParams.get(\"endDate\");\n    const now = /* @__PURE__ */ new Date();\n    let startDate = /* @__PURE__ */ new Date();\n    let endDate = now;\n    if (startDateParam && endDateParam) {\n      startDate = new Date(startDateParam);\n      endDate = new Date(endDateParam);\n    } else {\n      switch (timeRange) {\n        case \"24h\":\n          startDate.setHours(now.getHours() - 24);\n          break;\n        case \"7d\":\n          startDate.setDate(now.getDate() - 7);\n          break;\n        case \"30d\":\n          startDate.setDate(now.getDate() - 30);\n          break;\n        case \"90d\":\n          startDate.setDate(now.getDate() - 90);\n          break;\n        default:\n          startDate.setDate(now.getDate() - 7);\n      }\n    }\n    try {\n      await prisma.emailEvent.count();\n    } catch (error) {\n      logger.warn(\"EmailEvent table does not exist or is not accessible\");\n      return json({\n        stats: {\n          total: 0,\n          delivered: 0,\n          opened: 0,\n          clicked: 0,\n          bounced: 0,\n          complained: 0,\n          unsubscribed: 0\n        },\n        chartData: [],\n        topEmails: []\n      });\n    }\n    const whereConditions = {\n      timestamp: {\n        gte: startDate,\n        lte: endDate\n      }\n    };\n    if (template !== \"all\") {\n      whereConditions.templateName = template;\n    }\n    const eventCounts = await prisma.emailEvent.groupBy({\n      by: [\"type\"],\n      where: whereConditions,\n      _count: {\n        id: true\n      }\n    });\n    const stats = {\n      total: 0,\n      delivered: 0,\n      opened: 0,\n      clicked: 0,\n      bounced: 0,\n      complained: 0,\n      unsubscribed: 0\n    };\n    eventCounts.forEach((count) => {\n      const eventType = count.type;\n      const eventCount = count._count.id;\n      stats[eventType] = eventCount;\n      stats.total += eventCount;\n    });\n    const chartData = await getChartData(startDate, whereConditions);\n    const topEmails = await getTopEmails(startDate, template);\n    return json({\n      stats,\n      chartData,\n      topEmails\n    });\n  } catch (error) {\n    logger.error(\"Error getting email stats:\", error);\n    return json(\n      {\n        stats: {\n          total: 0,\n          delivered: 0,\n          opened: 0,\n          clicked: 0,\n          bounced: 0,\n          complained: 0,\n          unsubscribed: 0\n        },\n        chartData: [],\n        topEmails: [],\n        error: \"Failed to get email stats\"\n      },\n      { status: 200 }\n    );\n  }\n}\nasync function getChartData(startDate, whereConditions) {\n  try {\n    const dailyCounts = await prisma.$queryRaw`\n      SELECT\n        DATE_TRUNC('day', \"timestamp\") as day,\n        type,\n        COUNT(*) as count\n      FROM\n        web.\"EmailEvent\"\n      WHERE\n        \"timestamp\" >= ${startDate}\n        ${whereConditions.templateName ? ` AND \"templateName\" = ${whereConditions.templateName}` : \"\"}\n      GROUP BY\n        DATE_TRUNC('day', \"timestamp\"), type\n      ORDER BY\n        day ASC\n    `;\n    const chartData = [];\n    if (Array.isArray(dailyCounts)) {\n      const dayMap = /* @__PURE__ */ new Map();\n      dailyCounts.forEach((item) => {\n        const day = new Date(item.day).toISOString().split(\"T\")[0];\n        if (!dayMap.has(day)) {\n          dayMap.set(day, {\n            day,\n            delivered: 0,\n            opened: 0,\n            clicked: 0,\n            bounced: 0,\n            complained: 0,\n            unsubscribed: 0\n          });\n        }\n        dayMap.get(day)[item.type] = Number(item.count);\n      });\n      chartData.push(...Array.from(dayMap.values()));\n    }\n    return chartData;\n  } catch (error) {\n    logger.error(\"Error getting chart data:\", error);\n    return [];\n  }\n}\nasync function getTopEmails(startDate, template) {\n  try {\n    const topEmails = await prisma.$queryRaw`\n      WITH email_counts AS (\n        SELECT\n          \"templateName\" as template,\n          COUNT(*) FILTER (WHERE type = 'delivered') as delivered_count,\n          COUNT(*) FILTER (WHERE type = 'opened') as opened_count,\n          COUNT(*) FILTER (WHERE type = 'clicked') as clicked_count\n        FROM\n          web.\"EmailEvent\"\n        WHERE\n          \"timestamp\" >= ${startDate}\n          ${template !== \"all\" ? ` AND \"templateName\" = ${template}` : \"\"}\n          AND \"templateName\" IS NOT NULL\n        GROUP BY\n          \"templateName\"\n        HAVING\n          COUNT(*) FILTER (WHERE type = 'delivered') > 0\n      )\n      SELECT\n        template,\n        'Sample Subject' as subject,\n        delivered_count as sent,\n        ROUND((opened_count::float / NULLIF(delivered_count, 0)) * 100) as \"openRate\",\n        ROUND((clicked_count::float / NULLIF(opened_count, 0)) * 100) as \"clickRate\"\n      FROM\n        email_counts\n      ORDER BY\n        \"openRate\" DESC, \"clickRate\" DESC\n      LIMIT 5\n    `;\n    return Array.isArray(topEmails) ? topEmails : [];\n  } catch (error) {\n    logger.error(\"Error getting top emails:\", error);\n    return [];\n  }\n}\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;;AAGA,eAAe,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE;AAC5B,EAAE,IAAI;AACN,IAAI,MAAM,SAAS,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,IAAI;AAC/D,IAAI,MAAM,QAAQ,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,KAAK;AAC9D,IAAI,MAAM,cAAc,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC;AAC5D,IAAI,MAAM,YAAY,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC;AACxD,IAAI,MAAM,GAAG,mBAAmB,IAAI,IAAI,EAAE;AAC1C,IAAI,IAAI,SAAS,mBAAmB,IAAI,IAAI,EAAE;AAC9C,IAAI,IAAI,OAAO,GAAG,GAAG;AACrB,IAAI,IAAI,cAAc,IAAI,YAAY,EAAE;AACxC,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC;AAC1C,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC;AACtC,KAAK,MAAM;AACX,MAAM,QAAQ,SAAS;AACvB,QAAQ,KAAK,KAAK;AAClB,UAAU,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC;AACjD,UAAU;AACV,QAAQ,KAAK,IAAI;AACjB,UAAU,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AAC9C,UAAU;AACV,QAAQ,KAAK,KAAK;AAClB,UAAU,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC;AAC/C,UAAU;AACV,QAAQ,KAAK,KAAK;AAClB,UAAU,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC;AAC/C,UAAU;AACV,QAAQ;AACR,UAAU,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AAC9C;AACA;AACA,IAAI,IAAI;AACR,MAAM,MAAM,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE;AACrC,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,MAAM,CAAC,IAAI,CAAC,sDAAsD,CAAC;AACzE,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,KAAK,EAAE;AACf,UAAU,KAAK,EAAE,CAAC;AAClB,UAAU,SAAS,EAAE,CAAC;AACtB,UAAU,MAAM,EAAE,CAAC;AACnB,UAAU,OAAO,EAAE,CAAC;AACpB,UAAU,OAAO,EAAE,CAAC;AACpB,UAAU,UAAU,EAAE,CAAC;AACvB,UAAU,YAAY,EAAE;AACxB,SAAS;AACT,QAAQ,SAAS,EAAE,EAAE;AACrB,QAAQ,SAAS,EAAE;AACnB,OAAO,CAAC;AACR;AACA,IAAI,MAAM,eAAe,GAAG;AAC5B,MAAM,SAAS,EAAE;AACjB,QAAQ,GAAG,EAAE,SAAS;AACtB,QAAQ,GAAG,EAAE;AACb;AACA,KAAK;AACL,IAAI,IAAI,QAAQ,KAAK,KAAK,EAAE;AAC5B,MAAM,eAAe,CAAC,YAAY,GAAG,QAAQ;AAC7C;AACA,IAAI,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC;AACxD,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC;AAClB,MAAM,KAAK,EAAE,eAAe;AAC5B,MAAM,MAAM,EAAE;AACd,QAAQ,EAAE,EAAE;AACZ;AACA,KAAK,CAAC;AACN,IAAI,MAAM,KAAK,GAAG;AAClB,MAAM,KAAK,EAAE,CAAC;AACd,MAAM,SAAS,EAAE,CAAC;AAClB,MAAM,MAAM,EAAE,CAAC;AACf,MAAM,OAAO,EAAE,CAAC;AAChB,MAAM,OAAO,EAAE,CAAC;AAChB,MAAM,UAAU,EAAE,CAAC;AACnB,MAAM,YAAY,EAAE;AACpB,KAAK;AACL,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK;AACnC,MAAM,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI;AAClC,MAAM,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC,EAAE;AACxC,MAAM,KAAK,CAAC,SAAS,CAAC,GAAG,UAAU;AACnC,MAAM,KAAK,CAAC,KAAK,IAAI,UAAU;AAC/B,KAAK,CAAC;AACN,IAAI,MAAM,SAAS,GAAG,MAAM,YAAY,CAAC,SAAS,EAAE,eAAe,CAAC;AACpE,IAAI,MAAM,SAAS,GAAG,MAAM,YAAY,CAAC,SAAS,EAAE,QAAQ,CAAC;AAC7D,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,KAAK;AACX,MAAM,SAAS;AACf,MAAM;AACN,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC;AACrD,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,KAAK,EAAE;AACf,UAAU,KAAK,EAAE,CAAC;AAClB,UAAU,SAAS,EAAE,CAAC;AACtB,UAAU,MAAM,EAAE,CAAC;AACnB,UAAU,OAAO,EAAE,CAAC;AACpB,UAAU,OAAO,EAAE,CAAC;AACpB,UAAU,UAAU,EAAE,CAAC;AACvB,UAAU,YAAY,EAAE;AACxB,SAAS;AACT,QAAQ,SAAS,EAAE,EAAE;AACrB,QAAQ,SAAS,EAAE,EAAE;AACrB,QAAQ,KAAK,EAAE;AACf,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;AACA,eAAe,YAAY,CAAC,SAAS,EAAE,eAAe,EAAE;AACxD,EAAE,IAAI;AACN,IAAI,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,SAAS;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,EAAE,SAAS;AAClC,QAAQ,EAAE,eAAe,CAAC,YAAY,GAAG,CAAC,sBAAsB,EAAE,eAAe,CAAC,YAAY,CAAC,CAAC,GAAG,EAAE;AACrG;AACA;AACA;AACA;AACA,IAAI,CAAC;AACL,IAAI,MAAM,SAAS,GAAG,EAAE;AACxB,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;AACpC,MAAM,MAAM,MAAM,mBAAmB,IAAI,GAAG,EAAE;AAC9C,MAAM,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AACpC,QAAQ,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAClE,QAAQ,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AAC9B,UAAU,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE;AAC1B,YAAY,GAAG;AACf,YAAY,SAAS,EAAE,CAAC;AACxB,YAAY,MAAM,EAAE,CAAC;AACrB,YAAY,OAAO,EAAE,CAAC;AACtB,YAAY,OAAO,EAAE,CAAC;AACtB,YAAY,UAAU,EAAE,CAAC;AACzB,YAAY,YAAY,EAAE;AAC1B,WAAW,CAAC;AACZ;AACA,QAAQ,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;AACvD,OAAO,CAAC;AACR,MAAM,SAAS,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;AACpD;AACA,IAAI,OAAO,SAAS;AACpB,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC;AACpD,IAAI,OAAO,EAAE;AACb;AACA;AACA,eAAe,YAAY,CAAC,SAAS,EAAE,QAAQ,EAAE;AACjD,EAAE,IAAI;AACN,IAAI,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,SAAS;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,EAAE,SAAS;AACpC,UAAU,EAAE,QAAQ,KAAK,KAAK,GAAG,CAAC,sBAAsB,EAAE,QAAQ,CAAC,CAAC,GAAG,EAAE;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC;AACL,IAAI,OAAO,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,SAAS,GAAG,EAAE;AACpD,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC;AACpD,IAAI,OAAO,EAAE;AACb;AACA;;;;"}