// cron/utils/proxyQuotaDetector.ts
// Utility to detect proxy quota exhaustion and send notifications

import { logger } from "./logger";
import { sendEmailNotification, EmailNotificationType } from "./emailService";

/**
 * Common error patterns that indicate proxy quota exhaustion
 */
const QUOTA_EXHAUSTION_PATTERNS = [
  // SmartProxy specific patterns
  /traffic.*limit.*exceeded/i,
  /quota.*exceeded/i,
  /quota.*has.*been.*reached/i,
  /bandwidth.*limit.*reached/i,
  /bandwidth.*limit.*exceeded/i,
  /monthly.*limit.*reached/i,
  /usage.*limit.*exceeded/i,
  /account.*suspended/i,
  /insufficient.*traffic/i,
  /traffic.*depleted/i,
  /data.*allowance.*exceeded/i,

  // HTTP status codes that might indicate quota issues
  /402.*payment.*required/i,
  /429.*too.*many.*requests/i,
  /503.*service.*unavailable/i,

  // Generic quota patterns
  /limit.*reached/i,
  /quota.*depleted/i,
  /allowance.*exceeded/i,
  /subscription.*expired/i,
  /plan.*limit.*exceeded/i,
];

/**
 * Patterns that indicate general proxy connectivity issues (not quota)
 */
const CONNECTIVITY_PATTERNS = [
  /net::ERR_EMPTY_RESPONSE/i,
  /net::ERR_CONNECTION_REFUSED/i,
  /net::ERR_TIMED_OUT/i,
  /ECONNREFUSED/i,
  /ENOTFOUND/i,
  /timeout/i,
];

/**
 * Check if an error indicates proxy quota exhaustion
 */
export function isProxyQuotaExhausted(error: Error | string): boolean {
  const errorMessage = typeof error === "string" ? error : error.message;

  // Check for quota exhaustion patterns
  const isQuotaError = QUOTA_EXHAUSTION_PATTERNS.some((pattern) =>
    pattern.test(errorMessage)
  );

  // Make sure it's not just a connectivity issue
  const isConnectivityError = CONNECTIVITY_PATTERNS.some((pattern) =>
    pattern.test(errorMessage)
  );

  // Return true only if it matches quota patterns and is not a simple connectivity issue
  return isQuotaError && !isConnectivityError;
}

/**
 * Check if an error indicates general proxy connectivity issues
 */
export function isProxyConnectivityIssue(error: Error | string): boolean {
  const errorMessage = typeof error === "string" ? error : error.message;

  return CONNECTIVITY_PATTERNS.some((pattern) => pattern.test(errorMessage));
}

/**
 * Analyze proxy error and determine the appropriate action
 */
export function analyzeProxyError(error: Error | string): {
  type: "quota" | "connectivity" | "unknown";
  shouldNotify: boolean;
  message: string;
} {
  if (isProxyQuotaExhausted(error)) {
    return {
      type: "quota",
      shouldNotify: true,
      message: "Proxy quota/traffic limit has been exhausted",
    };
  }

  if (isProxyConnectivityIssue(error)) {
    return {
      type: "connectivity",
      shouldNotify: true,
      message: "Proxy connectivity issues detected",
    };
  }

  return {
    type: "unknown",
    shouldNotify: false,
    message: "Unknown proxy error",
  };
}

/**
 * Send proxy quota exhaustion notification
 */
export async function sendProxyQuotaNotification(
  error: Error | string,
  additionalContext?: Record<string, any>
): Promise<void> {
  try {
    const errorMessage = typeof error === "string" ? error : error.message;
    const timestamp = new Date().toISOString();

    logger.error(`🚨 Proxy quota exhausted: ${errorMessage}`);

    const emailData = {
      error: errorMessage,
      timestamp,
      status: "QUOTA_EXHAUSTED",
      severity: "HIGH",
      action_required: "Increase proxy quota or switch to backup proxy",
      proxy_provider: "SmartProxy",
      current_usage: "Traffic limit exceeded",
      recommendation:
        "Contact SmartProxy to increase traffic allowance or enable auto-renewal",
      ...additionalContext,
    };

    await sendEmailNotification(
      EmailNotificationType.PROXY_QUOTA_EXHAUSTED,
      emailData
    );

    logger.info(`📧 Proxy quota exhaustion notification sent`);
  } catch (notificationError) {
    logger.error(
      `❌ Failed to send proxy quota notification: ${notificationError}`
    );
  }
}

/**
 * Send general proxy down notification
 */
export async function sendProxyDownNotification(
  error: Error | string,
  additionalContext?: Record<string, any>
): Promise<void> {
  try {
    const errorMessage = typeof error === "string" ? error : error.message;
    const timestamp = new Date().toISOString();

    logger.error(`🚨 Proxy connectivity issues: ${errorMessage}`);

    const emailData = {
      error: errorMessage,
      timestamp,
      status: "PROXY_DOWN",
      severity: "MEDIUM",
      action_required: "Check proxy service status",
      proxy_provider: "SmartProxy",
      recommendation: "Verify proxy credentials and service status",
      ...additionalContext,
    };

    await sendEmailNotification(EmailNotificationType.PROXY_DOWN, emailData);

    logger.info(`📧 Proxy down notification sent`);
  } catch (notificationError) {
    logger.error(
      `❌ Failed to send proxy down notification: ${notificationError}`
    );
  }
}

/**
 * Handle proxy error with appropriate notification
 */
export async function handleProxyError(
  error: Error | string,
  context?: Record<string, any>
): Promise<void> {
  const analysis = analyzeProxyError(error);

  if (!analysis.shouldNotify) {
    logger.warn(`⚠️ Proxy error (no notification): ${analysis.message}`);
    return;
  }

  logger.error(`🚨 Proxy error detected: ${analysis.message}`);

  switch (analysis.type) {
    case "quota":
      await sendProxyQuotaNotification(error, context);
      break;
    case "connectivity":
      await sendProxyDownNotification(error, context);
      break;
    default:
      logger.warn(`⚠️ Unknown proxy error type: ${analysis.type}`);
  }
}

export default {
  isProxyQuotaExhausted,
  isProxyConnectivityIssue,
  analyzeProxyError,
  sendProxyQuotaNotification,
  sendProxyDownNotification,
  handleProxyError,
};
