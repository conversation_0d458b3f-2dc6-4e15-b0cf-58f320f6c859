{"version": 3, "file": "_server.ts-X-etYvj1.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/email/clear-failed/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { l as logger } from \"../../../../../chunks/logger.js\";\nimport { g as getRedisClient } from \"../../../../../chunks/redis.js\";\nasync function POST() {\n  try {\n    logger.info(\"Clearing failed email jobs\");\n    const redis = await getRedisClient();\n    if (!redis) {\n      return json({ error: \"Redis client not available\" }, { status: 500 });\n    }\n    const failedJobs = await redis.smembers(\"email:failed\");\n    if (failedJobs.length === 0) {\n      return json({\n        status: \"success\",\n        message: \"No failed jobs to clear\"\n      });\n    }\n    await redis.del(\"email:failed\");\n    return json({\n      status: \"success\",\n      message: `Cleared ${failedJobs.length} failed jobs`\n    });\n  } catch (error) {\n    logger.error(\"Error clearing failed email jobs:\", error);\n    return json({ error: \"Failed to clear failed email jobs\" }, { status: 500 });\n  }\n}\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;AAGA,eAAe,IAAI,GAAG;AACtB,EAAE,IAAI;AACN,IAAI,MAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC;AAC7C,IAAI,MAAM,KAAK,GAAG,MAAM,cAAc,EAAE;AACxC,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,4BAA4B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3E;AACA,IAAI,MAAM,UAAU,GAAG,MAAM,KAAK,CAAC,QAAQ,CAAC,cAAc,CAAC;AAC3D,IAAI,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;AACjC,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,MAAM,EAAE,SAAS;AACzB,QAAQ,OAAO,EAAE;AACjB,OAAO,CAAC;AACR;AACA,IAAI,MAAM,KAAK,CAAC,GAAG,CAAC,cAAc,CAAC;AACnC,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,OAAO,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC,MAAM,CAAC,YAAY;AACxD,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC;AAC5D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mCAAmC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAChF;AACA;;;;"}