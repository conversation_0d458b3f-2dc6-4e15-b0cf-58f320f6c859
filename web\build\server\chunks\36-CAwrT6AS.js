import { r as redirect } from './index-Ddp2AB5f.js';
import { p as prisma } from './prisma-Cit_HrSw.js';
import '@prisma/client';

async function load({ locals, url }) {
  const user = locals.user;
  if (!user) throw redirect(302, "/auth/sign-in");
  const searches = await prisma.jobSearch.findMany({
    where: {
      userId: user.id
    },
    orderBy: { createdAt: "desc" }
  });
  const title = url.searchParams.get("title") || "";
  const locations = url.searchParams.get("locations")?.split(",").filter(Boolean) || [];
  const location = url.searchParams.get("location") || "";
  const locationType = url.searchParams.get("locationType")?.split(",").filter(Boolean) || [];
  const experience = url.searchParams.get("experience")?.split(",").filter(Boolean) || [];
  let salary = url.searchParams.get("salary") || "";
  const state = url.searchParams.get("state") || "";
  const country = url.searchParams.get("country") || "US";
  const datePosted = url.searchParams.get("datePosted") || "";
  const easyApply = url.searchParams.get("easyApply") === "true";
  const companies = url.searchParams.get("companies")?.split(",").filter(Boolean) || [];
  const validSalaryValues = [
    "0-50000",
    "50000-75000",
    "75000-100000",
    "100000-150000",
    "150000+",
    ""
  ];
  if (!validSalaryValues.includes(salary)) {
    salary = "";
  }
  return {
    user,
    searches,
    searchParams: {
      title,
      locations,
      location,
      locationType,
      experience,
      salary,
      state,
      country,
      datePosted,
      easyApply,
      companies
    }
  };
}

var _page_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 36;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-DwSHDNEE.js')).default;
const server_id = "src/routes/dashboard/jobs/+page.server.ts";
const imports = ["_app/immutable/nodes/36.6ildDWrI.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/C6g8ubaU.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/B-Xjo-Yt.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/FN1sk3P2.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/DjPYYl4Z.js","_app/immutable/chunks/Cf6rS4LV.js","_app/immutable/chunks/DV_57wcZ.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/C3w0v0gR.js","_app/immutable/chunks/BvdI7LR8.js","_app/immutable/chunks/DMTMHyMa.js","_app/immutable/chunks/CzsE_FAw.js","_app/immutable/chunks/5V1tIHTN.js","_app/immutable/chunks/ncUU1dSD.js","_app/immutable/chunks/CGK0g3x_.js","_app/immutable/chunks/BfX7a-t9.js","_app/immutable/chunks/BosuxZz1.js","_app/immutable/chunks/CnMg5bH0.js","_app/immutable/chunks/DX6rZLP_.js","_app/immutable/chunks/D2egQzE8.js","_app/immutable/chunks/Ntteq2n_.js","_app/immutable/chunks/DrQfh6BY.js","_app/immutable/chunks/DxW95yuQ.js","_app/immutable/chunks/w80wGXGd.js","_app/immutable/chunks/D-o7ybA5.js","_app/immutable/chunks/XESq6qWN.js","_app/immutable/chunks/OOsIR5sE.js","_app/immutable/chunks/BaVT73bJ.js","_app/immutable/chunks/DT9WCdWY.js","_app/immutable/chunks/Bpi49Nrf.js","_app/immutable/chunks/Cb-3cdbh.js","_app/immutable/chunks/CIOgxH3l.js","_app/immutable/chunks/DuoUhxYL.js","_app/immutable/chunks/BJIrNhIJ.js","_app/immutable/chunks/Bd3zs5C6.js","_app/immutable/chunks/BjCTmJLi.js","_app/immutable/chunks/Ci8yIwIB.js","_app/immutable/chunks/3WmhYGjL.js","_app/immutable/chunks/B1K98fMG.js","_app/immutable/chunks/DM07Bv7T.js","_app/immutable/chunks/B6TiSgAN.js","_app/immutable/chunks/Dmwghw4a.js","_app/immutable/chunks/BniYvUIG.js","_app/immutable/chunks/DW5gea7N.js","_app/immutable/chunks/B5tu6DNS.js","_app/immutable/chunks/BwkAotBa.js","_app/immutable/chunks/BBa424ah.js","_app/immutable/chunks/D4f2twK-.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/BNEH2jqx.js","_app/immutable/chunks/CfcZq63z.js","_app/immutable/chunks/yW0TxTga.js","_app/immutable/chunks/9r-6KH_O.js","_app/immutable/chunks/CTn0v-X8.js","_app/immutable/chunks/DMoa_yM9.js","_app/immutable/chunks/tdzGgazS.js","_app/immutable/chunks/CnpHcmx3.js","_app/immutable/chunks/BKLOCbjP.js","_app/immutable/chunks/D9yI7a4E.js","_app/immutable/chunks/eW6QhNR3.js","_app/immutable/chunks/B2lQHLf_.js","_app/immutable/chunks/CVVv9lPb.js","_app/immutable/chunks/KVutzy_p.js","_app/immutable/chunks/CKh8VGVX.js","_app/immutable/chunks/VYoCKyli.js","_app/immutable/chunks/DaBofrVv.js","_app/immutable/chunks/CIPPbbaT.js","_app/immutable/chunks/CwgkX8t9.js","_app/immutable/chunks/6BxQgNmX.js","_app/immutable/chunks/CDnvByek.js","_app/immutable/chunks/-SpbofVw.js","_app/immutable/chunks/DYwWIJ9y.js","_app/immutable/chunks/BAawoUIy.js","_app/immutable/chunks/BM9SsHQg.js","_app/immutable/chunks/FAbXdqfL.js","_app/immutable/chunks/C2MdR6K0.js","_app/immutable/chunks/hQ6uUXJy.js","_app/immutable/chunks/BhzFx1Wy.js"];
const stylesheets = ["_app/immutable/assets/Toaster.DKF17Rty.css","_app/immutable/assets/index.CV-KWLNP.css","_app/immutable/assets/scroll-area.bHHIbcsu.css","_app/immutable/assets/36.m2vV48tT.css"];
const fonts = [];

export { component, fonts, imports, index, _page_server_ts as server, server_id, stylesheets };
//# sourceMappingURL=36-CAwrT6AS.js.map
