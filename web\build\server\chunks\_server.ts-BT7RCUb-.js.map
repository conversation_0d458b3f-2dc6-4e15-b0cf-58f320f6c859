{"version": 3, "file": "_server.ts-BT7RCUb-.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/billing/get-payment-methods/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../../../chunks/auth.js\";\nconst GET = async ({ cookies }) => {\n  const token = cookies.get(\"auth_token\");\n  const isProd = process.env.NODE_ENV === \"production\";\n  if (!token) return new Response(\"Unauthorized\", { status: 401 });\n  const userData = await verifySessionToken(token);\n  if (!userData?.id) return new Response(\"Unauthorized\", { status: 401 });\n  const stripeSecret = isProd ? process.env.STRIPE_SECRET_KEY_LIVE || \"sk_live_placeholder\" : process.env.STRIPE_SECRET_KEY_TEST || \"sk_test_placeholder\";\n  const Stripe = (await import(\"stripe\")).default;\n  const stripe = new Stripe(stripeSecret, {\n    apiVersion: \"2025-04-30.basil\"\n  });\n  let user = await prisma.user.findUnique({ where: { id: userData.id } });\n  if (!user) return new Response(\"User not found\", { status: 404 });\n  if (!user.stripeCustomerId) return json({ paymentMethods: [] });\n  try {\n    const paymentMethodsResponse = await stripe.paymentMethods.list({\n      customer: user.stripeCustomerId,\n      type: \"card\"\n    });\n    let paymentMethods = paymentMethodsResponse.data;\n    const customer = await stripe.customers.retrieve(user.stripeCustomerId);\n    let defaultPaymentMethod = null;\n    if (customer && !(\"deleted\" in customer) && customer.invoice_settings?.default_payment_method) {\n      defaultPaymentMethod = customer.invoice_settings.default_payment_method;\n    }\n    paymentMethods = paymentMethods.map((method) => ({\n      ...method,\n      isDefault: method.id === defaultPaymentMethod\n    }));\n    return json({ paymentMethods });\n  } catch (error) {\n    console.error(\"Error fetching payment methods:\", error);\n    return new Response(\"Failed to fetch payment methods\", { status: 500 });\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;;;;;;AAGK,MAAC,GAAG,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK;AACnC,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;AACtD,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE,EAAE,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AAClD,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE,EAAE,MAAM,YAAY,GAAG,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,qBAAqB,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,qBAAqB;AACzJ,EAAE,MAAM,MAAM,GAAG,CAAC,MAAM,OAAO,+BAAQ,CAAC,EAAE,OAAO;AACjD,EAAE,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,YAAY,EAAE;AAC1C,IAAI,UAAU,EAAE;AAChB,GAAG,CAAC;AACJ,EAAE,IAAI,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE,EAAE,CAAC;AACzE,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,QAAQ,CAAC,gBAAgB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnE,EAAE,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,OAAO,IAAI,CAAC,EAAE,cAAc,EAAE,EAAE,EAAE,CAAC;AACjE,EAAE,IAAI;AACN,IAAI,MAAM,sBAAsB,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC;AACpE,MAAM,QAAQ,EAAE,IAAI,CAAC,gBAAgB;AACrC,MAAM,IAAI,EAAE;AACZ,KAAK,CAAC;AACN,IAAI,IAAI,cAAc,GAAG,sBAAsB,CAAC,IAAI;AACpD,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC;AAC3E,IAAI,IAAI,oBAAoB,GAAG,IAAI;AACnC,IAAI,IAAI,QAAQ,IAAI,EAAE,SAAS,IAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,gBAAgB,EAAE,sBAAsB,EAAE;AACnG,MAAM,oBAAoB,GAAG,QAAQ,CAAC,gBAAgB,CAAC,sBAAsB;AAC7E;AACA,IAAI,cAAc,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,MAAM;AACrD,MAAM,GAAG,MAAM;AACf,MAAM,SAAS,EAAE,MAAM,CAAC,EAAE,KAAK;AAC/B,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,IAAI,CAAC,EAAE,cAAc,EAAE,CAAC;AACnC,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC;AAC3D,IAAI,OAAO,IAAI,QAAQ,CAAC,iCAAiC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3E;AACA;;;;"}