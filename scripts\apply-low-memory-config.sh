#!/bin/bash

# Apply Low Memory Configuration Script
# This script applies memory-optimized settings for 512MB systems

echo "🔧 Applying low-memory configuration for 512MB systems..."

# Copy the low-memory environment configuration
if [ -f "cron/.env.low-memory" ]; then
    echo "📋 Copying low-memory environment configuration..."
    cp cron/.env.low-memory cron/.env
    echo "✅ Environment configuration applied"
else
    echo "❌ Low-memory configuration file not found"
    exit 1
fi

# Set environment variables for current session
export CIRCUIT_BREAKER_MEMORY_THRESHOLD=50
export CIRCUIT_BREAKER_CPU_THRESHOLD=50
export CIRCUIT_BREAKER_DEGRADED_MEMORY_THRESHOLD=30
export CIRCUIT_BREAKER_DEGRADED_CPU_THRESHOLD=30
export ENRICH_JOBS_BATCH_SIZE=5
export ENRICH_JOBS_CONCURRENCY=1
export ENRICH_JOBS_MAX_MEMORY=400
export ENRICH_JOBS_WARNING_MEMORY=300
export JOB_DETAILS_BATCH_SIZE=1
export JOB_DETAILS_MAX_CONCURRENT=1
export JOB_DETAILS_MAX_JOBS=20
export SCRAPER_MAX_WORKERS=1
export SCRAPER_BATCH_SIZE=2
export SCRAPER_CONCURRENCY=1
export DISABLE_HTML_PREVIEWS=true
export DISABLE_SCREENSHOTS=true
export DISABLE_FILE_STORAGE=true
export NODE_OPTIONS="--max-old-space-size=400"

echo "🚀 Low-memory configuration applied successfully!"
echo "📊 Key settings:"
echo "   - Memory threshold: 50%"
echo "   - Batch size: 5"
echo "   - Concurrency: 1"
echo "   - Max memory: 400MB"
echo "   - HTML previews: Disabled"
echo ""
echo "💡 To make these settings permanent, ensure the .env file is used in your deployment."
