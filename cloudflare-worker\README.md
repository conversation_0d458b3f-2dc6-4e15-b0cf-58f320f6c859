# Hirli Static Assets Proxy Worker

This Cloudflare Worker serves as a proxy for R2 static assets, providing proper CORS headers, caching, and eliminating rate limits from the `pub-*.r2.dev` endpoints.

## Features

- ✅ **Proper CORS headers** - Allows cross-origin requests from any domain
- ✅ **CDN caching** - 1-year cache with immutable headers for static assets
- ✅ **No rate limits** - Uses R2 bindings instead of public endpoints
- ✅ **Multiple buckets** - Supports company logos, resumes, and user files
- ✅ **Security headers** - Includes proper security and content-type headers
- ✅ **Conditional requests** - Supports ETag-based caching

## Deployment

### 1. Install Wrangler CLI

```bash
npm install -g wrangler
```

### 2. Login to Cloudflare

```bash
wrangler login
```

### 3. Deploy the Worker

```bash
cd cloudflare-worker
wrangler deploy
```

### 4. Set up Custom Domain (Recommended)

1. Go to Cloudflare Dashboard → Workers & Pages
2. Click on your `hirli-static-assets` worker
3. Go to Settings → Triggers
4. Add a custom domain (e.g., `static.yourdomain.com`)

### 5. Update DNS

Add a CNAME record in your DNS:
```
static.yourdomain.com → hirli-static-assets.your-subdomain.workers.dev
```

## Usage

### Company Logos
```
https://static.yourdomain.com/logos/company-name-logo-optimized.webp
```

### Resumes
```
https://static.yourdomain.com/resumes/profile-123-resume-456.pdf
```

### User Files
```
https://static.yourdomain.com/user/profile-123-document.pdf
```

### Backward Compatibility
Direct file access (defaults to company logos bucket):
```
https://static.yourdomain.com/company-name-logo-optimized.webp
```

## Update Your Application

After deploying, update your SvelteKit component to use the new domain:

```javascript
// Before (rate-limited)
const logoUrl = `https://pub-46a6f782171b440493a823a520764a72.r2.dev/${filename}`;

// After (production-ready)
const logoUrl = `https://static.yourdomain.com/logos/${filename}`;
```

## Benefits

1. **No Rate Limits** - Uses R2 bindings instead of public endpoints
2. **Proper Caching** - Files cached at Cloudflare edge locations worldwide
3. **Better Performance** - Served from nearest edge location to users
4. **CORS Support** - Proper headers for web application embedding
5. **Security** - Content-type validation and security headers
6. **Scalability** - Handles high traffic without throttling

## Monitoring

Monitor your worker in the Cloudflare Dashboard:
- Workers & Pages → hirli-static-assets → Metrics
- View request volume, error rates, and performance metrics

## Troubleshooting

### Worker not serving files
1. Check R2 bucket bindings in wrangler.toml
2. Verify bucket names match your actual R2 buckets
3. Ensure files exist in R2 with correct keys

### CORS errors
1. Verify the worker is deployed and accessible
2. Check that your application is using the worker URL, not pub-*.r2.dev
3. Clear browser cache and try again

### Performance issues
1. Check worker metrics in Cloudflare Dashboard
2. Verify cache headers are being set correctly
3. Monitor R2 request patterns
