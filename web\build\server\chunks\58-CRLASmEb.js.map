{"version": 3, "file": "58-CRLASmEb.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/settings/analysis/_page.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/58.js"], "sourcesContent": ["import { e as error } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nconst load = async ({ locals }) => {\n  if (!locals.user) {\n    throw error(401, \"Unauthorized\");\n  }\n  try {\n    const applications = await prisma.application.findMany({\n      where: { userId: locals.user.id },\n      include: {\n        interviewStages: true\n        // Include interview stages instead of non-existent job relation\n      },\n      orderBy: { createdAt: \"desc\" },\n      take: 100\n      // Limit to recent applications\n    }).catch((err) => {\n      console.error(\"Error fetching applications:\", err);\n      return [];\n    });\n    const resumes = await prisma.resume.findMany({\n      where: {\n        document: {\n          userId: locals.user.id\n        }\n      },\n      include: {\n        document: true\n      },\n      orderBy: { updatedAt: \"desc\" }\n    }).catch((err) => {\n      console.error(\"Error fetching resumes:\", err);\n      return [];\n    });\n    return {\n      user: locals.user,\n      applications,\n      resumes\n    };\n  } catch (err) {\n    console.error(\"Error loading analysis data:\", err);\n    throw error(500, \"Failed to load analysis data\");\n  }\n};\nexport {\n  load\n};\n", "import * as server from '../entries/pages/dashboard/settings/analysis/_page.server.ts.js';\n\nexport const index = 58;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/dashboard/settings/analysis/_page.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/dashboard/settings/analysis/+page.server.ts\";\nexport const imports = [\"_app/immutable/nodes/58.BeKhYdV4.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/u21ee2wt.js\",\"_app/immutable/chunks/BvdI7LR8.js\",\"_app/immutable/chunks/I7hvcB12.js\",\"_app/immutable/chunks/ncUU1dSD.js\",\"_app/immutable/chunks/B-Xjo-Yt.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/Btcx8l8F.js\",\"_app/immutable/chunks/BfX7a-t9.js\",\"_app/immutable/chunks/BosuxZz1.js\",\"_app/immutable/chunks/CnMg5bH0.js\",\"_app/immutable/chunks/BJIrNhIJ.js\",\"_app/immutable/chunks/DuoUhxYL.js\",\"_app/immutable/chunks/Bd3zs5C6.js\",\"_app/immutable/chunks/OXTnUuEm.js\",\"_app/immutable/chunks/CIOgxH3l.js\",\"_app/immutable/chunks/Bpi49Nrf.js\",\"_app/immutable/chunks/DX6rZLP_.js\",\"_app/immutable/chunks/DuGukytH.js\",\"_app/immutable/chunks/5V1tIHTN.js\",\"_app/immutable/chunks/Cdn-N1RY.js\",\"_app/immutable/chunks/BkJY4La4.js\",\"_app/immutable/chunks/GwmmX_iF.js\",\"_app/immutable/chunks/D50jIuLr.js\",\"_app/immutable/chunks/B1K98fMG.js\",\"_app/immutable/chunks/DM07Bv7T.js\",\"_app/immutable/chunks/C6g8ubaU.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/FN1sk3P2.js\",\"_app/immutable/chunks/nZgk9enP.js\",\"_app/immutable/chunks/BBa424ah.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/C88uNE8B.js\",\"_app/immutable/chunks/B-l1ubNa.js\",\"_app/immutable/chunks/D4f2twK-.js\",\"_app/immutable/chunks/C3w0v0gR.js\",\"_app/immutable/chunks/w80wGXGd.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/Csk_I0QV.js\",\"_app/immutable/chunks/BLiq6Dlm.js\",\"_app/immutable/chunks/ChqRiddM.js\",\"_app/immutable/chunks/D1zde6Ej.js\",\"_app/immutable/chunks/DmZyh-PW.js\",\"_app/immutable/chunks/iDciRV2n.js\",\"_app/immutable/chunks/DW7T7T22.js\",\"_app/immutable/chunks/CTO_B1Jk.js\"];\nexport const stylesheets = [];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;AAEA,MAAM,IAAI,GAAG,OAAO,EAAE,MAAM,EAAE,KAAK;AACnC,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;AACpB,IAAI,MAAM,KAAK,CAAC,GAAG,EAAE,cAAc,CAAC;AACpC;AACA,EAAE,IAAI;AACN,IAAI,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;AAC3D,MAAM,KAAK,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE;AACvC,MAAM,OAAO,EAAE;AACf,QAAQ,eAAe,EAAE;AACzB;AACA,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;AACpC,MAAM,IAAI,EAAE;AACZ;AACA,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK;AACtB,MAAM,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,GAAG,CAAC;AACxD,MAAM,OAAO,EAAE;AACf,KAAK,CAAC;AACN,IAAI,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;AACjD,MAAM,KAAK,EAAE;AACb,QAAQ,QAAQ,EAAE;AAClB,UAAU,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC;AAC9B;AACA,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,QAAQ,EAAE;AAClB,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM;AAClC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK;AACtB,MAAM,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,GAAG,CAAC;AACnD,MAAM,OAAO,EAAE;AACf,KAAK,CAAC;AACN,IAAI,OAAO;AACX,MAAM,IAAI,EAAE,MAAM,CAAC,IAAI;AACvB,MAAM,YAAY;AAClB,MAAM;AACN,KAAK;AACL,GAAG,CAAC,OAAO,GAAG,EAAE;AAChB,IAAI,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,GAAG,CAAC;AACtD,IAAI,MAAM,KAAK,CAAC,GAAG,EAAE,8BAA8B,CAAC;AACpD;AACA,CAAC;;;;;;;ACzCW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAA8D,CAAC,EAAE;AAE5H,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACjvD,MAAC,WAAW,GAAG;AACf,MAAC,KAAK,GAAG;;;;"}