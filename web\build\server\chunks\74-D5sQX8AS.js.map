{"version": 3, "file": "74-D5sQX8AS.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/help/_page.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/74.js"], "sourcesContent": ["import { g as getHelpArticles, a as getFeaturedHelpArticles, b as getRecentHelpArticles } from \"../../../chunks/client2.js\";\nconst load = async () => {\n  try {\n    const allArticles = await getHelpArticles();\n    const featuredArticles = await getFeaturedHelpArticles(6);\n    const recentArticles = await getRecentHelpArticles(6);\n    const categoryCounts = allArticles.reduce((acc, article) => {\n      if (!acc[article.category]) {\n        acc[article.category] = {\n          count: 0,\n          name: getCategoryName(article.category),\n          slug: article.category,\n          icon: getCategoryIcon(article.category)\n        };\n      }\n      acc[article.category].count += 1;\n      return acc;\n    }, {});\n    const categories = Object.values(categoryCounts).map((cat) => ({\n      id: cat.slug,\n      name: cat.name,\n      slug: cat.slug,\n      icon: cat.icon,\n      articleCount: cat.count\n    })).sort((a, b) => a.name.localeCompare(b.name));\n    return {\n      categories,\n      featuredArticles,\n      recentArticles\n    };\n  } catch (error) {\n    console.error(\"Error loading help page data:\", error);\n    return {\n      categories: [],\n      featuredArticles: [],\n      recentArticles: []\n    };\n  }\n};\nfunction getCategoryName(slug) {\n  const categoryMap = {\n    \"getting-started\": \"Getting Started\",\n    \"auto-apply\": \"Using Auto Apply\",\n    \"account-billing\": \"Account & Billing\",\n    troubleshooting: \"Troubleshooting\",\n    \"privacy-security\": \"Privacy & Security\"\n  };\n  return categoryMap[slug] || slug;\n}\nfunction getCategoryIcon(slug) {\n  const iconMap = {\n    \"getting-started\": \"BookOpen\",\n    \"auto-apply\": \"FileText\",\n    \"account-billing\": \"CreditCard\",\n    troubleshooting: \"HelpCircle\",\n    \"privacy-security\": \"Shield\"\n  };\n  return iconMap[slug] || \"HelpCircle\";\n}\nexport {\n  load\n};\n", "import * as server from '../entries/pages/help/_page.server.ts.js';\n\nexport const index = 74;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/help/_page.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/help/+page.server.ts\";\nexport const imports = [\"_app/immutable/nodes/74.Cwr7M4hf.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/C3w0v0gR.js\",\"_app/immutable/chunks/BvdI7LR8.js\",\"_app/immutable/chunks/B-Xjo-Yt.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/Btcx8l8F.js\",\"_app/immutable/chunks/FN1sk3P2.js\",\"_app/immutable/chunks/nZgk9enP.js\",\"_app/immutable/chunks/C6g8ubaU.js\",\"_app/immutable/chunks/DuGukytH.js\",\"_app/immutable/chunks/ncUU1dSD.js\",\"_app/immutable/chunks/5V1tIHTN.js\",\"_app/immutable/chunks/BkJY4La4.js\",\"_app/immutable/chunks/DETxXRrJ.js\",\"_app/immutable/chunks/GwmmX_iF.js\",\"_app/immutable/chunks/D50jIuLr.js\",\"_app/immutable/chunks/B1K98fMG.js\",\"_app/immutable/chunks/u21ee2wt.js\",\"_app/immutable/chunks/DM07Bv7T.js\",\"_app/immutable/chunks/DR0R8QKE.js\",\"_app/immutable/chunks/CfcZq63z.js\",\"_app/immutable/chunks/Cf6rS4LV.js\",\"_app/immutable/chunks/yW0TxTga.js\",\"_app/immutable/chunks/BBa424ah.js\",\"_app/immutable/chunks/D4f2twK-.js\",\"_app/immutable/chunks/w80wGXGd.js\",\"_app/immutable/chunks/jRvHGFcG.js\",\"_app/immutable/chunks/CGtH72Kl.js\",\"_app/immutable/chunks/C1FmrZbK.js\",\"_app/immutable/chunks/3WmhYGjL.js\",\"_app/immutable/chunks/BaVT73bJ.js\",\"_app/immutable/chunks/BfX7a-t9.js\",\"_app/immutable/chunks/DT9WCdWY.js\",\"_app/immutable/chunks/Bpi49Nrf.js\",\"_app/immutable/chunks/OOsIR5sE.js\",\"_app/immutable/chunks/Cb-3cdbh.js\",\"_app/immutable/chunks/DX6rZLP_.js\",\"_app/immutable/chunks/CIOgxH3l.js\",\"_app/immutable/chunks/DuoUhxYL.js\",\"_app/immutable/chunks/CnMg5bH0.js\",\"_app/immutable/chunks/BJIrNhIJ.js\",\"_app/immutable/chunks/D-o7ybA5.js\",\"_app/immutable/chunks/XESq6qWN.js\",\"_app/immutable/chunks/Bd3zs5C6.js\",\"_app/immutable/chunks/C2MdR6K0.js\",\"_app/immutable/chunks/hQ6uUXJy.js\",\"_app/immutable/chunks/Cs0qIT7f.js\",\"_app/immutable/chunks/BJwwRUaF.js\",\"_app/immutable/chunks/DaBofrVv.js\",\"_app/immutable/chunks/ChqRiddM.js\",\"_app/immutable/chunks/CxmsTEaf.js\",\"_app/immutable/chunks/rNI1Perp.js\",\"_app/immutable/chunks/CsOU4yHs.js\",\"_app/immutable/chunks/iDciRV2n.js\",\"_app/immutable/chunks/QtAhPN2H.js\",\"_app/immutable/chunks/yPulTJ2h.js\",\"_app/immutable/chunks/PxawOV43.js\"];\nexport const stylesheets = [\"_app/immutable/assets/scroll-area.bHHIbcsu.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;AACA,MAAM,IAAI,GAAG,YAAY;AACzB,EAAE,IAAI;AACN,IAAI,MAAM,WAAW,GAAG,MAAM,eAAe,EAAE;AAC/C,IAAI,MAAM,gBAAgB,GAAG,MAAM,uBAAuB,CAAC,CAAC,CAAC;AAC7D,IAAI,MAAM,cAAc,GAAG,MAAM,qBAAqB,CAAC,CAAC,CAAC;AACzD,IAAI,MAAM,cAAc,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,OAAO,KAAK;AAChE,MAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AAClC,QAAQ,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG;AAChC,UAAU,KAAK,EAAE,CAAC;AAClB,UAAU,IAAI,EAAE,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC;AACjD,UAAU,IAAI,EAAE,OAAO,CAAC,QAAQ;AAChC,UAAU,IAAI,EAAE,eAAe,CAAC,OAAO,CAAC,QAAQ;AAChD,SAAS;AACT;AACA,MAAM,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,KAAK,IAAI,CAAC;AACtC,MAAM,OAAO,GAAG;AAChB,KAAK,EAAE,EAAE,CAAC;AACV,IAAI,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;AACnE,MAAM,EAAE,EAAE,GAAG,CAAC,IAAI;AAClB,MAAM,IAAI,EAAE,GAAG,CAAC,IAAI;AACpB,MAAM,IAAI,EAAE,GAAG,CAAC,IAAI;AACpB,MAAM,IAAI,EAAE,GAAG,CAAC,IAAI;AACpB,MAAM,YAAY,EAAE,GAAG,CAAC;AACxB,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AACpD,IAAI,OAAO;AACX,MAAM,UAAU;AAChB,MAAM,gBAAgB;AACtB,MAAM;AACN,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC;AACzD,IAAI,OAAO;AACX,MAAM,UAAU,EAAE,EAAE;AACpB,MAAM,gBAAgB,EAAE,EAAE;AAC1B,MAAM,cAAc,EAAE;AACtB,KAAK;AACL;AACA,CAAC;AACD,SAAS,eAAe,CAAC,IAAI,EAAE;AAC/B,EAAE,MAAM,WAAW,GAAG;AACtB,IAAI,iBAAiB,EAAE,iBAAiB;AACxC,IAAI,YAAY,EAAE,kBAAkB;AACpC,IAAI,iBAAiB,EAAE,mBAAmB;AAC1C,IAAI,eAAe,EAAE,iBAAiB;AACtC,IAAI,kBAAkB,EAAE;AACxB,GAAG;AACH,EAAE,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,IAAI;AAClC;AACA,SAAS,eAAe,CAAC,IAAI,EAAE;AAC/B,EAAE,MAAM,OAAO,GAAG;AAClB,IAAI,iBAAiB,EAAE,UAAU;AACjC,IAAI,YAAY,EAAE,UAAU;AAC5B,IAAI,iBAAiB,EAAE,YAAY;AACnC,IAAI,eAAe,EAAE,YAAY;AACjC,IAAI,kBAAkB,EAAE;AACxB,GAAG;AACH,EAAE,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,YAAY;AACtC;;;;;;;ACxDY,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAAuC,CAAC,EAAE;AAErG,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACzuE,MAAC,WAAW,GAAG,CAAC,gDAAgD;AAChE,MAAC,KAAK,GAAG;;;;"}