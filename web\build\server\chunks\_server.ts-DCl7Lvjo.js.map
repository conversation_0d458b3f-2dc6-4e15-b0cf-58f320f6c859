{"version": 3, "file": "_server.ts-DCl7Lvjo.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/webhooks/stripe/_server.ts.js"], "sourcesContent": ["import Stripe from \"stripe\";\nimport { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { m as mapStripePriceIdToPlanId } from \"../../../../../chunks/stripe2.js\";\nconst isProd = process.env.NODE_ENV === \"production\";\nconst stripeSecret = isProd ? process.env.STRIPE_SECRET_KEY_LIVE || \"sk_live_placeholder\" : process.env.STRIPE_SECRET_KEY_TEST || \"sk_test_placeholder\";\nconst stripe = new Stripe(stripeSecret, {\n  apiVersion: \"2025-04-30.basil\"\n});\nconst endpointSecret = process.env.STRIPE_WEBHOOK_SECRET || \"whsec_placeholder\";\nconst POST = async ({ request }) => {\n  const sig = request.headers.get(\"stripe-signature\");\n  const body = await request.text();\n  console.log(\"🔔 Received Stripe webhook\");\n  let event;\n  try {\n    event = stripe.webhooks.constructEvent(body, sig || \"\", endpointSecret);\n    console.log(`✅ Webhook signature verified for event type: ${event.type}`);\n  } catch (err) {\n    console.error(\"⚠️ Webhook signature verification failed.\", err);\n    return new Response(\"Webhook Error\", { status: 400 });\n  }\n  try {\n    switch (event.type) {\n      case \"checkout.session.completed\": {\n        const session = event.data.object;\n        await handleCheckoutSessionCompleted(session);\n        break;\n      }\n      case \"customer.subscription.created\":\n      case \"customer.subscription.updated\": {\n        const subscription = event.data.object;\n        await handleSubscriptionUpdated(subscription);\n        break;\n      }\n      case \"customer.subscription.deleted\": {\n        const subscription = event.data.object;\n        await handleSubscriptionDeleted(subscription);\n        break;\n      }\n      case \"invoice.payment_succeeded\": {\n        const invoice = event.data.object;\n        await handleInvoicePaymentSucceeded(invoice);\n        break;\n      }\n      case \"invoice.payment_failed\": {\n        const invoice = event.data.object;\n        await handleInvoicePaymentFailed(invoice);\n        break;\n      }\n      default:\n        console.log(`Unhandled event type ${event.type}`);\n    }\n  } catch (error) {\n    console.error(`Error processing webhook event ${event.type}:`, error);\n    return new Response(`Error processing webhook: ${error.message}`, { status: 500 });\n  }\n  return json({ received: true });\n};\nasync function handleCheckoutSessionCompleted(session) {\n  console.log(\"🔄 Processing checkout.session.completed webhook\", {\n    sessionId: session.id,\n    customerId: session.customer\n  });\n  const customerId = session.customer;\n  const priceId = session.line_items?.data?.[0]?.price?.id || session.metadata?.price_id;\n  const quantity = session.line_items?.data?.[0]?.quantity || session.metadata?.seats || 1;\n  console.log(\"📦 Checkout session details\", {\n    customerId,\n    priceId,\n    quantity,\n    metadata: session.metadata\n  });\n  if (!customerId || !priceId) {\n    console.error(\"❌ Missing customer ID or price ID in checkout session\");\n    return;\n  }\n  const planId = mapStripePriceIdToPlanId(priceId);\n  console.log(\"🔍 Mapping price ID to plan ID\", { priceId, planId });\n  if (!planId) {\n    console.error(`❌ Could not map price ID ${priceId} to a plan ID`);\n    return;\n  }\n  const user = await prisma.user.findFirst({\n    where: { stripeCustomerId: customerId }\n  });\n  if (!user) {\n    console.error(`❌ User not found for Stripe customer ID ${customerId}`);\n    return;\n  }\n  console.log(\"👤 Found user\", {\n    userId: user.id,\n    email: user.email,\n    currentRole: user.role,\n    newRole: planId\n  });\n  try {\n    const subscriptions = await stripe.subscriptions.list({\n      customer: customerId,\n      limit: 1\n    });\n    if (subscriptions.data.length > 0) {\n      const subscription2 = subscriptions.data[0];\n      const isPaused = subscription2.pause_collection !== null;\n      const isPausingAtPeriodEnd = subscription2.cancel_at_period_end && subscription2.metadata?.pause_at_period_end === \"true\";\n      if (isPaused || isPausingAtPeriodEnd) {\n        console.log(\"⚠️ User has a paused subscription. Not updating user role.\");\n        const subscriptionRecord = await prisma.subscription.create({\n          data: {\n            userId: user.id,\n            stripeSessionId: session.id,\n            stripePriceId: priceId,\n            planId,\n            quantity: Number(quantity)\n          }\n        });\n        console.log(\"✅ Created subscription record for paused subscription\", {\n          subscriptionId: subscriptionRecord.id,\n          userId: user.id,\n          planId,\n          isPaused,\n          isPausingAtPeriodEnd\n        });\n        return;\n      }\n    }\n  } catch (error) {\n    console.error(\"❌ Error checking subscription status:\", error);\n  }\n  const updatedUser = await prisma.user.update({\n    where: { id: user.id },\n    data: {\n      role: planId,\n      seats: Number(quantity),\n      updatedAt: /* @__PURE__ */ new Date()\n    }\n  });\n  console.log(\"✅ Updated user role\", {\n    userId: user.id,\n    oldRole: user.role,\n    newRole: updatedUser.role\n  });\n  const subscription = await prisma.subscription.create({\n    data: {\n      userId: user.id,\n      stripeSessionId: session.id,\n      stripePriceId: priceId,\n      planId,\n      quantity: Number(quantity)\n    }\n  });\n  console.log(\"✅ Created subscription record\", {\n    subscriptionId: subscription.id,\n    userId: user.id,\n    planId\n  });\n  console.log(`✅ User ${user.email} upgraded to ${planId} plan with ${quantity} seats`);\n}\nasync function handleSubscriptionUpdated(subscription) {\n  console.log(\"🔄 Processing subscription update webhook\", {\n    subscriptionId: subscription.id,\n    customerId: subscription.customer,\n    status: subscription.status\n  });\n  const customerId = subscription.customer;\n  const priceId = subscription.items.data[0]?.price.id;\n  const quantity = subscription.items.data[0]?.quantity || 1;\n  const isPaused = subscription.pause_collection !== null;\n  const isPausingAtPeriodEnd = subscription.cancel_at_period_end && subscription.metadata?.pause_at_period_end === \"true\";\n  console.log(\"📦 Subscription details\", {\n    customerId,\n    priceId,\n    quantity,\n    status: subscription.status,\n    isPaused,\n    isPausingAtPeriodEnd,\n    cancelAtPeriodEnd: subscription.cancel_at_period_end,\n    metadata: subscription.metadata,\n    currentPeriodEnd: new Date(subscription.current_period_end * 1e3)\n  });\n  if (!customerId || !priceId) {\n    console.error(\"❌ Missing customer ID or price ID in subscription\");\n    return;\n  }\n  const planId = mapStripePriceIdToPlanId(priceId);\n  console.log(\"🔍 Mapping price ID to plan ID\", { priceId, planId });\n  if (!planId) {\n    console.error(`❌ Could not map price ID ${priceId} to a plan ID`);\n    return;\n  }\n  const user = await prisma.user.findFirst({\n    where: { stripeCustomerId: customerId }\n  });\n  if (!user) {\n    console.error(`❌ User not found for Stripe customer ID ${customerId}`);\n    return;\n  }\n  console.log(\"👤 Found user\", {\n    userId: user.id,\n    email: user.email,\n    currentRole: user.role,\n    newRole: planId\n  });\n  if (isPaused || isPausingAtPeriodEnd) {\n    console.log(\"⚠️ Subscription is paused or pausing at period end. Not updating user role.\");\n    try {\n      const subscriptionRecord = await prisma.subscription.create({\n        data: {\n          userId: user.id,\n          stripeSessionId: subscription.id,\n          stripePriceId: priceId,\n          planId,\n          quantity: Number(quantity)\n        }\n      });\n      console.log(\"✅ Created subscription record for paused subscription\", {\n        subscriptionId: subscriptionRecord.id,\n        userId: user.id,\n        planId,\n        isPaused,\n        isPausingAtPeriodEnd\n      });\n    } catch (error) {\n      console.error(\"❌ Error creating subscription record\", error);\n    }\n    return;\n  }\n  const updatedUser = await prisma.user.update({\n    where: { id: user.id },\n    data: {\n      role: planId,\n      seats: Number(quantity),\n      updatedAt: /* @__PURE__ */ new Date()\n    }\n  });\n  console.log(\"✅ Updated user role\", {\n    userId: user.id,\n    oldRole: user.role,\n    newRole: updatedUser.role\n  });\n  try {\n    const subscriptionRecord = await prisma.subscription.create({\n      data: {\n        userId: user.id,\n        stripeSessionId: subscription.id,\n        stripePriceId: priceId,\n        planId,\n        quantity: Number(quantity)\n      }\n    });\n    console.log(\"✅ Created subscription record\", {\n      subscriptionId: subscriptionRecord.id,\n      userId: user.id,\n      planId\n    });\n  } catch (error) {\n    console.error(\"❌ Error creating subscription record\", error);\n  }\n  console.log(\n    `✅ User ${user.email} subscription updated to ${planId} plan with ${quantity} seats`\n  );\n}\nasync function handleSubscriptionDeleted(subscription) {\n  const customerId = subscription.customer;\n  console.log(\"🔄 Processing subscription deletion webhook\", {\n    subscriptionId: subscription.id,\n    customerId,\n    metadata: subscription.metadata\n  });\n  const user = await prisma.user.findFirst({\n    where: { stripeCustomerId: customerId }\n  });\n  if (!user) {\n    console.error(`❌ User not found for Stripe customer ID ${customerId}`);\n    return;\n  }\n  const wasPaused = subscription.pause_collection !== null;\n  const wasPausingAtPeriodEnd = subscription.cancel_at_period_end && subscription.metadata?.pause_at_period_end === \"true\";\n  if (wasPaused || wasPausingAtPeriodEnd) {\n    console.log(\n      `⚠️ Deleted subscription was paused. Checking if user has other active subscriptions.`\n    );\n    try {\n      const otherSubscriptions = await stripe.subscriptions.list({\n        customer: customerId,\n        status: \"active\",\n        limit: 1\n      });\n      if (otherSubscriptions.data.length > 0) {\n        console.log(\n          `✅ User ${user.email} has other active subscriptions. Not downgrading to free plan.`\n        );\n        return;\n      }\n    } catch (error) {\n      console.error(\"❌ Error checking for other subscriptions:\", error);\n    }\n  }\n  await prisma.user.update({\n    where: { id: user.id },\n    data: {\n      role: \"free\",\n      seats: 1,\n      updatedAt: /* @__PURE__ */ new Date()\n    }\n  });\n  console.log(`✅ User ${user.email} subscription cancelled, downgraded to free plan`);\n}\nasync function handleInvoicePaymentSucceeded(invoice) {\n  console.log(`Invoice payment succeeded for customer ${invoice.customer}`);\n}\nasync function handleInvoicePaymentFailed(invoice) {\n  const customerId = invoice.customer;\n  const user = await prisma.user.findFirst({\n    where: { stripeCustomerId: customerId }\n  });\n  if (!user) {\n    console.error(`User not found for Stripe customer ID ${customerId}`);\n    return;\n  }\n  console.log(`⚠️ Invoice payment failed for user ${user.email}`);\n}\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAIA,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;AACpD,MAAM,YAAY,GAAG,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,qBAAqB,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,qBAAqB;AACvJ,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,YAAY,EAAE;AACxC,EAAE,UAAU,EAAE;AACd,CAAC,CAAC;AACF,MAAM,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,mBAAmB;AAC1E,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK;AACpC,EAAE,MAAM,GAAG,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC;AACrD,EAAE,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACnC,EAAE,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC;AAC3C,EAAE,IAAI,KAAK;AACX,EAAE,IAAI;AACN,IAAI,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,IAAI,EAAE,EAAE,cAAc,CAAC;AAC3E,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,6CAA6C,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;AAC7E,GAAG,CAAC,OAAO,GAAG,EAAE;AAChB,IAAI,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,GAAG,CAAC;AACnE,IAAI,OAAO,IAAI,QAAQ,CAAC,eAAe,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzD;AACA,EAAE,IAAI;AACN,IAAI,QAAQ,KAAK,CAAC,IAAI;AACtB,MAAM,KAAK,4BAA4B,EAAE;AACzC,QAAQ,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM;AACzC,QAAQ,MAAM,8BAA8B,CAAC,OAAO,CAAC;AACrD,QAAQ;AACR;AACA,MAAM,KAAK,+BAA+B;AAC1C,MAAM,KAAK,+BAA+B,EAAE;AAC5C,QAAQ,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM;AAC9C,QAAQ,MAAM,yBAAyB,CAAC,YAAY,CAAC;AACrD,QAAQ;AACR;AACA,MAAM,KAAK,+BAA+B,EAAE;AAC5C,QAAQ,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM;AAC9C,QAAQ,MAAM,yBAAyB,CAAC,YAAY,CAAC;AACrD,QAAQ;AACR;AACA,MAAM,KAAK,2BAA2B,EAAE;AACxC,QAAQ,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM;AACzC,QAAQ,MAAM,6BAA6B,CAAC,OAAO,CAAC;AACpD,QAAQ;AACR;AACA,MAAM,KAAK,wBAAwB,EAAE;AACrC,QAAQ,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM;AACzC,QAAQ,MAAM,0BAA0B,CAAC,OAAO,CAAC;AACjD,QAAQ;AACR;AACA,MAAM;AACN,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;AACzD;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,+BAA+B,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;AACzE,IAAI,OAAO,IAAI,QAAQ,CAAC,CAAC,0BAA0B,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtF;AACA,EAAE,OAAO,IAAI,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;AACjC;AACA,eAAe,8BAA8B,CAAC,OAAO,EAAE;AACvD,EAAE,OAAO,CAAC,GAAG,CAAC,kDAAkD,EAAE;AAClE,IAAI,SAAS,EAAE,OAAO,CAAC,EAAE;AACzB,IAAI,UAAU,EAAE,OAAO,CAAC;AACxB,GAAG,CAAC;AACJ,EAAE,MAAM,UAAU,GAAG,OAAO,CAAC,QAAQ;AACrC,EAAE,MAAM,OAAO,GAAG,OAAO,CAAC,UAAU,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,IAAI,OAAO,CAAC,QAAQ,EAAE,QAAQ;AACxF,EAAE,MAAM,QAAQ,GAAG,OAAO,CAAC,UAAU,EAAE,IAAI,GAAG,CAAC,CAAC,EAAE,QAAQ,IAAI,OAAO,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC;AAC1F,EAAE,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE;AAC7C,IAAI,UAAU;AACd,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,QAAQ,EAAE,OAAO,CAAC;AACtB,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,EAAE;AAC/B,IAAI,OAAO,CAAC,KAAK,CAAC,uDAAuD,CAAC;AAC1E,IAAI;AACJ;AACA,EAAE,MAAM,MAAM,GAAG,wBAAwB,CAAC,OAAO,CAAC;AAClD,EAAE,OAAO,CAAC,GAAG,CAAC,gCAAgC,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;AACpE,EAAE,IAAI,CAAC,MAAM,EAAE;AACf,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,yBAAyB,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AACrE,IAAI;AACJ;AACA,EAAE,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;AAC3C,IAAI,KAAK,EAAE,EAAE,gBAAgB,EAAE,UAAU;AACzC,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,wCAAwC,EAAE,UAAU,CAAC,CAAC,CAAC;AAC1E,IAAI;AACJ;AACA,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE;AAC/B,IAAI,MAAM,EAAE,IAAI,CAAC,EAAE;AACnB,IAAI,KAAK,EAAE,IAAI,CAAC,KAAK;AACrB,IAAI,WAAW,EAAE,IAAI,CAAC,IAAI;AAC1B,IAAI,OAAO,EAAE;AACb,GAAG,CAAC;AACJ,EAAE,IAAI;AACN,IAAI,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC;AAC1D,MAAM,QAAQ,EAAE,UAAU;AAC1B,MAAM,KAAK,EAAE;AACb,KAAK,CAAC;AACN,IAAI,IAAI,aAAa,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;AACvC,MAAM,MAAM,aAAa,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;AACjD,MAAM,MAAM,QAAQ,GAAG,aAAa,CAAC,gBAAgB,KAAK,IAAI;AAC9D,MAAM,MAAM,oBAAoB,GAAG,aAAa,CAAC,oBAAoB,IAAI,aAAa,CAAC,QAAQ,EAAE,mBAAmB,KAAK,MAAM;AAC/H,MAAM,IAAI,QAAQ,IAAI,oBAAoB,EAAE;AAC5C,QAAQ,OAAO,CAAC,GAAG,CAAC,4DAA4D,CAAC;AACjF,QAAQ,MAAM,kBAAkB,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AACpE,UAAU,IAAI,EAAE;AAChB,YAAY,MAAM,EAAE,IAAI,CAAC,EAAE;AAC3B,YAAY,eAAe,EAAE,OAAO,CAAC,EAAE;AACvC,YAAY,aAAa,EAAE,OAAO;AAClC,YAAY,MAAM;AAClB,YAAY,QAAQ,EAAE,MAAM,CAAC,QAAQ;AACrC;AACA,SAAS,CAAC;AACV,QAAQ,OAAO,CAAC,GAAG,CAAC,uDAAuD,EAAE;AAC7E,UAAU,cAAc,EAAE,kBAAkB,CAAC,EAAE;AAC/C,UAAU,MAAM,EAAE,IAAI,CAAC,EAAE;AACzB,UAAU,MAAM;AAChB,UAAU,QAAQ;AAClB,UAAU;AACV,SAAS,CAAC;AACV,QAAQ;AACR;AACA;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC;AACjE;AACA,EAAE,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AAC/C,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;AAC1B,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,KAAK,EAAE,MAAM,CAAC,QAAQ,CAAC;AAC7B,MAAM,SAAS,kBAAkB,IAAI,IAAI;AACzC;AACA,GAAG,CAAC;AACJ,EAAE,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE;AACrC,IAAI,MAAM,EAAE,IAAI,CAAC,EAAE;AACnB,IAAI,OAAO,EAAE,IAAI,CAAC,IAAI;AACtB,IAAI,OAAO,EAAE,WAAW,CAAC;AACzB,GAAG,CAAC;AACJ,EAAE,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AACxD,IAAI,IAAI,EAAE;AACV,MAAM,MAAM,EAAE,IAAI,CAAC,EAAE;AACrB,MAAM,eAAe,EAAE,OAAO,CAAC,EAAE;AACjC,MAAM,aAAa,EAAE,OAAO;AAC5B,MAAM,MAAM;AACZ,MAAM,QAAQ,EAAE,MAAM,CAAC,QAAQ;AAC/B;AACA,GAAG,CAAC;AACJ,EAAE,OAAO,CAAC,GAAG,CAAC,+BAA+B,EAAE;AAC/C,IAAI,cAAc,EAAE,YAAY,CAAC,EAAE;AACnC,IAAI,MAAM,EAAE,IAAI,CAAC,EAAE;AACnB,IAAI;AACJ,GAAG,CAAC;AACJ,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,MAAM,CAAC,WAAW,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;AACvF;AACA,eAAe,yBAAyB,CAAC,YAAY,EAAE;AACvD,EAAE,OAAO,CAAC,GAAG,CAAC,2CAA2C,EAAE;AAC3D,IAAI,cAAc,EAAE,YAAY,CAAC,EAAE;AACnC,IAAI,UAAU,EAAE,YAAY,CAAC,QAAQ;AACrC,IAAI,MAAM,EAAE,YAAY,CAAC;AACzB,GAAG,CAAC;AACJ,EAAE,MAAM,UAAU,GAAG,YAAY,CAAC,QAAQ;AAC1C,EAAE,MAAM,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE;AACtD,EAAE,MAAM,QAAQ,GAAG,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,IAAI,CAAC;AAC5D,EAAE,MAAM,QAAQ,GAAG,YAAY,CAAC,gBAAgB,KAAK,IAAI;AACzD,EAAE,MAAM,oBAAoB,GAAG,YAAY,CAAC,oBAAoB,IAAI,YAAY,CAAC,QAAQ,EAAE,mBAAmB,KAAK,MAAM;AACzH,EAAE,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE;AACzC,IAAI,UAAU;AACd,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,MAAM,EAAE,YAAY,CAAC,MAAM;AAC/B,IAAI,QAAQ;AACZ,IAAI,oBAAoB;AACxB,IAAI,iBAAiB,EAAE,YAAY,CAAC,oBAAoB;AACxD,IAAI,QAAQ,EAAE,YAAY,CAAC,QAAQ;AACnC,IAAI,gBAAgB,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC,kBAAkB,GAAG,GAAG;AACpE,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,EAAE;AAC/B,IAAI,OAAO,CAAC,KAAK,CAAC,mDAAmD,CAAC;AACtE,IAAI;AACJ;AACA,EAAE,MAAM,MAAM,GAAG,wBAAwB,CAAC,OAAO,CAAC;AAClD,EAAE,OAAO,CAAC,GAAG,CAAC,gCAAgC,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;AACpE,EAAE,IAAI,CAAC,MAAM,EAAE;AACf,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,yBAAyB,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AACrE,IAAI;AACJ;AACA,EAAE,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;AAC3C,IAAI,KAAK,EAAE,EAAE,gBAAgB,EAAE,UAAU;AACzC,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,wCAAwC,EAAE,UAAU,CAAC,CAAC,CAAC;AAC1E,IAAI;AACJ;AACA,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE;AAC/B,IAAI,MAAM,EAAE,IAAI,CAAC,EAAE;AACnB,IAAI,KAAK,EAAE,IAAI,CAAC,KAAK;AACrB,IAAI,WAAW,EAAE,IAAI,CAAC,IAAI;AAC1B,IAAI,OAAO,EAAE;AACb,GAAG,CAAC;AACJ,EAAE,IAAI,QAAQ,IAAI,oBAAoB,EAAE;AACxC,IAAI,OAAO,CAAC,GAAG,CAAC,6EAA6E,CAAC;AAC9F,IAAI,IAAI;AACR,MAAM,MAAM,kBAAkB,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AAClE,QAAQ,IAAI,EAAE;AACd,UAAU,MAAM,EAAE,IAAI,CAAC,EAAE;AACzB,UAAU,eAAe,EAAE,YAAY,CAAC,EAAE;AAC1C,UAAU,aAAa,EAAE,OAAO;AAChC,UAAU,MAAM;AAChB,UAAU,QAAQ,EAAE,MAAM,CAAC,QAAQ;AACnC;AACA,OAAO,CAAC;AACR,MAAM,OAAO,CAAC,GAAG,CAAC,uDAAuD,EAAE;AAC3E,QAAQ,cAAc,EAAE,kBAAkB,CAAC,EAAE;AAC7C,QAAQ,MAAM,EAAE,IAAI,CAAC,EAAE;AACvB,QAAQ,MAAM;AACd,QAAQ,QAAQ;AAChB,QAAQ;AACR,OAAO,CAAC;AACR,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC;AAClE;AACA,IAAI;AACJ;AACA,EAAE,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AAC/C,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;AAC1B,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,KAAK,EAAE,MAAM,CAAC,QAAQ,CAAC;AAC7B,MAAM,SAAS,kBAAkB,IAAI,IAAI;AACzC;AACA,GAAG,CAAC;AACJ,EAAE,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE;AACrC,IAAI,MAAM,EAAE,IAAI,CAAC,EAAE;AACnB,IAAI,OAAO,EAAE,IAAI,CAAC,IAAI;AACtB,IAAI,OAAO,EAAE,WAAW,CAAC;AACzB,GAAG,CAAC;AACJ,EAAE,IAAI;AACN,IAAI,MAAM,kBAAkB,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AAChE,MAAM,IAAI,EAAE;AACZ,QAAQ,MAAM,EAAE,IAAI,CAAC,EAAE;AACvB,QAAQ,eAAe,EAAE,YAAY,CAAC,EAAE;AACxC,QAAQ,aAAa,EAAE,OAAO;AAC9B,QAAQ,MAAM;AACd,QAAQ,QAAQ,EAAE,MAAM,CAAC,QAAQ;AACjC;AACA,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,+BAA+B,EAAE;AACjD,MAAM,cAAc,EAAE,kBAAkB,CAAC,EAAE;AAC3C,MAAM,MAAM,EAAE,IAAI,CAAC,EAAE;AACrB,MAAM;AACN,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC;AAChE;AACA,EAAE,OAAO,CAAC,GAAG;AACb,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,yBAAyB,EAAE,MAAM,CAAC,WAAW,EAAE,QAAQ,CAAC,MAAM;AACvF,GAAG;AACH;AACA,eAAe,yBAAyB,CAAC,YAAY,EAAE;AACvD,EAAE,MAAM,UAAU,GAAG,YAAY,CAAC,QAAQ;AAC1C,EAAE,OAAO,CAAC,GAAG,CAAC,6CAA6C,EAAE;AAC7D,IAAI,cAAc,EAAE,YAAY,CAAC,EAAE;AACnC,IAAI,UAAU;AACd,IAAI,QAAQ,EAAE,YAAY,CAAC;AAC3B,GAAG,CAAC;AACJ,EAAE,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;AAC3C,IAAI,KAAK,EAAE,EAAE,gBAAgB,EAAE,UAAU;AACzC,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,wCAAwC,EAAE,UAAU,CAAC,CAAC,CAAC;AAC1E,IAAI;AACJ;AACA,EAAE,MAAM,SAAS,GAAG,YAAY,CAAC,gBAAgB,KAAK,IAAI;AAC1D,EAAE,MAAM,qBAAqB,GAAG,YAAY,CAAC,oBAAoB,IAAI,YAAY,CAAC,QAAQ,EAAE,mBAAmB,KAAK,MAAM;AAC1H,EAAE,IAAI,SAAS,IAAI,qBAAqB,EAAE;AAC1C,IAAI,OAAO,CAAC,GAAG;AACf,MAAM,CAAC,oFAAoF;AAC3F,KAAK;AACL,IAAI,IAAI;AACR,MAAM,MAAM,kBAAkB,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC;AACjE,QAAQ,QAAQ,EAAE,UAAU;AAC5B,QAAQ,MAAM,EAAE,QAAQ;AACxB,QAAQ,KAAK,EAAE;AACf,OAAO,CAAC;AACR,MAAM,IAAI,kBAAkB,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;AAC9C,QAAQ,OAAO,CAAC,GAAG;AACnB,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,8DAA8D;AAC7F,SAAS;AACT,QAAQ;AACR;AACA,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC;AACvE;AACA;AACA,EAAE,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AAC3B,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;AAC1B,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,KAAK,EAAE,CAAC;AACd,MAAM,SAAS,kBAAkB,IAAI,IAAI;AACzC;AACA,GAAG,CAAC;AACJ,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC;AACrF;AACA,eAAe,6BAA6B,CAAC,OAAO,EAAE;AACtD,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,uCAAuC,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;AAC3E;AACA,eAAe,0BAA0B,CAAC,OAAO,EAAE;AACnD,EAAE,MAAM,UAAU,GAAG,OAAO,CAAC,QAAQ;AACrC,EAAE,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;AAC3C,IAAI,KAAK,EAAE,EAAE,gBAAgB,EAAE,UAAU;AACzC,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,sCAAsC,EAAE,UAAU,CAAC,CAAC,CAAC;AACxE,IAAI;AACJ;AACA,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,mCAAmC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACjE;;;;"}