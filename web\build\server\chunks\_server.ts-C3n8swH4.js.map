{"version": 3, "file": "_server.ts-C3n8swH4.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/feature-usage/with-plan-limits/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { v as verifySessionToken } from \"../../../../../chunks/auth.js\";\nimport { getFeatureUsage } from \"../../../../../chunks/feature-usage.js\";\nconst GET = async ({ cookies }) => {\n  const token = cookies.get(\"auth_token\");\n  if (!token) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  const userData = verifySessionToken(token);\n  if (!userData?.id) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  try {\n    const usageData = await getFeatureUsage(userData.id);\n    const groupedUsage = usageData.reduce((acc, usage) => {\n      if (!acc[usage.featureId]) {\n        acc[usage.featureId] = {\n          id: usage.featureId,\n          name: usage.featureName,\n          usage: []\n        };\n      }\n      acc[usage.featureId].usage.push({\n        limitId: usage.limitId,\n        limitName: usage.limitName,\n        used: usage.used,\n        limit: usage.limit,\n        remaining: usage.remaining,\n        percentUsed: usage.percentUsed,\n        period: usage.period,\n        updatedAt: usage.updatedAt\n      });\n      return acc;\n    }, {});\n    const features = Object.values(groupedUsage);\n    return json({ features });\n  } catch (error) {\n    console.error(\"Error in feature usage API:\", error);\n    return json(\n      {\n        features: [],\n        error: error.message || \"An error occurred while fetching feature usage data\"\n      },\n      { status: 500 }\n    );\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAGK,MAAC,GAAG,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK;AACnC,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE;AACd,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,MAAM,QAAQ,GAAG,kBAAkB,CAAC,KAAK,CAAC;AAC5C,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,SAAS,GAAG,MAAM,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAC;AACxD,IAAI,MAAM,YAAY,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,KAAK;AAC1D,MAAM,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;AACjC,QAAQ,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG;AAC/B,UAAU,EAAE,EAAE,KAAK,CAAC,SAAS;AAC7B,UAAU,IAAI,EAAE,KAAK,CAAC,WAAW;AACjC,UAAU,KAAK,EAAE;AACjB,SAAS;AACT;AACA,MAAM,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC;AACtC,QAAQ,OAAO,EAAE,KAAK,CAAC,OAAO;AAC9B,QAAQ,SAAS,EAAE,KAAK,CAAC,SAAS;AAClC,QAAQ,IAAI,EAAE,KAAK,CAAC,IAAI;AACxB,QAAQ,KAAK,EAAE,KAAK,CAAC,KAAK;AAC1B,QAAQ,SAAS,EAAE,KAAK,CAAC,SAAS;AAClC,QAAQ,WAAW,EAAE,KAAK,CAAC,WAAW;AACtC,QAAQ,MAAM,EAAE,KAAK,CAAC,MAAM;AAC5B,QAAQ,SAAS,EAAE,KAAK,CAAC;AACzB,OAAO,CAAC;AACR,MAAM,OAAO,GAAG;AAChB,KAAK,EAAE,EAAE,CAAC;AACV,IAAI,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;AAChD,IAAI,OAAO,IAAI,CAAC,EAAE,QAAQ,EAAE,CAAC;AAC7B,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC;AACvD,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,QAAQ,EAAE,EAAE;AACpB,QAAQ,KAAK,EAAE,KAAK,CAAC,OAAO,IAAI;AAChC,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;;;;"}