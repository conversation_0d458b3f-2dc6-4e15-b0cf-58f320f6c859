import { p as prisma } from './prisma-Cit_HrSw.js';
import { r as redirect } from './index-Ddp2AB5f.js';
import '@prisma/client';

const load = async ({ params, locals }) => {
  const user = locals.user;
  if (!user) throw redirect(302, "/auth/sign-in");
  const resume = await prisma.resume.findFirst({
    where: {
      id: params.id,
      OR: [
        { profile: { userId: user.id } },
        {
          profile: {
            team: {
              members: { some: { userId: user.id } }
            }
          }
        }
      ]
    },
    include: {
      profile: true,
      document: true,
      // Include the document to get the fileUrl
      optimizationResult: true,
      JobSearch: {
        take: 1,
        orderBy: { createdAt: "desc" },
        include: {
          _count: { select: { results: true } }
        }
      }
    }
  });
  if (!resume) throw redirect(302, "/dashboard/resumes");
  return {
    resume: {
      id: resume.id,
      name: resume.document?.label ?? "",
      fileName: resume.document?.fileName ?? resume.document?.fileUrl?.split("/").pop() ?? "",
      fileUrl: resume.document?.fileUrl ?? "",
      createdAt: resume.createdAt,
      score: resume.optimizationResult?.score ?? null,
      profile: {
        name: resume.profile?.name ?? "Unknown"
      },
      jobSearch: resume.JobSearch[0] ?? null
    }
  };
};

var _page_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 41;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-DG6yyfpU.js')).default;
const server_id = "src/routes/dashboard/resumes/[id]/+page.server.ts";
const imports = ["_app/immutable/nodes/41.DivE1EN4.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/B-Xjo-Yt.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/B1K98fMG.js","_app/immutable/chunks/ncUU1dSD.js","_app/immutable/chunks/5V1tIHTN.js","_app/immutable/chunks/DM07Bv7T.js","_app/immutable/chunks/DuGukytH.js","_app/immutable/chunks/Cdn-N1RY.js","_app/immutable/chunks/GwmmX_iF.js","_app/immutable/chunks/D50jIuLr.js","_app/immutable/chunks/FN1sk3P2.js","_app/immutable/chunks/I7hvcB12.js","_app/immutable/chunks/BfX7a-t9.js","_app/immutable/chunks/BosuxZz1.js","_app/immutable/chunks/CnMg5bH0.js","_app/immutable/chunks/BvdI7LR8.js","_app/immutable/chunks/BJIrNhIJ.js","_app/immutable/chunks/DuoUhxYL.js","_app/immutable/chunks/Bd3zs5C6.js","_app/immutable/chunks/OXTnUuEm.js","_app/immutable/chunks/CIOgxH3l.js","_app/immutable/chunks/Bpi49Nrf.js","_app/immutable/chunks/DX6rZLP_.js","_app/immutable/chunks/C88uNE8B.js","_app/immutable/chunks/DmZyh-PW.js"];
const stylesheets = [];
const fonts = [];

export { component, fonts, imports, index, _page_server_ts as server, server_id, stylesheets };
//# sourceMappingURL=41-CvHPHC8L.js.map
