{"version": 3, "file": "_page.svelte-CcZE29wp.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/blog/_slug_/_page.svelte.js"], "sourcesContent": ["import { V as escape_html, R as attr, U as ensure_array_like, N as bind_props, y as pop, w as push } from \"../../../../chunks/index3.js\";\nimport { S as SEO } from \"../../../../chunks/SEO.js\";\nimport { u as urlFor } from \"../../../../chunks/sanityClient.js\";\nimport { P as PortableText } from \"../../../../chunks/PortableText.js\";\nimport { P as PostCard } from \"../../../../chunks/PostCard.js\";\nimport { A as Arrow_left } from \"../../../../chunks/arrow-left.js\";\nfunction _page($$payload, $$props) {\n  push();\n  let data = $$props[\"data\"];\n  const { post, relatedPosts } = data;\n  function formatDate(dateStr) {\n    const date = new Date(dateStr);\n    return date.toLocaleDateString(\"en-US\", {\n      year: \"numeric\",\n      month: \"long\",\n      day: \"numeric\"\n    });\n  }\n  SEO($$payload, {\n    title: post.title,\n    description: post.excerpt || `Read ${post.title} on the Hirli blog`,\n    keywords: post.categories?.map((cat) => cat.title).join(\", \") || \"blog, career advice, job search\"\n  });\n  $$payload.out += `<!----> <div class=\"container mx-auto max-w-4xl px-4 py-12\"><div class=\"mb-8\"><a href=\"/blog\" class=\"text-primary mb-4 inline-flex items-center hover:underline\">`;\n  Arrow_left($$payload, { class: \"mr-2 h-4 w-4\" });\n  $$payload.out += `<!----> Back to Blog</a> <h1 class=\"mb-4 text-3xl font-bold md:text-4xl\">${escape_html(post.title)}</h1> <div class=\"mb-6 flex flex-wrap items-center gap-x-4 gap-y-2 text-sm text-gray-600\">`;\n  if (post.author) {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<div class=\"flex items-center gap-2\">`;\n    if (post.author.image) {\n      $$payload.out += \"<!--[-->\";\n      $$payload.out += `<img${attr(\"src\", urlFor(post.author.image, { width: 40, height: 40 }))}${attr(\"alt\", post.author.name)} class=\"h-8 w-8 rounded-full object-cover\"/>`;\n    } else {\n      $$payload.out += \"<!--[!-->\";\n    }\n    $$payload.out += `<!--]--> <span>${escape_html(post.author.name)}</span></div> <span class=\"hidden md:inline\">•</span>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--> <span>${escape_html(formatDate(post.publishedAt))}</span> `;\n  if (post.estimatedReadingTime) {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<span class=\"hidden md:inline\">•</span> <span>${escape_html(post.estimatedReadingTime)} min read</span>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--> `;\n  if (post.categories && post.categories.length > 0) {\n    $$payload.out += \"<!--[-->\";\n    const each_array = ensure_array_like(post.categories);\n    $$payload.out += `<span class=\"hidden md:inline\">•</span> <div class=\"flex flex-wrap gap-2\"><!--[-->`;\n    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n      let category = each_array[$$index];\n      $$payload.out += `<span class=\"rounded-full bg-gray-100 px-3 py-1 text-xs\">${escape_html(category.title)}</span>`;\n    }\n    $$payload.out += `<!--]--></div>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--></div></div> `;\n  if (post.mainImage) {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<div class=\"mb-8 overflow-hidden rounded-lg\"><img${attr(\"src\", urlFor(post.mainImage, { width: 1200, height: 600 }))}${attr(\"alt\", post.title)} class=\"h-auto w-full object-cover\"/></div>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--> <div class=\"prose prose-lg max-w-none\">`;\n  if (post.body) {\n    $$payload.out += \"<!--[-->\";\n    PortableText($$payload, { value: post.body });\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<p>Content not available.</p>`;\n  }\n  $$payload.out += `<!--]--></div> `;\n  if (relatedPosts && relatedPosts.length > 0) {\n    $$payload.out += \"<!--[-->\";\n    const each_array_1 = ensure_array_like(relatedPosts);\n    $$payload.out += `<div class=\"mt-16\"><h2 class=\"mb-8 text-2xl font-bold\">Related Articles</h2> <div class=\"grid gap-8 md:grid-cols-2 lg:grid-cols-3\"><!--[-->`;\n    for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {\n      let relatedPost = each_array_1[$$index_1];\n      PostCard($$payload, { post: relatedPost });\n    }\n    $$payload.out += `<!--]--></div></div>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--></div>`;\n  bind_props($$props, { data });\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAMA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC;AAC5B,EAAE,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG,IAAI;AACrC,EAAE,SAAS,UAAU,CAAC,OAAO,EAAE;AAC/B,IAAI,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC;AAClC,IAAI,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE;AAC5C,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,KAAK,EAAE,MAAM;AACnB,MAAM,GAAG,EAAE;AACX,KAAK,CAAC;AACN;AACA,EAAE,GAAG,CAAC,SAAS,EAAE;AACjB,IAAI,KAAK,EAAE,IAAI,CAAC,KAAK;AACrB,IAAI,WAAW,EAAE,IAAI,CAAC,OAAO,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC;AACvE,IAAI,QAAQ,EAAE,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;AACrE,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,iKAAiK,CAAC;AACtL,EAAE,UAAU,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AAClD,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,yEAAyE,EAAE,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,0FAA0F,CAAC;AAClN,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE;AACnB,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,qCAAqC,CAAC;AAC5D,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;AAC3B,MAAM,SAAS,CAAC,GAAG,IAAI,UAAU;AACjC,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,4CAA4C,CAAC;AAC7K,KAAK,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,IAAI,WAAW;AAClC;AACA,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,eAAe,EAAE,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,qDAAqD,CAAC;AAC3H,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,eAAe,EAAE,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC;AACxF,EAAE,IAAI,IAAI,CAAC,oBAAoB,EAAE;AACjC,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,8CAA8C,EAAE,WAAW,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,gBAAgB,CAAC;AAC9H,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AAC9B,EAAE,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;AACrD,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,MAAM,UAAU,GAAG,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC;AACzD,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,kFAAkF,CAAC;AACzG,IAAI,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACvF,MAAM,IAAI,QAAQ,GAAG,UAAU,CAAC,OAAO,CAAC;AACxC,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,yDAAyD,EAAE,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC;AACvH;AACA,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACrC,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC;AAC1C,EAAE,IAAI,IAAI,CAAC,SAAS,EAAE;AACtB,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,iDAAiD,EAAE,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,2CAA2C,CAAC;AACjN,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,gDAAgD,CAAC;AACrE,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE;AACjB,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,YAAY,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC;AACjD,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,6BAA6B,CAAC;AACpD;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;AACpC,EAAE,IAAI,YAAY,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;AAC/C,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,MAAM,YAAY,GAAG,iBAAiB,CAAC,YAAY,CAAC;AACxD,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,2IAA2I,CAAC;AAClK,IAAI,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,QAAQ,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,QAAQ,EAAE,SAAS,EAAE,EAAE;AAC/F,MAAM,IAAI,WAAW,GAAG,YAAY,CAAC,SAAS,CAAC;AAC/C,MAAM,QAAQ,CAAC,SAAS,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC;AAChD;AACA,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC;AAC3C,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACnC,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,CAAC;AAC/B,EAAE,GAAG,EAAE;AACP;;;;"}