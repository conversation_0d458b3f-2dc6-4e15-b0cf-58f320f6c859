{"version": 3, "file": "_server.ts-Bi2DT42j.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/search/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../chunks/index.js\";\nimport { v as verifySessionToken } from \"../../../../chunks/auth.js\";\nimport { p as prisma } from \"../../../../chunks/prisma.js\";\nimport { R as RedisConnection } from \"../../../../chunks/redis.js\";\nasync function POST({ request, cookies }) {\n  const token = cookies.get(\"auth_token\");\n  const user = token && await verifySessionToken(token);\n  if (!user) return new Response(\"Unauthorized\", { status: 401 });\n  const { profileId, query, location, locationType, experience, category, education, salary } = await request.json();\n  if (!query || !profileId) {\n    return new Response(\"Missing required fields: query and profileId\", { status: 400 });\n  }\n  try {\n    const profile = await prisma.profile.findFirst({\n      where: {\n        id: profileId,\n        userId: user.id\n      }\n    });\n    if (!profile) {\n      return new Response(\"Profile not found or not authorized\", { status: 404 });\n    }\n    const jobSearch = await prisma.jobSearch.create({\n      data: {\n        title: query,\n        location: location || null,\n        workType: locationType?.join(\", \") || null,\n        experience: experience?.join(\", \") || null,\n        category: category?.join(\", \") || null,\n        education: education?.join(\", \") || null,\n        salary: salary || null,\n        profileId,\n        userId: user.id\n      }\n    });\n    const job = {\n      jobSearchId: jobSearch.id,\n      userId: user.id,\n      profileId,\n      query,\n      location,\n      locationType,\n      experience,\n      category,\n      education,\n      salary\n    };\n    await RedisConnection.xadd(\"job-search\", \"*\", \"job\", JSON.stringify(job));\n    return json({ searchId: jobSearch.id });\n  } catch (error) {\n    console.error(\"Job search error:\", error);\n    return new Response(\"Failed to create job search\", {\n      status: 500,\n      statusText: error instanceof Error ? error.message : String(error)\n    });\n  }\n}\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA,eAAe,IAAI,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE;AAC1C,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,MAAM,IAAI,GAAG,KAAK,IAAI,MAAM,kBAAkB,CAAC,KAAK,CAAC;AACvD,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,YAAY,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACpH,EAAE,IAAI,CAAC,KAAK,IAAI,CAAC,SAAS,EAAE;AAC5B,IAAI,OAAO,IAAI,QAAQ,CAAC,8CAA8C,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxF;AACA,EAAE,IAAI;AACN,IAAI,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;AACnD,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE,SAAS;AACrB,QAAQ,MAAM,EAAE,IAAI,CAAC;AACrB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,OAAO,EAAE;AAClB,MAAM,OAAO,IAAI,QAAQ,CAAC,qCAAqC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjF;AACA,IAAI,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;AACpD,MAAM,IAAI,EAAE;AACZ,QAAQ,KAAK,EAAE,KAAK;AACpB,QAAQ,QAAQ,EAAE,QAAQ,IAAI,IAAI;AAClC,QAAQ,QAAQ,EAAE,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;AAClD,QAAQ,UAAU,EAAE,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;AAClD,QAAQ,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;AAC9C,QAAQ,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;AAChD,QAAQ,MAAM,EAAE,MAAM,IAAI,IAAI;AAC9B,QAAQ,SAAS;AACjB,QAAQ,MAAM,EAAE,IAAI,CAAC;AACrB;AACA,KAAK,CAAC;AACN,IAAI,MAAM,GAAG,GAAG;AAChB,MAAM,WAAW,EAAE,SAAS,CAAC,EAAE;AAC/B,MAAM,MAAM,EAAE,IAAI,CAAC,EAAE;AACrB,MAAM,SAAS;AACf,MAAM,KAAK;AACX,MAAM,QAAQ;AACd,MAAM,YAAY;AAClB,MAAM,UAAU;AAChB,MAAM,QAAQ;AACd,MAAM,SAAS;AACf,MAAM;AACN,KAAK;AACL,IAAI,MAAM,eAAe,CAAC,IAAI,CAAC,YAAY,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AAC7E,IAAI,OAAO,IAAI,CAAC,EAAE,QAAQ,EAAE,SAAS,CAAC,EAAE,EAAE,CAAC;AAC3C,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC;AAC7C,IAAI,OAAO,IAAI,QAAQ,CAAC,6BAA6B,EAAE;AACvD,MAAM,MAAM,EAAE,GAAG;AACjB,MAAM,UAAU,EAAE,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC,KAAK;AACvE,KAAK,CAAC;AACN;AACA;;;;"}