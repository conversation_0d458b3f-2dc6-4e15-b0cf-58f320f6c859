{"version": 3, "file": "_server.ts-Bd1DUmVF.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/applications/add/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../../../chunks/auth.js\";\nconst POST = async ({ request, cookies }) => {\n  try {\n    const token = cookies.get(\"auth_token\");\n    if (!token) {\n      return json({ error: \"Unauthorized\" }, { status: 401 });\n    }\n    const user = await verifySessionToken(token);\n    if (!user) {\n      return json({ error: \"Invalid token\" }, { status: 401 });\n    }\n    const { jobId, jobUrl, company, position, location } = await request.json();\n    if (!company || !position) {\n      return json({ error: \"Company and position are required\" }, { status: 400 });\n    }\n    if (jobUrl) {\n      const existingApplication = await prisma.application.findFirst({\n        where: {\n          userId: user.id,\n          url: jobUrl\n        }\n      });\n      if (existingApplication) {\n        return json({\n          message: \"Already applied to this job\",\n          application: existingApplication\n        });\n      }\n    }\n    const application = await prisma.application.create({\n      data: {\n        userId: user.id,\n        company,\n        position,\n        location: location || null,\n        appliedDate: /* @__PURE__ */ new Date(),\n        status: \"Applied\",\n        url: jobUrl || null,\n        jobType: \"Full-time\",\n        resumeUploaded: false\n      }\n    });\n    if (jobId) {\n      try {\n        await prisma.job_match_result.updateMany({\n          where: {\n            userId: user.id,\n            jobId\n          },\n          data: {\n            applied: true\n          }\n        });\n      } catch (error) {\n        console.error(\"Error updating job match result:\", error);\n      }\n    }\n    return json({\n      success: true,\n      message: \"Application added successfully\",\n      application\n    });\n  } catch (error) {\n    console.error(\"Error adding application:\", error);\n    return json(\n      { error: \"Failed to add application\" },\n      { status: 500 }\n    );\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;AAGK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AAC7C,EAAE,IAAI;AACN,IAAI,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AAC3C,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7D;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AAChD,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9D;AACA,IAAI,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AAC/E,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,QAAQ,EAAE;AAC/B,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mCAAmC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClF;AACA,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,MAAM,mBAAmB,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC;AACrE,QAAQ,KAAK,EAAE;AACf,UAAU,MAAM,EAAE,IAAI,CAAC,EAAE;AACzB,UAAU,GAAG,EAAE;AACf;AACA,OAAO,CAAC;AACR,MAAM,IAAI,mBAAmB,EAAE;AAC/B,QAAQ,OAAO,IAAI,CAAC;AACpB,UAAU,OAAO,EAAE,6BAA6B;AAChD,UAAU,WAAW,EAAE;AACvB,SAAS,CAAC;AACV;AACA;AACA,IAAI,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;AACxD,MAAM,IAAI,EAAE;AACZ,QAAQ,MAAM,EAAE,IAAI,CAAC,EAAE;AACvB,QAAQ,OAAO;AACf,QAAQ,QAAQ;AAChB,QAAQ,QAAQ,EAAE,QAAQ,IAAI,IAAI;AAClC,QAAQ,WAAW,kBAAkB,IAAI,IAAI,EAAE;AAC/C,QAAQ,MAAM,EAAE,SAAS;AACzB,QAAQ,GAAG,EAAE,MAAM,IAAI,IAAI;AAC3B,QAAQ,OAAO,EAAE,WAAW;AAC5B,QAAQ,cAAc,EAAE;AACxB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,IAAI;AACV,QAAQ,MAAM,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC;AACjD,UAAU,KAAK,EAAE;AACjB,YAAY,MAAM,EAAE,IAAI,CAAC,EAAE;AAC3B,YAAY;AACZ,WAAW;AACX,UAAU,IAAI,EAAE;AAChB,YAAY,OAAO,EAAE;AACrB;AACA,SAAS,CAAC;AACV,OAAO,CAAC,OAAO,KAAK,EAAE;AACtB,QAAQ,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC;AAChE;AACA;AACA,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM;AACN,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC;AACrD,IAAI,OAAO,IAAI;AACf,MAAM,EAAE,KAAK,EAAE,2BAA2B,EAAE;AAC5C,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;;;;"}