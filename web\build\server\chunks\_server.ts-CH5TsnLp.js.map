{"version": 3, "file": "_server.ts-CH5TsnLp.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/applications/_applicationId_/interviews/_interviewId_/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../../../chunks/prisma.js\";\nasync function GET({ params, locals }) {\n  console.log(\"GET interview stage:\", params.interviewId);\n  console.log(\"User in locals:\", locals.user ? `ID: ${locals.user.id}` : \"Not authenticated\");\n  const isDev = process.env.NODE_ENV === \"development\";\n  if (!locals.user && !isDev) {\n    console.log(\"Unauthorized access attempt - no user in locals\");\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  const { applicationId, interviewId } = params;\n  try {\n    if (!isDev && locals.user) {\n      const application = await prisma.application.findUnique({\n        where: {\n          id: applicationId,\n          userId: locals.user.id\n        }\n      });\n      if (!application) {\n        console.log(\"Application not found or does not belong to user\");\n        return json({ error: \"Application not found\" }, { status: 404 });\n      }\n    }\n    const interviewStage = await prisma.interviewStage.findUnique({\n      where: {\n        id: interviewId,\n        applicationId\n      },\n      include: {\n        questions: true\n      }\n    });\n    if (!interviewStage) {\n      console.log(\"Interview stage not found\");\n      return json({ error: \"Interview stage not found\" }, { status: 404 });\n    }\n    console.log(\"Found interview stage:\", interviewStage.id);\n    return json({ interviewStage });\n  } catch (error) {\n    console.error(\"Error fetching interview stage:\", error);\n    return json({ error: \"Failed to fetch interview stage\" }, { status: 500 });\n  }\n}\nasync function PATCH({ request, params, locals }) {\n  console.log(\"PATCH interview stage:\", params.interviewId);\n  console.log(\"User in locals:\", locals.user ? `ID: ${locals.user.id}` : \"Not authenticated\");\n  const isDev = process.env.NODE_ENV === \"development\";\n  if (!locals.user && !isDev) {\n    console.log(\"Unauthorized access attempt - no user in locals\");\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  const { applicationId, interviewId } = params;\n  try {\n    if (!isDev && locals.user) {\n      const application = await prisma.application.findUnique({\n        where: {\n          id: applicationId,\n          userId: locals.user.id\n        }\n      });\n      if (!application) {\n        console.log(\"Application not found or does not belong to user\");\n        return json({ error: \"Application not found\" }, { status: 404 });\n      }\n    }\n    const existingStage = await prisma.interviewStage.findUnique({\n      where: {\n        id: interviewId,\n        applicationId\n      }\n    });\n    if (!existingStage) {\n      console.log(\"Interview stage not found\");\n      return json({ error: \"Interview stage not found\" }, { status: 404 });\n    }\n    const body = await request.json();\n    console.log(\"Update data:\", body);\n    const updatedStage = await prisma.interviewStage.update({\n      where: {\n        id: interviewId\n      },\n      data: {\n        stageName: body.stageName !== void 0 ? body.stageName : void 0,\n        stageDate: body.stageDate ? new Date(body.stageDate) : void 0,\n        outcome: body.outcome !== void 0 ? body.outcome : void 0,\n        feedback: body.feedback !== void 0 ? body.feedback : void 0,\n        interviewers: body.interviewers !== void 0 ? body.interviewers : void 0,\n        duration: body.duration !== void 0 ? body.duration ? parseInt(body.duration, 10) : null : void 0,\n        notes: body.notes !== void 0 ? body.notes : void 0,\n        nextAction: body.nextAction !== void 0 ? body.nextAction : void 0\n      }\n    });\n    console.log(\"Interview stage updated successfully:\", updatedStage.id);\n    return json({ interviewStage: updatedStage }, { status: 200 });\n  } catch (error) {\n    console.error(\"Error updating interview stage:\", error);\n    if (isDev) {\n      return json(\n        {\n          error: \"Failed to update interview stage\",\n          details: error.message,\n          code: error.code\n        },\n        { status: 500 }\n      );\n    } else {\n      return json({ error: \"Failed to update interview stage\" }, { status: 500 });\n    }\n  }\n}\nasync function DELETE({ params, locals }) {\n  console.log(\"DELETE interview stage:\", params.interviewId);\n  console.log(\"User in locals:\", locals.user ? `ID: ${locals.user.id}` : \"Not authenticated\");\n  const isDev = process.env.NODE_ENV === \"development\";\n  if (!locals.user && !isDev) {\n    console.log(\"Unauthorized access attempt - no user in locals\");\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  const { applicationId, interviewId } = params;\n  try {\n    if (!isDev && locals.user) {\n      const application = await prisma.application.findUnique({\n        where: {\n          id: applicationId,\n          userId: locals.user.id\n        }\n      });\n      if (!application) {\n        console.log(\"Application not found or does not belong to user\");\n        return json({ error: \"Application not found\" }, { status: 404 });\n      }\n    }\n    const existingStage = await prisma.interviewStage.findUnique({\n      where: {\n        id: interviewId,\n        applicationId\n      }\n    });\n    if (!existingStage) {\n      console.log(\"Interview stage not found\");\n      return json({ error: \"Interview stage not found\" }, { status: 404 });\n    }\n    await prisma.interviewStage.delete({\n      where: {\n        id: interviewId\n      }\n    });\n    console.log(\"Interview stage deleted successfully\");\n    return json({ success: true }, { status: 200 });\n  } catch (error) {\n    console.error(\"Error deleting interview stage:\", error);\n    return json({ error: \"Failed to delete interview stage\" }, { status: 500 });\n  }\n}\nexport {\n  DELETE,\n  GET,\n  PATCH\n};\n"], "names": [], "mappings": ";;;;AAEA,eAAe,GAAG,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;AACvC,EAAE,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,MAAM,CAAC,WAAW,CAAC;AACzD,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,MAAM,CAAC,IAAI,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,mBAAmB,CAAC;AAC7F,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa;AACtD,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE;AAC9B,IAAI,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC;AAClE,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,MAAM;AAC/C,EAAE,IAAI;AACN,IAAI,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,EAAE;AAC/B,MAAM,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;AAC9D,QAAQ,KAAK,EAAE;AACf,UAAU,EAAE,EAAE,aAAa;AAC3B,UAAU,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC;AAC9B;AACA,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,WAAW,EAAE;AACxB,QAAQ,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC;AACvE,QAAQ,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA;AACA,IAAI,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC;AAClE,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE,WAAW;AACvB,QAAQ;AACR,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,SAAS,EAAE;AACnB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,cAAc,EAAE;AACzB,MAAM,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC;AAC9C,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,2BAA2B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1E;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE,cAAc,CAAC,EAAE,CAAC;AAC5D,IAAI,OAAO,IAAI,CAAC,EAAE,cAAc,EAAE,CAAC;AACnC,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC;AAC3D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,iCAAiC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9E;AACA;AACA,eAAe,KAAK,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;AAClD,EAAE,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE,MAAM,CAAC,WAAW,CAAC;AAC3D,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,MAAM,CAAC,IAAI,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,mBAAmB,CAAC;AAC7F,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa;AACtD,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE;AAC9B,IAAI,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC;AAClE,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,MAAM;AAC/C,EAAE,IAAI;AACN,IAAI,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,EAAE;AAC/B,MAAM,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;AAC9D,QAAQ,KAAK,EAAE;AACf,UAAU,EAAE,EAAE,aAAa;AAC3B,UAAU,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC;AAC9B;AACA,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,WAAW,EAAE;AACxB,QAAQ,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC;AACvE,QAAQ,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA;AACA,IAAI,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC;AACjE,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE,WAAW;AACvB,QAAQ;AACR;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,aAAa,EAAE;AACxB,MAAM,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC;AAC9C,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,2BAA2B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1E;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC;AACrC,IAAI,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC;AAC5D,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE;AACZ,OAAO;AACP,MAAM,IAAI,EAAE;AACZ,QAAQ,SAAS,EAAE,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AACtE,QAAQ,SAAS,EAAE,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AACrE,QAAQ,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AAChE,QAAQ,QAAQ,EAAE,IAAI,CAAC,QAAQ,KAAK,KAAK,CAAC,GAAG,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AACnE,QAAQ,YAAY,EAAE,IAAI,CAAC,YAAY,KAAK,KAAK,CAAC,GAAG,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;AAC/E,QAAQ,QAAQ,EAAE,IAAI,CAAC,QAAQ,KAAK,KAAK,CAAC,GAAG,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC;AACxG,QAAQ,KAAK,EAAE,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AAC1D,QAAQ,UAAU,EAAE,IAAI,CAAC,UAAU,KAAK,KAAK,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,KAAK;AACxE;AACA,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,uCAAuC,EAAE,YAAY,CAAC,EAAE,CAAC;AACzE,IAAI,OAAO,IAAI,CAAC,EAAE,cAAc,EAAE,YAAY,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC;AAC3D,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,OAAO,IAAI;AACjB,QAAQ;AACR,UAAU,KAAK,EAAE,kCAAkC;AACnD,UAAU,OAAO,EAAE,KAAK,CAAC,OAAO;AAChC,UAAU,IAAI,EAAE,KAAK,CAAC;AACtB,SAAS;AACT,QAAQ,EAAE,MAAM,EAAE,GAAG;AACrB,OAAO;AACP,KAAK,MAAM;AACX,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,kCAAkC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjF;AACA;AACA;AACA,eAAe,MAAM,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;AAC1C,EAAE,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,MAAM,CAAC,WAAW,CAAC;AAC5D,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,MAAM,CAAC,IAAI,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,mBAAmB,CAAC;AAC7F,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa;AACtD,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE;AAC9B,IAAI,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC;AAClE,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,MAAM;AAC/C,EAAE,IAAI;AACN,IAAI,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,EAAE;AAC/B,MAAM,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;AAC9D,QAAQ,KAAK,EAAE;AACf,UAAU,EAAE,EAAE,aAAa;AAC3B,UAAU,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC;AAC9B;AACA,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,WAAW,EAAE;AACxB,QAAQ,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC;AACvE,QAAQ,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA;AACA,IAAI,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC;AACjE,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE,WAAW;AACvB,QAAQ;AACR;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,aAAa,EAAE;AACxB,MAAM,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC;AAC9C,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,2BAA2B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1E;AACA,IAAI,MAAM,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC;AACvC,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE;AACZ;AACA,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC;AACvD,IAAI,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnD,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC;AAC3D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,kCAAkC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC/E;AACA;;;;"}