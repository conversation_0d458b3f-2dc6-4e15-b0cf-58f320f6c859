{"version": 3, "file": "_page.svelte-CrPBXgQS.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/builder/_id_/_page.svelte.js"], "sourcesContent": ["import { O as copy_payload, P as assign_payload, y as pop, w as push, W as stringify, V as escape_html } from \"../../../../../chunks/index3.js\";\nimport { o as onDestroy } from \"../../../../../chunks/index-server.js\";\nimport { g as goto } from \"../../../../../chunks/client.js\";\nimport { a as toast } from \"../../../../../chunks/Toaster.svelte_svelte_type_style_lang.js\";\nimport \"clsx\";\nimport { S as SEO } from \"../../../../../chunks/SEO.js\";\nimport { B as Button } from \"../../../../../chunks/button.js\";\nimport { I as Input } from \"../../../../../chunks/input.js\";\nimport { R as ResumeTabs, E as EditDesignPanel, b as ResumeForm, a as Resume } from \"../../../../../chunks/EditDesignPanel.js\";\nimport { R as Root, D as Dropdown_menu_trigger, a as Dropdown_menu_content } from \"../../../../../chunks/index6.js\";\nimport { S as Square_pen } from \"../../../../../chunks/square-pen.js\";\nimport { S as Save } from \"../../../../../chunks/save.js\";\nimport { E as Ellipsis } from \"../../../../../chunks/ellipsis.js\";\nimport { D as Dropdown_menu_item } from \"../../../../../chunks/dropdown-menu-item.js\";\nimport { D as Dropdown_menu_separator } from \"../../../../../chunks/dropdown-menu-separator.js\";\nimport { C as Check } from \"../../../../../chunks/check.js\";\nfunction _page($$payload, $$props) {\n  push();\n  let { data } = $$props;\n  console.log(\"Builder page data:\", data);\n  if (!data.resumeData) {\n    console.error(\"Resume data is missing\");\n    goto();\n  }\n  let saving = false;\n  let lastSaved = null;\n  let isEditingTitle = false;\n  let resumeTitle = data.resumeData.document.label || \"Untitled Resume\";\n  let hasUnsavedChanges = false;\n  JSON.stringify(data.form.resume.data || {});\n  JSON.stringify(data.form.design.data || {});\n  onDestroy(() => {\n  });\n  async function saveResume() {\n    saving = true;\n    try {\n      const formData = data.form.resume.data;\n      let headerName = formData.header?.name || \"\";\n      let headerEmail = formData.header?.email || \"\";\n      let headerPhone = formData.header?.phone || \"\";\n      if (typeof document !== \"undefined\") {\n        try {\n          const nameInput = document.getElementById(\"name-input\");\n          const emailInput = document.getElementById(\"email-input\");\n          const phoneInput = document.getElementById(\"phone-input\");\n          if (nameInput && nameInput.value) {\n            headerName = nameInput.value;\n          }\n          if (emailInput && emailInput.value) {\n            headerEmail = emailInput.value;\n          }\n          if (phoneInput && phoneInput.value) {\n            headerPhone = phoneInput.value;\n          }\n        } catch (error) {\n          console.error(\"Error getting values from DOM:\", error);\n        }\n      }\n      const parsedData = {\n        header: {\n          name: headerName,\n          email: headerEmail,\n          phone: headerPhone\n        },\n        summary: { content: formData.summary?.content || \"\" },\n        experience: formData.experience || [],\n        education: formData.education || [],\n        skills: formData.skills || [],\n        projects: formData.projects || [],\n        certifications: formData.certifications || []\n      };\n      const response = await fetch(`/api/resume/${data.resumeData.id}/data`, {\n        method: \"PUT\",\n        headers: { \"Content-Type\": \"application/json\" },\n        body: JSON.stringify({ parsedData })\n      });\n      if (response.ok) {\n        const result = await response.json();\n        lastSaved = /* @__PURE__ */ new Date();\n        if (result.data) {\n          data.form.resume.data = result.data;\n          if (data.form.resume.data && data.form.resume.data.header) {\n            data.form.resume.data.header.name = parsedData.header.name;\n            data.form.resume.data.header.email = parsedData.header.email;\n            data.form.resume.data.header.phone = parsedData.header.phone;\n          }\n          console.log(\"Form data updated after save:\", data.form.resume.data);\n        }\n        hasUnsavedChanges = false;\n        toast.success(\"Resume saved\", {\n          description: \"Your resume has been saved successfully.\"\n        });\n      } else {\n        toast.error(\"Error saving resume\", {\n          description: \"Could not save your resume. Please try again.\"\n        });\n      }\n    } catch (error) {\n      console.error(\"Error saving resume:\", error);\n      toast.error(\"Error saving resume\", {\n        description: \"Could not save your resume. Please try again.\"\n      });\n    } finally {\n      saving = false;\n    }\n  }\n  function previewResume() {\n    saveResume().then(() => {\n      if (typeof window !== \"undefined\") {\n        window.open(`/api/resume/${data.resumeData.id}/preview`, \"_blank\");\n      }\n    });\n  }\n  function downloadResume() {\n    saveResume().then(() => {\n      if (typeof window !== \"undefined\") {\n        window.open(`/api/resume/${data.resumeData.id}/download`, \"_blank\");\n      }\n    });\n  }\n  async function saveResumeTitle() {\n    try {\n      const response = await fetch(`/api/documents/${data.resumeData.document.id}`, {\n        method: \"PATCH\",\n        headers: { \"Content-Type\": \"application/json\" },\n        body: JSON.stringify({ label: resumeTitle })\n      });\n      if (response.ok) {\n        data.resumeData.document.label = resumeTitle;\n        isEditingTitle = false;\n        toast.success(\"Resume title updated\");\n      } else {\n        toast.error(\"Failed to update resume title\");\n      }\n    } catch (error) {\n      console.error(\"Error updating resume title:\", error);\n      toast.error(\"Failed to update resume title\");\n    }\n  }\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    SEO($$payload2, {\n      title: `${stringify(resumeTitle)} | Auto Apply`,\n      description: \"Build your professional resume\"\n    });\n    $$payload2.out += `<!----> <main><div class=\"flex items-center justify-between border border-l border-r border-t border-neutral-500 px-6 py-4\"><div class=\"flex items-center gap-2\">`;\n    if (isEditingTitle) {\n      $$payload2.out += \"<!--[-->\";\n      $$payload2.out += `<div class=\"flex items-center gap-2\">`;\n      Input($$payload2, {\n        type: \"text\",\n        class: \"h-10 text-xl font-bold\",\n        autofocus: true,\n        get value() {\n          return resumeTitle;\n        },\n        set value($$value) {\n          resumeTitle = $$value;\n          $$settled = false;\n        }\n      });\n      $$payload2.out += `<!----> `;\n      Button($$payload2, {\n        variant: \"ghost\",\n        size: \"icon\",\n        onclick: saveResumeTitle,\n        children: ($$payload3) => {\n          Check($$payload3, { class: \"h-5 w-5\" });\n        },\n        $$slots: { default: true }\n      });\n      $$payload2.out += `<!----></div>`;\n    } else {\n      $$payload2.out += \"<!--[!-->\";\n      $$payload2.out += `<div class=\"flex items-center gap-2\"><h1 class=\"text-2xl font-bold\">${escape_html(data.resumeData.document.label || \"Resume Builder\")}</h1> `;\n      Button($$payload2, {\n        variant: \"ghost\",\n        size: \"icon\",\n        onclick: () => isEditingTitle = true,\n        children: ($$payload3) => {\n          Square_pen($$payload3, { class: \"h-4 w-4\" });\n        },\n        $$slots: { default: true }\n      });\n      $$payload2.out += `<!----></div>`;\n    }\n    $$payload2.out += `<!--]--></div> <div class=\"flex items-center gap-2\">`;\n    if (lastSaved) {\n      $$payload2.out += \"<!--[-->\";\n      $$payload2.out += `<span class=\"text-sm text-gray-500\">Last saved: ${escape_html(lastSaved.toLocaleTimeString())}</span>`;\n    } else {\n      $$payload2.out += \"<!--[!-->\";\n    }\n    $$payload2.out += `<!--]--> `;\n    Button($$payload2, {\n      variant: \"default\",\n      onclick: saveResume,\n      disabled: saving,\n      children: ($$payload3) => {\n        Save($$payload3, { class: \"mr-2 h-4 w-4\" });\n        $$payload3.out += `<!----> ${escape_html(saving ? \"Saving...\" : \"Save Resume\")}`;\n      },\n      $$slots: { default: true }\n    });\n    $$payload2.out += `<!----> <!---->`;\n    Root($$payload2, {\n      children: ($$payload3) => {\n        $$payload3.out += `<!---->`;\n        Dropdown_menu_trigger($$payload3, {\n          children: ($$payload4) => {\n            Button($$payload4, {\n              variant: \"ghost\",\n              class: \"relative rounded-md border\",\n              children: ($$payload5) => {\n                Ellipsis($$payload5, { class: \"mr-2 h-4 w-4\" });\n                $$payload5.out += `<!----> Actions`;\n              },\n              $$slots: { default: true }\n            });\n          },\n          $$slots: { default: true }\n        });\n        $$payload3.out += `<!----> <!---->`;\n        Dropdown_menu_content($$payload3, {\n          sideOffset: 15,\n          align: \"end\",\n          children: ($$payload4) => {\n            $$payload4.out += `<!---->`;\n            Dropdown_menu_item($$payload4, {\n              onclick: () => goto(),\n              children: ($$payload5) => {\n                $$payload5.out += `<!---->Documents`;\n              },\n              $$slots: { default: true }\n            });\n            $$payload4.out += `<!----> <!---->`;\n            Dropdown_menu_item($$payload4, {\n              onclick: previewResume,\n              children: ($$payload5) => {\n                $$payload5.out += `<!---->Preview`;\n              },\n              $$slots: { default: true }\n            });\n            $$payload4.out += `<!----> <!---->`;\n            Dropdown_menu_separator($$payload4, {});\n            $$payload4.out += `<!----> <!---->`;\n            Dropdown_menu_item($$payload4, {\n              onclick: downloadResume,\n              children: ($$payload5) => {\n                $$payload5.out += `<!---->Download`;\n              },\n              $$slots: { default: true }\n            });\n            $$payload4.out += `<!---->`;\n          },\n          $$slots: { default: true }\n        });\n        $$payload3.out += `<!---->`;\n      },\n      $$slots: { default: true }\n    });\n    $$payload2.out += `<!----></div></div> <div class=\"grid h-[calc(100vh-8.4rem)] grid-cols-1 md:grid-cols-[2.5fr_3fr]\"><div class=\"border border-b-0 border-l-0 border-t-0 border-zinc-900 text-white\">`;\n    ResumeTabs($$payload2, {\n      $$slots: {\n        content: ($$payload3) => {\n          $$payload3.out += `<div slot=\"content\">`;\n          ResumeForm($$payload3, { data: data.form.resume });\n          $$payload3.out += `<!----></div>`;\n        },\n        design: ($$payload3) => {\n          $$payload3.out += `<div slot=\"design\">`;\n          EditDesignPanel($$payload3, { data: data.form.design });\n          $$payload3.out += `<!----></div>`;\n        }\n      }\n    });\n    $$payload2.out += `<!----></div> <div class=\"flex items-center justify-center overflow-y-auto bg-neutral-950 p-4\">`;\n    Resume($$payload2, {\n      formData: data.form.resume,\n      designData: data.form.design\n    });\n    $$payload2.out += `<!----></div></div></main>`;\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO;AACxB,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAAC;AACzC,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AACxB,IAAI,OAAO,CAAC,KAAK,CAAC,wBAAwB,CAAC;AAC3C,IAAI,IAAI,EAAE;AACV;AACA,EAAE,IAAI,MAAM,GAAG,KAAK;AACpB,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,cAAc,GAAG,KAAK;AAC5B,EAAE,IAAI,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,IAAI,iBAAiB;AACvE,EAAE,IAAI,iBAAiB,GAAG,KAAK;AAC/B,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;AAC7C,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;AAC7C,EAAE,SAAS,CAAC,MAAM;AAClB,GAAG,CAAC;AACJ,EAAE,eAAe,UAAU,GAAG;AAC9B,IAAI,MAAM,GAAG,IAAI;AACjB,IAAI,IAAI;AACR,MAAM,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI;AAC5C,MAAM,IAAI,UAAU,GAAG,QAAQ,CAAC,MAAM,EAAE,IAAI,IAAI,EAAE;AAClD,MAAM,IAAI,WAAW,GAAG,QAAQ,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;AACpD,MAAM,IAAI,WAAW,GAAG,QAAQ,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;AACpD,MAAM,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;AAC3C,QAAQ,IAAI;AACZ,UAAU,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAC;AACjE,UAAU,MAAM,UAAU,GAAG,QAAQ,CAAC,cAAc,CAAC,aAAa,CAAC;AACnE,UAAU,MAAM,UAAU,GAAG,QAAQ,CAAC,cAAc,CAAC,aAAa,CAAC;AACnE,UAAU,IAAI,SAAS,IAAI,SAAS,CAAC,KAAK,EAAE;AAC5C,YAAY,UAAU,GAAG,SAAS,CAAC,KAAK;AACxC;AACA,UAAU,IAAI,UAAU,IAAI,UAAU,CAAC,KAAK,EAAE;AAC9C,YAAY,WAAW,GAAG,UAAU,CAAC,KAAK;AAC1C;AACA,UAAU,IAAI,UAAU,IAAI,UAAU,CAAC,KAAK,EAAE;AAC9C,YAAY,WAAW,GAAG,UAAU,CAAC,KAAK;AAC1C;AACA,SAAS,CAAC,OAAO,KAAK,EAAE;AACxB,UAAU,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC;AAChE;AACA;AACA,MAAM,MAAM,UAAU,GAAG;AACzB,QAAQ,MAAM,EAAE;AAChB,UAAU,IAAI,EAAE,UAAU;AAC1B,UAAU,KAAK,EAAE,WAAW;AAC5B,UAAU,KAAK,EAAE;AACjB,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,QAAQ,CAAC,OAAO,EAAE,OAAO,IAAI,EAAE,EAAE;AAC7D,QAAQ,UAAU,EAAE,QAAQ,CAAC,UAAU,IAAI,EAAE;AAC7C,QAAQ,SAAS,EAAE,QAAQ,CAAC,SAAS,IAAI,EAAE;AAC3C,QAAQ,MAAM,EAAE,QAAQ,CAAC,MAAM,IAAI,EAAE;AACrC,QAAQ,QAAQ,EAAE,QAAQ,CAAC,QAAQ,IAAI,EAAE;AACzC,QAAQ,cAAc,EAAE,QAAQ,CAAC,cAAc,IAAI;AACnD,OAAO;AACP,MAAM,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE;AAC7E,QAAQ,MAAM,EAAE,KAAK;AACrB,QAAQ,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;AACvD,QAAQ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,UAAU,EAAE;AAC3C,OAAO,CAAC;AACR,MAAM,IAAI,QAAQ,CAAC,EAAE,EAAE;AACvB,QAAQ,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE;AAC5C,QAAQ,SAAS,mBAAmB,IAAI,IAAI,EAAE;AAC9C,QAAQ,IAAI,MAAM,CAAC,IAAI,EAAE;AACzB,UAAU,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;AAC7C,UAAU,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE;AACrE,YAAY,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI;AACtE,YAAY,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC,KAAK;AACxE,YAAY,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC,KAAK;AACxE;AACA,UAAU,OAAO,CAAC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;AAC7E;AACA,QAAQ,iBAAiB,GAAG,KAAK;AACjC,QAAQ,KAAK,CAAC,OAAO,CAAC,cAAc,EAAE;AACtC,UAAU,WAAW,EAAE;AACvB,SAAS,CAAC;AACV,OAAO,MAAM;AACb,QAAQ,KAAK,CAAC,KAAK,CAAC,qBAAqB,EAAE;AAC3C,UAAU,WAAW,EAAE;AACvB,SAAS,CAAC;AACV;AACA,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC;AAClD,MAAM,KAAK,CAAC,KAAK,CAAC,qBAAqB,EAAE;AACzC,QAAQ,WAAW,EAAE;AACrB,OAAO,CAAC;AACR,KAAK,SAAS;AACd,MAAM,MAAM,GAAG,KAAK;AACpB;AACA;AACA,EAAE,SAAS,aAAa,GAAG;AAC3B,IAAI,UAAU,EAAE,CAAC,IAAI,CAAC,MAAM;AAC5B,MAAM,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AACzC,QAAQ,MAAM,CAAC,IAAI,CAAC,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC;AAC1E;AACA,KAAK,CAAC;AACN;AACA,EAAE,SAAS,cAAc,GAAG;AAC5B,IAAI,UAAU,EAAE,CAAC,IAAI,CAAC,MAAM;AAC5B,MAAM,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AACzC,QAAQ,MAAM,CAAC,IAAI,CAAC,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,QAAQ,CAAC;AAC3E;AACA,KAAK,CAAC;AACN;AACA,EAAE,eAAe,eAAe,GAAG;AACnC,IAAI,IAAI;AACR,MAAM,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,CAAC,eAAe,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE;AACpF,QAAQ,MAAM,EAAE,OAAO;AACvB,QAAQ,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;AACvD,QAAQ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE;AACnD,OAAO,CAAC;AACR,MAAM,IAAI,QAAQ,CAAC,EAAE,EAAE;AACvB,QAAQ,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,GAAG,WAAW;AACpD,QAAQ,cAAc,GAAG,KAAK;AAC9B,QAAQ,KAAK,CAAC,OAAO,CAAC,sBAAsB,CAAC;AAC7C,OAAO,MAAM;AACb,QAAQ,KAAK,CAAC,KAAK,CAAC,+BAA+B,CAAC;AACpD;AACA,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC;AAC1D,MAAM,KAAK,CAAC,KAAK,CAAC,+BAA+B,CAAC;AAClD;AACA;AACA,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,GAAG,CAAC,UAAU,EAAE;AACpB,MAAM,KAAK,EAAE,CAAC,EAAE,SAAS,CAAC,WAAW,CAAC,CAAC,aAAa,CAAC;AACrD,MAAM,WAAW,EAAE;AACnB,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,iKAAiK,CAAC;AACzL,IAAI,IAAI,cAAc,EAAE;AACxB,MAAM,UAAU,CAAC,GAAG,IAAI,UAAU;AAClC,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,qCAAqC,CAAC;AAC/D,MAAM,KAAK,CAAC,UAAU,EAAE;AACxB,QAAQ,IAAI,EAAE,MAAM;AACpB,QAAQ,KAAK,EAAE,wBAAwB;AACvC,QAAQ,SAAS,EAAE,IAAI;AACvB,QAAQ,IAAI,KAAK,GAAG;AACpB,UAAU,OAAO,WAAW;AAC5B,SAAS;AACT,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE;AAC3B,UAAU,WAAW,GAAG,OAAO;AAC/B,UAAU,SAAS,GAAG,KAAK;AAC3B;AACA,OAAO,CAAC;AACR,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAClC,MAAM,MAAM,CAAC,UAAU,EAAE;AACzB,QAAQ,OAAO,EAAE,OAAO;AACxB,QAAQ,IAAI,EAAE,MAAM;AACpB,QAAQ,OAAO,EAAE,eAAe;AAChC,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,KAAK,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AACjD,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC,OAAO,CAAC;AACR,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACvC,KAAK,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,IAAI,WAAW;AACnC,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,oEAAoE,EAAE,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,IAAI,gBAAgB,CAAC,CAAC,MAAM,CAAC;AACtK,MAAM,MAAM,CAAC,UAAU,EAAE;AACzB,QAAQ,OAAO,EAAE,OAAO;AACxB,QAAQ,IAAI,EAAE,MAAM;AACpB,QAAQ,OAAO,EAAE,MAAM,cAAc,GAAG,IAAI;AAC5C,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,UAAU,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AACtD,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC,OAAO,CAAC;AACR,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACvC;AACA,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,oDAAoD,CAAC;AAC5E,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,UAAU,CAAC,GAAG,IAAI,UAAU;AAClC,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,gDAAgD,EAAE,WAAW,CAAC,SAAS,CAAC,kBAAkB,EAAE,CAAC,CAAC,OAAO,CAAC;AAC/H,KAAK,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,IAAI,WAAW;AACnC;AACA,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AACjC,IAAI,MAAM,CAAC,UAAU,EAAE;AACvB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,QAAQ,EAAE,MAAM;AACtB,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,IAAI,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AACnD,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,MAAM,GAAG,WAAW,GAAG,aAAa,CAAC,CAAC,CAAC;AACxF,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;AACvC,IAAI,IAAI,CAAC,UAAU,EAAE;AACrB,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,QAAQ,qBAAqB,CAAC,UAAU,EAAE;AAC1C,UAAU,QAAQ,EAAE,CAAC,UAAU,KAAK;AACpC,YAAY,MAAM,CAAC,UAAU,EAAE;AAC/B,cAAc,OAAO,EAAE,OAAO;AAC9B,cAAc,KAAK,EAAE,4BAA4B;AACjD,cAAc,QAAQ,EAAE,CAAC,UAAU,KAAK;AACxC,gBAAgB,QAAQ,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AAC/D,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;AACnD,eAAe;AACf,cAAc,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACtC,aAAa,CAAC;AACd,WAAW;AACX,UAAU,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAClC,SAAS,CAAC;AACV,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;AAC3C,QAAQ,qBAAqB,CAAC,UAAU,EAAE;AAC1C,UAAU,UAAU,EAAE,EAAE;AACxB,UAAU,KAAK,EAAE,KAAK;AACtB,UAAU,QAAQ,EAAE,CAAC,UAAU,KAAK;AACpC,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACvC,YAAY,kBAAkB,CAAC,UAAU,EAAE;AAC3C,cAAc,OAAO,EAAE,MAAM,IAAI,EAAE;AACnC,cAAc,QAAQ,EAAE,CAAC,UAAU,KAAK;AACxC,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC;AACpD,eAAe;AACf,cAAc,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACtC,aAAa,CAAC;AACd,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;AAC/C,YAAY,kBAAkB,CAAC,UAAU,EAAE;AAC3C,cAAc,OAAO,EAAE,aAAa;AACpC,cAAc,QAAQ,EAAE,CAAC,UAAU,KAAK;AACxC,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AAClD,eAAe;AACf,cAAc,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACtC,aAAa,CAAC;AACd,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;AAC/C,YAAY,uBAAuB,CAAC,UAAU,EAAE,EAAE,CAAC;AACnD,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;AAC/C,YAAY,kBAAkB,CAAC,UAAU,EAAE;AAC3C,cAAc,OAAO,EAAE,cAAc;AACrC,cAAc,QAAQ,EAAE,CAAC,UAAU,KAAK;AACxC,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;AACnD,eAAe;AACf,cAAc,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACtC,aAAa,CAAC;AACd,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACvC,WAAW;AACX,UAAU,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAClC,SAAS,CAAC;AACV,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,kLAAkL,CAAC;AAC1M,IAAI,UAAU,CAAC,UAAU,EAAE;AAC3B,MAAM,OAAO,EAAE;AACf,QAAQ,OAAO,EAAE,CAAC,UAAU,KAAK;AACjC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC;AAClD,UAAU,UAAU,CAAC,UAAU,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC5D,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AAC3C,SAAS;AACT,QAAQ,MAAM,EAAE,CAAC,UAAU,KAAK;AAChC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC;AACjD,UAAU,eAAe,CAAC,UAAU,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AACjE,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AAC3C;AACA;AACA,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,+FAA+F,CAAC;AACvH,IAAI,MAAM,CAAC,UAAU,EAAE;AACvB,MAAM,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM;AAChC,MAAM,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC;AAC5B,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,0BAA0B,CAAC;AAClD;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,GAAG,EAAE;AACP;;;;"}