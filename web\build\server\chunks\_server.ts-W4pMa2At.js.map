{"version": 3, "file": "_server.ts-W4pMa2At.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/billing/create-setup-intent/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../../../chunks/auth.js\";\nconst POST = async ({ cookies, request }) => {\n  try {\n    const token = cookies.get(\"auth_token\");\n    const isProd = process.env.NODE_ENV === \"production\";\n    const { paymentMethod = \"card\" } = await request.json().catch(() => ({}));\n    if (!token) {\n      console.log(\"No auth_token cookie found\");\n      return json({ error: \"No authentication token found\" }, { status: 401 });\n    }\n    console.log(\"Verifying token from cookie\");\n    const userData = await verifySessionToken(token);\n    if (!userData || !userData.id) {\n      console.log(\"Invalid or expired token\");\n      return json({ error: \"Invalid or expired authentication token\" }, { status: 401 });\n    }\n    console.log(\"Token verified successfully for user:\", userData.id);\n    console.log(\"Creating setup intent for user:\", userData.id);\n    const stripeSecret = isProd ? process.env.STRIPE_SECRET_KEY_LIVE || \"sk_live_placeholder\" : process.env.STRIPE_SECRET_KEY_TEST || \"sk_test_placeholder\";\n    if (stripeSecret === \"sk_live_placeholder\" || stripeSecret === \"sk_test_placeholder\") {\n      console.error(\"Stripe secret key is not configured\");\n      return new Response(\"Stripe API key is not configured\", { status: 500 });\n    }\n    const Stripe = (await import(\"stripe\")).default;\n    const stripe = new Stripe(stripeSecret, {\n      apiVersion: \"2025-04-30.basil\"\n    });\n    let user = await prisma.user.findUnique({ where: { id: userData.id } });\n    if (!user) {\n      console.error(\"User not found:\", userData.id);\n      return new Response(\"User not found\", { status: 404 });\n    }\n    if (!user.stripeCustomerId) {\n      try {\n        console.log(\"Creating Stripe customer for user:\", user.id);\n        const customer = await stripe.customers.create({\n          email: user.email,\n          name: user.name ?? void 0\n        });\n        user = await prisma.user.update({\n          where: { id: user.id },\n          data: { stripeCustomerId: customer.id }\n        });\n        console.log(\"Created Stripe customer:\", customer.id);\n      } catch (error) {\n        console.error(\"Error creating Stripe customer:\", error);\n        return new Response(\"Failed to create customer\", { status: 500 });\n      }\n    }\n    try {\n      const paymentMethodTypes = [\"card\"];\n      console.log(`Creating SetupIntent with payment method type: ${paymentMethodTypes[0]}`);\n      const setupIntent = await stripe.setupIntents.create({\n        customer: user.stripeCustomerId,\n        payment_method_types: paymentMethodTypes,\n        usage: \"off_session\"\n        // Allow the payment method to be used for future payments\n      });\n      console.log(\"Setup intent created successfully\");\n      return json({\n        clientSecret: setupIntent.client_secret,\n        customerId: user.stripeCustomerId\n      });\n    } catch (error) {\n      console.error(\"Error creating setup intent:\", error);\n      return new Response(`Failed to create setup intent: ${error.message}`, { status: 500 });\n    }\n  } catch (error) {\n    console.error(\"Unexpected error in create-setup-intent endpoint:\", error);\n    return new Response(`Server error: ${error.message}`, { status: 500 });\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;AAGK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AAC7C,EAAE,IAAI;AACN,IAAI,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AAC3C,IAAI,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;AACxD,IAAI,MAAM,EAAE,aAAa,GAAG,MAAM,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;AAC7E,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC;AAC/C,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,+BAA+B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9E;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC;AAC9C,IAAI,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AACpD,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;AACnC,MAAM,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC;AAC7C,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yCAAyC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxF;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,uCAAuC,EAAE,QAAQ,CAAC,EAAE,CAAC;AACrE,IAAI,OAAO,CAAC,GAAG,CAAC,iCAAiC,EAAE,QAAQ,CAAC,EAAE,CAAC;AAC/D,IAAI,MAAM,YAAY,GAAG,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,qBAAqB,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,qBAAqB;AAC3J,IAAI,IAAI,YAAY,KAAK,qBAAqB,IAAI,YAAY,KAAK,qBAAqB,EAAE;AAC1F,MAAM,OAAO,CAAC,KAAK,CAAC,qCAAqC,CAAC;AAC1D,MAAM,OAAO,IAAI,QAAQ,CAAC,kCAAkC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9E;AACA,IAAI,MAAM,MAAM,GAAG,CAAC,MAAM,OAAO,+BAAQ,CAAC,EAAE,OAAO;AACnD,IAAI,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,YAAY,EAAE;AAC5C,MAAM,UAAU,EAAE;AAClB,KAAK,CAAC;AACN,IAAI,IAAI,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE,EAAE,CAAC;AAC3E,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,OAAO,CAAC,KAAK,CAAC,iBAAiB,EAAE,QAAQ,CAAC,EAAE,CAAC;AACnD,MAAM,OAAO,IAAI,QAAQ,CAAC,gBAAgB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC5D;AACA,IAAI,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;AAChC,MAAM,IAAI;AACV,QAAQ,OAAO,CAAC,GAAG,CAAC,oCAAoC,EAAE,IAAI,CAAC,EAAE,CAAC;AAClE,QAAQ,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;AACvD,UAAU,KAAK,EAAE,IAAI,CAAC,KAAK;AAC3B,UAAU,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,KAAK;AAClC,SAAS,CAAC;AACV,QAAQ,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AACxC,UAAU,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;AAChC,UAAU,IAAI,EAAE,EAAE,gBAAgB,EAAE,QAAQ,CAAC,EAAE;AAC/C,SAAS,CAAC;AACV,QAAQ,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE,QAAQ,CAAC,EAAE,CAAC;AAC5D,OAAO,CAAC,OAAO,KAAK,EAAE;AACtB,QAAQ,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC;AAC/D,QAAQ,OAAO,IAAI,QAAQ,CAAC,2BAA2B,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE;AACA;AACA,IAAI,IAAI;AACR,MAAM,MAAM,kBAAkB,GAAG,CAAC,MAAM,CAAC;AACzC,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,+CAA+C,EAAE,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5F,MAAM,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AAC3D,QAAQ,QAAQ,EAAE,IAAI,CAAC,gBAAgB;AACvC,QAAQ,oBAAoB,EAAE,kBAAkB;AAChD,QAAQ,KAAK,EAAE;AACf;AACA,OAAO,CAAC;AACR,MAAM,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC;AACtD,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,YAAY,EAAE,WAAW,CAAC,aAAa;AAC/C,QAAQ,UAAU,EAAE,IAAI,CAAC;AACzB,OAAO,CAAC;AACR,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC;AAC1D,MAAM,OAAO,IAAI,QAAQ,CAAC,CAAC,+BAA+B,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7F;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,mDAAmD,EAAE,KAAK,CAAC;AAC7E,IAAI,OAAO,IAAI,QAAQ,CAAC,CAAC,cAAc,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1E;AACA;;;;"}