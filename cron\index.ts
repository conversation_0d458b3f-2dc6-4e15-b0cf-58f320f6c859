// cron/index.ts - Background Scheduler Service

import { logger } from "./utils/logger";
import { startScheduledJobs, stopScheduledJobs } from "./jobs/scheduledJobs";
import {
  sendEmailNotification,
  EmailNotificationType,
} from "./utils/emailService";
import http from "http";
import os from "os";

// Keep-alive interval in milliseconds (5 minutes)
const KEEP_ALIVE_INTERVAL = 5 * 60 * 1000;

// Flag to track if we're shutting down (only used for SIGINT)
let isShuttingDown = false;

// Flag to track if jobs are currently running
let jobsRunning = false;

// Health check server
let healthServer: http.Server | null = null;

// Create a simple health check server
function createHealthCheckServer() {
  const port = process.env.HEALTH_CHECK_PORT || 8080;

  const server = http.createServer((req, res) => {
    if (req.url === "/health" || req.url === "/") {
      const memoryUsage = process.memoryUsage();
      const uptime = process.uptime();
      const systemMemory = os.totalmem();
      const freeMemory = os.freemem();

      const healthData = {
        status: "healthy",
        timestamp: new Date().toISOString(),
        uptime: `${Math.floor(uptime)} seconds`,
        jobsRunning,
        isShuttingDown,
        memory: {
          rss: `${Math.round(memoryUsage.rss / 1024 / 1024)} MB`,
          heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)} MB`,
          heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)} MB`,
          external: `${Math.round(memoryUsage.external / 1024 / 1024)} MB`,
        },
        system: {
          totalMemory: `${Math.round(systemMemory / 1024 / 1024)} MB`,
          freeMemory: `${Math.round(freeMemory / 1024 / 1024)} MB`,
          memoryUsage: `${Math.round(((systemMemory - freeMemory) / systemMemory) * 100)}%`,
          loadAverage: os.loadavg(),
          platform: os.platform(),
          arch: os.arch(),
        },
      };

      res.writeHead(200, { "Content-Type": "application/json" });
      res.end(JSON.stringify(healthData, null, 2));
    } else {
      res.writeHead(404, { "Content-Type": "text/plain" });
      res.end("Not Found");
    }
  });

  server.listen(port, () => {
    logger.info(`🏥 Health check server running on port ${port}`);
  });

  return server;
}

async function run() {
  logger.info("🚀 Starting scheduler service...");

  try {
    // Start health check server first
    healthServer = createHealthCheckServer();

    // Start the scheduled jobs
    startScheduledJobs();
    jobsRunning = true;
    logger.info("✅ Scheduled jobs started successfully.");

    // No need to send email notification when service starts
    // Just log the startup in the console

    // Keep the process running with a keep-alive mechanism
    logger.info(
      "🕒 Scheduler service is now running and waiting for scheduled jobs..."
    );

    // Set up a keep-alive interval to prevent the service from exiting
    setInterval(() => {
      if (!isShuttingDown) {
        // Keep-alive ping - no logging to reduce noise

        // If jobs aren't running for some reason, restart them
        if (!jobsRunning) {
          logger.info(`🔄 Jobs not running, restarting scheduled jobs...`);
          try {
            startScheduledJobs();
            jobsRunning = true;
            logger.info(`✅ Scheduled jobs restarted successfully`);

            // No need to send email notification when jobs are restarted
            // Just log the restart in the console
          } catch (error) {
            logger.error(`❌ Error restarting scheduled jobs:`, error);
          }
        }

        // Periodic memory cleanup
        if (global.gc) {
          try {
            global.gc();
          } catch (e) {
            // GC not available, that's okay
          }
        }
      } else {
        // If we're shutting down (SIGINT), don't log anything to reduce noise
      }
    }, KEEP_ALIVE_INTERVAL);

    // Prevent the Node.js event loop from exiting by adding a dummy interval
    // This ensures the process stays alive even if there are no other events
    setInterval(() => {}, 60000);

    // Add a handler for the beforeExit event to keep the process alive
    process.on("beforeExit", () => {
      logger.info("🚧 Process beforeExit event detected, keeping alive...");
    });
  } catch (error) {
    logger.error("❌ Error starting scheduler service:", error);

    // Send email notification about the error
    await sendEmailNotification(EmailNotificationType.JOB_SUMMARY, {
      jobType: "Scheduler Service",
      status: "Failed to Start",
      error: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString(),
    });

    process.exit(1);
  }
}

// Handle process termination
process.on("SIGINT", async () => {
  logger.info("Received SIGINT signal, shutting down scheduler service...");
  isShuttingDown = true;

  try {
    // Stop all scheduled jobs gracefully
    stopScheduledJobs();
    jobsRunning = false;

    // Close health check server
    if (healthServer) {
      healthServer.close(() => {
        logger.info("🏥 Health check server closed");
      });
    }

    // Send email notification about shutdown
    await sendEmailNotification(EmailNotificationType.JOB_SUMMARY, {
      jobType: "Scheduler Service",
      status: "Shutdown",
      message: "Service was shut down by SIGINT signal",
      timestamp: new Date().toISOString(),
    }).catch((err) =>
      logger.error("Failed to send shutdown notification:", err)
    );

    logger.info("Scheduler service shutdown complete. Exiting...");
  } catch (error) {
    logger.error("Error during shutdown:", error);
  } finally {
    // Give some time for logs to flush before exiting
    setTimeout(() => process.exit(0), 1000);
  }
});

process.on("SIGTERM", async () => {
  logger.info("Received SIGTERM signal from container orchestrator...");

  // Check if this is a graceful shutdown request or forced termination
  const isGracefulShutdown = process.env.GRACEFUL_SHUTDOWN === "true";

  if (isGracefulShutdown) {
    logger.info("Graceful shutdown requested, stopping scheduler service...");
    isShuttingDown = true;

    try {
      // Stop all scheduled jobs gracefully
      stopScheduledJobs();
      jobsRunning = false;

      // Close health check server
      if (healthServer) {
        healthServer.close(() => {
          logger.info("🏥 Health check server closed");
        });
      }

      // Send email notification about shutdown
      await sendEmailNotification(EmailNotificationType.JOB_SUMMARY, {
        jobType: "Scheduler Service",
        status: "Graceful Shutdown",
        message: "Service was gracefully shut down by SIGTERM signal",
        timestamp: new Date().toISOString(),
      }).catch((err) =>
        logger.error("Failed to send shutdown notification:", err)
      );

      logger.info("Scheduler service shutdown complete. Exiting...");
      process.exit(0);
    } catch (error) {
      logger.error("Error during graceful shutdown:", error);
      process.exit(1);
    }
  } else {
    // In production, we want to keep running despite SIGTERM
    // This handles container orchestrator restarts
    logger.info(
      "Ignoring SIGTERM to maintain continuous operation (set GRACEFUL_SHUTDOWN=true to enable graceful shutdown)"
    );

    try {
      // Send email notification about ignored shutdown
      await sendEmailNotification(EmailNotificationType.JOB_SUMMARY, {
        jobType: "Scheduler Service",
        status: "SIGTERM Ignored",
        message:
          "Received SIGTERM signal but ignored it to keep the scheduler running",
        timestamp: new Date().toISOString(),
      }).catch((err) =>
        logger.error("Failed to send SIGTERM notification:", err)
      );

      logger.info(
        "Scheduler service will continue running despite SIGTERM signal"
      );
    } catch (error) {
      logger.error("Error handling SIGTERM signal:", error);
    }
  }
});

// Handle uncaught exceptions
process.on("uncaughtException", async (error) => {
  logger.error("Uncaught exception:", error);
  isShuttingDown = true;

  try {
    // Close health check server
    if (healthServer) {
      healthServer.close(() => {
        logger.info("🏥 Health check server closed due to uncaught exception");
      });
    }

    // Send detailed email notification about the error
    await sendEmailNotification(EmailNotificationType.CRON_ERROR, {
      jobType: "Scheduler Service",
      status: "Error",
      errorTitle: "Uncaught Exception in Scheduler Service",
      errorMessage: error instanceof Error ? error.message : String(error),
      errorStack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      details: {
        processUptime: `${process.uptime().toFixed(2)} seconds`,
        memoryUsage: JSON.stringify(process.memoryUsage()),
        nodeVersion: process.version,
      },
    }).catch((err) => logger.error("Failed to send error notification:", err));
  } catch (notificationError) {
    logger.error("Error sending notification:", notificationError);
  } finally {
    // Exit with error code
    process.exit(1);
  }
});

// Handle unhandled promise rejections
process.on("unhandledRejection", async (reason, promise) => {
  logger.error("Unhandled promise rejection:", reason);
  logger.error("Promise:", promise);

  try {
    // Send email notification about the unhandled rejection
    await sendEmailNotification(EmailNotificationType.CRON_ERROR, {
      jobType: "Scheduler Service",
      status: "Warning",
      errorTitle: "Unhandled Promise Rejection in Scheduler Service",
      errorMessage: reason instanceof Error ? reason.message : String(reason),
      errorStack: reason instanceof Error ? reason.stack : undefined,
      timestamp: new Date().toISOString(),
      details: {
        processUptime: `${process.uptime().toFixed(2)} seconds`,
        memoryUsage: JSON.stringify(process.memoryUsage()),
        nodeVersion: process.version,
      },
    }).catch((err) => logger.error("Failed to send error notification:", err));
  } catch (notificationError) {
    logger.error("Error sending notification:", notificationError);
  }

  // Don't exit for unhandled rejections, just log them
});

// Start the scheduler service
run();
