{"version": 3, "file": "_server.ts-BM7O3JeU.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/help/tags/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nconst GET = async ({ url }) => {\n  try {\n    const includeArticleCount = url.searchParams.get(\"includeArticleCount\") === \"true\";\n    const tags = await prisma.helpTag.findMany({\n      orderBy: {\n        name: \"asc\"\n      },\n      include: {\n        _count: includeArticleCount ? {\n          select: {\n            articles: true\n          }\n        } : void 0\n      }\n    });\n    const formattedTags = tags.map((tag) => ({\n      ...tag,\n      articleCount: includeArticleCount ? tag._count.articles : void 0,\n      _count: void 0\n    }));\n    return json(formattedTags);\n  } catch (error) {\n    console.error(\"Error fetching help tags:\", error);\n    return json({ error: \"Failed to fetch help tags\" }, { status: 500 });\n  }\n};\nconst POST = async ({ request, locals }) => {\n  const user = locals.user;\n  if (!user || user.role !== \"ADMIN\") {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  try {\n    const { name, slug } = await request.json();\n    const tag = await prisma.helpTag.create({\n      data: {\n        name,\n        slug\n      }\n    });\n    return json(tag);\n  } catch (error) {\n    console.error(\"Error creating help tag:\", error);\n    return json({ error: \"Failed to create help tag\" }, { status: 500 });\n  }\n};\nexport {\n  GET,\n  POST\n};\n"], "names": [], "mappings": ";;;;AAEK,MAAC,GAAG,GAAG,OAAO,EAAE,GAAG,EAAE,KAAK;AAC/B,EAAE,IAAI;AACN,IAAI,MAAM,mBAAmB,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,qBAAqB,CAAC,KAAK,MAAM;AACtF,IAAI,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;AAC/C,MAAM,OAAO,EAAE;AACf,QAAQ,IAAI,EAAE;AACd,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,MAAM,EAAE,mBAAmB,GAAG;AACtC,UAAU,MAAM,EAAE;AAClB,YAAY,QAAQ,EAAE;AACtB;AACA,SAAS,GAAG,KAAK;AACjB;AACA,KAAK,CAAC;AACN,IAAI,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;AAC7C,MAAM,GAAG,GAAG;AACZ,MAAM,YAAY,EAAE,mBAAmB,GAAG,GAAG,CAAC,MAAM,CAAC,QAAQ,GAAG,KAAK,CAAC;AACtE,MAAM,MAAM,EAAE,KAAK;AACnB,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC;AAC9B,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC;AACrD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,2BAA2B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA;AACK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AAC5C,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;AACtC,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AAC/C,IAAI,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;AAC5C,MAAM,IAAI,EAAE;AACZ,QAAQ,IAAI;AACZ,QAAQ;AACR;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC;AACpB,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC;AACpD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,2BAA2B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA;;;;"}