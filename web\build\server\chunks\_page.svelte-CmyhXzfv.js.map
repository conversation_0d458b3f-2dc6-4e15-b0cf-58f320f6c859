{"version": 3, "file": "_page.svelte-CmyhXzfv.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/help/search/_page.svelte.js"], "sourcesContent": ["import { W as stringify, V as escape_html, U as ensure_array_like, N as bind_props, y as pop, w as push } from \"../../../../chunks/index3.js\";\nimport { S as SEO } from \"../../../../chunks/SEO.js\";\nimport { H as HelpSearch } from \"../../../../chunks/HelpSearch.js\";\nimport { H as HelpSidebar } from \"../../../../chunks/HelpSidebar.js\";\nimport { H as HelpArticleCard } from \"../../../../chunks/HelpArticleCard.js\";\nimport { A as Arrow_left } from \"../../../../chunks/arrow-left.js\";\nimport { S as Search } from \"../../../../chunks/search.js\";\nfunction _page($$payload, $$props) {\n  push();\n  let data = $$props[\"data\"];\n  SEO($$payload, {\n    title: \"Search Results | Help Center\",\n    description: `Search results for '${stringify(data.query)}' in the Help Center.`,\n    keywords: `help center, search, ${stringify(data.query)}, support, guides, tutorials`\n  });\n  $$payload.out += `<!----> <div class=\"container mx-auto px-4 py-12\"><div class=\"grid gap-8 lg:grid-cols-4\"><div class=\"lg:col-span-1\">`;\n  HelpSidebar($$payload, { categories: data.categories });\n  $$payload.out += `<!----></div> <div class=\"lg:col-span-3\"><div class=\"mb-6\"><a href=\"/help\" class=\"text-primary inline-flex items-center text-sm hover:underline\">`;\n  Arrow_left($$payload, { class: \"mr-1 h-4 w-4\" });\n  $$payload.out += `<!----> Back to Help Center</a></div> <div class=\"mb-8 flex items-center gap-3\"><div class=\"bg-primary/10 text-primary rounded-full p-3\">`;\n  Search($$payload, { class: \"h-6 w-6\" });\n  $$payload.out += `<!----></div> <h1 class=\"text-3xl font-bold\">Search Results</h1></div> <div class=\"mb-8\">`;\n  HelpSearch($$payload, { className: \"w-full\" });\n  $$payload.out += `<!----></div> `;\n  if (data.query) {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<div class=\"mb-6\"><p class=\"text-muted-foreground\">${escape_html(data.resultCount)} result${escape_html(data.resultCount !== 1 ? \"s\" : \"\")} for \"${escape_html(data.query)}\"</p></div> `;\n    if (data.searchResults.length > 0) {\n      $$payload.out += \"<!--[-->\";\n      const each_array = ensure_array_like(data.searchResults);\n      $$payload.out += `<div class=\"grid gap-6 md:grid-cols-2 xl:grid-cols-3\"><!--[-->`;\n      for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n        let article = each_array[$$index];\n        HelpArticleCard($$payload, { article });\n      }\n      $$payload.out += `<!--]--></div>`;\n    } else {\n      $$payload.out += \"<!--[!-->\";\n      $$payload.out += `<div class=\"bg-muted rounded-lg border p-8 text-center\"><p class=\"text-muted-foreground mb-2\">No results found for \"${escape_html(data.query)}\".</p> <p>Try different keywords or browse categories.</p></div>`;\n    }\n    $$payload.out += `<!--]-->`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<div class=\"bg-muted rounded-lg border p-8 text-center\"><p class=\"text-muted-foreground mb-2\">Enter a search term to find help articles.</p> <p>Or browse categories in the sidebar.</p></div>`;\n  }\n  $$payload.out += `<!--]--></div></div></div>`;\n  bind_props($$props, { data });\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC;AAC5B,EAAE,GAAG,CAAC,SAAS,EAAE;AACjB,IAAI,KAAK,EAAE,8BAA8B;AACzC,IAAI,WAAW,EAAE,CAAC,oBAAoB,EAAE,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,qBAAqB,CAAC;AACpF,IAAI,QAAQ,EAAE,CAAC,qBAAqB,EAAE,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,4BAA4B;AACxF,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,oHAAoH,CAAC;AACzI,EAAE,WAAW,CAAC,SAAS,EAAE,EAAE,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC;AACzD,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,iJAAiJ,CAAC;AACtK,EAAE,UAAU,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AAClD,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,yIAAyI,CAAC;AAC9J,EAAE,MAAM,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AACzC,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,yFAAyF,CAAC;AAC9G,EAAE,UAAU,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC;AAChD,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACnC,EAAE,IAAI,IAAI,CAAC,KAAK,EAAE;AAClB,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,mDAAmD,EAAE,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,EAAE,WAAW,CAAC,IAAI,CAAC,WAAW,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,MAAM,EAAE,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC;AAC9M,IAAI,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;AACvC,MAAM,SAAS,CAAC,GAAG,IAAI,UAAU;AACjC,MAAM,MAAM,UAAU,GAAG,iBAAiB,CAAC,IAAI,CAAC,aAAa,CAAC;AAC9D,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,8DAA8D,CAAC;AACvF,MAAM,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACzF,QAAQ,IAAI,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;AACzC,QAAQ,eAAe,CAAC,SAAS,EAAE,EAAE,OAAO,EAAE,CAAC;AAC/C;AACA,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACvC,KAAK,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,IAAI,WAAW;AAClC,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,oHAAoH,EAAE,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,gEAAgE,CAAC;AACvO;AACA,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC/B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,8LAA8L,CAAC;AACrN;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,0BAA0B,CAAC;AAC/C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,CAAC;AAC/B,EAAE,GAAG,EAAE;AACP;;;;"}