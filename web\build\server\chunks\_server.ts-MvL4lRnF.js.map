{"version": 3, "file": "_server.ts-MvL4lRnF.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/admin/feature-usage/summary/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { v as verifySessionToken } from \"../../../../../../chunks/auth.js\";\nimport { p as prisma } from \"../../../../../../chunks/prisma.js\";\nimport { featureTablesExist } from \"../../../../../../chunks/feature-usage.js\";\nconst GET = async ({ cookies, url }) => {\n  const token = cookies.get(\"auth_token\");\n  if (!token) return new Response(\"Unauthorized\", { status: 401 });\n  const userData = verifySessionToken(token);\n  if (!userData?.id) return new Response(\"Unauthorized\", { status: 401 });\n  if (userData.role !== \"admin\") {\n    return new Response(\"Forbidden - Admin access required\", { status: 403 });\n  }\n  try {\n    const tablesExist = await featureTablesExist();\n    if (!tablesExist) {\n      return json([]);\n    }\n    const groupBy = url.searchParams.get(\"groupBy\") || \"feature\";\n    const usageRecords = await prisma.featureUsage.findMany({\n      include: {\n        feature: true,\n        limit: true\n      }\n    });\n    if (groupBy === \"feature\") {\n      const featureSummary = [];\n      const features = await prisma.feature.findMany();\n      for (const feature of features) {\n        const featureUsage = usageRecords.filter((u) => u.featureId === feature.id);\n        if (featureUsage.length > 0) {\n          const userIds = [...new Set(featureUsage.map((u) => u.userId))];\n          const totalUsed = featureUsage.reduce((sum, u) => sum + u.used, 0);\n          const periods = {};\n          for (const usage of featureUsage) {\n            const period = usage.period || \"all\";\n            if (!periods[period]) {\n              periods[period] = 0;\n            }\n            periods[period] += usage.used;\n          }\n          const periodData = Object.entries(periods).map(([period, used]) => ({\n            period,\n            used\n          }));\n          featureSummary.push({\n            id: feature.id,\n            name: feature.name,\n            totalUsed,\n            userCount: userIds.length,\n            periods: periodData\n          });\n        }\n      }\n      return json(featureSummary);\n    } else {\n      const limitSummary = [];\n      const limits = await prisma.featureLimit.findMany();\n      for (const limit of limits) {\n        const limitUsage = usageRecords.filter((u) => u.limitId === limit.id);\n        if (limitUsage.length > 0) {\n          const userIds = [...new Set(limitUsage.map((u) => u.userId))];\n          const totalUsed = limitUsage.reduce((sum, u) => sum + u.used, 0);\n          const periods = {};\n          for (const usage of limitUsage) {\n            const period = usage.period || \"all\";\n            if (!periods[period]) {\n              periods[period] = 0;\n            }\n            periods[period] += usage.used;\n          }\n          const periodData = Object.entries(periods).map(([period, used]) => ({\n            period,\n            used\n          }));\n          limitSummary.push({\n            id: limit.id,\n            name: limit.name,\n            totalUsed,\n            userCount: userIds.length,\n            periods: periodData\n          });\n        }\n      }\n      return json(limitSummary);\n    }\n  } catch (error) {\n    console.error(\"Error getting feature usage summary:\", error);\n    return json({ error: \"Failed to get feature usage summary\" }, { status: 500 });\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAIK,MAAC,GAAG,GAAG,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK;AACxC,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE,EAAE,MAAM,QAAQ,GAAG,kBAAkB,CAAC,KAAK,CAAC;AAC5C,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE,EAAE,IAAI,QAAQ,CAAC,IAAI,KAAK,OAAO,EAAE;AACjC,IAAI,OAAO,IAAI,QAAQ,CAAC,mCAAmC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7E;AACA,EAAE,IAAI;AACN,IAAI,MAAM,WAAW,GAAG,MAAM,kBAAkB,EAAE;AAClD,IAAI,IAAI,CAAC,WAAW,EAAE;AACtB,MAAM,OAAO,IAAI,CAAC,EAAE,CAAC;AACrB;AACA,IAAI,MAAM,OAAO,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,SAAS;AAChE,IAAI,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;AAC5D,MAAM,OAAO,EAAE;AACf,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,KAAK,EAAE;AACf;AACA,KAAK,CAAC;AACN,IAAI,IAAI,OAAO,KAAK,SAAS,EAAE;AAC/B,MAAM,MAAM,cAAc,GAAG,EAAE;AAC/B,MAAM,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE;AACtD,MAAM,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;AACtC,QAAQ,MAAM,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,KAAK,OAAO,CAAC,EAAE,CAAC;AACnF,QAAQ,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;AACrC,UAAU,MAAM,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;AACzE,UAAU,MAAM,SAAS,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;AAC5E,UAAU,MAAM,OAAO,GAAG,EAAE;AAC5B,UAAU,KAAK,MAAM,KAAK,IAAI,YAAY,EAAE;AAC5C,YAAY,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,IAAI,KAAK;AAChD,YAAY,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;AAClC,cAAc,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC;AACjC;AACA,YAAY,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,IAAI;AACzC;AACA,UAAU,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM;AAC9E,YAAY,MAAM;AAClB,YAAY;AACZ,WAAW,CAAC,CAAC;AACb,UAAU,cAAc,CAAC,IAAI,CAAC;AAC9B,YAAY,EAAE,EAAE,OAAO,CAAC,EAAE;AAC1B,YAAY,IAAI,EAAE,OAAO,CAAC,IAAI;AAC9B,YAAY,SAAS;AACrB,YAAY,SAAS,EAAE,OAAO,CAAC,MAAM;AACrC,YAAY,OAAO,EAAE;AACrB,WAAW,CAAC;AACZ;AACA;AACA,MAAM,OAAO,IAAI,CAAC,cAAc,CAAC;AACjC,KAAK,MAAM;AACX,MAAM,MAAM,YAAY,GAAG,EAAE;AAC7B,MAAM,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,QAAQ,EAAE;AACzD,MAAM,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;AAClC,QAAQ,MAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,KAAK,KAAK,CAAC,EAAE,CAAC;AAC7E,QAAQ,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;AACnC,UAAU,MAAM,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;AACvE,UAAU,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;AAC1E,UAAU,MAAM,OAAO,GAAG,EAAE;AAC5B,UAAU,KAAK,MAAM,KAAK,IAAI,UAAU,EAAE;AAC1C,YAAY,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,IAAI,KAAK;AAChD,YAAY,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;AAClC,cAAc,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC;AACjC;AACA,YAAY,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,IAAI;AACzC;AACA,UAAU,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM;AAC9E,YAAY,MAAM;AAClB,YAAY;AACZ,WAAW,CAAC,CAAC;AACb,UAAU,YAAY,CAAC,IAAI,CAAC;AAC5B,YAAY,EAAE,EAAE,KAAK,CAAC,EAAE;AACxB,YAAY,IAAI,EAAE,KAAK,CAAC,IAAI;AAC5B,YAAY,SAAS;AACrB,YAAY,SAAS,EAAE,OAAO,CAAC,MAAM;AACrC,YAAY,OAAO,EAAE;AACrB,WAAW,CAAC;AACZ;AACA;AACA,MAAM,OAAO,IAAI,CAAC,YAAY,CAAC;AAC/B;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC;AAChE,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,qCAAqC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClF;AACA;;;;"}