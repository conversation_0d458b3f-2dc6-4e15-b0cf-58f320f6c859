{"version": 3, "file": "_server.ts-B3hQowIH.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/sitemap.xml/_server.ts.js"], "sourcesContent": ["import fs from \"fs\";\nimport path from \"path\";\nimport { fileURLToPath } from \"url\";\nfunction scanRoutes(routesDir, basePath = \"\", ignorePatterns = []) {\n  const routes = [];\n  if (!fs.existsSync(routesDir)) {\n    return routes;\n  }\n  const entries = fs.readdirSync(routesDir, { withFileTypes: true });\n  for (const entry of entries) {\n    const entryPath = path.join(routesDir, entry.name);\n    if (entry.name.startsWith(\".\") || entry.name.startsWith(\"_\")) {\n      continue;\n    }\n    if (entry.isDirectory()) {\n      if (entry.name === \"api\" || entry.name === \"dashboard\" || entry.name === \"sitemap.xml\" || entry.name === \"robots.txt\") {\n        continue;\n      }\n      const routeName = entry.name.replace(/\\[\\.\\.\\.(.*)\\]/, \"\").replace(/\\[(.*)\\]/, \"\");\n      const hasPage = fs.existsSync(path.join(entryPath, \"+page.svelte\")) || fs.existsSync(path.join(entryPath, \"+page.js\")) || fs.existsSync(path.join(entryPath, \"+page.ts\"));\n      if (hasPage) {\n        if (!entry.name.includes(\"[\")) {\n          const routePath = path.join(basePath, routeName).replace(/\\\\/g, \"/\");\n          const shouldIgnore = ignorePatterns.some((pattern) => {\n            const normalizedPath = routePath.replace(/\\\\/g, \"/\");\n            const regexPattern = pattern.replace(/\\./g, \"\\\\.\").replace(/\\*/g, \".*\");\n            const regex = new RegExp(`^${regexPattern}$|^${regexPattern}/`);\n            return regex.test(normalizedPath);\n          });\n          if (!shouldIgnore) {\n            routes.push(routePath);\n          }\n        }\n      }\n      const subRoutes = scanRoutes(entryPath, path.join(basePath, routeName), ignorePatterns);\n      routes.push(...subRoutes);\n    } else if (entry.name === \"+page.svelte\" || entry.name === \"+page.js\" || entry.name === \"+page.ts\") {\n      if (basePath && !routes.includes(basePath)) {\n        routes.push(basePath);\n      }\n    }\n  }\n  return routes;\n}\nfunction generateSitemap(domain, routes, options = {}, ignoreRoutes = []) {\n  const {\n    changefreq = \"weekly\",\n    priority = 0.7,\n    includeAgents = false,\n    includeImages = true,\n    includeNews = false,\n    includeVideo = false\n  } = options;\n  const today = (/* @__PURE__ */ new Date()).toISOString();\n  if (!routes.includes(\"\")) {\n    routes.unshift(\"\");\n  }\n  const filteredRoutes = routes.filter((route) => {\n    const normalizedRoute = route.replace(/\\\\/g, \"/\");\n    if (ignoreRoutes.includes(normalizedRoute)) {\n      return false;\n    }\n    for (const ignoreRoute of ignoreRoutes) {\n      if (normalizedRoute === ignoreRoute || normalizedRoute.startsWith(`${ignoreRoute}/`)) {\n        return false;\n      }\n    }\n    return true;\n  });\n  let namespaces = 'xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\"';\n  if (includeAgents) {\n    namespaces += ' xmlns:xhtml=\"http://www.w3.org/1999/xhtml\"';\n  }\n  if (includeImages) {\n    namespaces += ' xmlns:image=\"http://www.google.com/schemas/sitemap-image/1.1\"';\n  }\n  if (includeNews) {\n    namespaces += ' xmlns:news=\"http://www.google.com/schemas/sitemap-news/0.9\"';\n  }\n  if (includeVideo) {\n    namespaces += ' xmlns:video=\"http://www.google.com/schemas/sitemap-video/1.1\"';\n  }\n  let sitemap = `<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<urlset ${namespaces}>\n`;\n  const routeSettings = {\n    \"\": { changefreq: \"daily\", priority: \"1.0\" },\n    blog: { changefreq: \"daily\", priority: \"0.9\" },\n    press: { changefreq: \"weekly\", priority: \"0.8\" },\n    \"press/releases\": { changefreq: \"weekly\", priority: \"0.8\" },\n    jobs: { changefreq: \"daily\", priority: \"0.9\" },\n    about: { changefreq: \"monthly\", priority: \"0.7\" },\n    contact: { changefreq: \"monthly\", priority: \"0.7\" },\n    pricing: { changefreq: \"monthly\", priority: \"0.8\" },\n    terms: { changefreq: \"yearly\", priority: \"0.5\" },\n    privacy: { changefreq: \"yearly\", priority: \"0.5\" },\n    \"system-status\": { changefreq: \"hourly\", priority: \"0.6\" }\n  };\n  sitemap += filteredRoutes.map((route) => {\n    const settings = routeSettings[route] || {\n      changefreq,\n      priority\n    };\n    let entry = `  <url>\n    <loc>${domain}/${route}</loc>\n    <lastmod>${today}</lastmod>\n    <changefreq>${settings.changefreq}</changefreq>\n    <priority>${settings.priority}</priority>`;\n    if (includeAgents) {\n      entry += `\n    <xhtml:link rel=\"alternate\" href=\"${domain}/${route}\" hreflang=\"en\" />\n    <xhtml:link rel=\"alternate\" media=\"only screen and (max-width: 640px)\" href=\"${domain}/${route}\" />`;\n    }\n    if (includeImages && (route === \"\" || route === \"about\" || route.startsWith(\"press\"))) {\n      entry += `\n    <image:image>\n      <image:loc>${domain}/images/logo.png</image:loc>\n      <image:title>Hirli Logo</image:title>\n      <image:caption>Hirli - AI-powered job application platform</image:caption>\n    </image:image>`;\n    }\n    if (includeNews && route === \"press/releases\") {\n      entry += `\n    <news:news>\n      <news:publication>\n        <news:name>Hirli Press Releases</news:name>\n        <news:language>en</news:language>\n      </news:publication>\n      <news:publication_date>${today}</news:publication_date>\n      <news:title>Latest Hirli Press Releases</news:title>\n    </news:news>`;\n    }\n    entry += `\n  </url>`;\n    return entry;\n  }).join(\"\\n\");\n  sitemap += `\n</urlset>`;\n  return sitemap;\n}\nconst GET = async ({ url }) => {\n  const host = process.env.NODE_ENV === \"production\" ? \"hirli.com\" : url.host;\n  const protocol = process.env.NODE_ENV === \"production\" ? \"https\" : url.protocol.slice(0, -1);\n  const domain = `${protocol}://${host}`;\n  const __filename = fileURLToPath(import.meta.url);\n  const __dirname = path.dirname(__filename);\n  const routesDir = path.resolve(__dirname, \"..\");\n  const ignorePatterns = [\n    \"auth/*\",\n    \"dashboard/*\",\n    \"api/*\",\n    \"admin/*\",\n    \"legal/*/edit\",\n    \"profile/*\",\n    \"settings/*\",\n    \"account/*\",\n    \"user/*\",\n    \"notifications/*\",\n    \"test/*\",\n    \"demo/*\",\n    \"beta/*\",\n    \"dev/*\",\n    \"staging/*\"\n  ];\n  const ignoreRoutes = [\n    \"auth\",\n    \"login\",\n    \"signup\",\n    \"reset-password\",\n    \"verify-email\",\n    \"forgot-password\",\n    \"logout\",\n    \"callback\"\n  ];\n  const routes = scanRoutes(routesDir, \"\", ignorePatterns);\n  const additionalRoutes = [\n    \"blog\",\n    \"jobs\",\n    \"press\",\n    \"press/releases\",\n    \"press/images\",\n    \"press/logo-guidelines\",\n    \"press/brand-guidelines\",\n    \"press/contact\",\n    \"system-status\"\n  ];\n  const allRoutes = [.../* @__PURE__ */ new Set([...routes, ...additionalRoutes])];\n  const sitemap = generateSitemap(\n    domain,\n    allRoutes,\n    {\n      changefreq: \"weekly\",\n      priority: 0.8,\n      includeAgents: true,\n      includeImages: true,\n      includeNews: process.env.NODE_ENV === \"production\",\n      // Only include news in production\n      includeVideo: false\n    },\n    ignoreRoutes\n  );\n  return new Response(sitemap, {\n    headers: {\n      \"Content-Type\": \"application/xml\",\n      \"Cache-Control\": \"max-age=0, s-maxage=3600\"\n    }\n  });\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;AAGA,SAAS,UAAU,CAAC,SAAS,EAAE,QAAQ,GAAG,EAAE,EAAE,cAAc,GAAG,EAAE,EAAE;AACnE,EAAE,MAAM,MAAM,GAAG,EAAE;AACnB,EAAE,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;AACjC,IAAI,OAAO,MAAM;AACjB;AACA,EAAE,MAAM,OAAO,GAAG,EAAE,CAAC,WAAW,CAAC,SAAS,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC;AACpE,EAAE,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE;AAC/B,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC;AACtD,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;AAClE,MAAM;AACN;AACA,IAAI,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE;AAC7B,MAAM,IAAI,KAAK,CAAC,IAAI,KAAK,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,WAAW,IAAI,KAAK,CAAC,IAAI,KAAK,aAAa,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE;AAC7H,QAAQ;AACR;AACA,MAAM,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;AACxF,MAAM,MAAM,OAAO,GAAG,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;AAC/K,MAAM,IAAI,OAAO,EAAE;AACnB,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AACvC,UAAU,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;AAC9E,UAAU,MAAM,YAAY,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC,OAAO,KAAK;AAChE,YAAY,MAAM,cAAc,GAAG,SAAS,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;AAChE,YAAY,MAAM,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC;AACnF,YAAY,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;AAC3E,YAAY,OAAO,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC;AAC7C,WAAW,CAAC;AACZ,UAAU,IAAI,CAAC,YAAY,EAAE;AAC7B,YAAY,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;AAClC;AACA;AACA;AACA,MAAM,MAAM,SAAS,GAAG,UAAU,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAE,cAAc,CAAC;AAC7F,MAAM,MAAM,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC;AAC/B,KAAK,MAAM,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE;AACxG,MAAM,IAAI,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;AAClD,QAAQ,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC7B;AACA;AACA;AACA,EAAE,OAAO,MAAM;AACf;AACA,SAAS,eAAe,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,GAAG,EAAE,EAAE,YAAY,GAAG,EAAE,EAAE;AAC1E,EAAE,MAAM;AACR,IAAI,UAAU,GAAG,QAAQ;AACzB,IAAI,QAAQ,GAAG,GAAG;AAClB,IAAI,aAAa,GAAG,KAAK;AACzB,IAAI,aAAa,GAAG,IAAI;AACxB,IAAI,WAAW,GAAG,KAAK;AACvB,IAAI,YAAY,GAAG;AACnB,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,KAAK,GAAG,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW,EAAE;AAC1D,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;AAC5B,IAAI,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;AACtB;AACA,EAAE,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,KAAK;AAClD,IAAI,MAAM,eAAe,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;AACrD,IAAI,IAAI,YAAY,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE;AAChD,MAAM,OAAO,KAAK;AAClB;AACA,IAAI,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE;AAC5C,MAAM,IAAI,eAAe,KAAK,WAAW,IAAI,eAAe,CAAC,UAAU,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;AAC5F,QAAQ,OAAO,KAAK;AACpB;AACA;AACA,IAAI,OAAO,IAAI;AACf,GAAG,CAAC;AACJ,EAAE,IAAI,UAAU,GAAG,qDAAqD;AACxE,EAAE,IAAI,aAAa,EAAE;AACrB,IAAI,UAAU,IAAI,6CAA6C;AAC/D;AACA,EAAE,IAAI,aAAa,EAAE;AACrB,IAAI,UAAU,IAAI,gEAAgE;AAClF;AACA,EAAE,IAAI,WAAW,EAAE;AACnB,IAAI,UAAU,IAAI,8DAA8D;AAChF;AACA,EAAE,IAAI,YAAY,EAAE;AACpB,IAAI,UAAU,IAAI,gEAAgE;AAClF;AACA,EAAE,IAAI,OAAO,GAAG,CAAC;AACjB,QAAQ,EAAE,UAAU,CAAC;AACrB,CAAC;AACD,EAAE,MAAM,aAAa,GAAG;AACxB,IAAI,EAAE,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE;AAChD,IAAI,IAAI,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE;AAClD,IAAI,KAAK,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE;AACpD,IAAI,gBAAgB,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE;AAC/D,IAAI,IAAI,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE;AAClD,IAAI,KAAK,EAAE,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE;AACrD,IAAI,OAAO,EAAE,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE;AACvD,IAAI,OAAO,EAAE,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE;AACvD,IAAI,KAAK,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE;AACpD,IAAI,OAAO,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE;AACtD,IAAI,eAAe,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK;AAC5D,GAAG;AACH,EAAE,OAAO,IAAI,cAAc,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK;AAC3C,IAAI,MAAM,QAAQ,GAAG,aAAa,CAAC,KAAK,CAAC,IAAI;AAC7C,MAAM,UAAU;AAChB,MAAM;AACN,KAAK;AACL,IAAI,IAAI,KAAK,GAAG,CAAC;AACjB,SAAS,EAAE,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC;AAC3B,aAAa,EAAE,KAAK,CAAC;AACrB,gBAAgB,EAAE,QAAQ,CAAC,UAAU,CAAC;AACtC,cAAc,EAAE,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC;AAC9C,IAAI,IAAI,aAAa,EAAE;AACvB,MAAM,KAAK,IAAI;AACf,sCAAsC,EAAE,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC;AACxD,iFAAiF,EAAE,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC;AACxG;AACA,IAAI,IAAI,aAAa,KAAK,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,OAAO,IAAI,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,EAAE;AAC3F,MAAM,KAAK,IAAI;AACf;AACA,iBAAiB,EAAE,MAAM,CAAC;AAC1B;AACA;AACA,kBAAkB,CAAC;AACnB;AACA,IAAI,IAAI,WAAW,IAAI,KAAK,KAAK,gBAAgB,EAAE;AACnD,MAAM,KAAK,IAAI;AACf;AACA;AACA;AACA;AACA;AACA,6BAA6B,EAAE,KAAK,CAAC;AACrC;AACA,gBAAgB,CAAC;AACjB;AACA,IAAI,KAAK,IAAI;AACb,QAAQ,CAAC;AACT,IAAI,OAAO,KAAK;AAChB,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;AACf,EAAE,OAAO,IAAI;AACb,SAAS,CAAC;AACV,EAAE,OAAO,OAAO;AAChB;AACK,MAAC,GAAG,GAAG,OAAO,EAAE,GAAG,EAAE,KAAK;AAC/B,EAAE,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,GAAG,WAAW,GAAG,GAAG,CAAC,IAAI;AAC7E,EAAE,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,GAAG,OAAO,GAAG,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;AAC9F,EAAE,MAAM,MAAM,GAAG,CAAC,EAAE,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AACxC,EAAE,MAAM,UAAU,GAAG,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;AACnD,EAAE,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;AAC5C,EAAE,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC;AACjD,EAAE,MAAM,cAAc,GAAG;AACzB,IAAI,QAAQ;AACZ,IAAI,aAAa;AACjB,IAAI,OAAO;AACX,IAAI,SAAS;AACb,IAAI,cAAc;AAClB,IAAI,WAAW;AACf,IAAI,YAAY;AAChB,IAAI,WAAW;AACf,IAAI,QAAQ;AACZ,IAAI,iBAAiB;AACrB,IAAI,QAAQ;AACZ,IAAI,QAAQ;AACZ,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI;AACJ,GAAG;AACH,EAAE,MAAM,YAAY,GAAG;AACvB,IAAI,MAAM;AACV,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,gBAAgB;AACpB,IAAI,cAAc;AAClB,IAAI,iBAAiB;AACrB,IAAI,QAAQ;AACZ,IAAI;AACJ,GAAG;AACH,EAAE,MAAM,MAAM,GAAG,UAAU,CAAC,SAAS,EAAE,EAAE,EAAE,cAAc,CAAC;AAC1D,EAAE,MAAM,gBAAgB,GAAG;AAC3B,IAAI,MAAM;AACV,IAAI,MAAM;AACV,IAAI,OAAO;AACX,IAAI,gBAAgB;AACpB,IAAI,cAAc;AAClB,IAAI,uBAAuB;AAC3B,IAAI,wBAAwB;AAC5B,IAAI,eAAe;AACnB,IAAI;AACJ,GAAG;AACH,EAAE,MAAM,SAAS,GAAG,CAAC,mBAAmB,IAAI,GAAG,CAAC,CAAC,GAAG,MAAM,EAAE,GAAG,gBAAgB,CAAC,CAAC,CAAC;AAClF,EAAE,MAAM,OAAO,GAAG,eAAe;AACjC,IAAI,MAAM;AACV,IAAI,SAAS;AACb,IAAI;AACJ,MAAM,UAAU,EAAE,QAAQ;AAC1B,MAAM,QAAQ,EAAE,GAAG;AACnB,MAAM,aAAa,EAAE,IAAI;AACzB,MAAM,aAAa,EAAE,IAAI;AACzB,MAAM,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;AACxD;AACA,MAAM,YAAY,EAAE;AACpB,KAAK;AACL,IAAI;AACJ,GAAG;AACH,EAAE,OAAO,IAAI,QAAQ,CAAC,OAAO,EAAE;AAC/B,IAAI,OAAO,EAAE;AACb,MAAM,cAAc,EAAE,iBAAiB;AACvC,MAAM,eAAe,EAAE;AACvB;AACA,GAAG,CAAC;AACJ;;;;"}