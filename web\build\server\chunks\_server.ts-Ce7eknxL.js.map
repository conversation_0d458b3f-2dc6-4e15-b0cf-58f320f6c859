{"version": 3, "file": "_server.ts-Ce7eknxL.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/email/templates/list/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { l as logger } from \"../../../../../../chunks/logger.js\";\nimport { E as EmailTemplate } from \"../../../../../../chunks/types.js\";\nasync function GET() {\n  try {\n    const allTemplates = [\n      {\n        name: EmailTemplate.WELCOME,\n        label: \"Welcome Email\",\n        category: \"Transactional\",\n        description: \"Sent to new users after registration\"\n      },\n      {\n        name: EmailTemplate.VERIFICATION,\n        label: \"Email Verification\",\n        category: \"Transactional\",\n        description: \"Sent to verify a user's email address\"\n      },\n      {\n        name: EmailTemplate.PASSWORD_RESET,\n        label: \"Password Reset\",\n        category: \"Transactional\",\n        description: \"Sent when a user requests a password reset\"\n      },\n      {\n        name: EmailTemplate.PASSWORD_CHANGED,\n        label: \"Password Changed\",\n        category: \"Transactional\",\n        description: \"Sent when a user changes their password\"\n      },\n      {\n        name: EmailTemplate.JOB_APPLICATION_SUBMITTED,\n        label: \"Job Application Submitted\",\n        category: \"Notification\",\n        description: \"Sent when a user submits a job application\"\n      },\n      {\n        name: EmailTemplate.JOB_APPLICATION_STATUS_UPDATE,\n        label: \"Job Application Status Update\",\n        category: \"Notification\",\n        description: \"Sent when a job application status changes\"\n      },\n      {\n        name: EmailTemplate.RESUME_OPTIMIZATION_COMPLETE,\n        label: \"Resume Optimization Complete\",\n        category: \"Notification\",\n        description: \"Sent when resume optimization is complete\"\n      },\n      {\n        name: EmailTemplate.WEEKLY_SUMMARY,\n        label: \"Weekly Summary\",\n        category: \"Marketing\",\n        description: \"Weekly summary of activity\"\n      },\n      {\n        name: EmailTemplate.TEST_TEMPLATE,\n        label: \"Test Template\",\n        category: \"System\",\n        description: \"Used for testing email delivery\"\n      }\n    ];\n    const categories = [...new Set(allTemplates.map((template) => template.category))];\n    return json({\n      allTemplates,\n      categories\n    });\n  } catch (error) {\n    logger.error(\"Error listing templates:\", error);\n    return json({ error: \"Failed to list templates\" }, { status: 500 });\n  }\n}\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;AAGA,eAAe,GAAG,GAAG;AACrB,EAAE,IAAI;AACN,IAAI,MAAM,YAAY,GAAG;AACzB,MAAM;AACN,QAAQ,IAAI,EAAE,aAAa,CAAC,OAAO;AACnC,QAAQ,KAAK,EAAE,eAAe;AAC9B,QAAQ,QAAQ,EAAE,eAAe;AACjC,QAAQ,WAAW,EAAE;AACrB,OAAO;AACP,MAAM;AACN,QAAQ,IAAI,EAAE,aAAa,CAAC,YAAY;AACxC,QAAQ,KAAK,EAAE,oBAAoB;AACnC,QAAQ,QAAQ,EAAE,eAAe;AACjC,QAAQ,WAAW,EAAE;AACrB,OAAO;AACP,MAAM;AACN,QAAQ,IAAI,EAAE,aAAa,CAAC,cAAc;AAC1C,QAAQ,KAAK,EAAE,gBAAgB;AAC/B,QAAQ,QAAQ,EAAE,eAAe;AACjC,QAAQ,WAAW,EAAE;AACrB,OAAO;AACP,MAAM;AACN,QAAQ,IAAI,EAAE,aAAa,CAAC,gBAAgB;AAC5C,QAAQ,KAAK,EAAE,kBAAkB;AACjC,QAAQ,QAAQ,EAAE,eAAe;AACjC,QAAQ,WAAW,EAAE;AACrB,OAAO;AACP,MAAM;AACN,QAAQ,IAAI,EAAE,aAAa,CAAC,yBAAyB;AACrD,QAAQ,KAAK,EAAE,2BAA2B;AAC1C,QAAQ,QAAQ,EAAE,cAAc;AAChC,QAAQ,WAAW,EAAE;AACrB,OAAO;AACP,MAAM;AACN,QAAQ,IAAI,EAAE,aAAa,CAAC,6BAA6B;AACzD,QAAQ,KAAK,EAAE,+BAA+B;AAC9C,QAAQ,QAAQ,EAAE,cAAc;AAChC,QAAQ,WAAW,EAAE;AACrB,OAAO;AACP,MAAM;AACN,QAAQ,IAAI,EAAE,aAAa,CAAC,4BAA4B;AACxD,QAAQ,KAAK,EAAE,8BAA8B;AAC7C,QAAQ,QAAQ,EAAE,cAAc;AAChC,QAAQ,WAAW,EAAE;AACrB,OAAO;AACP,MAAM;AACN,QAAQ,IAAI,EAAE,aAAa,CAAC,cAAc;AAC1C,QAAQ,KAAK,EAAE,gBAAgB;AAC/B,QAAQ,QAAQ,EAAE,WAAW;AAC7B,QAAQ,WAAW,EAAE;AACrB,OAAO;AACP,MAAM;AACN,QAAQ,IAAI,EAAE,aAAa,CAAC,aAAa;AACzC,QAAQ,KAAK,EAAE,eAAe;AAC9B,QAAQ,QAAQ,EAAE,QAAQ;AAC1B,QAAQ,WAAW,EAAE;AACrB;AACA,KAAK;AACL,IAAI,MAAM,UAAU,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;AACtF,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,YAAY;AAClB,MAAM;AACN,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC;AACnD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,0BAA0B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACvE;AACA;;;;"}