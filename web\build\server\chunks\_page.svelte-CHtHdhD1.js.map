{"version": 3, "file": "_page.svelte-CHtHdhD1.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/auth/verified/_page.svelte.js"], "sourcesContent": ["import \"clsx\";\nimport { B as Button } from \"../../../../chunks/button.js\";\nimport { C as Card } from \"../../../../chunks/card.js\";\nimport { C as Card_content } from \"../../../../chunks/card-content.js\";\nimport { S as SEO } from \"../../../../chunks/SEO.js\";\nimport { C as Circle_check_big } from \"../../../../chunks/circle-check-big.js\";\nfunction _page($$payload) {\n  SEO($$payload, {\n    title: \"Email Verified | Auto Apply\",\n    description: \"Your email has been verified successfully\"\n  });\n  $$payload.out += `<!----> <div class=\"flex min-h-[calc(100vh-200px)] flex-col items-center justify-center p-4\"><div class=\"w-full max-w-md\">`;\n  Card($$payload, {\n    class: \"text-center\",\n    children: ($$payload2) => {\n      Card_content($$payload2, {\n        class: \"pt-6\",\n        children: ($$payload3) => {\n          $$payload3.out += `<div class=\"mb-4 flex justify-center\"><div class=\"rounded-full bg-green-100 p-3\">`;\n          Circle_check_big($$payload3, { class: \"h-10 w-10 text-green-600\" });\n          $$payload3.out += `<!----></div></div> <h1 class=\"mb-2 text-3xl font-bold\">Email Verified!</h1> <p class=\"text-muted-foreground mb-6 max-w-md\">Your email address has been successfully verified. You can now access all features of your\n          account.</p> <div class=\"space-y-3\">`;\n          Button($$payload3, {\n            href: \"/auth/sign-in\",\n            class: \"w-full\",\n            children: ($$payload4) => {\n              $$payload4.out += `<!---->Sign In to Your Account`;\n            },\n            $$slots: { default: true }\n          });\n          $$payload3.out += `<!----> `;\n          Button($$payload3, {\n            href: \"/\",\n            variant: \"outline\",\n            class: \"w-full\",\n            children: ($$payload4) => {\n              $$payload4.out += `<!---->Go to Homepage`;\n            },\n            $$slots: { default: true }\n          });\n          $$payload3.out += `<!----></div>`;\n        },\n        $$slots: { default: true }\n      });\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----></div></div>`;\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAMA,SAAS,KAAK,CAAC,SAAS,EAAE;AAC1B,EAAE,GAAG,CAAC,SAAS,EAAE;AACjB,IAAI,KAAK,EAAE,6BAA6B;AACxC,IAAI,WAAW,EAAE;AACjB,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,0HAA0H,CAAC;AAC/I,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,IAAI,KAAK,EAAE,aAAa;AACxB,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,YAAY,CAAC,UAAU,EAAE;AAC/B,QAAQ,KAAK,EAAE,MAAM;AACrB,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,iFAAiF,CAAC;AAC/G,UAAU,gBAAgB,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,0BAA0B,EAAE,CAAC;AAC7E,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC;AAC7B,8CAA8C,CAAC;AAC/C,UAAU,MAAM,CAAC,UAAU,EAAE;AAC7B,YAAY,IAAI,EAAE,eAAe;AACjC,YAAY,KAAK,EAAE,QAAQ;AAC3B,YAAY,QAAQ,EAAE,CAAC,UAAU,KAAK;AACtC,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,8BAA8B,CAAC;AAChE,aAAa;AACb,YAAY,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACpC,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACtC,UAAU,MAAM,CAAC,UAAU,EAAE;AAC7B,YAAY,IAAI,EAAE,GAAG;AACrB,YAAY,OAAO,EAAE,SAAS;AAC9B,YAAY,KAAK,EAAE,QAAQ;AAC3B,YAAY,QAAQ,EAAE,CAAC,UAAU,KAAK;AACtC,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC;AACvD,aAAa;AACb,YAAY,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACpC,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AAC3C,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC,OAAO,CAAC;AACR,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC;AACxC;;;;"}