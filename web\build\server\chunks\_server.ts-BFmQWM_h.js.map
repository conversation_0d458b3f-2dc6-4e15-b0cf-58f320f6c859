{"version": 3, "file": "_server.ts-BFmQWM_h.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/websocket/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../chunks/index.js\";\nimport { g as getRedisClient } from \"../../../../chunks/redis.js\";\nconst POST = async ({ request }) => {\n  try {\n    const data = await request.json();\n    const redis = await getRedisClient();\n    if (!redis) {\n      return json({ error: \"Redis client not available\" }, { status: 500 });\n    }\n    if (!data.timestamp) {\n      data.timestamp = (/* @__PURE__ */ new Date()).toISOString();\n    }\n    await redis.publish(\"websocket::broadcast\", JSON.stringify(data));\n    return json({ success: true, message: \"WebSocket message sent\" });\n  } catch (error) {\n    console.error(\"Error sending WebSocket message:\", error);\n    return json({ error: \"Failed to send WebSocket message\" }, { status: 500 });\n  }\n};\nconst GET = async () => {\n  return json({\n    status: \"WebSocket server is running\",\n    timestamp: (/* @__PURE__ */ new Date()).toISOString()\n  });\n};\nexport {\n  GET,\n  POST\n};\n"], "names": [], "mappings": ";;;;AAEK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK;AACpC,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,MAAM,KAAK,GAAG,MAAM,cAAc,EAAE;AACxC,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,4BAA4B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3E;AACA,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACzB,MAAM,IAAI,CAAC,SAAS,GAAG,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW,EAAE;AACjE;AACA,IAAI,MAAM,KAAK,CAAC,OAAO,CAAC,sBAAsB,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AACrE,IAAI,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,wBAAwB,EAAE,CAAC;AACrE,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC;AAC5D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,kCAAkC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC/E;AACA;AACK,MAAC,GAAG,GAAG,YAAY;AACxB,EAAE,OAAO,IAAI,CAAC;AACd,IAAI,MAAM,EAAE,6BAA6B;AACzC,IAAI,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AACvD,GAAG,CAAC;AACJ;;;;"}