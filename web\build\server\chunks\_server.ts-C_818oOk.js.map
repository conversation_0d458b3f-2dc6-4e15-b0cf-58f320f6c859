{"version": 3, "file": "_server.ts-C_818oOk.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/admin/features/sync-all/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../../../../chunks/auth.js\";\nimport { F as FeatureAccessLevel } from \"../../../../../../chunks/features.js\";\nasync function syncAllFeaturesWithPlans() {\n  try {\n    const features = await prisma.feature.findMany();\n    if (features.length === 0) {\n      console.log(\"No features found to synchronize.\");\n      return { count: 0, errors: 0 };\n    }\n    console.log(`Found ${features.length} features to synchronize.`);\n    const plans = await prisma.plan.findMany({\n      include: {\n        features: {\n          include: {\n            limits: true\n          }\n        }\n      }\n    });\n    if (plans.length === 0) {\n      console.log(\"No plans found to synchronize with.\");\n      return { count: 0, errors: 0 };\n    }\n    console.log(`Found ${plans.length} plans to synchronize.`);\n    const getDefaultAccessLevel = (planName, featureId) => {\n      const planNameLower = planName.toLowerCase();\n      if (featureId.startsWith(\"career_\") || featureId === \"privacy_settings\" || featureId === \"notification_preferences\") {\n        return FeatureAccessLevel.Included;\n      }\n      if (planNameLower.includes(\"free\")) {\n        if (featureId === \"job_alerts\" || featureId === \"resume_builder\" || featureId === \"application_tracking\") {\n          return FeatureAccessLevel.Limited;\n        }\n        return FeatureAccessLevel.NotIncluded;\n      }\n      if (planNameLower.includes(\"starter\") || planNameLower.includes(\"basic\") || planNameLower.includes(\"casual\")) {\n        return FeatureAccessLevel.Limited;\n      }\n      if (planNameLower.includes(\"pro\") || planNameLower.includes(\"premium\")) {\n        return FeatureAccessLevel.Included;\n      }\n      if (planNameLower.includes(\"enterprise\") || planNameLower.includes(\"business\")) {\n        return FeatureAccessLevel.Unlimited;\n      }\n      return FeatureAccessLevel.Limited;\n    };\n    let syncCount = 0;\n    let errorCount = 0;\n    for (const plan of plans) {\n      console.log(`\nSynchronizing plan: ${plan.name}`);\n      const existingFeatureIds = plan.features.map((f) => f.featureId);\n      const featuresToAdd = features.filter((feature) => !existingFeatureIds.includes(feature.id));\n      if (featuresToAdd.length === 0) {\n        console.log(`  No new features to add to plan ${plan.name}`);\n      } else {\n        console.log(`  Adding ${featuresToAdd.length} features to plan ${plan.name}`);\n        for (const feature of featuresToAdd) {\n          try {\n            const accessLevel = getDefaultAccessLevel(plan.name, feature.id);\n            await prisma.planFeature.create({\n              data: {\n                planId: plan.id,\n                featureId: feature.id,\n                accessLevel\n              }\n            });\n            console.log(`  Added feature ${feature.id} to plan ${plan.name} with access level: ${accessLevel}`);\n            syncCount++;\n          } catch (error) {\n            console.error(`  Error adding feature ${feature.id} to plan ${plan.name}:`, error);\n            errorCount++;\n          }\n        }\n      }\n    }\n    console.log(\"\\nPlan synchronization complete!\");\n    console.log(`Synchronized: ${syncCount} feature-plan relationships`);\n    console.log(`Errors: ${errorCount} during synchronization`);\n    return { count: syncCount, errors: errorCount };\n  } catch (error) {\n    console.error(\"Error synchronizing features with plans:\", error);\n    throw error;\n  }\n}\nconst POST = async ({ cookies }) => {\n  const token = cookies.get(\"auth_token\");\n  if (!token) return new Response(\"Unauthorized\", { status: 401 });\n  const userData = await verifySessionToken(token);\n  if (!userData?.id) return new Response(\"Unauthorized\", { status: 401 });\n  if (!userData.isAdmin) {\n    return new Response(\"Forbidden - Admin access required\", { status: 403 });\n  }\n  try {\n    const result = await syncAllFeaturesWithPlans();\n    return json({\n      success: true,\n      message: `Successfully synchronized ${result.count} feature-plan relationships`,\n      count: result.count,\n      errors: result.errors\n    });\n  } catch (error) {\n    console.error(\"Error in feature sync API:\", error);\n    return json({\n      success: false,\n      message: `Error synchronizing features: ${error.message}`\n    }, { status: 500 });\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAIA,eAAe,wBAAwB,GAAG;AAC1C,EAAE,IAAI;AACN,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE;AACpD,IAAI,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;AAC/B,MAAM,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC;AACtD,MAAM,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;AACpC;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,yBAAyB,CAAC,CAAC;AACpE,IAAI,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC7C,MAAM,OAAO,EAAE;AACf,QAAQ,QAAQ,EAAE;AAClB,UAAU,OAAO,EAAE;AACnB,YAAY,MAAM,EAAE;AACpB;AACA;AACA;AACA,KAAK,CAAC;AACN,IAAI,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;AAC5B,MAAM,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC;AACxD,MAAM,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;AACpC;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAC9D,IAAI,MAAM,qBAAqB,GAAG,CAAC,QAAQ,EAAE,SAAS,KAAK;AAC3D,MAAM,MAAM,aAAa,GAAG,QAAQ,CAAC,WAAW,EAAE;AAClD,MAAM,IAAI,SAAS,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,SAAS,KAAK,kBAAkB,IAAI,SAAS,KAAK,0BAA0B,EAAE;AAC3H,QAAQ,OAAO,kBAAkB,CAAC,QAAQ;AAC1C;AACA,MAAM,IAAI,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;AAC1C,QAAQ,IAAI,SAAS,KAAK,YAAY,IAAI,SAAS,KAAK,gBAAgB,IAAI,SAAS,KAAK,sBAAsB,EAAE;AAClH,UAAU,OAAO,kBAAkB,CAAC,OAAO;AAC3C;AACA,QAAQ,OAAO,kBAAkB,CAAC,WAAW;AAC7C;AACA,MAAM,IAAI,aAAa,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;AACpH,QAAQ,OAAO,kBAAkB,CAAC,OAAO;AACzC;AACA,MAAM,IAAI,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,aAAa,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;AAC9E,QAAQ,OAAO,kBAAkB,CAAC,QAAQ;AAC1C;AACA,MAAM,IAAI,aAAa,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,aAAa,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;AACtF,QAAQ,OAAO,kBAAkB,CAAC,SAAS;AAC3C;AACA,MAAM,OAAO,kBAAkB,CAAC,OAAO;AACvC,KAAK;AACL,IAAI,IAAI,SAAS,GAAG,CAAC;AACrB,IAAI,IAAI,UAAU,GAAG,CAAC;AACtB,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;AAC9B,MAAM,OAAO,CAAC,GAAG,CAAC;AAClB,oBAAoB,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAClC,MAAM,MAAM,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC;AACtE,MAAM,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,OAAO,KAAK,CAAC,kBAAkB,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AAClG,MAAM,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE;AACtC,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACpE,OAAO,MAAM;AACb,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,aAAa,CAAC,MAAM,CAAC,kBAAkB,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACrF,QAAQ,KAAK,MAAM,OAAO,IAAI,aAAa,EAAE;AAC7C,UAAU,IAAI;AACd,YAAY,MAAM,WAAW,GAAG,qBAAqB,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,CAAC;AAC5E,YAAY,MAAM,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;AAC5C,cAAc,IAAI,EAAE;AACpB,gBAAgB,MAAM,EAAE,IAAI,CAAC,EAAE;AAC/B,gBAAgB,SAAS,EAAE,OAAO,CAAC,EAAE;AACrC,gBAAgB;AAChB;AACA,aAAa,CAAC;AACd,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,gBAAgB,EAAE,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,WAAW,CAAC,CAAC,CAAC;AAC/G,YAAY,SAAS,EAAE;AACvB,WAAW,CAAC,OAAO,KAAK,EAAE;AAC1B,YAAY,OAAO,CAAC,KAAK,CAAC,CAAC,uBAAuB,EAAE,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;AAC9F,YAAY,UAAU,EAAE;AACxB;AACA;AACA;AACA;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC;AACnD,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,2BAA2B,CAAC,CAAC;AACxE,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC,uBAAuB,CAAC,CAAC;AAC/D,IAAI,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE;AACnD,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC;AACpE,IAAI,MAAM,KAAK;AACf;AACA;AACK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK;AACpC,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE,EAAE,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AAClD,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AACzB,IAAI,OAAO,IAAI,QAAQ,CAAC,mCAAmC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7E;AACA,EAAE,IAAI;AACN,IAAI,MAAM,MAAM,GAAG,MAAM,wBAAwB,EAAE;AACnD,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,OAAO,EAAE,CAAC,0BAA0B,EAAE,MAAM,CAAC,KAAK,CAAC,2BAA2B,CAAC;AACrF,MAAM,KAAK,EAAE,MAAM,CAAC,KAAK;AACzB,MAAM,MAAM,EAAE,MAAM,CAAC;AACrB,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC;AACtD,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,OAAO,EAAE,CAAC,8BAA8B,EAAE,KAAK,CAAC,OAAO,CAAC;AAC9D,KAAK,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACvB;AACA;;;;"}