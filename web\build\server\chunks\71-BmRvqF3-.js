import { f as fail } from './index-Ddp2AB5f.js';
import { s as superValidate, z as zod } from './zod-DfpldWlD.js';
import { o as objectType, s as stringType, l as literalType } from './types-D78SXuvm.js';
import { p as prisma } from './prisma-Cit_HrSw.js';
import { v as verifySessionToken } from './auth-BPad-IlN.js';

const jobApplicationSchema = objectType({
  company: stringType().min(1, { message: "Company is required" }),
  position: stringType().min(1, { message: "Position is required" }),
  location: stringType().optional(),
  appliedDate: stringType().min(1, { message: "Applied date is required" }),
  status: stringType().min(1, { message: "Status is required" }),
  nextAction: stringType().optional(),
  notes: stringType().optional(),
  url: stringType().url({ message: "Must be a valid URL" }).optional().or(literalType("")),
  jobType: stringType().min(1, { message: "Job type is required" }),
  resumeUploaded: stringType().min(1, { message: "Resume status is required" })
});

const load = async ({ cookies }) => {
  const form = await superValidate(zod(jobApplicationSchema));
  const token = cookies.get("auth_token");
  const user = token ? await verifySessionToken(token) : null;
  if (!user) {
    return {
      form,
      applications: []
    };
  }
  try {
    const applications = await prisma.application.findMany({
      where: {
        userId: user.id
      },
      orderBy: {
        appliedDate: "desc"
      }
    });
    const transformedApplications = applications.map((app) => ({
      id: app.id,
      company: app.company,
      position: app.position,
      location: app.location || "Remote",
      appliedDate: app.appliedDate.toISOString().split("T")[0],
      // Format as YYYY-MM-DD
      status: app.status,
      nextAction: app.nextAction || "",
      notes: app.notes || "",
      logo: "https://placehold.co/100x100",
      // Default logo for now
      url: app.url || "",
      jobType: app.jobType || "Full-time",
      resumeUploaded: app.resumeUploaded ? "Yes" : "No"
    }));
    return {
      form,
      applications: transformedApplications
    };
  } catch (error) {
    console.error("Error loading applications:", error);
    return {
      form,
      applications: []
    };
  }
};
const actions = {
  addJob: async ({ request, cookies }) => {
    const form = await superValidate(request, zod(jobApplicationSchema));
    if (!form.valid) {
      return fail(400, { form });
    }
    const token = cookies.get("auth_token");
    const user = token ? await verifySessionToken(token) : null;
    if (!user) {
      return fail(401, { form, error: "Unauthorized" });
    }
    try {
      const application = await prisma.application.create({
        data: {
          userId: user.id,
          company: form.data.company,
          position: form.data.position,
          location: form.data.location || null,
          appliedDate: new Date(form.data.appliedDate),
          status: form.data.status,
          nextAction: form.data.nextAction || null,
          notes: form.data.notes || null,
          url: form.data.url || null,
          jobType: form.data.jobType,
          resumeUploaded: form.data.resumeUploaded === "Yes"
        }
      });
      return {
        form,
        success: true,
        application
      };
    } catch (error) {
      console.error("Error creating application:", error);
      return fail(500, { form, error: "Failed to create application" });
    }
  }
};

var _page_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  actions: actions,
  load: load
});

const index = 71;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-COF2AHnY.js')).default;
const server_id = "src/routes/dashboard/tracker/+page.server.ts";
const imports = ["_app/immutable/nodes/71.D-yNOslQ.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/BvdI7LR8.js","_app/immutable/chunks/e3H2jrNf.js","_app/immutable/chunks/BSkrKq6e.js","_app/immutable/chunks/FN1sk3P2.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/CrHU05dq.js","_app/immutable/chunks/BosuxZz1.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/DjPYYl4Z.js","_app/immutable/chunks/C8B1VUaq.js","_app/immutable/chunks/DaBofrVv.js","_app/immutable/chunks/ncUU1dSD.js","_app/immutable/chunks/B-Xjo-Yt.js","_app/immutable/chunks/w80wGXGd.js","_app/immutable/chunks/C3w0v0gR.js","_app/immutable/chunks/5V1tIHTN.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/DM07Bv7T.js","_app/immutable/chunks/B1K98fMG.js","_app/immutable/chunks/I7hvcB12.js","_app/immutable/chunks/BfX7a-t9.js","_app/immutable/chunks/CnMg5bH0.js","_app/immutable/chunks/BJIrNhIJ.js","_app/immutable/chunks/DuoUhxYL.js","_app/immutable/chunks/Bd3zs5C6.js","_app/immutable/chunks/OXTnUuEm.js","_app/immutable/chunks/CIOgxH3l.js","_app/immutable/chunks/Bpi49Nrf.js","_app/immutable/chunks/DX6rZLP_.js","_app/immutable/chunks/CGK0g3x_.js","_app/immutable/chunks/D2egQzE8.js","_app/immutable/chunks/Ntteq2n_.js","_app/immutable/chunks/DrQfh6BY.js","_app/immutable/chunks/DxW95yuQ.js","_app/immutable/chunks/D-o7ybA5.js","_app/immutable/chunks/XESq6qWN.js","_app/immutable/chunks/OOsIR5sE.js","_app/immutable/chunks/BaVT73bJ.js","_app/immutable/chunks/DT9WCdWY.js","_app/immutable/chunks/Cb-3cdbh.js","_app/immutable/chunks/BjCTmJLi.js","_app/immutable/chunks/CzsE_FAw.js","_app/immutable/chunks/3WmhYGjL.js","_app/immutable/chunks/CyaAPBlz.js","_app/immutable/chunks/0ykhD7u6.js","_app/immutable/chunks/CQdOabBG.js","_app/immutable/chunks/C6g8ubaU.js","_app/immutable/chunks/DDUgF6Ik.js","_app/immutable/chunks/tdzGgazS.js","_app/immutable/chunks/DMoa_yM9.js","_app/immutable/chunks/CnpHcmx3.js","_app/immutable/chunks/BBa424ah.js","_app/immutable/chunks/D4f2twK-.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/BvvicRXk.js","_app/immutable/chunks/DMTMHyMa.js","_app/immutable/chunks/VNuMAkuB.js","_app/immutable/chunks/Ci8yIwIB.js","_app/immutable/chunks/Cf6rS4LV.js","_app/immutable/chunks/B6TiSgAN.js","_app/immutable/chunks/Dmwghw4a.js","_app/immutable/chunks/BniYvUIG.js","_app/immutable/chunks/DW5gea7N.js","_app/immutable/chunks/B5tu6DNS.js","_app/immutable/chunks/BwkAotBa.js","_app/immutable/chunks/BNEH2jqx.js","_app/immutable/chunks/CKh8VGVX.js","_app/immutable/chunks/BKLOCbjP.js","_app/immutable/chunks/B2lQHLf_.js","_app/immutable/chunks/CVVv9lPb.js","_app/immutable/chunks/DZCYCPd3.js","_app/immutable/chunks/BhzFx1Wy.js","_app/immutable/chunks/DR5zc253.js","_app/immutable/chunks/BYB878do.js","_app/immutable/chunks/DuGukytH.js","_app/immutable/chunks/T7uRAIbG.js","_app/immutable/chunks/C2AK_5VT.js","_app/immutable/chunks/CwgkX8t9.js","_app/immutable/chunks/BHEV2D3b.js","_app/immutable/chunks/C2MdR6K0.js","_app/immutable/chunks/hQ6uUXJy.js","_app/immutable/chunks/WD4kvFhR.js","_app/immutable/chunks/hrXlVaSN.js","_app/immutable/chunks/D871oxnv.js","_app/immutable/chunks/8b74MdfD.js","_app/immutable/chunks/BgDjIxoO.js","_app/immutable/chunks/Dz4exfp3.js","_app/immutable/chunks/Z6UAQTuv.js","_app/immutable/chunks/Cs0qIT7f.js","_app/immutable/chunks/DVGNPJty.js","_app/immutable/chunks/C33xR25f.js","_app/immutable/chunks/BAIxhb6t.js","_app/immutable/chunks/BSHZ37s_.js","_app/immutable/chunks/CKg8MWp_.js","_app/immutable/chunks/-SpbofVw.js","_app/immutable/chunks/CIPPbbaT.js","_app/immutable/chunks/CTn0v-X8.js","_app/immutable/chunks/P6MDDUUJ.js","_app/immutable/chunks/BHzYYMdu.js","_app/immutable/chunks/DumgozFE.js","_app/immutable/chunks/QtAhPN2H.js","_app/immutable/chunks/DW7T7T22.js","_app/immutable/chunks/C88uNE8B.js","_app/immutable/chunks/CDnvByek.js","_app/immutable/chunks/DmZyh-PW.js","_app/immutable/chunks/yW0TxTga.js","_app/immutable/chunks/CrpvsheG.js","_app/immutable/chunks/DdoUfFy4.js","_app/immutable/chunks/tr-scC-m.js","_app/immutable/chunks/G5Oo-PmU.js"];
const stylesheets = ["_app/immutable/assets/Toaster.DKF17Rty.css","_app/immutable/assets/index.CV-KWLNP.css","_app/immutable/assets/scroll-area.bHHIbcsu.css","_app/immutable/assets/71.DMVPnmWB.css"];
const fonts = [];

var _71 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  component: component,
  fonts: fonts,
  imports: imports,
  index: index,
  server: _page_server_ts,
  server_id: server_id,
  stylesheets: stylesheets
});

export { _71 as _, jobApplicationSchema as j };
//# sourceMappingURL=71-BmRvqF3-.js.map
