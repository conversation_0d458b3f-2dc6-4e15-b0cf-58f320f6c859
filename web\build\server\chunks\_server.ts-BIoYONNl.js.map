{"version": 3, "file": "_server.ts-BIoYONNl.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/email/process-queue/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { l as logger } from \"../../../../../chunks/logger.js\";\nimport { g as getRedisClient } from \"../../../../../chunks/redis.js\";\nasync function POST() {\n  try {\n    logger.info(\"Processing email queue\");\n    const redis = await getRedisClient();\n    if (!redis) {\n      return json({ error: \"Redis client not available\" }, { status: 500 });\n    }\n    await redis.publish(\"email:process\", \"process_all\");\n    return json({\n      status: \"success\",\n      message: \"Processing email queue\"\n    });\n  } catch (error) {\n    logger.error(\"Error processing email queue:\", error);\n    return json({ error: \"Failed to process email queue\" }, { status: 500 });\n  }\n}\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;AAGA,eAAe,IAAI,GAAG;AACtB,EAAE,IAAI;AACN,IAAI,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC;AACzC,IAAI,MAAM,KAAK,GAAG,MAAM,cAAc,EAAE;AACxC,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,4BAA4B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3E;AACA,IAAI,MAAM,KAAK,CAAC,OAAO,CAAC,eAAe,EAAE,aAAa,CAAC;AACvD,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,OAAO,EAAE;AACf,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC;AACxD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,+BAA+B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC5E;AACA;;;;"}