{"version": 3, "file": "_server.ts-BBXYcWpa.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/email/analytics/check/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../../chunks/prisma.js\";\nimport { l as logger } from \"../../../../../../chunks/logger.js\";\nasync function GET() {\n  try {\n    const count = await prisma.emailEvent.count();\n    return json({\n      exists: true,\n      count,\n      message: \"EmailEvent table exists and is accessible\"\n    });\n  } catch (error) {\n    logger.error(\"Error checking EmailEvent table:\", error);\n    try {\n      await prisma.$executeRaw`\n        CREATE TABLE IF NOT EXISTS \"web\".\"EmailEvent\" (\n          \"id\" TEXT NOT NULL,\n          \"email\" TEXT NOT NULL,\n          \"type\" TEXT NOT NULL,\n          \"timestamp\" TIMESTAMP(3) NOT NULL,\n          \"templateName\" TEXT,\n          \"category\" TEXT,\n          \"data\" JSONB,\n          \"createdAt\" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,\n          CONSTRAINT \"EmailEvent_pkey\" PRIMARY KEY (\"id\")\n        )\n      `;\n      await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS \"EmailEvent_email_idx\" ON \"web\".\"EmailEvent\"(\"email\")`;\n      await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS \"EmailEvent_type_idx\" ON \"web\".\"EmailEvent\"(\"type\")`;\n      await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS \"EmailEvent_timestamp_idx\" ON \"web\".\"EmailEvent\"(\"timestamp\")`;\n      await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS \"EmailEvent_templateName_idx\" ON \"web\".\"EmailEvent\"(\"templateName\")`;\n      return json({\n        exists: true,\n        count: 0,\n        message: \"EmailEvent table created successfully\"\n      });\n    } catch (createError) {\n      logger.error(\"Error creating EmailEvent table:\", createError);\n      return json(\n        {\n          exists: false,\n          error: createError instanceof Error ? createError.message : \"Unknown error\",\n          message: \"Failed to create EmailEvent table\"\n        },\n        { status: 500 }\n      );\n    }\n  }\n}\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;;AAGA,eAAe,GAAG,GAAG;AACrB,EAAE,IAAI;AACN,IAAI,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE;AACjD,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,MAAM,EAAE,IAAI;AAClB,MAAM,KAAK;AACX,MAAM,OAAO,EAAE;AACf,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC;AAC3D,IAAI,IAAI;AACR,MAAM,MAAM,MAAM,CAAC,WAAW;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,CAAC;AACP,MAAM,MAAM,MAAM,CAAC,WAAW,CAAC,gFAAgF,CAAC;AAChH,MAAM,MAAM,MAAM,CAAC,WAAW,CAAC,8EAA8E,CAAC;AAC9G,MAAM,MAAM,MAAM,CAAC,WAAW,CAAC,wFAAwF,CAAC;AACxH,MAAM,MAAM,MAAM,CAAC,WAAW,CAAC,8FAA8F,CAAC;AAC9H,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,MAAM,EAAE,IAAI;AACpB,QAAQ,KAAK,EAAE,CAAC;AAChB,QAAQ,OAAO,EAAE;AACjB,OAAO,CAAC;AACR,KAAK,CAAC,OAAO,WAAW,EAAE;AAC1B,MAAM,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,WAAW,CAAC;AACnE,MAAM,OAAO,IAAI;AACjB,QAAQ;AACR,UAAU,MAAM,EAAE,KAAK;AACvB,UAAU,KAAK,EAAE,WAAW,YAAY,KAAK,GAAG,WAAW,CAAC,OAAO,GAAG,eAAe;AACrF,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ,EAAE,MAAM,EAAE,GAAG;AACrB,OAAO;AACP;AACA;AACA;;;;"}