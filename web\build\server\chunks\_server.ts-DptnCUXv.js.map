{"version": 3, "file": "_server.ts-DptnCUXv.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/applications/check/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nconst GET = async ({ locals }) => {\n  try {\n    const applicationCount = await prisma.application.count();\n    if (applicationCount > 0) {\n      const applications = await prisma.application.findMany({\n        take: 1,\n        orderBy: {\n          createdAt: \"desc\"\n        }\n      });\n      if (applications.length > 0) {\n        const application = applications[0];\n        return json({\n          exists: true,\n          application: {\n            id: application.id,\n            company: application.company,\n            position: application.position\n          }\n        });\n      }\n    }\n    const users = await prisma.user.findMany({ take: 1 });\n    if (users.length === 0) {\n      return json(\n        {\n          error: \"No users found in the system to create a test application\"\n        },\n        { status: 500 }\n      );\n    }\n    const newApplication = await prisma.application.create({\n      data: {\n        company: \"Test Company\",\n        position: \"Test Position\",\n        location: \"Remote\",\n        appliedDate: /* @__PURE__ */ new Date(),\n        status: \"Applied\",\n        userId: users[0].id\n      }\n    });\n    return json({\n      exists: false,\n      created: true,\n      application: {\n        id: newApplication.id,\n        company: newApplication.company,\n        position: newApplication.position\n      }\n    });\n  } catch (error) {\n    console.error(\"Error checking application:\", error);\n    return json(\n      {\n        error: \"Failed to check application\",\n        details: error instanceof Error ? error.message : String(error)\n      },\n      { status: 500 }\n    );\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;AAEK,MAAC,GAAG,GAAG,OAAO,EAAE,MAAM,EAAE,KAAK;AAClC,EAAE,IAAI;AACN,IAAI,MAAM,gBAAgB,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,KAAK,EAAE;AAC7D,IAAI,IAAI,gBAAgB,GAAG,CAAC,EAAE;AAC9B,MAAM,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;AAC7D,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQ,OAAO,EAAE;AACjB,UAAU,SAAS,EAAE;AACrB;AACA,OAAO,CAAC;AACR,MAAM,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;AACnC,QAAQ,MAAM,WAAW,GAAG,YAAY,CAAC,CAAC,CAAC;AAC3C,QAAQ,OAAO,IAAI,CAAC;AACpB,UAAU,MAAM,EAAE,IAAI;AACtB,UAAU,WAAW,EAAE;AACvB,YAAY,EAAE,EAAE,WAAW,CAAC,EAAE;AAC9B,YAAY,OAAO,EAAE,WAAW,CAAC,OAAO;AACxC,YAAY,QAAQ,EAAE,WAAW,CAAC;AAClC;AACA,SAAS,CAAC;AACV;AACA;AACA,IAAI,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;AACzD,IAAI,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;AAC5B,MAAM,OAAO,IAAI;AACjB,QAAQ;AACR,UAAU,KAAK,EAAE;AACjB,SAAS;AACT,QAAQ,EAAE,MAAM,EAAE,GAAG;AACrB,OAAO;AACP;AACA,IAAI,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;AAC3D,MAAM,IAAI,EAAE;AACZ,QAAQ,OAAO,EAAE,cAAc;AAC/B,QAAQ,QAAQ,EAAE,eAAe;AACjC,QAAQ,QAAQ,EAAE,QAAQ;AAC1B,QAAQ,WAAW,kBAAkB,IAAI,IAAI,EAAE;AAC/C,QAAQ,MAAM,EAAE,SAAS;AACzB,QAAQ,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;AACzB;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,WAAW,EAAE;AACnB,QAAQ,EAAE,EAAE,cAAc,CAAC,EAAE;AAC7B,QAAQ,OAAO,EAAE,cAAc,CAAC,OAAO;AACvC,QAAQ,QAAQ,EAAE,cAAc,CAAC;AACjC;AACA,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC;AACvD,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,KAAK,EAAE,6BAA6B;AAC5C,QAAQ,OAAO,EAAE,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC,KAAK;AACtE,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;;;;"}