{"version": 3, "file": "_server.ts-CCAOh3cQ.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/locations/resolve/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nconst POST = async ({ request }) => {\n  try {\n    const { ids } = await request.json();\n    if (!Array.isArray(ids) || ids.length === 0) {\n      return json([]);\n    }\n    const cities = await prisma.city.findMany({\n      where: {\n        id: {\n          in: ids\n        }\n      },\n      include: {\n        state: {\n          include: {\n            country: true\n          }\n        }\n      }\n    });\n    return json(cities);\n  } catch (error) {\n    console.error(\"Error resolving city IDs:\", error);\n    return json({ error: \"Failed to resolve city IDs\" }, { status: 500 });\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;AAEK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK;AACpC,EAAE,IAAI;AACN,IAAI,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACxC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;AACjD,MAAM,OAAO,IAAI,CAAC,EAAE,CAAC;AACrB;AACA,IAAI,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC9C,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE;AACZ,UAAU,EAAE,EAAE;AACd;AACA,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,KAAK,EAAE;AACf,UAAU,OAAO,EAAE;AACnB,YAAY,OAAO,EAAE;AACrB;AACA;AACA;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC;AACvB,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC;AACrD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,4BAA4B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE;AACA;;;;"}