{"version": 3, "file": "_server.ts-r2kB-OsJ.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/resume/optimize/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { v as verifySessionToken } from \"../../../../../chunks/auth.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { R as RedisConnection } from \"../../../../../chunks/redis.js\";\nasync function POST({ request, cookies, locals }) {\n  const token = cookies.get(\"auth_token\");\n  if (!token) return new Response(\"Unauthorized\", { status: 401 });\n  const user = await verifySessionToken(token);\n  if (!user) return new Response(\"Unauthorized\", { status: 401 });\n  const { resumeId } = await request.json();\n  if (!resumeId) return new Response(\"Missing fields\", { status: 400 });\n  const resume = await prisma.resume.findFirst({\n    where: {\n      id: resumeId,\n      profile: {\n        OR: [{ userId: user.id }, { team: { members: { some: { userId: user.id } } } }]\n      }\n    }\n  });\n  if (!resume) return new Response(\"Resume not found\", { status: 404 });\n  const job = {\n    resumeId: resume.id,\n    userId: user.id,\n    profileId: resume.profileId,\n    fileUrl: resume.fileUrl\n  };\n  await RedisConnection.xadd(\"optimize-resume\", \"*\", \"job\", JSON.stringify(job));\n  return json({ success: true });\n}\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA,eAAe,IAAI,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE;AAClD,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE,EAAE,MAAM,IAAI,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AAC9C,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AAC3C,EAAE,IAAI,CAAC,QAAQ,EAAE,OAAO,IAAI,QAAQ,CAAC,gBAAgB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACvE,EAAE,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC;AAC/C,IAAI,KAAK,EAAE;AACX,MAAM,EAAE,EAAE,QAAQ;AAClB,MAAM,OAAO,EAAE;AACf,QAAQ,EAAE,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE;AACtF;AACA;AACA,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,IAAI,QAAQ,CAAC,kBAAkB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACvE,EAAE,MAAM,GAAG,GAAG;AACd,IAAI,QAAQ,EAAE,MAAM,CAAC,EAAE;AACvB,IAAI,MAAM,EAAE,IAAI,CAAC,EAAE;AACnB,IAAI,SAAS,EAAE,MAAM,CAAC,SAAS;AAC/B,IAAI,OAAO,EAAE,MAAM,CAAC;AACpB,GAAG;AACH,EAAE,MAAM,eAAe,CAAC,IAAI,CAAC,iBAAiB,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AAChF,EAAE,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;AAChC;;;;"}