{"version": 3, "file": "_page.svelte-DG6yyfpU.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/resumes/_id_/_page.svelte.js"], "sourcesContent": ["import { V as escape_html, N as bind_props, y as pop, w as push } from \"../../../../../chunks/index3.js\";\nimport { B as <PERSON><PERSON> } from \"../../../../../chunks/button.js\";\nimport { C as Card } from \"../../../../../chunks/card.js\";\nimport { C as Card_content } from \"../../../../../chunks/card-content.js\";\nimport { C as Card_header } from \"../../../../../chunks/card-header.js\";\nimport { C as Card_title } from \"../../../../../chunks/card-title.js\";\nimport { g as goto } from \"../../../../../chunks/client.js\";\nimport { R as Root, T as Tabs_list, a as Tabs_content } from \"../../../../../chunks/index9.js\";\nimport { T as Tabs_trigger } from \"../../../../../chunks/tabs-trigger.js\";\nfunction _page($$payload, $$props) {\n  push();\n  let data = $$props[\"data\"];\n  let activeTab = \"details\";\n  $$payload.out += `<h1 class=\"mb-4 text-2xl font-semibold\">${escape_html(data.resume.name)}</h1> <div class=\"text-muted-foreground mb-6 text-sm\">Dashboard > Resume > ${escape_html(data.resume.name)}</div> `;\n  Root($$payload, {\n    value: activeTab,\n    onValueChange: (value) => activeTab = value,\n    class: \"w-full\",\n    children: ($$payload2) => {\n      Tabs_list($$payload2, {\n        children: ($$payload3) => {\n          Tabs_trigger($$payload3, {\n            value: \"details\",\n            children: ($$payload4) => {\n              $$payload4.out += `<!---->Resume Details`;\n            },\n            $$slots: { default: true }\n          });\n          $$payload3.out += `<!----> `;\n          Tabs_trigger($$payload3, {\n            value: \"parsed\",\n            children: ($$payload4) => {\n              $$payload4.out += `<!---->Parsed Data`;\n            },\n            $$slots: { default: true }\n          });\n          $$payload3.out += `<!----> `;\n          Tabs_trigger($$payload3, {\n            value: \"view\",\n            children: ($$payload4) => {\n              $$payload4.out += `<!---->View Document`;\n            },\n            $$slots: { default: true }\n          });\n          $$payload3.out += `<!---->`;\n        },\n        $$slots: { default: true }\n      });\n      $$payload2.out += `<!----> `;\n      Tabs_content($$payload2, {\n        value: \"details\",\n        class: \"mt-4\",\n        children: ($$payload3) => {\n          Card($$payload3, {\n            class: \"mb-6\",\n            children: ($$payload4) => {\n              Card_header($$payload4, {\n                children: ($$payload5) => {\n                  Card_title($$payload5, {\n                    children: ($$payload6) => {\n                      $$payload6.out += `<!---->Resume Details`;\n                    },\n                    $$slots: { default: true }\n                  });\n                },\n                $$slots: { default: true }\n              });\n              $$payload4.out += `<!----> `;\n              Card_content($$payload4, {\n                children: ($$payload5) => {\n                  $$payload5.out += `<p class=\"text-muted-foreground mb-1 text-sm\">Uploaded: ${escape_html(new Date(data.resume.createdAt).toLocaleDateString())}</p> <p class=\"mb-1 text-sm\">Profile: ${escape_html(data.resume.profile.name)}</p> <p class=\"mb-4 text-sm\">Filename: ${escape_html(data.resume.fileName)}</p> <div class=\"flex gap-2\">`;\n                  Button($$payload5, {\n                    onclick: () => goto(`/dashboard/resume/${data.resume.id}/optimize`),\n                    children: ($$payload6) => {\n                      $$payload6.out += `<!---->Optimize Resume`;\n                    },\n                    $$slots: { default: true }\n                  });\n                  $$payload5.out += `<!----> `;\n                  Button($$payload5, {\n                    variant: \"outline\",\n                    onclick: () => activeTab = \"parsed\",\n                    children: ($$payload6) => {\n                      $$payload6.out += `<!---->View Parsed Data`;\n                    },\n                    $$slots: { default: true }\n                  });\n                  $$payload5.out += `<!----> `;\n                  Button($$payload5, {\n                    variant: \"outline\",\n                    onclick: () => activeTab = \"view\",\n                    children: ($$payload6) => {\n                      $$payload6.out += `<!---->View Document`;\n                    },\n                    $$slots: { default: true }\n                  });\n                  $$payload5.out += `<!----></div>`;\n                },\n                $$slots: { default: true }\n              });\n              $$payload4.out += `<!---->`;\n            },\n            $$slots: { default: true }\n          });\n        },\n        $$slots: { default: true }\n      });\n      $$payload2.out += `<!----> `;\n      Tabs_content($$payload2, {\n        value: \"view\",\n        class: \"mt-4\",\n        children: ($$payload3) => {\n          Card($$payload3, {\n            children: ($$payload4) => {\n              Card_header($$payload4, {\n                children: ($$payload5) => {\n                  Card_title($$payload5, {\n                    children: ($$payload6) => {\n                      $$payload6.out += `<!---->Document Viewer`;\n                    },\n                    $$slots: { default: true }\n                  });\n                },\n                $$slots: { default: true }\n              });\n              $$payload4.out += `<!----> `;\n              Card_content($$payload4, {\n                children: ($$payload5) => {\n                  {\n                    $$payload5.out += \"<!--[!-->\";\n                    $$payload5.out += `<p class=\"text-muted-foreground\">Document not available for viewing</p>`;\n                  }\n                  $$payload5.out += `<!--]-->`;\n                },\n                $$slots: { default: true }\n              });\n              $$payload4.out += `<!---->`;\n            },\n            $$slots: { default: true }\n          });\n        },\n        $$slots: { default: true }\n      });\n      $$payload2.out += `<!---->`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!---->`;\n  bind_props($$props, { data });\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AASA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC;AAC5B,EAAE,IAAI,SAAS,GAAG,SAAS;AAC3B,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,wCAAwC,EAAE,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,2EAA2E,EAAE,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;AAC/M,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,aAAa,EAAE,CAAC,KAAK,KAAK,SAAS,GAAG,KAAK;AAC/C,IAAI,KAAK,EAAE,QAAQ;AACnB,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,SAAS,CAAC,UAAU,EAAE;AAC5B,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,YAAY,CAAC,UAAU,EAAE;AACnC,YAAY,KAAK,EAAE,SAAS;AAC5B,YAAY,QAAQ,EAAE,CAAC,UAAU,KAAK;AACtC,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC;AACvD,aAAa;AACb,YAAY,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACpC,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACtC,UAAU,YAAY,CAAC,UAAU,EAAE;AACnC,YAAY,KAAK,EAAE,QAAQ;AAC3B,YAAY,QAAQ,EAAE,CAAC,UAAU,KAAK;AACtC,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;AACpD,aAAa;AACb,YAAY,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACpC,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACtC,UAAU,YAAY,CAAC,UAAU,EAAE;AACnC,YAAY,KAAK,EAAE,MAAM;AACzB,YAAY,QAAQ,EAAE,CAAC,UAAU,KAAK;AACtC,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC;AACtD,aAAa;AACb,YAAY,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACpC,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACrC,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC,OAAO,CAAC;AACR,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAClC,MAAM,YAAY,CAAC,UAAU,EAAE;AAC/B,QAAQ,KAAK,EAAE,SAAS;AACxB,QAAQ,KAAK,EAAE,MAAM;AACrB,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,IAAI,CAAC,UAAU,EAAE;AAC3B,YAAY,KAAK,EAAE,MAAM;AACzB,YAAY,QAAQ,EAAE,CAAC,UAAU,KAAK;AACtC,cAAc,WAAW,CAAC,UAAU,EAAE;AACtC,gBAAgB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC1C,kBAAkB,UAAU,CAAC,UAAU,EAAE;AACzC,oBAAoB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9C,sBAAsB,UAAU,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC;AAC/D,qBAAqB;AACrB,oBAAoB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5C,mBAAmB,CAAC;AACpB,iBAAiB;AACjB,gBAAgB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACxC,eAAe,CAAC;AAChB,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC1C,cAAc,YAAY,CAAC,UAAU,EAAE;AACvC,gBAAgB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC1C,kBAAkB,UAAU,CAAC,GAAG,IAAI,CAAC,wDAAwD,EAAE,WAAW,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,kBAAkB,EAAE,CAAC,CAAC,sCAAsC,EAAE,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,uCAAuC,EAAE,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,6BAA6B,CAAC;AACxV,kBAAkB,MAAM,CAAC,UAAU,EAAE;AACrC,oBAAoB,OAAO,EAAE,MAAM,IAAI,CAAC,CAAC,kBAAkB,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;AACvF,oBAAoB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9C,sBAAsB,UAAU,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC;AAChE,qBAAqB;AACrB,oBAAoB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5C,mBAAmB,CAAC;AACpB,kBAAkB,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC9C,kBAAkB,MAAM,CAAC,UAAU,EAAE;AACrC,oBAAoB,OAAO,EAAE,SAAS;AACtC,oBAAoB,OAAO,EAAE,MAAM,SAAS,GAAG,QAAQ;AACvD,oBAAoB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9C,sBAAsB,UAAU,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC;AACjE,qBAAqB;AACrB,oBAAoB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5C,mBAAmB,CAAC;AACpB,kBAAkB,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC9C,kBAAkB,MAAM,CAAC,UAAU,EAAE;AACrC,oBAAoB,OAAO,EAAE,SAAS;AACtC,oBAAoB,OAAO,EAAE,MAAM,SAAS,GAAG,MAAM;AACrD,oBAAoB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9C,sBAAsB,UAAU,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC;AAC9D,qBAAqB;AACrB,oBAAoB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5C,mBAAmB,CAAC;AACpB,kBAAkB,UAAU,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACnD,iBAAiB;AACjB,gBAAgB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACxC,eAAe,CAAC;AAChB,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACzC,aAAa;AACb,YAAY,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACpC,WAAW,CAAC;AACZ,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC,OAAO,CAAC;AACR,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAClC,MAAM,YAAY,CAAC,UAAU,EAAE;AAC/B,QAAQ,KAAK,EAAE,MAAM;AACrB,QAAQ,KAAK,EAAE,MAAM;AACrB,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,IAAI,CAAC,UAAU,EAAE;AAC3B,YAAY,QAAQ,EAAE,CAAC,UAAU,KAAK;AACtC,cAAc,WAAW,CAAC,UAAU,EAAE;AACtC,gBAAgB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC1C,kBAAkB,UAAU,CAAC,UAAU,EAAE;AACzC,oBAAoB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9C,sBAAsB,UAAU,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC;AAChE,qBAAqB;AACrB,oBAAoB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5C,mBAAmB,CAAC;AACpB,iBAAiB;AACjB,gBAAgB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACxC,eAAe,CAAC;AAChB,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC1C,cAAc,YAAY,CAAC,UAAU,EAAE;AACvC,gBAAgB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC1C,kBAAkB;AAClB,oBAAoB,UAAU,CAAC,GAAG,IAAI,WAAW;AACjD,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,uEAAuE,CAAC;AAC/G;AACA,kBAAkB,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC9C,iBAAiB;AACjB,gBAAgB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACxC,eAAe,CAAC;AAChB,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACzC,aAAa;AACb,YAAY,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACpC,WAAW,CAAC;AACZ,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC,OAAO,CAAC;AACR,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACjC,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC5B,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,CAAC;AAC/B,EAAE,GAAG,EAAE;AACP;;;;"}