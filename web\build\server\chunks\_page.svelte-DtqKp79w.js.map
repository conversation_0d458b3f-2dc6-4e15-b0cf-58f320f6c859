{"version": 3, "file": "_page.svelte-DtqKp79w.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/settings/admin/email/_page.svelte.js"], "sourcesContent": ["import { U as ensure_array_like, V as escape_html, y as pop, w as push } from \"../../../../../../chunks/index3.js\";\nimport { R as Root, T as Tabs_list, a as Tabs_content } from \"../../../../../../chunks/index9.js\";\nimport { S as SEO } from \"../../../../../../chunks/SEO.js\";\nimport _page$2 from \"./audiences/_page.svelte.js\";\nimport _page$3 from \"./broadcast/_page.svelte.js\";\nimport _page$1 from \"./analytics/_page.svelte.js\";\nimport { T as Tabs_trigger } from \"../../../../../../chunks/tabs-trigger.js\";\nfunction _page($$payload, $$props) {\n  push();\n  let activeTab = \"analytics\";\n  const tabs = [\n    {\n      id: \"analytics\",\n      label: \"Analytics\",\n      component: _page$1\n    },\n    {\n      id: \"audiences\",\n      label: \"Audiences\",\n      component: _page$2\n    },\n    {\n      id: \"broadcast\",\n      label: \"Broadcast\",\n      component: _page$3\n    }\n  ];\n  SEO($$payload, { title: \"Email Management - Hirli\" });\n  $$payload.out += `<!----> <div class=\"border-border flex items-center justify-between border-b px-4 py-2\"><h2 class=\"text-lg font-semibold\">Email Settings</h2></div> <!---->`;\n  Root($$payload, {\n    value: activeTab,\n    onValueChange: (value) => {\n      activeTab = value;\n    },\n    children: ($$payload2) => {\n      const each_array_1 = ensure_array_like(tabs);\n      $$payload2.out += `<div class=\"border-border border-b p-0\"><!---->`;\n      Tabs_list($$payload2, {\n        class: \"flex flex-row divide-x\",\n        children: ($$payload3) => {\n          const each_array = ensure_array_like(tabs);\n          $$payload3.out += `<!--[-->`;\n          for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n            let tab = each_array[$$index];\n            $$payload3.out += `<!---->`;\n            Tabs_trigger($$payload3, {\n              value: tab.id,\n              class: \"no-border flex items-center rounded-none p-2\",\n              children: ($$payload4) => {\n                $$payload4.out += `<!---->${escape_html(tab.label)}`;\n              },\n              $$slots: { default: true }\n            });\n            $$payload3.out += `<!---->`;\n          }\n          $$payload3.out += `<!--]-->`;\n        },\n        $$slots: { default: true }\n      });\n      $$payload2.out += `<!----></div> <!--[-->`;\n      for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {\n        let tab = each_array_1[$$index_1];\n        $$payload2.out += `<!---->`;\n        Tabs_content($$payload2, {\n          value: tab.id,\n          class: \"space-y-4\",\n          children: ($$payload3) => {\n            if (tab.component) {\n              $$payload3.out += \"<!--[-->\";\n              $$payload3.out += `<!---->`;\n              tab.component($$payload3, {});\n              $$payload3.out += `<!---->`;\n            } else {\n              $$payload3.out += \"<!--[!-->\";\n            }\n            $$payload3.out += `<!--]-->`;\n          },\n          $$slots: { default: true }\n        });\n        $$payload2.out += `<!---->`;\n      }\n      $$payload2.out += `<!--]-->`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!---->`;\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,SAAS,GAAG,WAAW;AAC7B,EAAE,MAAM,IAAI,GAAG;AACf,IAAI;AACJ,MAAM,EAAE,EAAE,WAAW;AACrB,MAAM,KAAK,EAAE,WAAW;AACxB,MAAM,SAAS,EAAE;AACjB,KAAK;AACL,IAAI;AACJ,MAAM,EAAE,EAAE,WAAW;AACrB,MAAM,KAAK,EAAE,WAAW;AACxB,MAAM,SAAS,EAAE;AACjB,KAAK;AACL,IAAI;AACJ,MAAM,EAAE,EAAE,WAAW;AACrB,MAAM,KAAK,EAAE,WAAW;AACxB,MAAM,SAAS,EAAE;AACjB;AACA,GAAG;AACH,EAAE,GAAG,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,0BAA0B,EAAE,CAAC;AACvD,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,2JAA2J,CAAC;AAChL,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,aAAa,EAAE,CAAC,KAAK,KAAK;AAC9B,MAAM,SAAS,GAAG,KAAK;AACvB,KAAK;AACL,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,MAAM,YAAY,GAAG,iBAAiB,CAAC,IAAI,CAAC;AAClD,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,+CAA+C,CAAC;AACzE,MAAM,SAAS,CAAC,UAAU,EAAE;AAC5B,QAAQ,KAAK,EAAE,wBAAwB;AACvC,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,MAAM,UAAU,GAAG,iBAAiB,CAAC,IAAI,CAAC;AACpD,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACtC,UAAU,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AAC7F,YAAY,IAAI,GAAG,GAAG,UAAU,CAAC,OAAO,CAAC;AACzC,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACvC,YAAY,YAAY,CAAC,UAAU,EAAE;AACrC,cAAc,KAAK,EAAE,GAAG,CAAC,EAAE;AAC3B,cAAc,KAAK,EAAE,8CAA8C;AACnE,cAAc,QAAQ,EAAE,CAAC,UAAU,KAAK;AACxC,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;AACpE,eAAe;AACf,cAAc,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACtC,aAAa,CAAC;AACd,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACvC;AACA,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACtC,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC,OAAO,CAAC;AACR,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC;AAChD,MAAM,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,QAAQ,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,QAAQ,EAAE,SAAS,EAAE,EAAE;AACjG,QAAQ,IAAI,GAAG,GAAG,YAAY,CAAC,SAAS,CAAC;AACzC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,QAAQ,YAAY,CAAC,UAAU,EAAE;AACjC,UAAU,KAAK,EAAE,GAAG,CAAC,EAAE;AACvB,UAAU,KAAK,EAAE,WAAW;AAC5B,UAAU,QAAQ,EAAE,CAAC,UAAU,KAAK;AACpC,YAAY,IAAI,GAAG,CAAC,SAAS,EAAE;AAC/B,cAAc,UAAU,CAAC,GAAG,IAAI,UAAU;AAC1C,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACzC,cAAc,GAAG,CAAC,SAAS,CAAC,UAAU,EAAE,EAAE,CAAC;AAC3C,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACzC,aAAa,MAAM;AACnB,cAAc,UAAU,CAAC,GAAG,IAAI,WAAW;AAC3C;AACA,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACxC,WAAW;AACX,UAAU,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAClC,SAAS,CAAC;AACV,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC;AACA,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAClC,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC5B,EAAE,GAAG,EAAE;AACP;;;;"}