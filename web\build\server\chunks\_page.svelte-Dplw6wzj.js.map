{"version": 3, "file": "_page.svelte-Dplw6wzj.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/press/_page.svelte.js"], "sourcesContent": ["import { V as escape_html, U as ensure_array_like, R as attr, N as bind_props, y as pop, w as push } from \"../../../chunks/index3.js\";\nimport { S as SEO } from \"../../../chunks/SEO.js\";\nimport { B as Button } from \"../../../chunks/button.js\";\nimport { P as PortableText } from \"../../../chunks/PortableText.js\";\nimport { C as Calendar } from \"../../../chunks/calendar.js\";\nimport { M as Map_pin } from \"../../../chunks/map-pin.js\";\nimport { F as File_text } from \"../../../chunks/file-text.js\";\nimport { E as External_link } from \"../../../chunks/external-link.js\";\nfunction _page($$payload, $$props) {\n  push();\n  let data = $$props[\"data\"];\n  const { pressPage, pressReleases } = data;\n  function formatDate(dateStr) {\n    const date = new Date(dateStr);\n    return date.toLocaleDateString(\"en-US\", {\n      year: \"numeric\",\n      month: \"long\",\n      day: \"numeric\"\n    });\n  }\n  SEO($$payload, {\n    title: pressPage?.seo?.metaTitle || \"Press & Media | Hirli\",\n    description: pressPage?.seo?.metaDescription || \"Hirli press releases, media resources, and company information for journalists and media professionals.\",\n    keywords: pressPage?.seo?.keywords?.join(\", \") || \"Hirli press, media kit, press releases, company news, media resources, brand assets\"\n  });\n  $$payload.out += `<!----> <div><div class=\"mb-16\"><div class=\"border-border bg-card text-card-foreground mb-12 rounded-lg border p-8 shadow-sm\">`;\n  if (pressPage?.content) {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<div class=\"text-muted-foreground mb-6 text-lg\">`;\n    PortableText($$payload, { value: pressPage.content });\n    $$payload.out += `<!----></div>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<p class=\"text-muted-foreground mb-6 text-lg\">Hirli is an AI-powered job application platform that automates the job search and\n          application process. Our mission is to help job seekers find and apply to relevant\n          opportunities more efficiently, saving them time and increasing their chances of landing\n          the right job.</p> <p class=\"text-muted-foreground mb-6 text-lg\">Founded in 2022, Hirli has helped over 100,000 job seekers apply to millions of jobs\n          across various industries. Our platform uses advanced AI to match candidates with suitable\n          positions, automatically fill out applications, and track progress—all with a single\n          click.</p>`;\n  }\n  $$payload.out += `<!--]--> `;\n  if (pressPage?.companyInfo) {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<div class=\"mb-6 mt-6\"><h3 class=\"mb-3 text-xl font-semibold\">Company Information</h3> <div class=\"grid grid-cols-1 gap-4 md:grid-cols-2\">`;\n    if (pressPage.companyInfo.foundedYear) {\n      $$payload.out += \"<!--[-->\";\n      $$payload.out += `<div class=\"flex items-center gap-2\">`;\n      Calendar($$payload, { class: \"text-muted-foreground h-5 w-5\" });\n      $$payload.out += `<!----> <span>Founded: ${escape_html(pressPage.companyInfo.foundedYear)}</span></div>`;\n    } else {\n      $$payload.out += \"<!--[!-->\";\n    }\n    $$payload.out += `<!--]--> `;\n    if (pressPage.companyInfo.headquarters) {\n      $$payload.out += \"<!--[-->\";\n      $$payload.out += `<div class=\"flex items-center gap-2\">`;\n      Map_pin($$payload, { class: \"text-muted-foreground h-5 w-5\" });\n      $$payload.out += `<!----> <span>Headquarters: ${escape_html(pressPage.companyInfo.headquarters)}</span></div>`;\n    } else {\n      $$payload.out += \"<!--[!-->\";\n    }\n    $$payload.out += `<!--]--></div> `;\n    if (pressPage.companyInfo.boilerplate) {\n      $$payload.out += \"<!--[-->\";\n      $$payload.out += `<div class=\"mt-4\"><h4 class=\"text-muted-foreground mb-2 text-sm font-medium\">Company Boilerplate</h4> <p class=\"text-muted-foreground text-sm\">${escape_html(pressPage.companyInfo.boilerplate)}</p></div>`;\n    } else {\n      $$payload.out += \"<!--[!-->\";\n    }\n    $$payload.out += `<!--]--></div>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--> <div class=\"mt-8 flex flex-wrap gap-4\">`;\n  if (pressReleases && pressReleases.length > 0) {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<a href=\"/press/releases\">`;\n    Button($$payload, {\n      variant: \"outline\",\n      class: \"flex items-center gap-2 border-2\",\n      children: ($$payload2) => {\n        File_text($$payload2, { class: \"h-4 w-4\" });\n        $$payload2.out += `<!----> <span>Press Releases</span>`;\n      },\n      $$slots: { default: true }\n    });\n    $$payload.out += `<!----></a>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--> <a href=\"/press/images\">`;\n  Button($$payload, {\n    variant: \"outline\",\n    class: \"flex items-center gap-2 border-2\",\n    children: ($$payload2) => {\n      File_text($$payload2, { class: \"h-4 w-4\" });\n      $$payload2.out += `<!----> <span>Images</span>`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----></a> <a href=\"/press/media-kit\">`;\n  Button($$payload, {\n    variant: \"outline\",\n    class: \"flex items-center gap-2 border-2\",\n    children: ($$payload2) => {\n      File_text($$payload2, { class: \"h-4 w-4\" });\n      $$payload2.out += `<!----> <span>Media Kit</span>`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----></a> <a href=\"/press/contact\">`;\n  Button($$payload, {\n    variant: \"outline\",\n    class: \"flex items-center gap-2 border-2\",\n    children: ($$payload2) => {\n      File_text($$payload2, { class: \"h-4 w-4\" });\n      $$payload2.out += `<!----> <span>Contact</span>`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----></a></div></div></div> `;\n  if (pressReleases && pressReleases.length > 0) {\n    $$payload.out += \"<!--[-->\";\n    const each_array = ensure_array_like(pressReleases.slice(0, 3));\n    $$payload.out += `<div class=\"mt-16\"><div class=\"mb-8 flex items-center justify-between\"><h2 class=\"text-3xl font-semibold\">Press Releases</h2> <a href=\"/press/releases\" class=\"text-primary inline-flex items-center text-sm font-medium hover:underline\">View All `;\n    External_link($$payload, { class: \"ml-1 h-3 w-3\" });\n    $$payload.out += `<!----></a></div> <div class=\"space-y-6\"><!--[-->`;\n    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n      let release = each_array[$$index];\n      $$payload.out += `<div class=\"border-b pb-6\"><div class=\"flex flex-col\"><div class=\"mb-2 flex items-center gap-2\">`;\n      Calendar($$payload, { class: \"text-muted-foreground h-4 w-4\" });\n      $$payload.out += `<!----> <p class=\"text-muted-foreground text-sm\">${escape_html(formatDate(release.publishedAt))}</p> `;\n      if (release.location) {\n        $$payload.out += \"<!--[-->\";\n        $$payload.out += `<span class=\"text-muted-foreground mx-1\">•</span> `;\n        Map_pin($$payload, { class: \"text-muted-foreground h-4 w-4\" });\n        $$payload.out += `<!----> <p class=\"text-muted-foreground text-sm\">${escape_html(release.location)}</p>`;\n      } else {\n        $$payload.out += \"<!--[!-->\";\n      }\n      $$payload.out += `<!--]--></div> <h3 class=\"mb-2 text-xl font-medium\">${escape_html(release.title)}</h3> `;\n      if (release.subtitle) {\n        $$payload.out += \"<!--[-->\";\n        $$payload.out += `<p class=\"text-muted-foreground mb-2 text-lg\">${escape_html(release.subtitle)}</p>`;\n      } else {\n        $$payload.out += \"<!--[!-->\";\n      }\n      $$payload.out += `<!--]--> `;\n      if (release.excerpt) {\n        $$payload.out += \"<!--[-->\";\n        $$payload.out += `<p class=\"text-muted-foreground mb-4\">${escape_html(release.excerpt)}</p>`;\n      } else {\n        $$payload.out += \"<!--[!-->\";\n      }\n      $$payload.out += `<!--]--> <div class=\"mt-2\"><a${attr(\"href\", `/press/releases/${release.slug.current}`)} class=\"text-primary inline-flex items-center text-sm font-medium hover:underline\">Read More `;\n      External_link($$payload, { class: \"ml-1 h-3 w-3\" });\n      $$payload.out += `<!----></a></div></div></div>`;\n    }\n    $$payload.out += `<!--]--></div></div>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--></div>`;\n  bind_props($$props, { data });\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAQA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC;AAC5B,EAAE,MAAM,EAAE,SAAS,EAAE,aAAa,EAAE,GAAG,IAAI;AAC3C,EAAE,SAAS,UAAU,CAAC,OAAO,EAAE;AAC/B,IAAI,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC;AAClC,IAAI,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE;AAC5C,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,KAAK,EAAE,MAAM;AACnB,MAAM,GAAG,EAAE;AACX,KAAK,CAAC;AACN;AACA,EAAE,GAAG,CAAC,SAAS,EAAE;AACjB,IAAI,KAAK,EAAE,SAAS,EAAE,GAAG,EAAE,SAAS,IAAI,uBAAuB;AAC/D,IAAI,WAAW,EAAE,SAAS,EAAE,GAAG,EAAE,eAAe,IAAI,yGAAyG;AAC7J,IAAI,QAAQ,EAAE,SAAS,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI;AACtD,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,8HAA8H,CAAC;AACnJ,EAAE,IAAI,SAAS,EAAE,OAAO,EAAE;AAC1B,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,gDAAgD,CAAC;AACvE,IAAI,YAAY,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,SAAS,CAAC,OAAO,EAAE,CAAC;AACzD,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACpC,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC;AACtB;AACA;AACA;AACA;AACA;AACA,oBAAoB,CAAC;AACrB;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AAC9B,EAAE,IAAI,SAAS,EAAE,WAAW,EAAE;AAC9B,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,0IAA0I,CAAC;AACjK,IAAI,IAAI,SAAS,CAAC,WAAW,CAAC,WAAW,EAAE;AAC3C,MAAM,SAAS,CAAC,GAAG,IAAI,UAAU;AACjC,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,qCAAqC,CAAC;AAC9D,MAAM,QAAQ,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,+BAA+B,EAAE,CAAC;AACrE,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,uBAAuB,EAAE,WAAW,CAAC,SAAS,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,aAAa,CAAC;AAC9G,KAAK,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,IAAI,WAAW;AAClC;AACA,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AAChC,IAAI,IAAI,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE;AAC5C,MAAM,SAAS,CAAC,GAAG,IAAI,UAAU;AACjC,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,qCAAqC,CAAC;AAC9D,MAAM,OAAO,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,+BAA+B,EAAE,CAAC;AACpE,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,4BAA4B,EAAE,WAAW,CAAC,SAAS,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,aAAa,CAAC;AACpH,KAAK,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,IAAI,WAAW;AAClC;AACA,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;AACtC,IAAI,IAAI,SAAS,CAAC,WAAW,CAAC,WAAW,EAAE;AAC3C,MAAM,SAAS,CAAC,GAAG,IAAI,UAAU;AACjC,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,+IAA+I,EAAE,WAAW,CAAC,SAAS,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,UAAU,CAAC;AACnO,KAAK,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,IAAI,WAAW;AAClC;AACA,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACrC,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,gDAAgD,CAAC;AACrE,EAAE,IAAI,aAAa,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;AACjD,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,0BAA0B,CAAC;AACjD,IAAI,MAAM,CAAC,SAAS,EAAE;AACtB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,KAAK,EAAE,kCAAkC;AAC/C,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,SAAS,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AACnD,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,mCAAmC,CAAC;AAC/D,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B,KAAK,CAAC;AACN,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC;AAClC,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,iCAAiC,CAAC;AACtD,EAAE,MAAM,CAAC,SAAS,EAAE;AACpB,IAAI,OAAO,EAAE,SAAS;AACtB,IAAI,KAAK,EAAE,kCAAkC;AAC7C,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,SAAS,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AACjD,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,2BAA2B,CAAC;AACrD,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,uCAAuC,CAAC;AAC5D,EAAE,MAAM,CAAC,SAAS,EAAE;AACpB,IAAI,OAAO,EAAE,SAAS;AACtB,IAAI,KAAK,EAAE,kCAAkC;AAC7C,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,SAAS,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AACjD,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,8BAA8B,CAAC;AACxD,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,qCAAqC,CAAC;AAC1D,EAAE,MAAM,CAAC,SAAS,EAAE;AACpB,IAAI,OAAO,EAAE,SAAS;AACtB,IAAI,KAAK,EAAE,kCAAkC;AAC7C,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,SAAS,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AACjD,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,4BAA4B,CAAC;AACtD,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,8BAA8B,CAAC;AACnD,EAAE,IAAI,aAAa,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;AACjD,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,MAAM,UAAU,GAAG,iBAAiB,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACnE,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,mPAAmP,CAAC;AAC1Q,IAAI,aAAa,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AACvD,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,iDAAiD,CAAC;AACxE,IAAI,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACvF,MAAM,IAAI,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;AACvC,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,gGAAgG,CAAC;AACzH,MAAM,QAAQ,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,+BAA+B,EAAE,CAAC;AACrE,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,iDAAiD,EAAE,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC;AAC9H,MAAM,IAAI,OAAO,CAAC,QAAQ,EAAE;AAC5B,QAAQ,SAAS,CAAC,GAAG,IAAI,UAAU;AACnC,QAAQ,SAAS,CAAC,GAAG,IAAI,CAAC,kDAAkD,CAAC;AAC7E,QAAQ,OAAO,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,+BAA+B,EAAE,CAAC;AACtE,QAAQ,SAAS,CAAC,GAAG,IAAI,CAAC,iDAAiD,EAAE,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC;AAChH,OAAO,MAAM;AACb,QAAQ,SAAS,CAAC,GAAG,IAAI,WAAW;AACpC;AACA,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,oDAAoD,EAAE,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AAChH,MAAM,IAAI,OAAO,CAAC,QAAQ,EAAE;AAC5B,QAAQ,SAAS,CAAC,GAAG,IAAI,UAAU;AACnC,QAAQ,SAAS,CAAC,GAAG,IAAI,CAAC,8CAA8C,EAAE,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC;AAC7G,OAAO,MAAM;AACb,QAAQ,SAAS,CAAC,GAAG,IAAI,WAAW;AACpC;AACA,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AAClC,MAAM,IAAI,OAAO,CAAC,OAAO,EAAE;AAC3B,QAAQ,SAAS,CAAC,GAAG,IAAI,UAAU;AACnC,QAAQ,SAAS,CAAC,GAAG,IAAI,CAAC,sCAAsC,EAAE,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC;AACpG,OAAO,MAAM;AACb,QAAQ,SAAS,CAAC,GAAG,IAAI,WAAW;AACpC;AACA,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,6BAA6B,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,gBAAgB,EAAE,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,6FAA6F,CAAC;AAC7M,MAAM,aAAa,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AACzD,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,6BAA6B,CAAC;AACtD;AACA,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC;AAC3C,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACnC,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,CAAC;AAC/B,EAAE,GAAG,EAAE;AACP;;;;"}