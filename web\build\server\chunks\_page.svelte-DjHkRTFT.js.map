{"version": 3, "file": "_page.svelte-DjHkRTFT.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/contact/_page.svelte.js"], "sourcesContent": ["import { O as copy_payload, P as assign_payload, N as bind_props, y as pop, w as push, U as ensure_array_like, V as escape_html, R as attr, aa as store_mutate, _ as store_get, ab as maybe_selected, a1 as unsubscribe_stores } from \"../../../chunks/index3.js\";\nimport { o as onDestroy } from \"../../../chunks/index-server.js\";\nimport { a as toast } from \"../../../chunks/Toaster.svelte_svelte_type_style_lang.js\";\nimport { S as SEO } from \"../../../chunks/SEO.js\";\nimport { B as Button } from \"../../../chunks/button.js\";\nimport { I as Input } from \"../../../chunks/input.js\";\nimport { T as Textarea } from \"../../../chunks/textarea.js\";\nimport { L as Label } from \"../../../chunks/label.js\";\nimport { s as superForm } from \"../../../chunks/superForm.js\";\nimport \"ts-deepmerge\";\nimport \"../../../chunks/index.js\";\nimport \"../../../chunks/formData.js\";\nimport { M as Message_square } from \"../../../chunks/message-square.js\";\nimport { M as Mail } from \"../../../chunks/mail.js\";\nimport { C as Circle_help } from \"../../../chunks/circle-help.js\";\nimport { M as Message_circle } from \"../../../chunks/message-circle.js\";\nimport { A as Arrow_right } from \"../../../chunks/arrow-right.js\";\nimport { S as Send } from \"../../../chunks/send.js\";\nconst botpressConfig = {\n  botId: \"94f9362a-6e6e-4d4f-bb1b-7eef90c7655c\",\n  clientId: \"df7939d8-55e5-46c3-991b-f0502408e646\",\n  configuration: {\n    website: {},\n    email: {},\n    phone: {},\n    termsOfService: {},\n    privacyPolicy: {},\n    variant: \"soft\",\n    themeMode: \"light\",\n    fontFamily: \"inter\"\n  }\n};\nfunction initBotpress(selector, forceInit = false) {\n  if (typeof window === \"undefined\") return;\n  if (!forceInit && typeof window.location !== \"undefined\") {\n    const isContactPage = window.location.pathname === \"/contact\";\n    if (!isContactPage) {\n      console.log(\"Not on contact page, skipping Botpress initialization\");\n      return;\n    }\n  }\n  const config = { ...botpressConfig };\n  if (document.getElementById(\"botpress-script\")) {\n    if (window.botpress) {\n      try {\n        window.botpress.init(config);\n        window.dispatchEvent(new CustomEvent(\"botpress-ready\"));\n      } catch (e) {\n        console.log(\"Error reinitializing Botpress:\", e);\n        const script2 = document.getElementById(\"botpress-script\");\n        if (script2) {\n          script2.remove();\n        }\n        window.botpress = void 0;\n        setTimeout(() => initBotpress(selector, forceInit), 100);\n      }\n    }\n    return;\n  }\n  const script = document.createElement(\"script\");\n  script.id = \"botpress-script\";\n  script.src = \"https://cdn.botpress.cloud/webchat/v2.4/inject.js\";\n  script.async = true;\n  document.body.appendChild(script);\n  script.onload = () => {\n    try {\n      window.botpress?.init(config);\n      console.log(\"Botpress initialized successfully\");\n      window.dispatchEvent(new CustomEvent(\"botpress-ready\"));\n    } catch (e) {\n      console.log(\"Error initializing Botpress:\", e);\n    }\n  };\n}\nfunction showChat(forceShow = false) {\n  if (typeof window === \"undefined\") return;\n  if (!forceShow && typeof window.location !== \"undefined\") {\n    const isContactPage = window.location.pathname === \"/contact\";\n    if (!isContactPage) {\n      console.log(\"Not on contact page, cannot show chat\");\n      return;\n    }\n  }\n  if (!window.botpress) {\n    console.log(\"Botpress not loaded yet, initializing...\");\n    initBotpress(void 0, true);\n    setTimeout(() => {\n      if (window.botpress) {\n        try {\n          window.botpress.open();\n          console.log(\"Botpress chat opened after initialization\");\n        } catch (e) {\n          console.log(\"Error opening Botpress chat after initialization:\", e);\n        }\n      }\n    }, 1e3);\n    return;\n  }\n  try {\n    window.botpress.open();\n    console.log(\"Botpress chat opened successfully\");\n  } catch (e) {\n    console.log(\"Error opening Botpress chat:\", e);\n    initBotpress(void 0, true);\n    setTimeout(() => {\n      if (window.botpress) {\n        try {\n          window.botpress.open();\n        } catch (innerError) {\n          console.log(\"Failed to open Botpress chat after retry:\", innerError);\n        }\n      }\n    }, 1e3);\n  }\n}\nfunction cleanupBotpress() {\n  if (typeof window === \"undefined\") return;\n  if (window.botpress) {\n    try {\n      window.botpress.close();\n    } catch (e) {\n      console.log(\"Error closing Botpress chat:\", e);\n    }\n  }\n  const script = document.getElementById(\"botpress-script\");\n  if (script) {\n    script.remove();\n  }\n  const botpressElements = document.querySelectorAll('[id^=\"bp-\"]');\n  botpressElements.forEach((element) => {\n    element.remove();\n  });\n  const iframes = document.querySelectorAll('iframe[src*=\"botpress\"]');\n  iframes.forEach((element) => {\n    element.remove();\n  });\n  if (window.botpress) {\n    try {\n      window.botpress = void 0;\n    } catch (e) {\n      console.log(\"Error resetting Botpress object:\", e);\n    }\n  }\n  console.log(\"Botpress cleanup completed\");\n}\nfunction _page($$payload, $$props) {\n  push();\n  var $$store_subs;\n  let data = $$props[\"data\"];\n  const {\n    form,\n    errors,\n    enhance: superEnhance,\n    submitting\n  } = superForm(data.form, {\n    onResult: ({ result }) => {\n      if (result.type === \"success\") {\n        toast.success(\"Your message has been sent successfully!\");\n      } else {\n        toast.error(\"An error occurred. Please try again.\");\n      }\n    }\n  });\n  const departments = [\n    { value: \"general\", label: \"General Inquiries\" },\n    { value: \"support\", label: \"Technical Support\" },\n    { value: \"sales\", label: \"Sales\" },\n    { value: \"partnerships\", label: \"Partnerships\" },\n    { value: \"careers\", label: \"Careers\" },\n    { value: \"press\", label: \"Press & Media\" },\n    { value: \"legal\", label: \"Legal\" }\n  ];\n  let isChatLoading = false;\n  onDestroy(() => {\n    console.log(\"Contact page unmounted, cleaning up Botpress...\");\n    cleanupBotpress();\n  });\n  const handleOpenChat = () => {\n    console.log(\"Opening chat from contact page...\");\n    isChatLoading = true;\n    showChat(true);\n    setTimeout(\n      () => {\n        isChatLoading = false;\n      },\n      2e3\n    );\n  };\n  const supportOptions = [\n    {\n      icon: Message_square,\n      title: \"Live Chat\",\n      description: \"Chat with our support team in real-time during business hours.\",\n      action: \"Start Chat\",\n      link: \"#\",\n      onClick: handleOpenChat\n    },\n    {\n      icon: Mail,\n      title: \"Email Support\",\n      description: \"Send us an email and we'll respond within 24 hours.\",\n      action: \"Email Us\",\n      link: \"mailto:<EMAIL>\"\n    },\n    {\n      icon: Circle_help,\n      title: \"Help Center\",\n      description: \"Browse our knowledge base for answers to common questions.\",\n      action: \"Visit Help Center\",\n      link: \"/help\"\n    },\n    {\n      icon: Message_circle,\n      title: \"Submit Feedback\",\n      description: \"Share your thoughts and suggestions to help us improve.\",\n      action: \"Give Feedback\",\n      link: \"https://autoapply.featurebase.app/\"\n    }\n  ];\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    const each_array = ensure_array_like(supportOptions);\n    const each_array_1 = ensure_array_like(departments);\n    SEO($$payload2, {\n      title: \"Contact Us | Hirli\",\n      description: \"Get in touch with the Hirli team. We're here to help with any questions or issues you may have about our job application automation platform.\",\n      keywords: \"contact, support, help, customer service, Hirli support\"\n    });\n    $$payload2.out += `<!----> <section class=\"bg-background text-foreground relative grid grid-cols-4\"><div class=\"border-border col-span-1 flex h-full flex-col divide-y border-r p-12\"><!--[-->`;\n    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n      let option = each_array[$$index];\n      $$payload2.out += `<div class=\"border-border flex-1 border p-12\"><div class=\"flex items-start gap-4\"><div class=\"bg-primary/10 text-primary flex h-10 w-10 items-center justify-center rounded-full\"><!---->`;\n      option.icon?.($$payload2, { class: \"h-5 w-5\" });\n      $$payload2.out += `<!----></div> <div><h3 class=\"mb-2 text-xl font-medium\">${escape_html(option.title)}</h3> <p class=\"text-muted-foreground mb-4 text-sm\">${escape_html(option.description)}</p> `;\n      if (option.onClick) {\n        $$payload2.out += \"<!--[-->\";\n        $$payload2.out += `<button class=\"text-primary inline-flex items-center text-sm font-medium hover:underline\"${attr(\"disabled\", isChatLoading, true)}>`;\n        if (isChatLoading && option.title === \"Live Chat\") {\n          $$payload2.out += \"<!--[-->\";\n          $$payload2.out += `<span class=\"flex items-center\">Loading chat <span class=\"ml-2 inline-block h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent\"></span></span>`;\n        } else {\n          $$payload2.out += \"<!--[!-->\";\n          $$payload2.out += `${escape_html(option.action)} `;\n          Arrow_right($$payload2, { class: \"ml-1 h-4 w-4\" });\n          $$payload2.out += `<!---->`;\n        }\n        $$payload2.out += `<!--]--></button>`;\n      } else {\n        $$payload2.out += \"<!--[!-->\";\n        $$payload2.out += `<a${attr(\"href\", option.link)} class=\"text-primary inline-flex items-center text-sm font-medium hover:underline\">${escape_html(option.action)} `;\n        Arrow_right($$payload2, { class: \"ml-1 h-4 w-4\" });\n        $$payload2.out += `<!----></a>`;\n      }\n      $$payload2.out += `<!--]--></div></div></div>`;\n    }\n    $$payload2.out += `<!--]--></div> <div class=\"col-span-3\"><div class=\"border-border flex flex-col gap-4 border-b p-12\"><h1 class=\"text-4xl font-bold\">Contact Us</h1> <p class=\"text-muted-foreground text-lg\">Have questions or need assistance? We're here to help.</p></div> <div class=\"py-18 px-22\"><form method=\"POST\" class=\"space-y-6\"><div class=\"grid gap-6 sm:grid-cols-2\"><div class=\"space-y-2\">`;\n    Label($$payload2, {\n      for: \"name\",\n      children: ($$payload3) => {\n        $$payload3.out += `<!---->Name`;\n      },\n      $$slots: { default: true }\n    });\n    $$payload2.out += `<!----> `;\n    Input($$payload2, {\n      id: \"name\",\n      name: \"name\",\n      placeholder: \"Your name\",\n      \"aria-invalid\": store_get($$store_subs ??= {}, \"$errors\", errors).name ? \"true\" : void 0,\n      get value() {\n        return store_get($$store_subs ??= {}, \"$form\", form).name;\n      },\n      set value($$value) {\n        store_mutate($$store_subs ??= {}, \"$form\", form, store_get($$store_subs ??= {}, \"$form\", form).name = $$value);\n        $$settled = false;\n      }\n    });\n    $$payload2.out += `<!----> `;\n    if (store_get($$store_subs ??= {}, \"$errors\", errors).name) {\n      $$payload2.out += \"<!--[-->\";\n      $$payload2.out += `<p class=\"text-destructive text-sm\">${escape_html(store_get($$store_subs ??= {}, \"$errors\", errors).name)}</p>`;\n    } else {\n      $$payload2.out += \"<!--[!-->\";\n    }\n    $$payload2.out += `<!--]--></div> <div class=\"space-y-2\">`;\n    Label($$payload2, {\n      for: \"email\",\n      children: ($$payload3) => {\n        $$payload3.out += `<!---->Email`;\n      },\n      $$slots: { default: true }\n    });\n    $$payload2.out += `<!----> `;\n    Input($$payload2, {\n      id: \"email\",\n      name: \"email\",\n      type: \"email\",\n      placeholder: \"Your email address\",\n      \"aria-invalid\": store_get($$store_subs ??= {}, \"$errors\", errors).email ? \"true\" : void 0,\n      get value() {\n        return store_get($$store_subs ??= {}, \"$form\", form).email;\n      },\n      set value($$value) {\n        store_mutate($$store_subs ??= {}, \"$form\", form, store_get($$store_subs ??= {}, \"$form\", form).email = $$value);\n        $$settled = false;\n      }\n    });\n    $$payload2.out += `<!----> `;\n    if (store_get($$store_subs ??= {}, \"$errors\", errors).email) {\n      $$payload2.out += \"<!--[-->\";\n      $$payload2.out += `<p class=\"text-destructive text-sm\">${escape_html(store_get($$store_subs ??= {}, \"$errors\", errors).email)}</p>`;\n    } else {\n      $$payload2.out += \"<!--[!-->\";\n    }\n    $$payload2.out += `<!--]--></div></div> <div class=\"space-y-2\">`;\n    Label($$payload2, {\n      for: \"department\",\n      children: ($$payload3) => {\n        $$payload3.out += `<!---->Department`;\n      },\n      $$slots: { default: true }\n    });\n    $$payload2.out += `<!----> <select id=\"department\" name=\"department\" class=\"border-input bg-background ring-offset-background focus-visible:ring-ring w-full rounded-md border px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2\"${attr(\"aria-invalid\", store_get($$store_subs ??= {}, \"$errors\", errors).department ? \"true\" : void 0)}>`;\n    $$payload2.select_value = store_get($$store_subs ??= {}, \"$form\", form).department;\n    $$payload2.out += `<option value=\"\"${maybe_selected($$payload2, \"\")} disabled selected>Select a department</option><!--[-->`;\n    for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {\n      let department = each_array_1[$$index_1];\n      $$payload2.out += `<option${attr(\"value\", department.value)}${maybe_selected($$payload2, department.value)}>${escape_html(department.label)}</option>`;\n    }\n    $$payload2.out += `<!--]-->`;\n    $$payload2.select_value = void 0;\n    $$payload2.out += `</select> `;\n    if (store_get($$store_subs ??= {}, \"$errors\", errors).department) {\n      $$payload2.out += \"<!--[-->\";\n      $$payload2.out += `<p class=\"text-destructive text-sm\">${escape_html(store_get($$store_subs ??= {}, \"$errors\", errors).department)}</p>`;\n    } else {\n      $$payload2.out += \"<!--[!-->\";\n    }\n    $$payload2.out += `<!--]--></div> <div class=\"space-y-2\">`;\n    Label($$payload2, {\n      for: \"subject\",\n      children: ($$payload3) => {\n        $$payload3.out += `<!---->Subject`;\n      },\n      $$slots: { default: true }\n    });\n    $$payload2.out += `<!----> `;\n    Input($$payload2, {\n      id: \"subject\",\n      name: \"subject\",\n      placeholder: \"What is your message about?\",\n      \"aria-invalid\": store_get($$store_subs ??= {}, \"$errors\", errors).subject ? \"true\" : void 0,\n      get value() {\n        return store_get($$store_subs ??= {}, \"$form\", form).subject;\n      },\n      set value($$value) {\n        store_mutate($$store_subs ??= {}, \"$form\", form, store_get($$store_subs ??= {}, \"$form\", form).subject = $$value);\n        $$settled = false;\n      }\n    });\n    $$payload2.out += `<!----> `;\n    if (store_get($$store_subs ??= {}, \"$errors\", errors).subject) {\n      $$payload2.out += \"<!--[-->\";\n      $$payload2.out += `<p class=\"text-destructive text-sm\">${escape_html(store_get($$store_subs ??= {}, \"$errors\", errors).subject)}</p>`;\n    } else {\n      $$payload2.out += \"<!--[!-->\";\n    }\n    $$payload2.out += `<!--]--></div> <div class=\"space-y-2\">`;\n    Label($$payload2, {\n      for: \"message\",\n      children: ($$payload3) => {\n        $$payload3.out += `<!---->Message`;\n      },\n      $$slots: { default: true }\n    });\n    $$payload2.out += `<!----> `;\n    Textarea($$payload2, {\n      id: \"message\",\n      name: \"message\",\n      placeholder: \"Your message\",\n      rows: 5,\n      \"aria-invalid\": store_get($$store_subs ??= {}, \"$errors\", errors).message ? \"true\" : void 0,\n      get value() {\n        return store_get($$store_subs ??= {}, \"$form\", form).message;\n      },\n      set value($$value) {\n        store_mutate($$store_subs ??= {}, \"$form\", form, store_get($$store_subs ??= {}, \"$form\", form).message = $$value);\n        $$settled = false;\n      }\n    });\n    $$payload2.out += `<!----> `;\n    if (store_get($$store_subs ??= {}, \"$errors\", errors).message) {\n      $$payload2.out += \"<!--[-->\";\n      $$payload2.out += `<p class=\"text-destructive text-sm\">${escape_html(store_get($$store_subs ??= {}, \"$errors\", errors).message)}</p>`;\n    } else {\n      $$payload2.out += \"<!--[!-->\";\n    }\n    $$payload2.out += `<!--]--></div> `;\n    Button($$payload2, {\n      type: \"submit\",\n      class: \"w-full\",\n      variant: \"default\",\n      size: \"lg\",\n      disabled: store_get($$store_subs ??= {}, \"$submitting\", submitting),\n      children: ($$payload3) => {\n        if (store_get($$store_subs ??= {}, \"$submitting\", submitting)) {\n          $$payload3.out += \"<!--[-->\";\n          $$payload3.out += `<span>Sending...</span>`;\n        } else {\n          $$payload3.out += \"<!--[!-->\";\n          $$payload3.out += `<span class=\"flex items-center justify-center\">`;\n          Send($$payload3, { class: \"mr-2 h-4 w-4\" });\n          $$payload3.out += `<!----> Send Message</span>`;\n        }\n        $$payload3.out += `<!--]-->`;\n      },\n      $$slots: { default: true }\n    });\n    $$payload2.out += `<!----></form></div></div></section>`;\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  if ($$store_subs) unsubscribe_stores($$store_subs);\n  bind_props($$props, { data });\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBA,MAAM,cAAc,GAAG;AACvB,EAAE,KAAK,EAAE,sCAAsC;AAC/C,EAAE,QAAQ,EAAE,sCAAsC;AAClD,EAAE,aAAa,EAAE;AACjB,IAAI,OAAO,EAAE,EAAE;AACf,IAAI,KAAK,EAAE,EAAE;AACb,IAAI,KAAK,EAAE,EAAE;AACb,IAAI,cAAc,EAAE,EAAE;AACtB,IAAI,aAAa,EAAE,EAAE;AACrB,IAAI,OAAO,EAAE,MAAM;AACnB,IAAI,SAAS,EAAE,OAAO;AACtB,IAAI,UAAU,EAAE;AAChB;AACA,CAAC;AACD,SAAS,YAAY,CAAC,QAAQ,EAAE,SAAS,GAAG,KAAK,EAAE;AACnD,EAAE,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AACrC,EAAE,IAAI,CAAC,SAAS,IAAI,OAAO,MAAM,CAAC,QAAQ,KAAK,WAAW,EAAE;AAC5D,IAAI,MAAM,aAAa,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,KAAK,UAAU;AACjE,IAAI,IAAI,CAAC,aAAa,EAAE;AACxB,MAAM,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC;AAC1E,MAAM;AACN;AACA;AACA,EAAE,MAAM,MAAM,GAAG,EAAE,GAAG,cAAc,EAAE;AACtC,EAAE,IAAI,QAAQ,CAAC,cAAc,CAAC,iBAAiB,CAAC,EAAE;AAClD,IAAI,IAAI,MAAM,CAAC,QAAQ,EAAE;AACzB,MAAM,IAAI;AACV,QAAQ,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;AACpC,QAAQ,MAAM,CAAC,aAAa,CAAC,IAAI,WAAW,CAAC,gBAAgB,CAAC,CAAC;AAC/D,OAAO,CAAC,OAAO,CAAC,EAAE;AAClB,QAAQ,OAAO,CAAC,GAAG,CAAC,gCAAgC,EAAE,CAAC,CAAC;AACxD,QAAQ,MAAM,OAAO,GAAG,QAAQ,CAAC,cAAc,CAAC,iBAAiB,CAAC;AAClE,QAAQ,IAAI,OAAO,EAAE;AACrB,UAAU,OAAO,CAAC,MAAM,EAAE;AAC1B;AACA,QAAQ,MAAM,CAAC,QAAQ,GAAG,MAAM;AAChC,QAAQ,UAAU,CAAC,MAAM,YAAY,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAE,GAAG,CAAC;AAChE;AACA;AACA,IAAI;AACJ;AACA,EAAE,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC;AACjD,EAAE,MAAM,CAAC,EAAE,GAAG,iBAAiB;AAC/B,EAAE,MAAM,CAAC,GAAG,GAAG,mDAAmD;AAClE,EAAE,MAAM,CAAC,KAAK,GAAG,IAAI;AACrB,EAAE,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;AACnC,EAAE,MAAM,CAAC,MAAM,GAAG,MAAM;AACxB,IAAI,IAAI;AACR,MAAM,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC;AACnC,MAAM,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC;AACtD,MAAM,MAAM,CAAC,aAAa,CAAC,IAAI,WAAW,CAAC,gBAAgB,CAAC,CAAC;AAC7D,KAAK,CAAC,OAAO,CAAC,EAAE;AAChB,MAAM,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,CAAC,CAAC;AACpD;AACA,GAAG;AACH;AACA,SAAS,QAAQ,CAAC,SAAS,GAAG,KAAK,EAAE;AACrC,EAAE,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AACrC,EAAE,IAAI,CAAC,SAAS,IAAI,OAAO,MAAM,CAAC,QAAQ,KAAK,WAAW,EAAE;AAC5D,IAAI,MAAM,aAAa,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,KAAK,UAAU;AACjE,IAAI,IAAI,CAAC,aAAa,EAAE;AACxB,MAAM,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC;AAC1D,MAAM;AACN;AACA;AACA,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;AACxB,IAAI,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC;AAC3D,IAAI,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC;AAC9B,IAAI,UAAU,CAAC,MAAM;AACrB,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE;AAC3B,QAAQ,IAAI;AACZ,UAAU,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE;AAChC,UAAU,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC;AAClE,SAAS,CAAC,OAAO,CAAC,EAAE;AACpB,UAAU,OAAO,CAAC,GAAG,CAAC,mDAAmD,EAAE,CAAC,CAAC;AAC7E;AACA;AACA,KAAK,EAAE,GAAG,CAAC;AACX,IAAI;AACJ;AACA,EAAE,IAAI;AACN,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE;AAC1B,IAAI,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC;AACpD,GAAG,CAAC,OAAO,CAAC,EAAE;AACd,IAAI,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,CAAC,CAAC;AAClD,IAAI,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC;AAC9B,IAAI,UAAU,CAAC,MAAM;AACrB,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE;AAC3B,QAAQ,IAAI;AACZ,UAAU,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE;AAChC,SAAS,CAAC,OAAO,UAAU,EAAE;AAC7B,UAAU,OAAO,CAAC,GAAG,CAAC,2CAA2C,EAAE,UAAU,CAAC;AAC9E;AACA;AACA,KAAK,EAAE,GAAG,CAAC;AACX;AACA;AACA,SAAS,eAAe,GAAG;AAC3B,EAAE,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AACrC,EAAE,IAAI,MAAM,CAAC,QAAQ,EAAE;AACvB,IAAI,IAAI;AACR,MAAM,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE;AAC7B,KAAK,CAAC,OAAO,CAAC,EAAE;AAChB,MAAM,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,CAAC,CAAC;AACpD;AACA;AACA,EAAE,MAAM,MAAM,GAAG,QAAQ,CAAC,cAAc,CAAC,iBAAiB,CAAC;AAC3D,EAAE,IAAI,MAAM,EAAE;AACd,IAAI,MAAM,CAAC,MAAM,EAAE;AACnB;AACA,EAAE,MAAM,gBAAgB,GAAG,QAAQ,CAAC,gBAAgB,CAAC,aAAa,CAAC;AACnE,EAAE,gBAAgB,CAAC,OAAO,CAAC,CAAC,OAAO,KAAK;AACxC,IAAI,OAAO,CAAC,MAAM,EAAE;AACpB,GAAG,CAAC;AACJ,EAAE,MAAM,OAAO,GAAG,QAAQ,CAAC,gBAAgB,CAAC,yBAAyB,CAAC;AACtE,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO,KAAK;AAC/B,IAAI,OAAO,CAAC,MAAM,EAAE;AACpB,GAAG,CAAC;AACJ,EAAE,IAAI,MAAM,CAAC,QAAQ,EAAE;AACvB,IAAI,IAAI;AACR,MAAM,MAAM,CAAC,QAAQ,GAAG,KAAK,CAAC;AAC9B,KAAK,CAAC,OAAO,CAAC,EAAE;AAChB,MAAM,OAAO,CAAC,GAAG,CAAC,kCAAkC,EAAE,CAAC,CAAC;AACxD;AACA;AACA,EAAE,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC;AAC3C;AACA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,YAAY;AAClB,EAAE,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC;AAC5B,EAAE,MAAM;AACR,IAAI,IAAI;AACR,IAAI,MAAM;AACV,IAAI,OAAO,EAAE,YAAY;AACzB,IAAI;AACJ,GAAG,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE;AAC3B,IAAI,QAAQ,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK;AAC9B,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS,EAAE;AACrC,QAAQ,KAAK,CAAC,OAAO,CAAC,0CAA0C,CAAC;AACjE,OAAO,MAAM;AACb,QAAQ,KAAK,CAAC,KAAK,CAAC,sCAAsC,CAAC;AAC3D;AACA;AACA,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG;AACtB,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,mBAAmB,EAAE;AACpD,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,mBAAmB,EAAE;AACpD,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE;AACtC,IAAI,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,cAAc,EAAE;AACpD,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE;AAC1C,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,eAAe,EAAE;AAC9C,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO;AACpC,GAAG;AACH,EAAE,IAAI,aAAa,GAAG,KAAK;AAC3B,EAAE,SAAS,CAAC,MAAM;AAClB,IAAI,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC;AAClE,IAAI,eAAe,EAAE;AACrB,GAAG,CAAC;AACJ,EAAE,MAAM,cAAc,GAAG,MAAM;AAC/B,IAAI,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC;AACpD,IAAI,aAAa,GAAG,IAAI;AACxB,IAAI,QAAQ,CAAC,IAAI,CAAC;AAClB,IAAI,UAAU;AACd,MAAM,MAAM;AACZ,QAAQ,aAAa,GAAG,KAAK;AAC7B,OAAO;AACP,MAAM;AACN,KAAK;AACL,GAAG;AACH,EAAE,MAAM,cAAc,GAAG;AACzB,IAAI;AACJ,MAAM,IAAI,EAAE,cAAc;AAC1B,MAAM,KAAK,EAAE,WAAW;AACxB,MAAM,WAAW,EAAE,gEAAgE;AACnF,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,IAAI,EAAE,GAAG;AACf,MAAM,OAAO,EAAE;AACf,KAAK;AACL,IAAI;AACJ,MAAM,IAAI,EAAE,IAAI;AAChB,MAAM,KAAK,EAAE,eAAe;AAC5B,MAAM,WAAW,EAAE,qDAAqD;AACxE,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,IAAI,EAAE;AACZ,KAAK;AACL,IAAI;AACJ,MAAM,IAAI,EAAE,WAAW;AACvB,MAAM,KAAK,EAAE,aAAa;AAC1B,MAAM,WAAW,EAAE,4DAA4D;AAC/E,MAAM,MAAM,EAAE,mBAAmB;AACjC,MAAM,IAAI,EAAE;AACZ,KAAK;AACL,IAAI;AACJ,MAAM,IAAI,EAAE,cAAc;AAC1B,MAAM,KAAK,EAAE,iBAAiB;AAC9B,MAAM,WAAW,EAAE,yDAAyD;AAC5E,MAAM,MAAM,EAAE,eAAe;AAC7B,MAAM,IAAI,EAAE;AACZ;AACA,GAAG;AACH,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,MAAM,UAAU,GAAG,iBAAiB,CAAC,cAAc,CAAC;AACxD,IAAI,MAAM,YAAY,GAAG,iBAAiB,CAAC,WAAW,CAAC;AACvD,IAAI,GAAG,CAAC,UAAU,EAAE;AACpB,MAAM,KAAK,EAAE,oBAAoB;AACjC,MAAM,WAAW,EAAE,+IAA+I;AAClK,MAAM,QAAQ,EAAE;AAChB,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,2KAA2K,CAAC;AACnM,IAAI,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACvF,MAAM,IAAI,MAAM,GAAG,UAAU,CAAC,OAAO,CAAC;AACtC,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,yLAAyL,CAAC;AACnN,MAAM,MAAM,CAAC,IAAI,GAAG,UAAU,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AACrD,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,wDAAwD,EAAE,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,oDAAoD,EAAE,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC;AACzM,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE;AAC1B,QAAQ,UAAU,CAAC,GAAG,IAAI,UAAU;AACpC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,yFAAyF,EAAE,IAAI,CAAC,UAAU,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC9J,QAAQ,IAAI,aAAa,IAAI,MAAM,CAAC,KAAK,KAAK,WAAW,EAAE;AAC3D,UAAU,UAAU,CAAC,GAAG,IAAI,UAAU;AACtC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,0KAA0K,CAAC;AACxM,SAAS,MAAM;AACf,UAAU,UAAU,CAAC,GAAG,IAAI,WAAW;AACvC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,EAAE,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC5D,UAAU,WAAW,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AAC5D,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACrC;AACA,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC;AAC7C,OAAO,MAAM;AACb,QAAQ,UAAU,CAAC,GAAG,IAAI,WAAW;AACrC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,mFAAmF,EAAE,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC3K,QAAQ,WAAW,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AAC1D,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC;AACvC;AACA,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,0BAA0B,CAAC;AACpD;AACA,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,0XAA0X,CAAC;AAClZ,IAAI,KAAK,CAAC,UAAU,EAAE;AACtB,MAAM,GAAG,EAAE,MAAM;AACjB,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC;AACvC,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChC,IAAI,KAAK,CAAC,UAAU,EAAE;AACtB,MAAM,EAAE,EAAE,MAAM;AAChB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,WAAW,EAAE,WAAW;AAC9B,MAAM,cAAc,EAAE,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,IAAI,GAAG,MAAM,GAAG,MAAM;AAC9F,MAAM,IAAI,KAAK,GAAG;AAClB,QAAQ,OAAO,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,IAAI;AACjE,OAAO;AACP,MAAM,IAAI,KAAK,CAAC,OAAO,EAAE;AACzB,QAAQ,YAAY,CAAC,YAAY,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,IAAI,GAAG,OAAO,CAAC;AACtH,QAAQ,SAAS,GAAG,KAAK;AACzB;AACA,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChC,IAAI,IAAI,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,IAAI,EAAE;AAChE,MAAM,UAAU,CAAC,GAAG,IAAI,UAAU;AAClC,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,oCAAoC,EAAE,WAAW,CAAC,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AACxI,KAAK,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,IAAI,WAAW;AACnC;AACA,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,sCAAsC,CAAC;AAC9D,IAAI,KAAK,CAAC,UAAU,EAAE;AACtB,MAAM,GAAG,EAAE,OAAO;AAClB,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;AACxC,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChC,IAAI,KAAK,CAAC,UAAU,EAAE;AACtB,MAAM,EAAE,EAAE,OAAO;AACjB,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,WAAW,EAAE,oBAAoB;AACvC,MAAM,cAAc,EAAE,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,KAAK,GAAG,MAAM,GAAG,MAAM;AAC/F,MAAM,IAAI,KAAK,GAAG;AAClB,QAAQ,OAAO,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,KAAK;AAClE,OAAO;AACP,MAAM,IAAI,KAAK,CAAC,OAAO,EAAE;AACzB,QAAQ,YAAY,CAAC,YAAY,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,KAAK,GAAG,OAAO,CAAC;AACvH,QAAQ,SAAS,GAAG,KAAK;AACzB;AACA,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChC,IAAI,IAAI,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,KAAK,EAAE;AACjE,MAAM,UAAU,CAAC,GAAG,IAAI,UAAU;AAClC,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,oCAAoC,EAAE,WAAW,CAAC,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AACzI,KAAK,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,IAAI,WAAW;AACnC;AACA,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,4CAA4C,CAAC;AACpE,IAAI,KAAK,CAAC,UAAU,EAAE;AACtB,MAAM,GAAG,EAAE,YAAY;AACvB,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC;AAC7C,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,0PAA0P,EAAE,IAAI,CAAC,cAAc,EAAE,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,UAAU,GAAG,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAC1X,IAAI,UAAU,CAAC,YAAY,GAAG,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,UAAU;AACtF,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,gBAAgB,EAAE,cAAc,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,uDAAuD,CAAC;AAChI,IAAI,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,QAAQ,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,QAAQ,EAAE,SAAS,EAAE,EAAE;AAC/F,MAAM,IAAI,UAAU,GAAG,YAAY,CAAC,SAAS,CAAC;AAC9C,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,UAAU,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC;AAC5J;AACA,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChC,IAAI,UAAU,CAAC,YAAY,GAAG,MAAM;AACpC,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC;AAClC,IAAI,IAAI,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,UAAU,EAAE;AACtE,MAAM,UAAU,CAAC,GAAG,IAAI,UAAU;AAClC,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,oCAAoC,EAAE,WAAW,CAAC,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;AAC9I,KAAK,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,IAAI,WAAW;AACnC;AACA,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,sCAAsC,CAAC;AAC9D,IAAI,KAAK,CAAC,UAAU,EAAE;AACtB,MAAM,GAAG,EAAE,SAAS;AACpB,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AAC1C,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChC,IAAI,KAAK,CAAC,UAAU,EAAE;AACtB,MAAM,EAAE,EAAE,SAAS;AACnB,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,WAAW,EAAE,6BAA6B;AAChD,MAAM,cAAc,EAAE,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,OAAO,GAAG,MAAM,GAAG,MAAM;AACjG,MAAM,IAAI,KAAK,GAAG;AAClB,QAAQ,OAAO,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,OAAO;AACpE,OAAO;AACP,MAAM,IAAI,KAAK,CAAC,OAAO,EAAE;AACzB,QAAQ,YAAY,CAAC,YAAY,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,OAAO,GAAG,OAAO,CAAC;AACzH,QAAQ,SAAS,GAAG,KAAK;AACzB;AACA,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChC,IAAI,IAAI,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,OAAO,EAAE;AACnE,MAAM,UAAU,CAAC,GAAG,IAAI,UAAU;AAClC,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,oCAAoC,EAAE,WAAW,CAAC,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC;AAC3I,KAAK,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,IAAI,WAAW;AACnC;AACA,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,sCAAsC,CAAC;AAC9D,IAAI,KAAK,CAAC,UAAU,EAAE;AACtB,MAAM,GAAG,EAAE,SAAS;AACpB,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AAC1C,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChC,IAAI,QAAQ,CAAC,UAAU,EAAE;AACzB,MAAM,EAAE,EAAE,SAAS;AACnB,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,cAAc,EAAE,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,OAAO,GAAG,MAAM,GAAG,MAAM;AACjG,MAAM,IAAI,KAAK,GAAG;AAClB,QAAQ,OAAO,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,OAAO;AACpE,OAAO;AACP,MAAM,IAAI,KAAK,CAAC,OAAO,EAAE;AACzB,QAAQ,YAAY,CAAC,YAAY,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,OAAO,GAAG,OAAO,CAAC;AACzH,QAAQ,SAAS,GAAG,KAAK;AACzB;AACA,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChC,IAAI,IAAI,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,OAAO,EAAE;AACnE,MAAM,UAAU,CAAC,GAAG,IAAI,UAAU;AAClC,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,oCAAoC,EAAE,WAAW,CAAC,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC;AAC3I,KAAK,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,IAAI,WAAW;AACnC;AACA,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;AACvC,IAAI,MAAM,CAAC,UAAU,EAAE;AACvB,MAAM,IAAI,EAAE,QAAQ;AACpB,MAAM,KAAK,EAAE,QAAQ;AACrB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,IAAI,EAAE,IAAI;AAChB,MAAM,QAAQ,EAAE,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,aAAa,EAAE,UAAU,CAAC;AACzE,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,IAAI,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,aAAa,EAAE,UAAU,CAAC,EAAE;AACvE,UAAU,UAAU,CAAC,GAAG,IAAI,UAAU;AACtC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC;AACrD,SAAS,MAAM;AACf,UAAU,UAAU,CAAC,GAAG,IAAI,WAAW;AACvC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,+CAA+C,CAAC;AAC7E,UAAU,IAAI,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AACrD,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,2BAA2B,CAAC;AACzD;AACA,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACpC,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,oCAAoC,CAAC;AAC5D;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,IAAI,YAAY,EAAE,kBAAkB,CAAC,YAAY,CAAC;AACpD,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,CAAC;AAC/B,EAAE,GAAG,EAAE;AACP;;;;"}