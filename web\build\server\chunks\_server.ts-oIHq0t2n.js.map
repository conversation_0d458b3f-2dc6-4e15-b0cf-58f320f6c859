{"version": 3, "file": "_server.ts-oIHq0t2n.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/email/broadcasts/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { l as logger } from \"../../../../../chunks/logger.js\";\nimport { Resend } from \"resend\";\nimport { d as private_env } from \"../../../../../chunks/shared-server.js\";\nconst resend = private_env.RESEND_API_KEY ? new Resend(private_env.RESEND_API_KEY) : null;\nasync function GET() {\n  try {\n    logger.info(\"Getting all broadcasts\");\n    if (!resend) {\n      logger.warn(\"Resend API key not configured, returning sample broadcast data\");\n      return json([\n        {\n          id: \"sample-broadcast-1\",\n          subject: \"Welcome to Our Platform\",\n          templateName: \"newsletter\",\n          audienceId: \"sample-audience-1\",\n          status: \"sent\",\n          scheduledAt: null,\n          createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1e3).toISOString()\n        }\n      ]);\n    }\n    return json([\n      {\n        id: \"sample-1\",\n        subject: \"Welcome to Our Platform\",\n        templateName: \"newsletter\",\n        audienceId: \"sample-audience\",\n        status: \"sent\",\n        scheduledAt: null,\n        createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1e3).toISOString()\n      }\n    ]);\n  } catch (error) {\n    logger.error(\"Error getting broadcasts:\", error);\n    return json({ error: \"Failed to get broadcasts\" }, { status: 500 });\n  }\n}\nasync function POST({ request }) {\n  try {\n    const data = await request.json();\n    logger.info(\"Creating broadcast:\", data);\n    if (!data.templateName) {\n      return json({ error: \"Template name is required\" }, { status: 400 });\n    }\n    if (!data.audienceId) {\n      return json({ error: \"Audience ID is required\" }, { status: 400 });\n    }\n    if (!data.subject) {\n      return json({ error: \"Subject is required\" }, { status: 400 });\n    }\n    if (!resend) {\n      logger.warn(\"Resend API key not configured, creating sample broadcast\");\n      return json({\n        id: `sample-broadcast-${Date.now()}`,\n        subject: data.subject,\n        templateName: data.templateName,\n        audienceId: data.audienceId,\n        status: data.scheduledAt ? \"scheduled\" : \"sent\",\n        scheduledAt: data.scheduledAt || null,\n        createdAt: (/* @__PURE__ */ new Date()).toISOString()\n      });\n    }\n    try {\n      const audienceResponse = await resend.contacts.list({ audienceId: data.audienceId });\n      if (audienceResponse.error) {\n        logger.error(\"Error fetching audience contacts:\", audienceResponse.error);\n        return json({ error: \"Failed to fetch audience contacts\" }, { status: 500 });\n      }\n      let contacts = [];\n      if (audienceResponse.data) {\n        if (Array.isArray(audienceResponse.data)) {\n          contacts = audienceResponse.data;\n        } else if (audienceResponse.data.data && Array.isArray(audienceResponse.data.data)) {\n          contacts = audienceResponse.data.data;\n        }\n      }\n      if (contacts.length === 0) {\n        return json({ error: \"Audience has no contacts\" }, { status: 400 });\n      }\n      logger.info(`Sending broadcast to ${contacts.length} contacts`);\n      const broadcastId = Math.random().toString(36).substring(2, 15);\n      const newBroadcast = {\n        id: broadcastId,\n        subject: data.subject,\n        templateName: data.templateName,\n        audienceId: data.audienceId,\n        status: data.scheduledAt ? \"scheduled\" : \"queued\",\n        scheduledAt: data.scheduledAt || null,\n        createdAt: (/* @__PURE__ */ new Date()).toISOString(),\n        contactCount: contacts.length\n      };\n      if (contacts.length > 0 && !data.scheduledAt) {\n        try {\n          const firstContact = contacts[0];\n          await resend.emails.send({\n            from: private_env.RESEND_FROM || \"<EMAIL>\",\n            to: firstContact.email,\n            subject: data.subject,\n            html: `\n              <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n                <h1>${data.title || \"Newsletter\"}</h1>\n                <div>${data.content || \"<p>Newsletter content</p>\"}</div>\n                ${data.imageUrl ? `<img src=\"${data.imageUrl}\" alt=\"Newsletter Image\" style=\"max-width: 100%;\" />` : \"\"}\n                ${data.ctaText && data.ctaUrl ? `<p style=\"text-align: center; margin-top: 20px;\"><a href=\"${data.ctaUrl}\" style=\"display: inline-block; padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 4px;\">${data.ctaText}</a></p>` : \"\"}\n                <hr />\n                <p style=\"font-size: 12px; color: #666;\">This is a broadcast email from ${private_env.RESEND_FROM || \"<EMAIL>\"}</p>\n                <p style=\"font-size: 12px; color: #666;\">To unsubscribe, please click <a href=\"#\">here</a>.</p>\n              </div>\n            `,\n            tags: [\n              { name: \"template\", value: data.templateName || \"broadcast\" },\n              { name: \"category\", value: \"marketing\" },\n              { name: \"broadcast_id\", value: broadcastId },\n              { name: \"audience_id\", value: data.audienceId }\n            ]\n          });\n          logger.info(`Test email sent to ${firstContact.email}`);\n        } catch (emailError) {\n          logger.error(\"Error sending test email:\", emailError);\n        }\n      }\n      return json(newBroadcast);\n    } catch (resendError) {\n      logger.error(\"Error with Resend API:\", resendError);\n      return json({ error: \"Failed to process broadcast with Resend API\" }, { status: 500 });\n    }\n  } catch (error) {\n    logger.error(\"Error creating broadcast:\", error);\n    return json({ error: \"Failed to create broadcast\" }, { status: 500 });\n  }\n}\nexport {\n  GET,\n  POST\n};\n"], "names": [], "mappings": ";;;;;AAIA,MAAM,MAAM,GAAG,WAAW,CAAC,cAAc,GAAG,IAAI,MAAM,CAAC,WAAW,CAAC,cAAc,CAAC,GAAG,IAAI;AACzF,eAAe,GAAG,GAAG;AACrB,EAAE,IAAI;AACN,IAAI,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC;AACzC,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,MAAM,CAAC,IAAI,CAAC,gEAAgE,CAAC;AACnF,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ;AACR,UAAU,EAAE,EAAE,oBAAoB;AAClC,UAAU,OAAO,EAAE,yBAAyB;AAC5C,UAAU,YAAY,EAAE,YAAY;AACpC,UAAU,UAAU,EAAE,mBAAmB;AACzC,UAAU,MAAM,EAAE,MAAM;AACxB,UAAU,WAAW,EAAE,IAAI;AAC3B,UAAU,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,WAAW;AAC9E;AACA,OAAO,CAAC;AACR;AACA,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM;AACN,QAAQ,EAAE,EAAE,UAAU;AACtB,QAAQ,OAAO,EAAE,yBAAyB;AAC1C,QAAQ,YAAY,EAAE,YAAY;AAClC,QAAQ,UAAU,EAAE,iBAAiB;AACrC,QAAQ,MAAM,EAAE,MAAM;AACtB,QAAQ,WAAW,EAAE,IAAI;AACzB,QAAQ,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,WAAW;AAC5E;AACA,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC;AACpD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,0BAA0B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACvE;AACA;AACA,eAAe,IAAI,CAAC,EAAE,OAAO,EAAE,EAAE;AACjC,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,MAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC;AAC5C,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AAC5B,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,2BAA2B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1E;AACA,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AAC1B,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACvB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,qBAAqB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpE;AACA,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,MAAM,CAAC,IAAI,CAAC,0DAA0D,CAAC;AAC7E,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,EAAE,EAAE,CAAC,iBAAiB,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;AAC5C,QAAQ,OAAO,EAAE,IAAI,CAAC,OAAO;AAC7B,QAAQ,YAAY,EAAE,IAAI,CAAC,YAAY;AACvC,QAAQ,UAAU,EAAE,IAAI,CAAC,UAAU;AACnC,QAAQ,MAAM,EAAE,IAAI,CAAC,WAAW,GAAG,WAAW,GAAG,MAAM;AACvD,QAAQ,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,IAAI;AAC7C,QAAQ,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC3D,OAAO,CAAC;AACR;AACA,IAAI,IAAI;AACR,MAAM,MAAM,gBAAgB,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC;AAC1F,MAAM,IAAI,gBAAgB,CAAC,KAAK,EAAE;AAClC,QAAQ,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,gBAAgB,CAAC,KAAK,CAAC;AACjF,QAAQ,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mCAAmC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpF;AACA,MAAM,IAAI,QAAQ,GAAG,EAAE;AACvB,MAAM,IAAI,gBAAgB,CAAC,IAAI,EAAE;AACjC,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;AAClD,UAAU,QAAQ,GAAG,gBAAgB,CAAC,IAAI;AAC1C,SAAS,MAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AAC5F,UAAU,QAAQ,GAAG,gBAAgB,CAAC,IAAI,CAAC,IAAI;AAC/C;AACA;AACA,MAAM,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;AACjC,QAAQ,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,0BAA0B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3E;AACA,MAAM,MAAM,CAAC,IAAI,CAAC,CAAC,qBAAqB,EAAE,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AACrE,MAAM,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;AACrE,MAAM,MAAM,YAAY,GAAG;AAC3B,QAAQ,EAAE,EAAE,WAAW;AACvB,QAAQ,OAAO,EAAE,IAAI,CAAC,OAAO;AAC7B,QAAQ,YAAY,EAAE,IAAI,CAAC,YAAY;AACvC,QAAQ,UAAU,EAAE,IAAI,CAAC,UAAU;AACnC,QAAQ,MAAM,EAAE,IAAI,CAAC,WAAW,GAAG,WAAW,GAAG,QAAQ;AACzD,QAAQ,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,IAAI;AAC7C,QAAQ,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW,EAAE;AAC7D,QAAQ,YAAY,EAAE,QAAQ,CAAC;AAC/B,OAAO;AACP,MAAM,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;AACpD,QAAQ,IAAI;AACZ,UAAU,MAAM,YAAY,GAAG,QAAQ,CAAC,CAAC,CAAC;AAC1C,UAAU,MAAM,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;AACnC,YAAY,IAAI,EAAE,WAAW,CAAC,WAAW,IAAI,kBAAkB;AAC/D,YAAY,EAAE,EAAE,YAAY,CAAC,KAAK;AAClC,YAAY,OAAO,EAAE,IAAI,CAAC,OAAO;AACjC,YAAY,IAAI,EAAE;AAClB;AACA,oBAAoB,EAAE,IAAI,CAAC,KAAK,IAAI,YAAY,CAAC;AACjD,qBAAqB,EAAE,IAAI,CAAC,OAAO,IAAI,2BAA2B,CAAC;AACnE,gBAAgB,EAAE,IAAI,CAAC,QAAQ,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,oDAAoD,CAAC,GAAG,EAAE;AACvH,gBAAgB,EAAE,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,0DAA0D,EAAE,IAAI,CAAC,MAAM,CAAC,yIAAyI,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE;AAC/R;AACA,wFAAwF,EAAE,WAAW,CAAC,WAAW,IAAI,kBAAkB,CAAC;AACxI;AACA;AACA,YAAY,CAAC;AACb,YAAY,IAAI,EAAE;AAClB,cAAc,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,IAAI,CAAC,YAAY,IAAI,WAAW,EAAE;AAC3E,cAAc,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,WAAW,EAAE;AACtD,cAAc,EAAE,IAAI,EAAE,cAAc,EAAE,KAAK,EAAE,WAAW,EAAE;AAC1D,cAAc,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,UAAU;AAC3D;AACA,WAAW,CAAC;AACZ,UAAU,MAAM,CAAC,IAAI,CAAC,CAAC,mBAAmB,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;AACjE,SAAS,CAAC,OAAO,UAAU,EAAE;AAC7B,UAAU,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,UAAU,CAAC;AAC/D;AACA;AACA,MAAM,OAAO,IAAI,CAAC,YAAY,CAAC;AAC/B,KAAK,CAAC,OAAO,WAAW,EAAE;AAC1B,MAAM,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,WAAW,CAAC;AACzD,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,6CAA6C,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC5F;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC;AACpD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,4BAA4B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE;AACA;;;;"}