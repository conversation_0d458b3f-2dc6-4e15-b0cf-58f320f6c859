{"version": 3, "file": "_server.ts-BSSNWPVT.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/auth/linkedin/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { a as verifyLinkedInCode } from \"../../../../../chunks/auth-providers.js\";\nconst POST = async ({ request, cookies }) => {\n  try {\n    const { code, redirectUri } = await request.json();\n    if (!code || !redirectUri) {\n      return json(\n        { error: \"Authorization code and redirect URI are required\" },\n        { status: 400 }\n      );\n    }\n    const result = await verifyLinkedInCode(code, redirectUri);\n    if (!result.success) {\n      return json({ error: result.error || \"Authentication failed\" }, { status: 401 });\n    }\n    cookies.set(\"auth_token\", result.sessionToken, {\n      path: \"/\",\n      httpOnly: true,\n      sameSite: \"lax\",\n      secure: process.env.NODE_ENV === \"production\",\n      maxAge: 60 * 60 * 24 * 30\n      // 30 days\n    });\n    return json({\n      success: true,\n      user: result.user\n    });\n  } catch (error) {\n    console.error(\"LinkedIn authentication error:\", error);\n    return json(\n      {\n        error: \"Authentication failed\",\n        message: \"An unexpected error occurred during authentication.\"\n      },\n      { status: 500 }\n    );\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAEK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AAC7C,EAAE,IAAI;AACN,IAAI,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACtD,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE;AAC/B,MAAM,OAAO,IAAI;AACjB,QAAQ,EAAE,KAAK,EAAE,kDAAkD,EAAE;AACrE,QAAQ,EAAE,MAAM,EAAE,GAAG;AACrB,OAAO;AACP;AACA,IAAI,MAAM,MAAM,GAAG,MAAM,kBAAkB,CAAC,IAAI,EAAE,WAAW,CAAC;AAC9D,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;AACzB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,IAAI,uBAAuB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtF;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,MAAM,CAAC,YAAY,EAAE;AACnD,MAAM,IAAI,EAAE,GAAG;AACf,MAAM,QAAQ,EAAE,IAAI;AACpB,MAAM,QAAQ,EAAE,KAAK;AACrB,MAAM,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;AACnD,MAAM,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;AAC7B;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,IAAI,EAAE,MAAM,CAAC;AACnB,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC;AAC1D,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,KAAK,EAAE,uBAAuB;AACtC,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;;;;"}