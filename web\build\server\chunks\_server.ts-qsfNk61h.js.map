{"version": 3, "file": "_server.ts-qsfNk61h.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/notifications/send/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { g as getUserFromToken } from \"../../../../../chunks/auth.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { N as NotificationPriority, a as NotificationType, s as sendGlobalNotification, b as sendJobNotification, c as sendNotificationToUser } from \"../../../../../chunks/notification-service.js\";\nconst POST = async ({ cookies, request }) => {\n  const user = await getUserFromToken(cookies);\n  if (!user) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  try {\n    const data = await request.json();\n    if (!data.title || !data.message) {\n      return json({ error: \"Title and message are required\" }, { status: 400 });\n    }\n    const notificationData = {\n      title: data.title,\n      message: data.message,\n      url: data.url,\n      type: data.type || NotificationType.INFO,\n      priority: data.priority || NotificationPriority.MEDIUM,\n      metadata: data.metadata,\n      expiresAt: data.expiresAt ? new Date(data.expiresAt) : void 0\n    };\n    if (data.global) {\n      const userData = await prisma.user.findUnique({\n        where: { id: user.id },\n        select: { isAdmin: true, role: true }\n      });\n      if (!userData || !userData.isAdmin && userData.role !== \"admin\") {\n        return json({ error: \"Unauthorized to send global notifications\" }, { status: 403 });\n      }\n      const result2 = await sendGlobalNotification(notificationData);\n      if (result2) {\n        return json({\n          success: true,\n          message: \"Global notification sent successfully\"\n        });\n      } else {\n        return json({ error: \"Failed to send global notification\" }, { status: 500 });\n      }\n    }\n    if (data.type === \"job\") {\n      if (data.userId) {\n        const result2 = await sendJobNotification(data.userId, notificationData);\n        if (result2) {\n          return json({\n            success: true,\n            message: \"Job notification sent successfully\"\n          });\n        } else {\n          return json({ error: \"Failed to send job notification\" }, { status: 500 });\n        }\n      }\n    }\n    if (data.userId) {\n      const result2 = await sendNotificationToUser(data.userId, notificationData);\n      if (result2) {\n        return json({\n          success: true,\n          message: \"Notification sent successfully\"\n        });\n      } else {\n        return json({ error: \"Failed to send notification\" }, { status: 500 });\n      }\n    }\n    const result = await sendNotificationToUser(user.id, notificationData);\n    if (result) {\n      return json({\n        success: true,\n        message: \"Notification sent successfully\"\n      });\n    } else {\n      return json({ error: \"Failed to send notification\" }, { status: 500 });\n    }\n  } catch (error) {\n    console.error(\"Error sending notification:\", error);\n    return json({ error: \"Failed to send notification\" }, { status: 500 });\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAIK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AAC7C,EAAE,MAAM,IAAI,GAAG,MAAM,gBAAgB,CAAC,OAAO,CAAC;AAC9C,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACtC,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,gCAAgC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC/E;AACA,IAAI,MAAM,gBAAgB,GAAG;AAC7B,MAAM,KAAK,EAAE,IAAI,CAAC,KAAK;AACvB,MAAM,OAAO,EAAE,IAAI,CAAC,OAAO;AAC3B,MAAM,GAAG,EAAE,IAAI,CAAC,GAAG;AACnB,MAAM,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,gBAAgB,CAAC,IAAI;AAC9C,MAAM,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,oBAAoB,CAAC,MAAM;AAC5D,MAAM,QAAQ,EAAE,IAAI,CAAC,QAAQ;AAC7B,MAAM,SAAS,EAAE,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,KAAK;AAClE,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;AACrB,MAAM,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AACpD,QAAQ,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;AAC9B,QAAQ,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AAC3C,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,IAAI,KAAK,OAAO,EAAE;AACvE,QAAQ,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,2CAA2C,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC5F;AACA,MAAM,MAAM,OAAO,GAAG,MAAM,sBAAsB,CAAC,gBAAgB,CAAC;AACpE,MAAM,IAAI,OAAO,EAAE;AACnB,QAAQ,OAAO,IAAI,CAAC;AACpB,UAAU,OAAO,EAAE,IAAI;AACvB,UAAU,OAAO,EAAE;AACnB,SAAS,CAAC;AACV,OAAO,MAAM;AACb,QAAQ,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oCAAoC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACrF;AACA;AACA,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,KAAK,EAAE;AAC7B,MAAM,IAAI,IAAI,CAAC,MAAM,EAAE;AACvB,QAAQ,MAAM,OAAO,GAAG,MAAM,mBAAmB,CAAC,IAAI,CAAC,MAAM,EAAE,gBAAgB,CAAC;AAChF,QAAQ,IAAI,OAAO,EAAE;AACrB,UAAU,OAAO,IAAI,CAAC;AACtB,YAAY,OAAO,EAAE,IAAI;AACzB,YAAY,OAAO,EAAE;AACrB,WAAW,CAAC;AACZ,SAAS,MAAM;AACf,UAAU,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,iCAAiC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpF;AACA;AACA;AACA,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;AACrB,MAAM,MAAM,OAAO,GAAG,MAAM,sBAAsB,CAAC,IAAI,CAAC,MAAM,EAAE,gBAAgB,CAAC;AACjF,MAAM,IAAI,OAAO,EAAE;AACnB,QAAQ,OAAO,IAAI,CAAC;AACpB,UAAU,OAAO,EAAE,IAAI;AACvB,UAAU,OAAO,EAAE;AACnB,SAAS,CAAC;AACV,OAAO,MAAM;AACb,QAAQ,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,6BAA6B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9E;AACA;AACA,IAAI,MAAM,MAAM,GAAG,MAAM,sBAAsB,CAAC,IAAI,CAAC,EAAE,EAAE,gBAAgB,CAAC;AAC1E,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO,CAAC;AACR,KAAK,MAAM;AACX,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,6BAA6B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC5E;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC;AACvD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,6BAA6B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1E;AACA;;;;"}