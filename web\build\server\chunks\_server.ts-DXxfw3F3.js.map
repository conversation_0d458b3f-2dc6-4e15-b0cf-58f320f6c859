{"version": 3, "file": "_server.ts-DXxfw3F3.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/admin/feature-usage/check/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../../../../chunks/auth.js\";\nimport { featureTablesExist } from \"../../../../../../chunks/feature-usage.js\";\nconst GET = async ({ cookies }) => {\n  const token = cookies.get(\"auth_token\");\n  if (!token) return new Response(\"Unauthorized\", { status: 401 });\n  const userData = await verifySessionToken(token);\n  if (!userData?.id) return new Response(\"Unauthorized\", { status: 401 });\n  const user = await prisma.user.findUnique({ where: { id: userData.id } });\n  if (!user || user.role !== \"admin\") {\n    return new Response(\"Unauthorized\", { status: 401 });\n  }\n  try {\n    const tablesExist = await featureTablesExist();\n    console.log(\"Feature tables exist:\", tablesExist);\n    return json({\n      tablesExist,\n      message: tablesExist ? \"Feature usage tables exist\" : 'Feature usage tables do not exist yet. Use the \"Initialize Feature Data\" button to create them.'\n    });\n  } catch (error) {\n    console.error(\"Error checking feature usage tables:\", error);\n    return json(\n      {\n        tablesExist: false,\n        error: \"Failed to check feature usage tables\"\n      },\n      { status: 500 }\n    );\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAIK,MAAC,GAAG,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK;AACnC,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE,EAAE,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AAClD,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE,EAAE,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE,EAAE,CAAC;AAC3E,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;AACtC,IAAI,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxD;AACA,EAAE,IAAI;AACN,IAAI,MAAM,WAAW,GAAG,MAAM,kBAAkB,EAAE;AAClD,IAAI,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,WAAW,CAAC;AACrD,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,WAAW;AACjB,MAAM,OAAO,EAAE,WAAW,GAAG,4BAA4B,GAAG;AAC5D,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC;AAChE,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,WAAW,EAAE,KAAK;AAC1B,QAAQ,KAAK,EAAE;AACf,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;;;;"}