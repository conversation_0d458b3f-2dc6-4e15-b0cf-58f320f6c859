#!/usr/bin/env tsx
// cron/scripts/verify-r2-objects.ts - Verify R2 objects exist and are accessible

import { getPrismaClient } from "../utils/prismaClient";
import { logger } from "../utils/logger";
import { downloadFile, getBucketName, BucketType } from "../lib/storage/r2Storage";
import dotenv from "dotenv";
import path from "path";

// Load environment variables
dotenv.config({ path: path.join(process.cwd(), ".env") });

async function verifyR2Objects() {
  logger.info("🔍 Verifying R2 objects exist and are accessible");

  const prisma = await getPrismaClient("cron");

  try {
    // Get companies with logo URLs
    const companies = await prisma.company.findMany({
      where: {
        logoUrl: { not: null },
      },
      select: {
        id: true,
        name: true,
        logoUrl: true,
      },
      take: 10, // Test first 10 companies
    });

    logger.info(`📊 Testing ${companies.length} company logos`);

    const bucketName = getBucketName(BucketType.COMPANY);
    logger.info(`🪣 Using bucket: ${bucketName}`);

    let existsCount = 0;
    let missingCount = 0;
    let errorCount = 0;

    for (const company of companies) {
      if (!company.logoUrl) continue;

      try {
        // Extract file key from URL
        const url = new URL(company.logoUrl);
        const fileKey = url.pathname.substring(1); // Remove leading slash

        logger.info(`🔍 Checking: ${company.name} -> ${fileKey}`);

        // Try to download the file to verify it exists
        const result = await downloadFile(fileKey, BucketType.COMPANY);

        if (result.success) {
          logger.info(`✅ EXISTS: ${company.name} (${result.buffer?.length} bytes)`);
          existsCount++;
        } else {
          logger.error(`❌ MISSING: ${company.name} - ${result.error}`);
          missingCount++;
        }

      } catch (error) {
        logger.error(`💥 ERROR checking ${company.name}:`, error);
        errorCount++;
      }
    }

    logger.info(`📊 Verification Results:`);
    logger.info(`   ✅ Exists: ${existsCount}`);
    logger.info(`   ❌ Missing: ${missingCount}`);
    logger.info(`   💥 Errors: ${errorCount}`);

    if (missingCount > 0) {
      logger.warn(`⚠️ ${missingCount} logos are missing from R2!`);
      logger.info(`💡 You may need to re-run the logo processing script`);
    }

    if (existsCount > 0) {
      logger.info(`✅ ${existsCount} logos are properly stored in R2`);
      logger.info(`🔧 The issue is likely the rate-limited pub-*.r2.dev endpoint`);
    }

  } catch (error) {
    logger.error("❌ Error verifying R2 objects:", error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
verifyR2Objects()
  .then(() => {
    logger.info("✅ R2 object verification completed");
    process.exit(0);
  })
  .catch((error) => {
    logger.error("❌ R2 object verification failed:", error);
    process.exit(1);
  });

export { verifyR2Objects };
