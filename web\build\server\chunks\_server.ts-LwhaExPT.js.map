{"version": 3, "file": "_server.ts-LwhaExPT.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/maintenance/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../chunks/prisma.js\";\nimport { l as logger } from \"../../../../chunks/logger.js\";\nimport { s as sendGlobalNotification, N as NotificationPriority, a as NotificationType } from \"../../../../chunks/notification-service.js\";\nasync function notifySystemMaintenance(startTime, endTime, details) {\n  try {\n    const startTimeStr = startTime.toLocaleString(\"en-US\", {\n      weekday: \"long\",\n      year: \"numeric\",\n      month: \"long\",\n      day: \"numeric\",\n      hour: \"numeric\",\n      minute: \"numeric\",\n      timeZoneName: \"short\"\n    });\n    const endTimeStr = endTime.toLocaleString(\"en-US\", {\n      hour: \"numeric\",\n      minute: \"numeric\",\n      timeZoneName: \"short\"\n    });\n    const result = await sendGlobalNotification({\n      title: \"Scheduled Maintenance\",\n      message: `The system will be undergoing maintenance from ${startTimeStr} to ${endTimeStr}. ${details}`,\n      type: NotificationType.SYSTEM,\n      priority: NotificationPriority.HIGH,\n      metadata: {\n        startTime: startTime.toISOString(),\n        endTime: endTime.toISOString(),\n        details\n      }\n    });\n    return result;\n  } catch (error) {\n    logger.error(\"Error sending system maintenance notification:\", error);\n    return false;\n  }\n}\nasync function updateServiceStatus(event, status) {\n  try {\n    const maintenanceStatus = status || event.status;\n    const affectedServices = Array.isArray(event.affectedServices) ? event.affectedServices : typeof event.affectedServices === \"string\" ? event.affectedServices.split(\",\").map((s) => s.trim()) : [];\n    const serviceMapping = {\n      Matches: [\"API\", \"Job Search\", \"Matching\"],\n      Jobs: [\"API\", \"Job Search\", \"Jobs\"],\n      Tracker: [\"API\", \"Application System\", \"Tracker\"],\n      Documents: [\"API\", \"Resume Builder\", \"Documents\"],\n      Automation: [\"Worker\", \"Automation\"],\n      System: [\"Database\", \"System\", \"Core\"],\n      Website: [\"Web\", \"Website\", \"Frontend\"]\n    };\n    const affectedCategories = Object.entries(serviceMapping).filter(\n      ([_, keywords]) => keywords.some(\n        (keyword) => affectedServices.some((service) => service.toLowerCase().includes(keyword.toLowerCase()))\n      )\n    ).map(([category]) => category);\n    if (affectedCategories.length === 0) {\n      return;\n    }\n    const services = await prisma.serviceStatus.findMany({\n      where: {\n        name: {\n          in: affectedCategories\n        }\n      }\n    });\n    for (const service of services) {\n      let newStatus = service.status;\n      if (maintenanceStatus === \"in-progress\") {\n        newStatus = \"maintenance\";\n      } else if (maintenanceStatus === \"completed\") {\n        newStatus = \"operational\";\n      }\n      if (service.status !== newStatus) {\n        await prisma.serviceStatus.update({\n          where: { id: service.id },\n          data: {\n            status: newStatus,\n            lastCheckedAt: /* @__PURE__ */ new Date()\n          }\n        });\n        await prisma.serviceStatusHistory.create({\n          data: {\n            serviceId: service.id,\n            status: newStatus\n          }\n        });\n        logger.info(`Updated status for ${service.name} to ${newStatus} due to maintenance event`);\n      }\n    }\n  } catch (error) {\n    logger.error(\"Error updating service status from maintenance event:\", error);\n  }\n}\nconst GET = async ({ url, locals }) => {\n  try {\n    const user = locals.user;\n    const isAdmin = user?.isAdmin === true;\n    const status = url.searchParams.get(\"status\");\n    const upcoming = url.searchParams.get(\"upcoming\") === \"true\";\n    const past = url.searchParams.get(\"past\") === \"true\";\n    const limit = parseInt(url.searchParams.get(\"limit\") || \"10\");\n    const query = {};\n    if (status) {\n      query.status = status;\n    }\n    if (upcoming) {\n      query.startTime = {\n        gte: /* @__PURE__ */ new Date()\n      };\n    }\n    if (past) {\n      query.endTime = {\n        lt: /* @__PURE__ */ new Date()\n      };\n    }\n    let events = [];\n    try {\n      events = await prisma.maintenanceEvent.findMany({\n        where: query,\n        orderBy: {\n          startTime: upcoming ? \"asc\" : \"desc\"\n        },\n        take: limit\n      });\n    } catch (error) {\n      logger.warn(\"MaintenanceEvent table may not exist yet, using mock data:\", error);\n      if (status === \"in-progress\") {\n        events = [\n          {\n            id: \"mock-in-progress-1\",\n            title: \"Database Maintenance\",\n            description: \"Scheduled database optimization and index rebuilding.\",\n            startTime: new Date(Date.now() - 36e5),\n            // 1 hour ago\n            endTime: new Date(Date.now() + 36e5),\n            // 1 hour from now\n            status: \"in-progress\",\n            severity: \"info\",\n            affectedServices: [\"Database\", \"API\"],\n            createdBy: \"system\",\n            createdAt: new Date(Date.now() - 864e5),\n            // 1 day ago\n            updatedAt: /* @__PURE__ */ new Date(),\n            notifiedAt: new Date(Date.now() - 864e5),\n            completedAt: null\n          }\n        ];\n      } else if (upcoming) {\n        events = [\n          {\n            id: \"mock-upcoming-1\",\n            title: \"System Upgrade\",\n            description: \"Upgrading core system components for improved performance.\",\n            startTime: new Date(Date.now() + 864e5),\n            // 1 day from now\n            endTime: new Date(Date.now() + 9e7),\n            // 25 hours from now\n            status: \"scheduled\",\n            severity: \"info\",\n            affectedServices: [\"Website\", \"API\", \"Job Search\"],\n            createdBy: \"system\",\n            createdAt: new Date(Date.now() - 6048e5),\n            // 1 week ago\n            updatedAt: /* @__PURE__ */ new Date(),\n            notifiedAt: new Date(Date.now() - 6048e5),\n            completedAt: null\n          },\n          {\n            id: \"mock-upcoming-2\",\n            title: \"Security Patch Deployment\",\n            description: \"Applying critical security updates to all services.\",\n            startTime: new Date(Date.now() + 1728e5),\n            // 2 days from now\n            endTime: new Date(Date.now() + 18e7),\n            // 2 days + 2 hours from now\n            status: \"scheduled\",\n            severity: \"warning\",\n            affectedServices: [\"All Services\"],\n            createdBy: \"system\",\n            createdAt: new Date(Date.now() - 2592e5),\n            // 3 days ago\n            updatedAt: /* @__PURE__ */ new Date(),\n            notifiedAt: new Date(Date.now() - 2592e5),\n            completedAt: null\n          }\n        ];\n      } else if (past) {\n        events = [\n          {\n            id: \"mock-past-1\",\n            title: \"Network Infrastructure Update\",\n            description: \"Updated network infrastructure for improved reliability.\",\n            startTime: new Date(Date.now() - 6048e5),\n            // 1 week ago\n            endTime: new Date(Date.now() - 5904e5),\n            // 1 week - 4 hours ago\n            status: \"completed\",\n            severity: \"info\",\n            affectedServices: [\"All Services\"],\n            createdBy: \"system\",\n            createdAt: new Date(Date.now() - 12096e5),\n            // 2 weeks ago\n            updatedAt: new Date(Date.now() - 5904e5),\n            notifiedAt: new Date(Date.now() - 12096e5),\n            completedAt: new Date(Date.now() - 5904e5)\n          },\n          {\n            id: \"mock-past-2\",\n            title: \"Emergency Hotfix\",\n            description: \"Applied emergency fix for critical issue affecting job applications.\",\n            startTime: new Date(Date.now() - 2592e5),\n            // 3 days ago\n            endTime: new Date(Date.now() - 2556e5),\n            // 3 days - 1 hour ago\n            status: \"completed\",\n            severity: \"critical\",\n            affectedServices: [\"Application System\"],\n            createdBy: \"system\",\n            createdAt: new Date(Date.now() - 2628e5),\n            // 3 days + 1 hour ago\n            updatedAt: new Date(Date.now() - 2556e5),\n            notifiedAt: new Date(Date.now() - 2628e5),\n            completedAt: new Date(Date.now() - 2556e5)\n          }\n        ];\n      } else {\n        events = [\n          {\n            id: \"mock-default-1\",\n            title: \"Database Maintenance\",\n            description: \"Scheduled database optimization and index rebuilding.\",\n            startTime: new Date(Date.now() - 36e5),\n            // 1 hour ago\n            endTime: new Date(Date.now() + 36e5),\n            // 1 hour from now\n            status: \"in-progress\",\n            severity: \"info\",\n            affectedServices: [\"Database\", \"API\"],\n            createdBy: \"system\",\n            createdAt: new Date(Date.now() - 864e5),\n            // 1 day ago\n            updatedAt: /* @__PURE__ */ new Date(),\n            notifiedAt: new Date(Date.now() - 864e5),\n            completedAt: null\n          },\n          {\n            id: \"mock-default-2\",\n            title: \"System Upgrade\",\n            description: \"Upgrading core system components for improved performance.\",\n            startTime: new Date(Date.now() + 864e5),\n            // 1 day from now\n            endTime: new Date(Date.now() + 9e7),\n            // 25 hours from now\n            status: \"scheduled\",\n            severity: \"info\",\n            affectedServices: [\"Website\", \"API\", \"Job Search\"],\n            createdBy: \"system\",\n            createdAt: new Date(Date.now() - 6048e5),\n            // 1 week ago\n            updatedAt: /* @__PURE__ */ new Date(),\n            notifiedAt: new Date(Date.now() - 6048e5),\n            completedAt: null\n          }\n        ];\n      }\n    }\n    return json(events);\n  } catch (error) {\n    logger.error(\"Error fetching maintenance events:\", error);\n    return json({ error: \"Failed to fetch maintenance events\" }, { status: 500 });\n  }\n};\nconst POST = async ({ request, locals }) => {\n  try {\n    const user = locals.user;\n    if (!user || !user.isAdmin) {\n      return json({ error: \"Unauthorized\" }, { status: 401 });\n    }\n    const data = await request.json();\n    if (!data.title || !data.description || !data.startTime || !data.endTime || !data.affectedServices) {\n      return json({ error: \"Missing required fields\" }, { status: 400 });\n    }\n    const event = await prisma.maintenanceEvent.create({\n      data: {\n        title: data.title,\n        description: data.description,\n        startTime: new Date(data.startTime),\n        endTime: new Date(data.endTime),\n        status: data.status || \"scheduled\",\n        affectedServices: data.affectedServices,\n        createdBy: user.id,\n        severity: data.severity || \"info\"\n      }\n    });\n    if (data.sendNotification) {\n      await notifySystemMaintenance(\n        new Date(data.startTime),\n        new Date(data.endTime),\n        data.description\n      );\n      await prisma.maintenanceEvent.update({\n        where: { id: event.id },\n        data: { notifiedAt: /* @__PURE__ */ new Date() }\n      });\n    }\n    return json(event, { status: 201 });\n  } catch (error) {\n    logger.error(\"Error creating maintenance event:\", error);\n    return json({ error: \"Failed to create maintenance event\" }, { status: 500 });\n  }\n};\nconst PUT = async ({ request, locals }) => {\n  try {\n    const user = locals.user;\n    if (!user || !user.isAdmin) {\n      return json({ error: \"Unauthorized\" }, { status: 401 });\n    }\n    const data = await request.json();\n    if (!data.id) {\n      return json({ error: \"Missing event ID\" }, { status: 400 });\n    }\n    const existingEvent = await prisma.maintenanceEvent.findUnique({\n      where: { id: data.id }\n    });\n    if (!existingEvent) {\n      return json({ error: \"Maintenance event not found\" }, { status: 404 });\n    }\n    const updateData = {};\n    if (data.title) updateData.title = data.title;\n    if (data.description) updateData.description = data.description;\n    if (data.startTime) updateData.startTime = new Date(data.startTime);\n    if (data.endTime) updateData.endTime = new Date(data.endTime);\n    if (data.status) updateData.status = data.status;\n    if (data.affectedServices) updateData.affectedServices = data.affectedServices;\n    if (data.severity) updateData.severity = data.severity;\n    if (data.status === \"completed\" && existingEvent.status !== \"completed\") {\n      updateData.completedAt = /* @__PURE__ */ new Date();\n    }\n    const historyData = {\n      eventId: data.id,\n      userId: user.id,\n      comment: data.comment || null,\n      metadata: {}\n    };\n    if (data.status && data.status !== existingEvent.status) {\n      historyData.changeType = \"status_change\";\n      historyData.previousStatus = existingEvent.status;\n      historyData.newStatus = data.status;\n      historyData.metadata.statusChange = true;\n    } else if (data.comment) {\n      historyData.changeType = \"comment\";\n    } else {\n      historyData.changeType = \"update\";\n      const changedFields = [];\n      if (data.title && data.title !== existingEvent.title) changedFields.push(\"title\");\n      if (data.description && data.description !== existingEvent.description)\n        changedFields.push(\"description\");\n      if (data.startTime && new Date(data.startTime).toISOString() !== existingEvent.startTime.toISOString())\n        changedFields.push(\"startTime\");\n      if (data.endTime && new Date(data.endTime).toISOString() !== existingEvent.endTime.toISOString())\n        changedFields.push(\"endTime\");\n      if (data.severity && data.severity !== existingEvent.severity) changedFields.push(\"severity\");\n      if (data.affectedServices && JSON.stringify(data.affectedServices) !== JSON.stringify(existingEvent.affectedServices))\n        changedFields.push(\"affectedServices\");\n      historyData.metadata.changedFields = changedFields;\n    }\n    const updatedEvent = await prisma.maintenanceEvent.update({\n      where: { id: data.id },\n      data: updateData\n    });\n    await prisma.maintenanceEventHistory.create({\n      data: historyData\n    });\n    if (data.status && data.status !== existingEvent.status) {\n      await updateServiceStatus(updatedEvent, data.status);\n      logger.info(\n        `Maintenance event ${updatedEvent.id} status changed to ${data.status}, updating affected services`\n      );\n    }\n    if (data.sendNotification) {\n      await notifySystemMaintenance(\n        new Date(updateData.startTime || existingEvent.startTime),\n        new Date(updateData.endTime || existingEvent.endTime),\n        updateData.description || existingEvent.description\n      );\n      await prisma.maintenanceEvent.update({\n        where: { id: updatedEvent.id },\n        data: { notifiedAt: /* @__PURE__ */ new Date() }\n      });\n    }\n    return json(updatedEvent);\n  } catch (error) {\n    logger.error(\"Error updating maintenance event:\", error);\n    return json({ error: \"Failed to update maintenance event\" }, { status: 500 });\n  }\n};\nconst DELETE = async ({ request, locals }) => {\n  try {\n    const user = locals.user;\n    if (!user || !user.isAdmin) {\n      return json({ error: \"Unauthorized\" }, { status: 401 });\n    }\n    const data = await request.json();\n    if (!data.id) {\n      return json({ error: \"Missing event ID\" }, { status: 400 });\n    }\n    await prisma.maintenanceEvent.delete({\n      where: { id: data.id }\n    });\n    return json({ success: true });\n  } catch (error) {\n    logger.error(\"Error deleting maintenance event:\", error);\n    return json({ error: \"Failed to delete maintenance event\" }, { status: 500 });\n  }\n};\nexport {\n  DELETE,\n  GET,\n  POST,\n  PUT\n};\n"], "names": [], "mappings": ";;;;;;;;AAIA,eAAe,uBAAuB,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE;AACpE,EAAE,IAAI;AACN,IAAI,MAAM,YAAY,GAAG,SAAS,CAAC,cAAc,CAAC,OAAO,EAAE;AAC3D,MAAM,OAAO,EAAE,MAAM;AACrB,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,KAAK,EAAE,MAAM;AACnB,MAAM,GAAG,EAAE,SAAS;AACpB,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,YAAY,EAAE;AACpB,KAAK,CAAC;AACN,IAAI,MAAM,UAAU,GAAG,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE;AACvD,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,YAAY,EAAE;AACpB,KAAK,CAAC;AACN,IAAI,MAAM,MAAM,GAAG,MAAM,sBAAsB,CAAC;AAChD,MAAM,KAAK,EAAE,uBAAuB;AACpC,MAAM,OAAO,EAAE,CAAC,+CAA+C,EAAE,YAAY,CAAC,IAAI,EAAE,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;AAC5G,MAAM,IAAI,EAAE,gBAAgB,CAAC,MAAM;AACnC,MAAM,QAAQ,EAAE,oBAAoB,CAAC,IAAI;AACzC,MAAM,QAAQ,EAAE;AAChB,QAAQ,SAAS,EAAE,SAAS,CAAC,WAAW,EAAE;AAC1C,QAAQ,OAAO,EAAE,OAAO,CAAC,WAAW,EAAE;AACtC,QAAQ;AACR;AACA,KAAK,CAAC;AACN,IAAI,OAAO,MAAM;AACjB,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,gDAAgD,EAAE,KAAK,CAAC;AACzE,IAAI,OAAO,KAAK;AAChB;AACA;AACA,eAAe,mBAAmB,CAAC,KAAK,EAAE,MAAM,EAAE;AAClD,EAAE,IAAI;AACN,IAAI,MAAM,iBAAiB,GAAG,MAAM,IAAI,KAAK,CAAC,MAAM;AACpD,IAAI,MAAM,gBAAgB,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,GAAG,KAAK,CAAC,gBAAgB,GAAG,OAAO,KAAK,CAAC,gBAAgB,KAAK,QAAQ,GAAG,KAAK,CAAC,gBAAgB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE;AACtM,IAAI,MAAM,cAAc,GAAG;AAC3B,MAAM,OAAO,EAAE,CAAC,KAAK,EAAE,YAAY,EAAE,UAAU,CAAC;AAChD,MAAM,IAAI,EAAE,CAAC,KAAK,EAAE,YAAY,EAAE,MAAM,CAAC;AACzC,MAAM,OAAO,EAAE,CAAC,KAAK,EAAE,oBAAoB,EAAE,SAAS,CAAC;AACvD,MAAM,SAAS,EAAE,CAAC,KAAK,EAAE,gBAAgB,EAAE,WAAW,CAAC;AACvD,MAAM,UAAU,EAAE,CAAC,QAAQ,EAAE,YAAY,CAAC;AAC1C,MAAM,MAAM,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,MAAM,CAAC;AAC5C,MAAM,OAAO,EAAE,CAAC,KAAK,EAAE,SAAS,EAAE,UAAU;AAC5C,KAAK;AACL,IAAI,MAAM,kBAAkB,GAAG,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,MAAM;AACpE,MAAM,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,KAAK,QAAQ,CAAC,IAAI;AACtC,QAAQ,CAAC,OAAO,KAAK,gBAAgB,CAAC,IAAI,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;AAC7G;AACA,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,QAAQ,CAAC;AACnC,IAAI,IAAI,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE;AACzC,MAAM;AACN;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;AACzD,MAAM,KAAK,EAAE;AACb,QAAQ,IAAI,EAAE;AACd,UAAU,EAAE,EAAE;AACd;AACA;AACA,KAAK,CAAC;AACN,IAAI,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;AACpC,MAAM,IAAI,SAAS,GAAG,OAAO,CAAC,MAAM;AACpC,MAAM,IAAI,iBAAiB,KAAK,aAAa,EAAE;AAC/C,QAAQ,SAAS,GAAG,aAAa;AACjC,OAAO,MAAM,IAAI,iBAAiB,KAAK,WAAW,EAAE;AACpD,QAAQ,SAAS,GAAG,aAAa;AACjC;AACA,MAAM,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE;AACxC,QAAQ,MAAM,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;AAC1C,UAAU,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE;AACnC,UAAU,IAAI,EAAE;AAChB,YAAY,MAAM,EAAE,SAAS;AAC7B,YAAY,aAAa,kBAAkB,IAAI,IAAI;AACnD;AACA,SAAS,CAAC;AACV,QAAQ,MAAM,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC;AACjD,UAAU,IAAI,EAAE;AAChB,YAAY,SAAS,EAAE,OAAO,CAAC,EAAE;AACjC,YAAY,MAAM,EAAE;AACpB;AACA,SAAS,CAAC;AACV,QAAQ,MAAM,CAAC,IAAI,CAAC,CAAC,mBAAmB,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,yBAAyB,CAAC,CAAC;AAClG;AACA;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,uDAAuD,EAAE,KAAK,CAAC;AAChF;AACA;AACK,MAAC,GAAG,GAAG,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK;AACvC,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC5B,IAAI,MAAM,OAAO,GAAG,IAAI,EAAE,OAAO,KAAK,IAAI;AAC1C,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC;AACjD,IAAI,MAAM,QAAQ,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,MAAM;AAChE,IAAI,MAAM,IAAI,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,MAAM;AACxD,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC;AACjE,IAAI,MAAM,KAAK,GAAG,EAAE;AACpB,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,KAAK,CAAC,MAAM,GAAG,MAAM;AAC3B;AACA,IAAI,IAAI,QAAQ,EAAE;AAClB,MAAM,KAAK,CAAC,SAAS,GAAG;AACxB,QAAQ,GAAG,kBAAkB,IAAI,IAAI;AACrC,OAAO;AACP;AACA,IAAI,IAAI,IAAI,EAAE;AACd,MAAM,KAAK,CAAC,OAAO,GAAG;AACtB,QAAQ,EAAE,kBAAkB,IAAI,IAAI;AACpC,OAAO;AACP;AACA,IAAI,IAAI,MAAM,GAAG,EAAE;AACnB,IAAI,IAAI;AACR,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC;AACtD,QAAQ,KAAK,EAAE,KAAK;AACpB,QAAQ,OAAO,EAAE;AACjB,UAAU,SAAS,EAAE,QAAQ,GAAG,KAAK,GAAG;AACxC,SAAS;AACT,QAAQ,IAAI,EAAE;AACd,OAAO,CAAC;AACR,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,MAAM,CAAC,IAAI,CAAC,4DAA4D,EAAE,KAAK,CAAC;AACtF,MAAM,IAAI,MAAM,KAAK,aAAa,EAAE;AACpC,QAAQ,MAAM,GAAG;AACjB,UAAU;AACV,YAAY,EAAE,EAAE,oBAAoB;AACpC,YAAY,KAAK,EAAE,sBAAsB;AACzC,YAAY,WAAW,EAAE,uDAAuD;AAChF,YAAY,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;AAClD;AACA,YAAY,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;AAChD;AACA,YAAY,MAAM,EAAE,aAAa;AACjC,YAAY,QAAQ,EAAE,MAAM;AAC5B,YAAY,gBAAgB,EAAE,CAAC,UAAU,EAAE,KAAK,CAAC;AACjD,YAAY,SAAS,EAAE,QAAQ;AAC/B,YAAY,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC;AACnD;AACA,YAAY,SAAS,kBAAkB,IAAI,IAAI,EAAE;AACjD,YAAY,UAAU,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC;AACpD,YAAY,WAAW,EAAE;AACzB;AACA,SAAS;AACT,OAAO,MAAM,IAAI,QAAQ,EAAE;AAC3B,QAAQ,MAAM,GAAG;AACjB,UAAU;AACV,YAAY,EAAE,EAAE,iBAAiB;AACjC,YAAY,KAAK,EAAE,gBAAgB;AACnC,YAAY,WAAW,EAAE,4DAA4D;AACrF,YAAY,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC;AACnD;AACA,YAAY,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC;AAC/C;AACA,YAAY,MAAM,EAAE,WAAW;AAC/B,YAAY,QAAQ,EAAE,MAAM;AAC5B,YAAY,gBAAgB,EAAE,CAAC,SAAS,EAAE,KAAK,EAAE,YAAY,CAAC;AAC9D,YAAY,SAAS,EAAE,QAAQ;AAC/B,YAAY,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC;AACpD;AACA,YAAY,SAAS,kBAAkB,IAAI,IAAI,EAAE;AACjD,YAAY,UAAU,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC;AACrD,YAAY,WAAW,EAAE;AACzB,WAAW;AACX,UAAU;AACV,YAAY,EAAE,EAAE,iBAAiB;AACjC,YAAY,KAAK,EAAE,2BAA2B;AAC9C,YAAY,WAAW,EAAE,qDAAqD;AAC9E,YAAY,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC;AACpD;AACA,YAAY,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;AAChD;AACA,YAAY,MAAM,EAAE,WAAW;AAC/B,YAAY,QAAQ,EAAE,SAAS;AAC/B,YAAY,gBAAgB,EAAE,CAAC,cAAc,CAAC;AAC9C,YAAY,SAAS,EAAE,QAAQ;AAC/B,YAAY,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC;AACpD;AACA,YAAY,SAAS,kBAAkB,IAAI,IAAI,EAAE;AACjD,YAAY,UAAU,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC;AACrD,YAAY,WAAW,EAAE;AACzB;AACA,SAAS;AACT,OAAO,MAAM,IAAI,IAAI,EAAE;AACvB,QAAQ,MAAM,GAAG;AACjB,UAAU;AACV,YAAY,EAAE,EAAE,aAAa;AAC7B,YAAY,KAAK,EAAE,+BAA+B;AAClD,YAAY,WAAW,EAAE,0DAA0D;AACnF,YAAY,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC;AACpD;AACA,YAAY,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC;AAClD;AACA,YAAY,MAAM,EAAE,WAAW;AAC/B,YAAY,QAAQ,EAAE,MAAM;AAC5B,YAAY,gBAAgB,EAAE,CAAC,cAAc,CAAC;AAC9C,YAAY,SAAS,EAAE,QAAQ;AAC/B,YAAY,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC;AACrD;AACA,YAAY,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC;AACpD,YAAY,UAAU,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC;AACtD,YAAY,WAAW,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM;AACrD,WAAW;AACX,UAAU;AACV,YAAY,EAAE,EAAE,aAAa;AAC7B,YAAY,KAAK,EAAE,kBAAkB;AACrC,YAAY,WAAW,EAAE,sEAAsE;AAC/F,YAAY,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC;AACpD;AACA,YAAY,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC;AAClD;AACA,YAAY,MAAM,EAAE,WAAW;AAC/B,YAAY,QAAQ,EAAE,UAAU;AAChC,YAAY,gBAAgB,EAAE,CAAC,oBAAoB,CAAC;AACpD,YAAY,SAAS,EAAE,QAAQ;AAC/B,YAAY,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC;AACpD;AACA,YAAY,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC;AACpD,YAAY,UAAU,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC;AACrD,YAAY,WAAW,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM;AACrD;AACA,SAAS;AACT,OAAO,MAAM;AACb,QAAQ,MAAM,GAAG;AACjB,UAAU;AACV,YAAY,EAAE,EAAE,gBAAgB;AAChC,YAAY,KAAK,EAAE,sBAAsB;AACzC,YAAY,WAAW,EAAE,uDAAuD;AAChF,YAAY,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;AAClD;AACA,YAAY,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;AAChD;AACA,YAAY,MAAM,EAAE,aAAa;AACjC,YAAY,QAAQ,EAAE,MAAM;AAC5B,YAAY,gBAAgB,EAAE,CAAC,UAAU,EAAE,KAAK,CAAC;AACjD,YAAY,SAAS,EAAE,QAAQ;AAC/B,YAAY,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC;AACnD;AACA,YAAY,SAAS,kBAAkB,IAAI,IAAI,EAAE;AACjD,YAAY,UAAU,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC;AACpD,YAAY,WAAW,EAAE;AACzB,WAAW;AACX,UAAU;AACV,YAAY,EAAE,EAAE,gBAAgB;AAChC,YAAY,KAAK,EAAE,gBAAgB;AACnC,YAAY,WAAW,EAAE,4DAA4D;AACrF,YAAY,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC;AACnD;AACA,YAAY,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC;AAC/C;AACA,YAAY,MAAM,EAAE,WAAW;AAC/B,YAAY,QAAQ,EAAE,MAAM;AAC5B,YAAY,gBAAgB,EAAE,CAAC,SAAS,EAAE,KAAK,EAAE,YAAY,CAAC;AAC9D,YAAY,SAAS,EAAE,QAAQ;AAC/B,YAAY,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC;AACpD;AACA,YAAY,SAAS,kBAAkB,IAAI,IAAI,EAAE;AACjD,YAAY,UAAU,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC;AACrD,YAAY,WAAW,EAAE;AACzB;AACA,SAAS;AACT;AACA;AACA,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC;AACvB,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC;AAC7D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oCAAoC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjF;AACA;AACK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AAC5C,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC5B,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AAChC,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7D;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;AACxG,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA,IAAI,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;AACvD,MAAM,IAAI,EAAE;AACZ,QAAQ,KAAK,EAAE,IAAI,CAAC,KAAK;AACzB,QAAQ,WAAW,EAAE,IAAI,CAAC,WAAW;AACrC,QAAQ,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;AAC3C,QAAQ,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;AACvC,QAAQ,MAAM,EAAE,IAAI,CAAC,MAAM,IAAI,WAAW;AAC1C,QAAQ,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;AAC/C,QAAQ,SAAS,EAAE,IAAI,CAAC,EAAE;AAC1B,QAAQ,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI;AACnC;AACA,KAAK,CAAC;AACN,IAAI,IAAI,IAAI,CAAC,gBAAgB,EAAE;AAC/B,MAAM,MAAM,uBAAuB;AACnC,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;AAChC,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;AAC9B,QAAQ,IAAI,CAAC;AACb,OAAO;AACP,MAAM,MAAM,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;AAC3C,QAAQ,KAAK,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,EAAE,EAAE;AAC/B,QAAQ,IAAI,EAAE,EAAE,UAAU,kBAAkB,IAAI,IAAI,EAAE;AACtD,OAAO,CAAC;AACR;AACA,IAAI,OAAO,IAAI,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACvC,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC;AAC5D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oCAAoC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjF;AACA;AACK,MAAC,GAAG,GAAG,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AAC3C,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC5B,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AAChC,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7D;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;AAClB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,kBAAkB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE;AACA,IAAI,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC;AACnE,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE;AAC1B,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,aAAa,EAAE;AACxB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,6BAA6B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC5E;AACA,IAAI,MAAM,UAAU,GAAG,EAAE;AACzB,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK;AACjD,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW;AACnE,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,UAAU,CAAC,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;AACvE,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,OAAO,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;AACjE,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM;AACpD,IAAI,IAAI,IAAI,CAAC,gBAAgB,EAAE,UAAU,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB;AAClF,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ;AAC1D,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,WAAW,IAAI,aAAa,CAAC,MAAM,KAAK,WAAW,EAAE;AAC7E,MAAM,UAAU,CAAC,WAAW,mBAAmB,IAAI,IAAI,EAAE;AACzD;AACA,IAAI,MAAM,WAAW,GAAG;AACxB,MAAM,OAAO,EAAE,IAAI,CAAC,EAAE;AACtB,MAAM,MAAM,EAAE,IAAI,CAAC,EAAE;AACrB,MAAM,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,IAAI;AACnC,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,KAAK,aAAa,CAAC,MAAM,EAAE;AAC7D,MAAM,WAAW,CAAC,UAAU,GAAG,eAAe;AAC9C,MAAM,WAAW,CAAC,cAAc,GAAG,aAAa,CAAC,MAAM;AACvD,MAAM,WAAW,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM;AACzC,MAAM,WAAW,CAAC,QAAQ,CAAC,YAAY,GAAG,IAAI;AAC9C,KAAK,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;AAC7B,MAAM,WAAW,CAAC,UAAU,GAAG,SAAS;AACxC,KAAK,MAAM;AACX,MAAM,WAAW,CAAC,UAAU,GAAG,QAAQ;AACvC,MAAM,MAAM,aAAa,GAAG,EAAE;AAC9B,MAAM,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,aAAa,CAAC,KAAK,EAAE,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC;AACvF,MAAM,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,KAAK,aAAa,CAAC,WAAW;AAC5E,QAAQ,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC;AACzC,MAAM,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,KAAK,aAAa,CAAC,SAAS,CAAC,WAAW,EAAE;AAC5G,QAAQ,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC;AACvC,MAAM,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,KAAK,aAAa,CAAC,OAAO,CAAC,WAAW,EAAE;AACtG,QAAQ,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC;AACrC,MAAM,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,KAAK,aAAa,CAAC,QAAQ,EAAE,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC;AACnG,MAAM,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,gBAAgB,CAAC;AAC3H,QAAQ,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC;AAC9C,MAAM,WAAW,CAAC,QAAQ,CAAC,aAAa,GAAG,aAAa;AACxD;AACA,IAAI,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;AAC9D,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;AAC5B,MAAM,IAAI,EAAE;AACZ,KAAK,CAAC;AACN,IAAI,MAAM,MAAM,CAAC,uBAAuB,CAAC,MAAM,CAAC;AAChD,MAAM,IAAI,EAAE;AACZ,KAAK,CAAC;AACN,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,KAAK,aAAa,CAAC,MAAM,EAAE;AAC7D,MAAM,MAAM,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC;AAC1D,MAAM,MAAM,CAAC,IAAI;AACjB,QAAQ,CAAC,kBAAkB,EAAE,YAAY,CAAC,EAAE,CAAC,mBAAmB,EAAE,IAAI,CAAC,MAAM,CAAC,4BAA4B;AAC1G,OAAO;AACP;AACA,IAAI,IAAI,IAAI,CAAC,gBAAgB,EAAE;AAC/B,MAAM,MAAM,uBAAuB;AACnC,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,IAAI,aAAa,CAAC,SAAS,CAAC;AACjE,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,aAAa,CAAC,OAAO,CAAC;AAC7D,QAAQ,UAAU,CAAC,WAAW,IAAI,aAAa,CAAC;AAChD,OAAO;AACP,MAAM,MAAM,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;AAC3C,QAAQ,KAAK,EAAE,EAAE,EAAE,EAAE,YAAY,CAAC,EAAE,EAAE;AACtC,QAAQ,IAAI,EAAE,EAAE,UAAU,kBAAkB,IAAI,IAAI,EAAE;AACtD,OAAO,CAAC;AACR;AACA,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC;AAC7B,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC;AAC5D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oCAAoC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjF;AACA;AACK,MAAC,MAAM,GAAG,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AAC9C,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC5B,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AAChC,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7D;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;AAClB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,kBAAkB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE;AACA,IAAI,MAAM,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;AACzC,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE;AAC1B,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;AAClC,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC;AAC5D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oCAAoC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjF;AACA;;;;"}