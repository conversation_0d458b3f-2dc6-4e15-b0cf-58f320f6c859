{"version": 3, "file": "_server.ts-Dn53ipB8.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/referrals/validate/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nconst GET = async ({ url }) => {\n  const referralCode = url.searchParams.get(\"code\");\n  if (!referralCode) {\n    return json({ error: \"Referral code is required\" }, { status: 400 });\n  }\n  try {\n    const referrer = await prisma.user.findUnique({\n      where: { referralCode: referralCode.toUpperCase() },\n      select: {\n        id: true,\n        name: true,\n        email: true,\n        referralCode: true,\n        createdAt: true\n      }\n    });\n    if (!referrer) {\n      return json({\n        valid: false,\n        error: \"Invalid referral code\"\n      }, { status: 404 });\n    }\n    return json({\n      valid: true,\n      referrer: {\n        name: referrer.name,\n        referralCode: referrer.referralCode\n      }\n    });\n  } catch (error) {\n    console.error(\"Error validating referral code:\", error);\n    return json({ error: \"Failed to validate referral code\" }, { status: 500 });\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;AAEK,MAAC,GAAG,GAAG,OAAO,EAAE,GAAG,EAAE,KAAK;AAC/B,EAAE,MAAM,YAAY,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC;AACnD,EAAE,IAAI,CAAC,YAAY,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,2BAA2B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA,EAAE,IAAI;AACN,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAClD,MAAM,KAAK,EAAE,EAAE,YAAY,EAAE,YAAY,CAAC,WAAW,EAAE,EAAE;AACzD,MAAM,MAAM,EAAE;AACd,QAAQ,EAAE,EAAE,IAAI;AAChB,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,KAAK,EAAE,IAAI;AACnB,QAAQ,YAAY,EAAE,IAAI;AAC1B,QAAQ,SAAS,EAAE;AACnB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,KAAK,EAAE,KAAK;AACpB,QAAQ,KAAK,EAAE;AACf,OAAO,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzB;AACA,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,KAAK,EAAE,IAAI;AACjB,MAAM,QAAQ,EAAE;AAChB,QAAQ,IAAI,EAAE,QAAQ,CAAC,IAAI;AAC3B,QAAQ,YAAY,EAAE,QAAQ,CAAC;AAC/B;AACA,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC;AAC3D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,kCAAkC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC/E;AACA;;;;"}