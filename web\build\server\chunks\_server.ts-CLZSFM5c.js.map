{"version": 3, "file": "_server.ts-CLZSFM5c.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/ws/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../chunks/index.js\";\nconst GET = async ({ request }) => {\n  console.log(\"WebSocket status endpoint accessed\");\n  const isDev = process.env.NODE_ENV === \"development\";\n  const wsHost = isDev ? \"localhost:3000\" : request.headers.get(\"host\");\n  const wsProtocol = request.headers.get(\"x-forwarded-proto\") === \"https\" ? \"wss\" : \"ws\";\n  return json({\n    status: \"ok\",\n    message: \"WebSocket server is running\",\n    endpoint: \"/ws\",\n    wsUrl: `${wsProtocol}://${wsHost}/ws`,\n    environment: isDev ? \"development\" : \"production\",\n    instructions: \"Connect to the WebSocket endpoint with a WebSocket client, not this HTTP endpoint\",\n    timestamp: (/* @__PURE__ */ new Date()).toISOString()\n  });\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;AACK,MAAC,GAAG,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK;AACnC,EAAE,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC;AACnD,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa;AACtD,EAAE,MAAM,MAAM,GAAG,KAAK,GAAG,gBAAgB,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC;AACvE,EAAE,MAAM,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,KAAK,OAAO,GAAG,KAAK,GAAG,IAAI;AACxF,EAAE,OAAO,IAAI,CAAC;AACd,IAAI,MAAM,EAAE,IAAI;AAChB,IAAI,OAAO,EAAE,6BAA6B;AAC1C,IAAI,QAAQ,EAAE,KAAK;AACnB,IAAI,KAAK,EAAE,CAAC,EAAE,UAAU,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC;AACzC,IAAI,WAAW,EAAE,KAAK,GAAG,aAAa,GAAG,YAAY;AACrD,IAAI,YAAY,EAAE,mFAAmF;AACrG,IAAI,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AACvD,GAAG,CAAC;AACJ;;;;"}