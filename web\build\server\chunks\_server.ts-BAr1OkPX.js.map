{"version": 3, "file": "_server.ts-BAr1OkPX.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/notifications/settings/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { g as getUserFromToken } from \"../../../../../chunks/auth.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nconst POST = async ({ request, cookies }) => {\n  try {\n    const user = await getUserFromToken(cookies);\n    if (!user) {\n      return json({ error: \"Unauthorized\" }, { status: 401 });\n    }\n    const { pushEnabled } = await request.json();\n    await prisma.notificationSettings.upsert({\n      where: { userId: user.id },\n      update: { pushEnabled },\n      create: {\n        userId: user.id,\n        pushEnabled,\n        emailEnabled: true,\n        browserEnabled: true,\n        jobMatchEnabled: true,\n        applicationStatusEnabled: true,\n        automationEnabled: true\n      }\n    });\n    console.log(`Push notifications ${pushEnabled ? \"enabled\" : \"disabled\"} for user ${user.id}`);\n    return json({ success: true });\n  } catch (error) {\n    console.error(\"Error updating notification settings:\", error);\n    return json({ error: \"Failed to update settings\" }, { status: 500 });\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;AAGK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AAC7C,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,gBAAgB,CAAC,OAAO,CAAC;AAChD,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7D;AACA,IAAI,MAAM,EAAE,WAAW,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AAChD,IAAI,MAAM,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC;AAC7C,MAAM,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;AAChC,MAAM,MAAM,EAAE,EAAE,WAAW,EAAE;AAC7B,MAAM,MAAM,EAAE;AACd,QAAQ,MAAM,EAAE,IAAI,CAAC,EAAE;AACvB,QAAQ,WAAW;AACnB,QAAQ,YAAY,EAAE,IAAI;AAC1B,QAAQ,cAAc,EAAE,IAAI;AAC5B,QAAQ,eAAe,EAAE,IAAI;AAC7B,QAAQ,wBAAwB,EAAE,IAAI;AACtC,QAAQ,iBAAiB,EAAE;AAC3B;AACA,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,mBAAmB,EAAE,WAAW,GAAG,SAAS,GAAG,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AACjG,IAAI,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;AAClC,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC;AACjE,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,2BAA2B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA;;;;"}