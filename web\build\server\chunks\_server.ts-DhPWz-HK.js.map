{"version": 3, "file": "_server.ts-DhPWz-HK.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/admin/feature-usage/export/_server.ts.js"], "sourcesContent": ["import { p as prisma } from \"../../../../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../../../../chunks/auth.js\";\nconst GET = async ({ cookies, url }) => {\n  const token = cookies.get(\"auth_token\");\n  if (!token) return new Response(\"Unauthorized\", { status: 401 });\n  const userData = await verifySessionToken(token);\n  if (!userData?.id) return new Response(\"Unauthorized\", { status: 401 });\n  const user = await prisma.user.findUnique({ where: { id: userData.id } });\n  if (!user || user.role !== \"admin\") {\n    return new Response(\"Unauthorized\", { status: 401 });\n  }\n  try {\n    const featureId = url.searchParams.get(\"featureId\");\n    const limitId = url.searchParams.get(\"limitId\");\n    const period = url.searchParams.get(\"period\");\n    const userId = url.searchParams.get(\"userId\");\n    const planId = url.searchParams.get(\"planId\");\n    const format = url.searchParams.get(\"format\") || \"csv\";\n    const query = {\n      where: {},\n      include: {\n        user: {\n          select: {\n            id: true,\n            email: true,\n            firstName: true,\n            lastName: true,\n            role: true,\n            subscriptions: {\n              orderBy: { createdAt: \"desc\" },\n              take: 1,\n              include: {\n                plan: true\n              }\n            }\n          }\n        },\n        feature: true,\n        limit: true\n      },\n      orderBy: {\n        updatedAt: \"desc\"\n      }\n    };\n    if (featureId) query.where.featureId = featureId;\n    if (limitId) query.where.limitId = limitId;\n    if (period) query.where.period = period;\n    if (userId) query.where.userId = userId;\n    if (planId) {\n      query.where.user = {\n        subscriptions: {\n          some: {\n            planId\n          }\n        }\n      };\n    }\n    const featureUsage = await prisma.featureUsage.findMany(query);\n    const formattedData = featureUsage.map((usage) => {\n      const currentPlan = usage.user.subscriptions[0]?.plan;\n      const userName = `${usage.user.firstName || \"\"} ${usage.user.lastName || \"\"}`.trim();\n      return {\n        \"User ID\": usage.userId,\n        \"User Email\": usage.user.email,\n        \"User Name\": userName,\n        \"User Role\": usage.user.role,\n        Plan: currentPlan?.name || \"No Plan\",\n        Feature: usage.feature.name,\n        Limit: usage.limit.name,\n        Used: usage.used,\n        Period: usage.period || \"N/A\",\n        \"Last Updated\": usage.updatedAt.toISOString()\n      };\n    });\n    if (format === \"csv\") {\n      const headers = Object.keys(formattedData[0] || {});\n      const csv = [\n        headers.join(\",\"),\n        ...formattedData.map(\n          (row) => headers.map((header) => {\n            const value = row[header];\n            return typeof value === \"string\" && (value.includes(\",\") || value.includes('\"')) ? `\"${value.replace(/\"/g, '\"\"')}\"` : value;\n          }).join(\",\")\n        )\n      ].join(\"\\n\");\n      return new Response(csv, {\n        headers: {\n          \"Content-Type\": \"text/csv\",\n          \"Content-Disposition\": `attachment; filename=\"feature-usage-export-${(/* @__PURE__ */ new Date()).toISOString().split(\"T\")[0]}.csv\"`\n        }\n      });\n    }\n    return new Response(JSON.stringify(formattedData, null, 2), {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        \"Content-Disposition\": `attachment; filename=\"feature-usage-export-${(/* @__PURE__ */ new Date()).toISOString().split(\"T\")[0]}.json\"`\n      }\n    });\n  } catch (error) {\n    console.error(\"Error exporting feature usage:\", error);\n    return new Response(\"Failed to export feature usage\", { status: 500 });\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;;;;;AAEK,MAAC,GAAG,GAAG,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK;AACxC,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE,EAAE,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AAClD,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE,EAAE,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE,EAAE,CAAC;AAC3E,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;AACtC,IAAI,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxD;AACA,EAAE,IAAI;AACN,IAAI,MAAM,SAAS,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC;AACvD,IAAI,MAAM,OAAO,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC;AACnD,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC;AACjD,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC;AACjD,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC;AACjD,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,KAAK;AAC1D,IAAI,MAAM,KAAK,GAAG;AAClB,MAAM,KAAK,EAAE,EAAE;AACf,MAAM,OAAO,EAAE;AACf,QAAQ,IAAI,EAAE;AACd,UAAU,MAAM,EAAE;AAClB,YAAY,EAAE,EAAE,IAAI;AACpB,YAAY,KAAK,EAAE,IAAI;AACvB,YAAY,SAAS,EAAE,IAAI;AAC3B,YAAY,QAAQ,EAAE,IAAI;AAC1B,YAAY,IAAI,EAAE,IAAI;AACtB,YAAY,aAAa,EAAE;AAC3B,cAAc,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;AAC5C,cAAc,IAAI,EAAE,CAAC;AACrB,cAAc,OAAO,EAAE;AACvB,gBAAgB,IAAI,EAAE;AACtB;AACA;AACA;AACA,SAAS;AACT,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,KAAK,EAAE;AACf,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,SAAS,EAAE;AACnB;AACA,KAAK;AACL,IAAI,IAAI,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,SAAS,GAAG,SAAS;AACpD,IAAI,IAAI,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO;AAC9C,IAAI,IAAI,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM;AAC3C,IAAI,IAAI,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM;AAC3C,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG;AACzB,QAAQ,aAAa,EAAE;AACvB,UAAU,IAAI,EAAE;AAChB,YAAY;AACZ;AACA;AACA,OAAO;AACP;AACA,IAAI,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC;AAClE,IAAI,MAAM,aAAa,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK;AACtD,MAAM,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI;AAC3D,MAAM,MAAM,QAAQ,GAAG,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE;AAC1F,MAAM,OAAO;AACb,QAAQ,SAAS,EAAE,KAAK,CAAC,MAAM;AAC/B,QAAQ,YAAY,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK;AACtC,QAAQ,WAAW,EAAE,QAAQ;AAC7B,QAAQ,WAAW,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI;AACpC,QAAQ,IAAI,EAAE,WAAW,EAAE,IAAI,IAAI,SAAS;AAC5C,QAAQ,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI;AACnC,QAAQ,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI;AAC/B,QAAQ,IAAI,EAAE,KAAK,CAAC,IAAI;AACxB,QAAQ,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,KAAK;AACrC,QAAQ,cAAc,EAAE,KAAK,CAAC,SAAS,CAAC,WAAW;AACnD,OAAO;AACP,KAAK,CAAC;AACN,IAAI,IAAI,MAAM,KAAK,KAAK,EAAE;AAC1B,MAAM,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;AACzD,MAAM,MAAM,GAAG,GAAG;AAClB,QAAQ,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC;AACzB,QAAQ,GAAG,aAAa,CAAC,GAAG;AAC5B,UAAU,CAAC,GAAG,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK;AAC3C,YAAY,MAAM,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC;AACrC,YAAY,OAAO,OAAO,KAAK,KAAK,QAAQ,KAAK,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK;AACvI,WAAW,CAAC,CAAC,IAAI,CAAC,GAAG;AACrB;AACA,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;AAClB,MAAM,OAAO,IAAI,QAAQ,CAAC,GAAG,EAAE;AAC/B,QAAQ,OAAO,EAAE;AACjB,UAAU,cAAc,EAAE,UAAU;AACpC,UAAU,qBAAqB,EAAE,CAAC,2CAA2C,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK;AAC7I;AACA,OAAO,CAAC;AACR;AACA,IAAI,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE;AAChE,MAAM,OAAO,EAAE;AACf,QAAQ,cAAc,EAAE,kBAAkB;AAC1C,QAAQ,qBAAqB,EAAE,CAAC,2CAA2C,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM;AAC5I;AACA,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC;AAC1D,IAAI,OAAO,IAAI,QAAQ,CAAC,gCAAgC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1E;AACA;;;;"}