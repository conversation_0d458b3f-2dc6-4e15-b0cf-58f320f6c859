{"version": 3, "file": "_server.ts-C3q5j8iz.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/billing/resume-subscription/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../../../chunks/auth.js\";\nconst POST = async ({ cookies, request }) => {\n  const token = cookies.get(\"auth_token\");\n  const isProd = process.env.NODE_ENV === \"production\";\n  if (!token) return new Response(\"Unauthorized\", { status: 401 });\n  const userData = await verifySessionToken(token);\n  if (!userData?.id) return new Response(\"Unauthorized\", { status: 401 });\n  const stripeSecret = isProd ? process.env.STRIPE_SECRET_KEY_LIVE || \"sk_live_placeholder\" : process.env.STRIPE_SECRET_KEY_TEST || \"sk_test_placeholder\";\n  const Stripe = (await import(\"stripe\")).default;\n  const stripe = new Stripe(stripeSecret, {\n    apiVersion: \"2025-04-30.basil\"\n  });\n  let user = await prisma.user.findUnique({ where: { id: userData.id } });\n  if (!user) return new Response(\"User not found\", { status: 404 });\n  if (!user.stripeCustomerId) {\n    return new Response(\"No subscription found\", { status: 404 });\n  }\n  try {\n    const keyPrefix = stripeSecret.substring(0, 4);\n    console.log(\n      `Using Stripe API key with prefix: ${keyPrefix}... (${isProd ? \"LIVE\" : \"TEST\"} mode)`\n    );\n    let subscriptions;\n    try {\n      subscriptions = await stripe.subscriptions.list({\n        customer: user.stripeCustomerId,\n        limit: 1\n      });\n    } catch (error) {\n      console.error(\"Error fetching subscriptions from Stripe:\", error);\n      return json(\n        {\n          error: \"Failed to fetch subscription data from Stripe\",\n          details: error.message || \"Unknown error\"\n        },\n        { status: 500 }\n      );\n    }\n    if (!subscriptions || subscriptions.data.length === 0) {\n      return json(\n        {\n          error: \"No subscription found\",\n          details: \"No active subscription was found for this account\"\n        },\n        { status: 404 }\n      );\n    }\n    const subscription = subscriptions.data[0];\n    const isPausingAtPeriodEnd = subscription.cancel_at_period_end && subscription.metadata?.pause_at_period_end === \"true\";\n    const isAlreadyActive = !subscription.pause_collection && !subscription.cancel_at_period_end && subscription.status === \"active\" && !subscription.metadata?.pause_at_period_end;\n    console.log(\"Subscription status check:\", {\n      isPauseCollection: !!subscription.pause_collection,\n      isCancelAtPeriodEnd: subscription.cancel_at_period_end,\n      status: subscription.status,\n      metadata: subscription.metadata,\n      isPauseAtPeriodEnd: subscription.metadata?.pause_at_period_end === \"true\",\n      isAlreadyActive\n    });\n    if (isAlreadyActive) {\n      console.log(\"Subscription is already active, no need to resume\");\n      return json({\n        success: true,\n        status: \"active\",\n        subscription,\n        message: \"Subscription is already active\"\n      });\n    }\n    console.log(\"Resuming subscription:\", {\n      id: subscription.id,\n      isPausingAtPeriodEnd,\n      cancel_at_period_end: subscription.cancel_at_period_end,\n      metadata: subscription.metadata\n    });\n    let updatedSubscription;\n    try {\n      updatedSubscription = await stripe.subscriptions.update(subscription.id, {\n        pause_collection: \"\",\n        // Empty string removes the pause_collection\n        cancel_at_period_end: false,\n        // Also ensure it's not set to cancel\n        metadata: {\n          // Clear the pause_at_period_end flag if it exists\n          pause_at_period_end: \"\",\n          action_at_period_end: \"\"\n        }\n      });\n      console.log(\"Successfully resumed subscription:\", {\n        id: updatedSubscription.id,\n        status: updatedSubscription.status,\n        pause_collection: updatedSubscription.pause_collection,\n        cancel_at_period_end: updatedSubscription.cancel_at_period_end,\n        metadata: updatedSubscription.metadata\n      });\n      return json({\n        success: true,\n        status: \"active\",\n        subscription: updatedSubscription\n      });\n    } catch (error) {\n      console.error(\"Error updating subscription in Stripe:\", error);\n      return json(\n        {\n          error: \"Failed to resume subscription in Stripe\",\n          details: error.message || \"Unknown error\"\n        },\n        { status: 500 }\n      );\n    }\n  } catch (error) {\n    console.error(\"Error resuming subscription:\", error);\n    const errorMessage = error.message || \"Failed to resume subscription\";\n    const errorType = error.type || \"unknown_error\";\n    if (error.raw) {\n      console.error(\"Stripe error details:\", error.raw);\n    }\n    return json(\n      {\n        error: errorMessage,\n        details: errorType,\n        code: error.code || \"unknown_code\"\n      },\n      { status: 500 }\n    );\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;AAGK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AAC7C,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;AACtD,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE,EAAE,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AAClD,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE,EAAE,MAAM,YAAY,GAAG,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,qBAAqB,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,qBAAqB;AACzJ,EAAE,MAAM,MAAM,GAAG,CAAC,MAAM,OAAO,+BAAQ,CAAC,EAAE,OAAO;AACjD,EAAE,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,YAAY,EAAE;AAC1C,IAAI,UAAU,EAAE;AAChB,GAAG,CAAC;AACJ,EAAE,IAAI,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE,EAAE,CAAC;AACzE,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,QAAQ,CAAC,gBAAgB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnE,EAAE,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;AAC9B,IAAI,OAAO,IAAI,QAAQ,CAAC,uBAAuB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE;AACA,EAAE,IAAI;AACN,IAAI,MAAM,SAAS,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;AAClD,IAAI,OAAO,CAAC,GAAG;AACf,MAAM,CAAC,kCAAkC,EAAE,SAAS,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC,MAAM;AAC3F,KAAK;AACL,IAAI,IAAI,aAAa;AACrB,IAAI,IAAI;AACR,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC;AACtD,QAAQ,QAAQ,EAAE,IAAI,CAAC,gBAAgB;AACvC,QAAQ,KAAK,EAAE;AACf,OAAO,CAAC;AACR,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC;AACvE,MAAM,OAAO,IAAI;AACjB,QAAQ;AACR,UAAU,KAAK,EAAE,+CAA+C;AAChE,UAAU,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI;AACpC,SAAS;AACT,QAAQ,EAAE,MAAM,EAAE,GAAG;AACrB,OAAO;AACP;AACA,IAAI,IAAI,CAAC,aAAa,IAAI,aAAa,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;AAC3D,MAAM,OAAO,IAAI;AACjB,QAAQ;AACR,UAAU,KAAK,EAAE,uBAAuB;AACxC,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ,EAAE,MAAM,EAAE,GAAG;AACrB,OAAO;AACP;AACA,IAAI,MAAM,YAAY,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;AAC9C,IAAI,MAAM,oBAAoB,GAAG,YAAY,CAAC,oBAAoB,IAAI,YAAY,CAAC,QAAQ,EAAE,mBAAmB,KAAK,MAAM;AAC3H,IAAI,MAAM,eAAe,GAAG,CAAC,YAAY,CAAC,gBAAgB,IAAI,CAAC,YAAY,CAAC,oBAAoB,IAAI,YAAY,CAAC,MAAM,KAAK,QAAQ,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,mBAAmB;AACnL,IAAI,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE;AAC9C,MAAM,iBAAiB,EAAE,CAAC,CAAC,YAAY,CAAC,gBAAgB;AACxD,MAAM,mBAAmB,EAAE,YAAY,CAAC,oBAAoB;AAC5D,MAAM,MAAM,EAAE,YAAY,CAAC,MAAM;AACjC,MAAM,QAAQ,EAAE,YAAY,CAAC,QAAQ;AACrC,MAAM,kBAAkB,EAAE,YAAY,CAAC,QAAQ,EAAE,mBAAmB,KAAK,MAAM;AAC/E,MAAM;AACN,KAAK,CAAC;AACN,IAAI,IAAI,eAAe,EAAE;AACzB,MAAM,OAAO,CAAC,GAAG,CAAC,mDAAmD,CAAC;AACtE,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,MAAM,EAAE,QAAQ;AACxB,QAAQ,YAAY;AACpB,QAAQ,OAAO,EAAE;AACjB,OAAO,CAAC;AACR;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE;AAC1C,MAAM,EAAE,EAAE,YAAY,CAAC,EAAE;AACzB,MAAM,oBAAoB;AAC1B,MAAM,oBAAoB,EAAE,YAAY,CAAC,oBAAoB;AAC7D,MAAM,QAAQ,EAAE,YAAY,CAAC;AAC7B,KAAK,CAAC;AACN,IAAI,IAAI,mBAAmB;AAC3B,IAAI,IAAI;AACR,MAAM,mBAAmB,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,EAAE;AAC/E,QAAQ,gBAAgB,EAAE,EAAE;AAC5B;AACA,QAAQ,oBAAoB,EAAE,KAAK;AACnC;AACA,QAAQ,QAAQ,EAAE;AAClB;AACA,UAAU,mBAAmB,EAAE,EAAE;AACjC,UAAU,oBAAoB,EAAE;AAChC;AACA,OAAO,CAAC;AACR,MAAM,OAAO,CAAC,GAAG,CAAC,oCAAoC,EAAE;AACxD,QAAQ,EAAE,EAAE,mBAAmB,CAAC,EAAE;AAClC,QAAQ,MAAM,EAAE,mBAAmB,CAAC,MAAM;AAC1C,QAAQ,gBAAgB,EAAE,mBAAmB,CAAC,gBAAgB;AAC9D,QAAQ,oBAAoB,EAAE,mBAAmB,CAAC,oBAAoB;AACtE,QAAQ,QAAQ,EAAE,mBAAmB,CAAC;AACtC,OAAO,CAAC;AACR,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,MAAM,EAAE,QAAQ;AACxB,QAAQ,YAAY,EAAE;AACtB,OAAO,CAAC;AACR,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC;AACpE,MAAM,OAAO,IAAI;AACjB,QAAQ;AACR,UAAU,KAAK,EAAE,yCAAyC;AAC1D,UAAU,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI;AACpC,SAAS;AACT,QAAQ,EAAE,MAAM,EAAE,GAAG;AACrB,OAAO;AACP;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC;AACxD,IAAI,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,IAAI,+BAA+B;AACzE,IAAI,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,IAAI,eAAe;AACnD,IAAI,IAAI,KAAK,CAAC,GAAG,EAAE;AACnB,MAAM,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,GAAG,CAAC;AACvD;AACA,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,KAAK,EAAE,YAAY;AAC3B,QAAQ,OAAO,EAAE,SAAS;AAC1B,QAAQ,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI;AAC5B,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;;;;"}