{"version": 3, "file": "_server.ts-CcCEJYJT.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/notifications/mark-all-read/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { g as getUserFromToken } from \"../../../../../chunks/auth.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nconst POST = async ({ cookies }) => {\n  const user = await getUserFromToken(cookies);\n  if (!user) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  try {\n    await prisma.notification.updateMany({\n      where: {\n        OR: [\n          { userId: user.id },\n          { global: true }\n        ],\n        read: false\n      },\n      data: {\n        read: true\n      }\n    });\n    return json({\n      success: true,\n      message: \"All notifications marked as read\"\n    });\n  } catch (error) {\n    console.error(\"Error marking all notifications as read:\", error);\n    return json({ error: \"Failed to mark all notifications as read\" }, { status: 500 });\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;AAGK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK;AACpC,EAAE,MAAM,IAAI,GAAG,MAAM,gBAAgB,CAAC,OAAO,CAAC;AAC9C,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC;AACzC,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE;AACZ,UAAU,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;AAC7B,UAAU,EAAE,MAAM,EAAE,IAAI;AACxB,SAAS;AACT,QAAQ,IAAI,EAAE;AACd,OAAO;AACP,MAAM,IAAI,EAAE;AACZ,QAAQ,IAAI,EAAE;AACd;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,OAAO,EAAE;AACf,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC;AACpE,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,0CAA0C,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACvF;AACA;;;;"}