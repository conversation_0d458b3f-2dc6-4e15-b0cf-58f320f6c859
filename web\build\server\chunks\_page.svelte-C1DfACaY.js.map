{"version": 3, "file": "_page.svelte-C1DfACaY.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/api/email/webhook/setup/_page.svelte.js"], "sourcesContent": ["import { O as copy_payload, P as assign_payload, y as pop, w as push, V as escape_html } from \"../../../../../../chunks/index3.js\";\nimport \"clsx\";\nimport \"../../../../../../chunks/client.js\";\nimport { B as Button } from \"../../../../../../chunks/button.js\";\nimport { C as Card } from \"../../../../../../chunks/card.js\";\nimport { C as Card_content } from \"../../../../../../chunks/card-content.js\";\nimport { C as Card_description } from \"../../../../../../chunks/card-description.js\";\nimport { C as Card_footer } from \"../../../../../../chunks/card-footer.js\";\nimport { C as Card_header } from \"../../../../../../chunks/card-header.js\";\nimport { C as Card_title } from \"../../../../../../chunks/card-title.js\";\nimport { I as Input } from \"../../../../../../chunks/input.js\";\nimport { L as Label } from \"../../../../../../chunks/label.js\";\nimport { S as Separator } from \"../../../../../../chunks/separator.js\";\nimport { A as Alert, a as Alert_title, b as Alert_description } from \"../../../../../../chunks/alert-title.js\";\nimport { R as Root, T as Tabs_list, a as Tabs_content } from \"../../../../../../chunks/index9.js\";\nimport \"../../../../../../chunks/Toaster.svelte_svelte_type_style_lang.js\";\nimport { T as Tabs_trigger } from \"../../../../../../chunks/tabs-trigger.js\";\nimport { C as Clipboard } from \"../../../../../../chunks/clipboard.js\";\nfunction _page($$payload, $$props) {\n  push();\n  let webhookUrl = \"\";\n  let webhookSecret = \"\";\n  let isLoading = false;\n  let activeTab = \"setup\";\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    $$payload2.out += `<div class=\"container mx-auto py-8 max-w-4xl\"><h1 class=\"text-3xl font-bold mb-6\">Resend Webhook Setup</h1> `;\n    Root($$payload2, {\n      get value() {\n        return activeTab;\n      },\n      set value($$value) {\n        activeTab = $$value;\n        $$settled = false;\n      },\n      children: ($$payload3) => {\n        Tabs_list($$payload3, {\n          class: \"grid w-full grid-cols-3\",\n          children: ($$payload4) => {\n            Tabs_trigger($$payload4, {\n              value: \"setup\",\n              children: ($$payload5) => {\n                $$payload5.out += `<!---->Setup Guide`;\n              },\n              $$slots: { default: true }\n            });\n            $$payload4.out += `<!----> `;\n            Tabs_trigger($$payload4, {\n              value: \"test\",\n              children: ($$payload5) => {\n                $$payload5.out += `<!---->Test Webhook`;\n              },\n              $$slots: { default: true }\n            });\n            $$payload4.out += `<!----> `;\n            Tabs_trigger($$payload4, {\n              value: \"events\",\n              children: ($$payload5) => {\n                $$payload5.out += `<!---->Event Types`;\n              },\n              $$slots: { default: true }\n            });\n            $$payload4.out += `<!---->`;\n          },\n          $$slots: { default: true }\n        });\n        $$payload3.out += `<!----> `;\n        Tabs_content($$payload3, {\n          value: \"setup\",\n          children: ($$payload4) => {\n            Card($$payload4, {\n              children: ($$payload5) => {\n                Card_header($$payload5, {\n                  children: ($$payload6) => {\n                    Card_title($$payload6, {\n                      children: ($$payload7) => {\n                        $$payload7.out += `<!---->Setup Resend Webhooks`;\n                      },\n                      $$slots: { default: true }\n                    });\n                    $$payload6.out += `<!----> `;\n                    Card_description($$payload6, {\n                      children: ($$payload7) => {\n                        $$payload7.out += `<!---->Follow these steps to configure webhooks in your Resend account.`;\n                      },\n                      $$slots: { default: true }\n                    });\n                    $$payload6.out += `<!---->`;\n                  },\n                  $$slots: { default: true }\n                });\n                $$payload5.out += `<!----> `;\n                Card_content($$payload5, {\n                  class: \"space-y-6\",\n                  children: ($$payload6) => {\n                    $$payload6.out += `<div class=\"space-y-2\"><h3 class=\"text-lg font-medium\">1. Generate a Webhook Secret</h3> <div class=\"flex gap-2\">`;\n                    Input($$payload6, { value: webhookSecret, readonly: true });\n                    $$payload6.out += `<!----> `;\n                    Button($$payload6, {\n                      variant: \"outline\",\n                      children: ($$payload7) => {\n                        Clipboard($$payload7, { class: \"h-4 w-4 mr-2\" });\n                        $$payload7.out += `<!----> Copy`;\n                      },\n                      $$slots: { default: true }\n                    });\n                    $$payload6.out += `<!----> `;\n                    Button($$payload6, {\n                      variant: \"outline\",\n                      children: ($$payload7) => {\n                        $$payload7.out += `<!---->Regenerate`;\n                      },\n                      $$slots: { default: true }\n                    });\n                    $$payload6.out += `<!----></div> <p class=\"text-sm text-muted-foreground\">This secret will be used to verify webhook requests from Resend.</p></div> `;\n                    Separator($$payload6, {});\n                    $$payload6.out += `<!----> <div class=\"space-y-2\"><h3 class=\"text-lg font-medium\">2. Set Environment Variable</h3> `;\n                    Alert($$payload6, {\n                      children: ($$payload7) => {\n                        Alert_title($$payload7, {\n                          children: ($$payload8) => {\n                            $$payload8.out += `<!---->Add this to your .env file`;\n                          },\n                          $$slots: { default: true }\n                        });\n                        $$payload7.out += `<!----> `;\n                        Alert_description($$payload7, {\n                          children: ($$payload8) => {\n                            $$payload8.out += `<code class=\"bg-muted p-2 rounded block\">RESEND_WEBHOOK_SECRET=${escape_html(webhookSecret)}</code> `;\n                            Button($$payload8, {\n                              variant: \"outline\",\n                              class: \"mt-2\",\n                              children: ($$payload9) => {\n                                Clipboard($$payload9, { class: \"h-4 w-4 mr-2\" });\n                                $$payload9.out += `<!----> Copy`;\n                              },\n                              $$slots: { default: true }\n                            });\n                            $$payload8.out += `<!---->`;\n                          },\n                          $$slots: { default: true }\n                        });\n                        $$payload7.out += `<!---->`;\n                      },\n                      $$slots: { default: true }\n                    });\n                    $$payload6.out += `<!----></div> `;\n                    Separator($$payload6, {});\n                    $$payload6.out += `<!----> <div class=\"space-y-2\"><h3 class=\"text-lg font-medium\">3. Configure Webhook in Resend Dashboard</h3> <p>Go to the <a href=\"https://resend.com/webhooks\" target=\"_blank\" class=\"text-primary underline\">Resend Webhooks page</a> and add a new webhook with these details:</p> <div class=\"space-y-4 mt-4\"><div>`;\n                    Label($$payload6, {\n                      for: \"webhook-url\",\n                      children: ($$payload7) => {\n                        $$payload7.out += `<!---->Webhook URL`;\n                      },\n                      $$slots: { default: true }\n                    });\n                    $$payload6.out += `<!----> <div class=\"flex gap-2\">`;\n                    Input($$payload6, {\n                      id: \"webhook-url\",\n                      value: webhookUrl,\n                      readonly: true\n                    });\n                    $$payload6.out += `<!----> `;\n                    Button($$payload6, {\n                      variant: \"outline\",\n                      children: ($$payload7) => {\n                        Clipboard($$payload7, { class: \"h-4 w-4 mr-2\" });\n                        $$payload7.out += `<!----> Copy`;\n                      },\n                      $$slots: { default: true }\n                    });\n                    $$payload6.out += `<!----></div></div> <div>`;\n                    Label($$payload6, {\n                      for: \"webhook-secret\",\n                      children: ($$payload7) => {\n                        $$payload7.out += `<!---->Webhook Secret`;\n                      },\n                      $$slots: { default: true }\n                    });\n                    $$payload6.out += `<!----> <div class=\"flex gap-2\">`;\n                    Input($$payload6, {\n                      id: \"webhook-secret\",\n                      value: webhookSecret,\n                      readonly: true\n                    });\n                    $$payload6.out += `<!----> `;\n                    Button($$payload6, {\n                      variant: \"outline\",\n                      children: ($$payload7) => {\n                        Clipboard($$payload7, { class: \"h-4 w-4 mr-2\" });\n                        $$payload7.out += `<!----> Copy`;\n                      },\n                      $$slots: { default: true }\n                    });\n                    $$payload6.out += `<!----></div></div></div></div> `;\n                    Separator($$payload6, {});\n                    $$payload6.out += `<!----> <div class=\"space-y-2\"><h3 class=\"text-lg font-medium\">4. Select Event Types</h3> <p>Enable these event types in the Resend dashboard:</p> <ul class=\"list-disc pl-6 space-y-1\"><li>email.delivered</li> <li>email.opened</li> <li>email.clicked</li> <li>email.bounced</li> <li>email.complained</li></ul></div>`;\n                  },\n                  $$slots: { default: true }\n                });\n                $$payload5.out += `<!----> `;\n                Card_footer($$payload5, {\n                  class: \"flex justify-between\",\n                  children: ($$payload6) => {\n                    Button($$payload6, {\n                      variant: \"outline\",\n                      children: ($$payload7) => {\n                        $$payload7.out += `<!---->Check EmailEvent Table`;\n                      },\n                      $$slots: { default: true }\n                    });\n                    $$payload6.out += `<!----> `;\n                    Button($$payload6, {\n                      children: ($$payload7) => {\n                        $$payload7.out += `<!---->Test Webhook`;\n                      },\n                      $$slots: { default: true }\n                    });\n                    $$payload6.out += `<!---->`;\n                  },\n                  $$slots: { default: true }\n                });\n                $$payload5.out += `<!---->`;\n              },\n              $$slots: { default: true }\n            });\n          },\n          $$slots: { default: true }\n        });\n        $$payload3.out += `<!----> `;\n        Tabs_content($$payload3, {\n          value: \"test\",\n          children: ($$payload4) => {\n            Card($$payload4, {\n              children: ($$payload5) => {\n                Card_header($$payload5, {\n                  children: ($$payload6) => {\n                    Card_title($$payload6, {\n                      children: ($$payload7) => {\n                        $$payload7.out += `<!---->Test Webhook`;\n                      },\n                      $$slots: { default: true }\n                    });\n                    $$payload6.out += `<!----> `;\n                    Card_description($$payload6, {\n                      children: ($$payload7) => {\n                        $$payload7.out += `<!---->Send a test webhook event to verify your setup.`;\n                      },\n                      $$slots: { default: true }\n                    });\n                    $$payload6.out += `<!---->`;\n                  },\n                  $$slots: { default: true }\n                });\n                $$payload5.out += `<!----> `;\n                Card_content($$payload5, {\n                  children: ($$payload6) => {\n                    $$payload6.out += `<div class=\"space-y-4\">`;\n                    Button($$payload6, {\n                      disabled: isLoading,\n                      children: ($$payload7) => {\n                        {\n                          $$payload7.out += \"<!--[!-->\";\n                          $$payload7.out += `Send Test Event`;\n                        }\n                        $$payload7.out += `<!--]-->`;\n                      },\n                      $$slots: { default: true }\n                    });\n                    $$payload6.out += `<!----> `;\n                    {\n                      $$payload6.out += \"<!--[!-->\";\n                    }\n                    $$payload6.out += `<!--]--></div>`;\n                  },\n                  $$slots: { default: true }\n                });\n                $$payload5.out += `<!---->`;\n              },\n              $$slots: { default: true }\n            });\n          },\n          $$slots: { default: true }\n        });\n        $$payload3.out += `<!----> `;\n        Tabs_content($$payload3, {\n          value: \"events\",\n          children: ($$payload4) => {\n            Card($$payload4, {\n              children: ($$payload5) => {\n                Card_header($$payload5, {\n                  children: ($$payload6) => {\n                    Card_title($$payload6, {\n                      children: ($$payload7) => {\n                        $$payload7.out += `<!---->Resend Event Types`;\n                      },\n                      $$slots: { default: true }\n                    });\n                    $$payload6.out += `<!----> `;\n                    Card_description($$payload6, {\n                      children: ($$payload7) => {\n                        $$payload7.out += `<!---->These are the event types that Resend can send to your webhook endpoint.`;\n                      },\n                      $$slots: { default: true }\n                    });\n                    $$payload6.out += `<!---->`;\n                  },\n                  $$slots: { default: true }\n                });\n                $$payload5.out += `<!----> `;\n                Card_content($$payload5, {\n                  children: ($$payload6) => {\n                    $$payload6.out += `<div class=\"space-y-4\"><div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\"><div class=\"border rounded p-4\"><h3 class=\"font-medium\">email.delivered</h3> <p class=\"text-sm text-muted-foreground\">Triggered when an email is successfully delivered to the recipient's mail server.</p></div> <div class=\"border rounded p-4\"><h3 class=\"font-medium\">email.opened</h3> <p class=\"text-sm text-muted-foreground\">Triggered when a recipient opens an email (requires tracking pixel).</p></div> <div class=\"border rounded p-4\"><h3 class=\"font-medium\">email.clicked</h3> <p class=\"text-sm text-muted-foreground\">Triggered when a recipient clicks a link in an email (requires link tracking).</p></div> <div class=\"border rounded p-4\"><h3 class=\"font-medium\">email.bounced</h3> <p class=\"text-sm text-muted-foreground\">Triggered when an email permanently bounces.</p></div> <div class=\"border rounded p-4\"><h3 class=\"font-medium\">email.complained</h3> <p class=\"text-sm text-muted-foreground\">Triggered when a recipient marks an email as spam.</p></div> <div class=\"border rounded p-4\"><h3 class=\"font-medium\">email.delivery_delayed</h3> <p class=\"text-sm text-muted-foreground\">Triggered when email delivery is temporarily delayed.</p></div></div> <div class=\"mt-4\"><a href=\"https://resend.com/docs/dashboard/webhooks/event-types\" target=\"_blank\" class=\"text-primary underline\">View Resend Webhook Documentation</a></div></div>`;\n                  },\n                  $$slots: { default: true }\n                });\n                $$payload5.out += `<!---->`;\n              },\n              $$slots: { default: true }\n            });\n          },\n          $$slots: { default: true }\n        });\n        $$payload3.out += `<!---->`;\n      },\n      $$slots: { default: true }\n    });\n    $$payload2.out += `<!----></div>`;\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,UAAU,GAAG,EAAE;AACrB,EAAE,IAAI,aAAa,GAAG,EAAE;AACxB,EAAE,IAAI,SAAS,GAAG,KAAK;AACvB,EAAE,IAAI,SAAS,GAAG,OAAO;AACzB,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,4GAA4G,CAAC;AACpI,IAAI,IAAI,CAAC,UAAU,EAAE;AACrB,MAAM,IAAI,KAAK,GAAG;AAClB,QAAQ,OAAO,SAAS;AACxB,OAAO;AACP,MAAM,IAAI,KAAK,CAAC,OAAO,EAAE;AACzB,QAAQ,SAAS,GAAG,OAAO;AAC3B,QAAQ,SAAS,GAAG,KAAK;AACzB,OAAO;AACP,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,SAAS,CAAC,UAAU,EAAE;AAC9B,UAAU,KAAK,EAAE,yBAAyB;AAC1C,UAAU,QAAQ,EAAE,CAAC,UAAU,KAAK;AACpC,YAAY,YAAY,CAAC,UAAU,EAAE;AACrC,cAAc,KAAK,EAAE,OAAO;AAC5B,cAAc,QAAQ,EAAE,CAAC,UAAU,KAAK;AACxC,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;AACtD,eAAe;AACf,cAAc,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACtC,aAAa,CAAC;AACd,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACxC,YAAY,YAAY,CAAC,UAAU,EAAE;AACrC,cAAc,KAAK,EAAE,MAAM;AAC3B,cAAc,QAAQ,EAAE,CAAC,UAAU,KAAK;AACxC,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC;AACvD,eAAe;AACf,cAAc,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACtC,aAAa,CAAC;AACd,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACxC,YAAY,YAAY,CAAC,UAAU,EAAE;AACrC,cAAc,KAAK,EAAE,QAAQ;AAC7B,cAAc,QAAQ,EAAE,CAAC,UAAU,KAAK;AACxC,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;AACtD,eAAe;AACf,cAAc,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACtC,aAAa,CAAC;AACd,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACvC,WAAW;AACX,UAAU,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAClC,SAAS,CAAC;AACV,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACpC,QAAQ,YAAY,CAAC,UAAU,EAAE;AACjC,UAAU,KAAK,EAAE,OAAO;AACxB,UAAU,QAAQ,EAAE,CAAC,UAAU,KAAK;AACpC,YAAY,IAAI,CAAC,UAAU,EAAE;AAC7B,cAAc,QAAQ,EAAE,CAAC,UAAU,KAAK;AACxC,gBAAgB,WAAW,CAAC,UAAU,EAAE;AACxC,kBAAkB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC5C,oBAAoB,UAAU,CAAC,UAAU,EAAE;AAC3C,sBAAsB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChD,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,4BAA4B,CAAC;AACxE,uBAAuB;AACvB,sBAAsB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9C,qBAAqB,CAAC;AACtB,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChD,oBAAoB,gBAAgB,CAAC,UAAU,EAAE;AACjD,sBAAsB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChD,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,uEAAuE,CAAC;AACnH,uBAAuB;AACvB,sBAAsB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9C,qBAAqB,CAAC;AACtB,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/C,mBAAmB;AACnB,kBAAkB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC1C,iBAAiB,CAAC;AAClB,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC5C,gBAAgB,YAAY,CAAC,UAAU,EAAE;AACzC,kBAAkB,KAAK,EAAE,WAAW;AACpC,kBAAkB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC5C,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,iHAAiH,CAAC;AACzJ,oBAAoB,KAAK,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;AAC/E,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChD,oBAAoB,MAAM,CAAC,UAAU,EAAE;AACvC,sBAAsB,OAAO,EAAE,SAAS;AACxC,sBAAsB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChD,wBAAwB,SAAS,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AACxE,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;AACxD,uBAAuB;AACvB,sBAAsB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9C,qBAAqB,CAAC;AACtB,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChD,oBAAoB,MAAM,CAAC,UAAU,EAAE;AACvC,sBAAsB,OAAO,EAAE,SAAS;AACxC,sBAAsB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChD,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC;AAC7D,uBAAuB;AACvB,sBAAsB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9C,qBAAqB,CAAC;AACtB,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,kIAAkI,CAAC;AAC1K,oBAAoB,SAAS,CAAC,UAAU,EAAE,EAAE,CAAC;AAC7C,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,gGAAgG,CAAC;AACxI,oBAAoB,KAAK,CAAC,UAAU,EAAE;AACtC,sBAAsB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChD,wBAAwB,WAAW,CAAC,UAAU,EAAE;AAChD,0BAA0B,QAAQ,EAAE,CAAC,UAAU,KAAK;AACpD,4BAA4B,UAAU,CAAC,GAAG,IAAI,CAAC,iCAAiC,CAAC;AACjF,2BAA2B;AAC3B,0BAA0B,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAClD,yBAAyB,CAAC;AAC1B,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACpD,wBAAwB,iBAAiB,CAAC,UAAU,EAAE;AACtD,0BAA0B,QAAQ,EAAE,CAAC,UAAU,KAAK;AACpD,4BAA4B,UAAU,CAAC,GAAG,IAAI,CAAC,+DAA+D,EAAE,WAAW,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC;AACpJ,4BAA4B,MAAM,CAAC,UAAU,EAAE;AAC/C,8BAA8B,OAAO,EAAE,SAAS;AAChD,8BAA8B,KAAK,EAAE,MAAM;AAC3C,8BAA8B,QAAQ,EAAE,CAAC,UAAU,KAAK;AACxD,gCAAgC,SAAS,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AAChF,gCAAgC,UAAU,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;AAChE,+BAA+B;AAC/B,8BAA8B,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACtD,6BAA6B,CAAC;AAC9B,4BAA4B,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACvD,2BAA2B;AAC3B,0BAA0B,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAClD,yBAAyB,CAAC;AAC1B,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnD,uBAAuB;AACvB,sBAAsB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9C,qBAAqB,CAAC;AACtB,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACtD,oBAAoB,SAAS,CAAC,UAAU,EAAE,EAAE,CAAC;AAC7C,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,uTAAuT,CAAC;AAC/V,oBAAoB,KAAK,CAAC,UAAU,EAAE;AACtC,sBAAsB,GAAG,EAAE,aAAa;AACxC,sBAAsB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChD,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;AAC9D,uBAAuB;AACvB,sBAAsB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9C,qBAAqB,CAAC;AACtB,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,gCAAgC,CAAC;AACxE,oBAAoB,KAAK,CAAC,UAAU,EAAE;AACtC,sBAAsB,EAAE,EAAE,aAAa;AACvC,sBAAsB,KAAK,EAAE,UAAU;AACvC,sBAAsB,QAAQ,EAAE;AAChC,qBAAqB,CAAC;AACtB,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChD,oBAAoB,MAAM,CAAC,UAAU,EAAE;AACvC,sBAAsB,OAAO,EAAE,SAAS;AACxC,sBAAsB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChD,wBAAwB,SAAS,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AACxE,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;AACxD,uBAAuB;AACvB,sBAAsB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9C,qBAAqB,CAAC;AACtB,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,yBAAyB,CAAC;AACjE,oBAAoB,KAAK,CAAC,UAAU,EAAE;AACtC,sBAAsB,GAAG,EAAE,gBAAgB;AAC3C,sBAAsB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChD,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC;AACjE,uBAAuB;AACvB,sBAAsB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9C,qBAAqB,CAAC;AACtB,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,gCAAgC,CAAC;AACxE,oBAAoB,KAAK,CAAC,UAAU,EAAE;AACtC,sBAAsB,EAAE,EAAE,gBAAgB;AAC1C,sBAAsB,KAAK,EAAE,aAAa;AAC1C,sBAAsB,QAAQ,EAAE;AAChC,qBAAqB,CAAC;AACtB,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChD,oBAAoB,MAAM,CAAC,UAAU,EAAE;AACvC,sBAAsB,OAAO,EAAE,SAAS;AACxC,sBAAsB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChD,wBAAwB,SAAS,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AACxE,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;AACxD,uBAAuB;AACvB,sBAAsB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9C,qBAAqB,CAAC;AACtB,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,gCAAgC,CAAC;AACxE,oBAAoB,SAAS,CAAC,UAAU,EAAE,EAAE,CAAC;AAC7C,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,yTAAyT,CAAC;AACjW,mBAAmB;AACnB,kBAAkB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC1C,iBAAiB,CAAC;AAClB,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC5C,gBAAgB,WAAW,CAAC,UAAU,EAAE;AACxC,kBAAkB,KAAK,EAAE,sBAAsB;AAC/C,kBAAkB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC5C,oBAAoB,MAAM,CAAC,UAAU,EAAE;AACvC,sBAAsB,OAAO,EAAE,SAAS;AACxC,sBAAsB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChD,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,6BAA6B,CAAC;AACzE,uBAAuB;AACvB,sBAAsB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9C,qBAAqB,CAAC;AACtB,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChD,oBAAoB,MAAM,CAAC,UAAU,EAAE;AACvC,sBAAsB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChD,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC;AAC/D,uBAAuB;AACvB,sBAAsB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9C,qBAAqB,CAAC;AACtB,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/C,mBAAmB;AACnB,kBAAkB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC1C,iBAAiB,CAAC;AAClB,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC3C,eAAe;AACf,cAAc,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACtC,aAAa,CAAC;AACd,WAAW;AACX,UAAU,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAClC,SAAS,CAAC;AACV,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACpC,QAAQ,YAAY,CAAC,UAAU,EAAE;AACjC,UAAU,KAAK,EAAE,MAAM;AACvB,UAAU,QAAQ,EAAE,CAAC,UAAU,KAAK;AACpC,YAAY,IAAI,CAAC,UAAU,EAAE;AAC7B,cAAc,QAAQ,EAAE,CAAC,UAAU,KAAK;AACxC,gBAAgB,WAAW,CAAC,UAAU,EAAE;AACxC,kBAAkB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC5C,oBAAoB,UAAU,CAAC,UAAU,EAAE;AAC3C,sBAAsB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChD,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC;AAC/D,uBAAuB;AACvB,sBAAsB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9C,qBAAqB,CAAC;AACtB,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChD,oBAAoB,gBAAgB,CAAC,UAAU,EAAE;AACjD,sBAAsB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChD,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,sDAAsD,CAAC;AAClG,uBAAuB;AACvB,sBAAsB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9C,qBAAqB,CAAC;AACtB,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/C,mBAAmB;AACnB,kBAAkB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC1C,iBAAiB,CAAC;AAClB,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC5C,gBAAgB,YAAY,CAAC,UAAU,EAAE;AACzC,kBAAkB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC5C,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC;AAC/D,oBAAoB,MAAM,CAAC,UAAU,EAAE;AACvC,sBAAsB,QAAQ,EAAE,SAAS;AACzC,sBAAsB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChD,wBAAwB;AACxB,0BAA0B,UAAU,CAAC,GAAG,IAAI,WAAW;AACvD,0BAA0B,UAAU,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;AAC7D;AACA,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACpD,uBAAuB;AACvB,sBAAsB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9C,qBAAqB,CAAC;AACtB,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChD,oBAAoB;AACpB,sBAAsB,UAAU,CAAC,GAAG,IAAI,WAAW;AACnD;AACA,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACtD,mBAAmB;AACnB,kBAAkB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC1C,iBAAiB,CAAC;AAClB,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC3C,eAAe;AACf,cAAc,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACtC,aAAa,CAAC;AACd,WAAW;AACX,UAAU,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAClC,SAAS,CAAC;AACV,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACpC,QAAQ,YAAY,CAAC,UAAU,EAAE;AACjC,UAAU,KAAK,EAAE,QAAQ;AACzB,UAAU,QAAQ,EAAE,CAAC,UAAU,KAAK;AACpC,YAAY,IAAI,CAAC,UAAU,EAAE;AAC7B,cAAc,QAAQ,EAAE,CAAC,UAAU,KAAK;AACxC,gBAAgB,WAAW,CAAC,UAAU,EAAE;AACxC,kBAAkB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC5C,oBAAoB,UAAU,CAAC,UAAU,EAAE;AAC3C,sBAAsB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChD,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,yBAAyB,CAAC;AACrE,uBAAuB;AACvB,sBAAsB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9C,qBAAqB,CAAC;AACtB,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChD,oBAAoB,gBAAgB,CAAC,UAAU,EAAE;AACjD,sBAAsB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChD,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,+EAA+E,CAAC;AAC3H,uBAAuB;AACvB,sBAAsB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9C,qBAAqB,CAAC;AACtB,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/C,mBAAmB;AACnB,kBAAkB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC1C,iBAAiB,CAAC;AAClB,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC5C,gBAAgB,YAAY,CAAC,UAAU,EAAE;AACzC,kBAAkB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC5C,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,g4CAAg4C,CAAC;AACx6C,mBAAmB;AACnB,kBAAkB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC1C,iBAAiB,CAAC;AAClB,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC3C,eAAe;AACf,cAAc,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACtC,aAAa,CAAC;AACd,WAAW;AACX,UAAU,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAClC,SAAS,CAAC;AACV,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACrC;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,GAAG,EAAE;AACP;;;;"}