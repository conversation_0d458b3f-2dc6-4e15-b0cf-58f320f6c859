{"version": 3, "file": "_server.ts-B0gK_0Es.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/occupations/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../chunks/prisma.js\";\nconst GET = async ({ url }) => {\n  try {\n    const search = url.searchParams.get(\"search\") || \"\";\n    const limit = parseInt(url.searchParams.get(\"limit\") || \"500\");\n    if (!prisma) {\n      console.error(\"Prisma client is not initialized\");\n      return json({ error: \"Database connection not available\" }, { status: 500 });\n    }\n    const occupations = await prisma.occupation.findMany({\n      where: {\n        OR: [\n          { title: { contains: search, mode: \"insensitive\" } },\n          { shortTitle: { contains: search, mode: \"insensitive\" } }\n        ]\n      },\n      select: {\n        id: true,\n        title: true,\n        shortTitle: true,\n        category: true\n      },\n      take: limit,\n      orderBy: {\n        title: \"asc\"\n      }\n    });\n    return json(occupations);\n  } catch (error) {\n    console.error(\"Error fetching occupations:\", error);\n    return json({ error: \"Failed to fetch occupations\" }, { status: 500 });\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;AAEK,MAAC,GAAG,GAAG,OAAO,EAAE,GAAG,EAAE,KAAK;AAC/B,EAAE,IAAI;AACN,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE;AACvD,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC;AAClE,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,OAAO,CAAC,KAAK,CAAC,kCAAkC,CAAC;AACvD,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mCAAmC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClF;AACA,IAAI,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;AACzD,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE;AACZ,UAAU,EAAE,KAAK,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,aAAa,EAAE,EAAE;AAC9D,UAAU,EAAE,UAAU,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,aAAa,EAAE;AACjE;AACA,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,EAAE,EAAE,IAAI;AAChB,QAAQ,KAAK,EAAE,IAAI;AACnB,QAAQ,UAAU,EAAE,IAAI;AACxB,QAAQ,QAAQ,EAAE;AAClB,OAAO;AACP,MAAM,IAAI,EAAE,KAAK;AACjB,MAAM,OAAO,EAAE;AACf,QAAQ,KAAK,EAAE;AACf;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC;AAC5B,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC;AACvD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,6BAA6B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1E;AACA;;;;"}