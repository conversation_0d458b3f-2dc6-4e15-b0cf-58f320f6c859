{"version": 3, "file": "_server.ts-B4Fsquxo.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/companies/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../chunks/prisma.js\";\nconst GET = async ({ url }) => {\n  try {\n    const search = url.searchParams.get(\"search\") || \"\";\n    const limit = parseInt(url.searchParams.get(\"limit\") || \"100\");\n    if (!prisma) {\n      console.error(\"Prisma client is not initialized\");\n      return json({ error: \"Database connection not available\" }, { status: 500 });\n    }\n    const companies = await prisma.company.findMany({\n      where: {\n        name: { contains: search, mode: \"insensitive\" }\n      },\n      select: {\n        id: true,\n        name: true,\n        domain: true,\n        logoUrl: true\n      },\n      take: limit,\n      orderBy: {\n        name: \"asc\"\n      }\n    });\n    return json(companies);\n  } catch (error) {\n    console.error(\"Error fetching companies:\", error);\n    return json({ error: \"Failed to fetch companies\" }, { status: 500 });\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;AAEK,MAAC,GAAG,GAAG,OAAO,EAAE,GAAG,EAAE,KAAK;AAC/B,EAAE,IAAI;AACN,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE;AACvD,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC;AAClE,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,OAAO,CAAC,KAAK,CAAC,kCAAkC,CAAC;AACvD,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mCAAmC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClF;AACA,IAAI,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;AACpD,MAAM,KAAK,EAAE;AACb,QAAQ,IAAI,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,aAAa;AACrD,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,EAAE,EAAE,IAAI;AAChB,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,MAAM,EAAE,IAAI;AACpB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM,IAAI,EAAE,KAAK;AACjB,MAAM,OAAO,EAAE;AACf,QAAQ,IAAI,EAAE;AACd;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC;AAC1B,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC;AACrD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,2BAA2B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA;;;;"}