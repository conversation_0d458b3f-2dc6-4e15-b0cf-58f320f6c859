{"version": 3, "file": "_page.svelte-DkMu32E2.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/settings/profile/_id_/_page.svelte.js"], "sourcesContent": ["import { V as escape_html, y as pop, w as push } from \"../../../../../../chunks/index3.js\";\nimport \"clsx\";\nimport { B as <PERSON><PERSON> } from \"../../../../../../chunks/button.js\";\nimport { R as Root, D as Dropdown_menu_trigger, a as Dropdown_menu_content } from \"../../../../../../chunks/index6.js\";\nimport { S as SEO } from \"../../../../../../chunks/SEO.js\";\nimport \"../../../../../../chunks/client.js\";\nimport \"../../../../../../chunks/Toaster.svelte_svelte_type_style_lang.js\";\nimport { E as Ellipsis } from \"../../../../../../chunks/ellipsis.js\";\nimport { D as Dropdown_menu_label } from \"../../../../../../chunks/dropdown-menu-label.js\";\nimport { D as Dropdown_menu_separator } from \"../../../../../../chunks/dropdown-menu-separator.js\";\nimport { E as Eye_off } from \"../../../../../../chunks/eye-off.js\";\nimport { E as Eye } from \"../../../../../../chunks/eye.js\";\nimport { T as Trash_2 } from \"../../../../../../chunks/trash-2.js\";\nfunction _page($$payload, $$props) {\n  push();\n  const { data } = $$props;\n  let profile = {\n    ...data.profile\n  };\n  let profileData = {};\n  console.log(\"Profile data:\", data.profile);\n  function createCompleteProfile() {\n    const typedProfileData = profileData;\n    const resumeData = typedProfileData.resumeData || {};\n    const jobSearchStatus = typedProfileData.jobPreferences?.jobSearchStatus || \"actively_looking\";\n    return {\n      header: {\n        profileName: profile.name || \"\",\n        fullName: typedProfileData.fullName || typedProfileData.personalInfo?.fullName || typedProfileData.header?.fullName || \"\",\n        jobTitle: typedProfileData.jobType || typedProfileData.personalInfo?.jobTitle || \"\",\n        jobSearchStatus\n      },\n      visibility: {\n        showToRecruiters: typedProfileData.visibility?.showToRecruiters || false,\n        getDiscovered: typedProfileData.visibility?.getDiscovered || true,\n        hideFromCurrentEmployer: typedProfileData.visibility?.hideFromCurrentEmployer || false\n      },\n      personalInfo: {\n        email: typedProfileData.email || typedProfileData.personalInfo?.email || \"\",\n        phone: typedProfileData.phone || typedProfileData.personalInfo?.phone || \"\",\n        address: typedProfileData.personalInfo?.address || \"\",\n        city: typedProfileData.personalInfo?.city || \"\",\n        state: typedProfileData.personalInfo?.state || \"\",\n        zip: typedProfileData.personalInfo?.zip || \"\",\n        country: typedProfileData.personalInfo?.country || \"USA\"\n      },\n      resume: resumeData.id || typedProfileData.resumeId ? {\n        resumeId: resumeData.id || typedProfileData.resumeId,\n        fileName: resumeData.name || typedProfileData.resumeName || \"resume.pdf\",\n        uploadedAt: resumeData.updatedAt || typedProfileData.resumeUpdatedAt,\n        isDefault: true\n      } : null,\n      workExperiences: typedProfileData.workExperience || typedProfileData.workExperiences || [],\n      educations: typedProfileData.education || typedProfileData.educations || [],\n      projects: typedProfileData.projects || [],\n      portfolioLinks: {\n        linkedinUrl: typedProfileData.portfolioLinks?.linkedin || typedProfileData.personalInfo?.linkedin || \"\",\n        githubUrl: typedProfileData.portfolioLinks?.github || typedProfileData.personalInfo?.github || \"\",\n        portfolioUrl: typedProfileData.website || typedProfileData.personalInfo?.website || \"\",\n        otherUrl: typedProfileData.portfolioLinks?.other || \"\"\n      },\n      skills: {\n        skills: Array.isArray(typedProfileData.skills) ? typedProfileData.skills : typedProfileData.skillsData?.list || typedProfileData.skillsData?.technical || []\n      },\n      languages: typedProfileData.languages ? typedProfileData.languages.map((lang) => ({\n        ...lang,\n        proficiency: lang.proficiency || \"intermediate\"\n      })) : []\n    };\n  }\n  let completeProfile = createCompleteProfile();\n  function formatDate(dateString) {\n    const date = new Date(dateString);\n    return date.toLocaleDateString(\"en-US\", {\n      year: \"numeric\",\n      month: \"short\",\n      day: \"numeric\"\n    });\n  }\n  const lastUpdated = profile?.updatedAt ? formatDate(profile.updatedAt) : \"Recently\";\n  SEO($$payload, {\n    title: `${profile?.name || \"Profile\"} - Hirli`,\n    description: \"Edit your professional profile for job applications.\",\n    keywords: \"profile, resume, job search, career profile, professional information\",\n    url: `https://hirli.com/dashboard/settings/profile/${profile?.id}`\n  });\n  $$payload.out += `<!----> <div class=\"border-border flex items-center justify-between border-b p-6\"><div><h1 class=\"text-xl font-semibold\">${escape_html(profile?.name || \"Profile\")}</h1> <p class=\"text-muted-foreground text-sm\">Last updated: ${escape_html(lastUpdated)}</p></div> <div><!---->`;\n  Root($$payload, {\n    children: ($$payload2) => {\n      $$payload2.out += `<!---->`;\n      Dropdown_menu_trigger($$payload2, {\n        children: ($$payload3) => {\n          Button($$payload3, {\n            variant: \"ghost\",\n            size: \"icon\",\n            class: \"h-8 w-8 rounded-full\",\n            children: ($$payload4) => {\n              Ellipsis($$payload4, { class: \"h-4 w-4\" });\n              $$payload4.out += `<!----> <span class=\"sr-only\">Open menu</span>`;\n            },\n            $$slots: { default: true }\n          });\n        },\n        $$slots: { default: true }\n      });\n      $$payload2.out += `<!----> <!---->`;\n      Dropdown_menu_content($$payload2, {\n        align: \"end\",\n        class: \"w-48\",\n        children: ($$payload3) => {\n          $$payload3.out += `<!---->`;\n          Dropdown_menu_label($$payload3, {\n            children: ($$payload4) => {\n              $$payload4.out += `<!---->Profile Actions`;\n            },\n            $$slots: { default: true }\n          });\n          $$payload3.out += `<!----> <!---->`;\n          Dropdown_menu_separator($$payload3, {});\n          $$payload3.out += `<!----> <button class=\"hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground relative flex w-full cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors data-[disabled]:pointer-events-none data-[disabled]:opacity-50\">`;\n          if (completeProfile.visibility.showToRecruiters) {\n            $$payload3.out += \"<!--[-->\";\n            Eye_off($$payload3, { class: \"mr-2 h-4 w-4\" });\n            $$payload3.out += `<!----> <span>Hide from Recruiters</span>`;\n          } else {\n            $$payload3.out += \"<!--[!-->\";\n            Eye($$payload3, { class: \"mr-2 h-4 w-4\" });\n            $$payload3.out += `<!----> <span>Show to Recruiters</span>`;\n          }\n          $$payload3.out += `<!--]--></button> <!---->`;\n          Dropdown_menu_separator($$payload3, {});\n          $$payload3.out += `<!----> <button class=\"text-destructive hover:bg-destructive hover:text-destructive-foreground focus:bg-destructive focus:text-destructive-foreground relative flex w-full cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors\">`;\n          Trash_2($$payload3, { class: \"mr-2 h-4 w-4\" });\n          $$payload3.out += `<!----> <span>Delete Profile</span></button>`;\n        },\n        $$slots: { default: true }\n      });\n      $$payload2.out += `<!---->`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----></div></div> <div class=\"container mx-auto p-6\">`;\n  {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<div class=\"flex flex-col items-center justify-center py-12\"><div class=\"border-primary h-12 w-12 animate-spin rounded-full border-4 border-t-transparent\"></div> <p class=\"mt-4 text-lg\">Loading profile...</p></div>`;\n  }\n  $$payload.out += `<!--]--></div>`;\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO;AAC1B,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,GAAG,IAAI,CAAC;AACZ,GAAG;AACH,EAAE,IAAI,WAAW,GAAG,EAAE;AACtB,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC;AAC5C,EAAE,SAAS,qBAAqB,GAAG;AACnC,IAAI,MAAM,gBAAgB,GAAG,WAAW;AACxC,IAAI,MAAM,UAAU,GAAG,gBAAgB,CAAC,UAAU,IAAI,EAAE;AACxD,IAAI,MAAM,eAAe,GAAG,gBAAgB,CAAC,cAAc,EAAE,eAAe,IAAI,kBAAkB;AAClG,IAAI,OAAO;AACX,MAAM,MAAM,EAAE;AACd,QAAQ,WAAW,EAAE,OAAO,CAAC,IAAI,IAAI,EAAE;AACvC,QAAQ,QAAQ,EAAE,gBAAgB,CAAC,QAAQ,IAAI,gBAAgB,CAAC,YAAY,EAAE,QAAQ,IAAI,gBAAgB,CAAC,MAAM,EAAE,QAAQ,IAAI,EAAE;AACjI,QAAQ,QAAQ,EAAE,gBAAgB,CAAC,OAAO,IAAI,gBAAgB,CAAC,YAAY,EAAE,QAAQ,IAAI,EAAE;AAC3F,QAAQ;AACR,OAAO;AACP,MAAM,UAAU,EAAE;AAClB,QAAQ,gBAAgB,EAAE,gBAAgB,CAAC,UAAU,EAAE,gBAAgB,IAAI,KAAK;AAChF,QAAQ,aAAa,EAAE,gBAAgB,CAAC,UAAU,EAAE,aAAa,IAAI,IAAI;AACzE,QAAQ,uBAAuB,EAAE,gBAAgB,CAAC,UAAU,EAAE,uBAAuB,IAAI;AACzF,OAAO;AACP,MAAM,YAAY,EAAE;AACpB,QAAQ,KAAK,EAAE,gBAAgB,CAAC,KAAK,IAAI,gBAAgB,CAAC,YAAY,EAAE,KAAK,IAAI,EAAE;AACnF,QAAQ,KAAK,EAAE,gBAAgB,CAAC,KAAK,IAAI,gBAAgB,CAAC,YAAY,EAAE,KAAK,IAAI,EAAE;AACnF,QAAQ,OAAO,EAAE,gBAAgB,CAAC,YAAY,EAAE,OAAO,IAAI,EAAE;AAC7D,QAAQ,IAAI,EAAE,gBAAgB,CAAC,YAAY,EAAE,IAAI,IAAI,EAAE;AACvD,QAAQ,KAAK,EAAE,gBAAgB,CAAC,YAAY,EAAE,KAAK,IAAI,EAAE;AACzD,QAAQ,GAAG,EAAE,gBAAgB,CAAC,YAAY,EAAE,GAAG,IAAI,EAAE;AACrD,QAAQ,OAAO,EAAE,gBAAgB,CAAC,YAAY,EAAE,OAAO,IAAI;AAC3D,OAAO;AACP,MAAM,MAAM,EAAE,UAAU,CAAC,EAAE,IAAI,gBAAgB,CAAC,QAAQ,GAAG;AAC3D,QAAQ,QAAQ,EAAE,UAAU,CAAC,EAAE,IAAI,gBAAgB,CAAC,QAAQ;AAC5D,QAAQ,QAAQ,EAAE,UAAU,CAAC,IAAI,IAAI,gBAAgB,CAAC,UAAU,IAAI,YAAY;AAChF,QAAQ,UAAU,EAAE,UAAU,CAAC,SAAS,IAAI,gBAAgB,CAAC,eAAe;AAC5E,QAAQ,SAAS,EAAE;AACnB,OAAO,GAAG,IAAI;AACd,MAAM,eAAe,EAAE,gBAAgB,CAAC,cAAc,IAAI,gBAAgB,CAAC,eAAe,IAAI,EAAE;AAChG,MAAM,UAAU,EAAE,gBAAgB,CAAC,SAAS,IAAI,gBAAgB,CAAC,UAAU,IAAI,EAAE;AACjF,MAAM,QAAQ,EAAE,gBAAgB,CAAC,QAAQ,IAAI,EAAE;AAC/C,MAAM,cAAc,EAAE;AACtB,QAAQ,WAAW,EAAE,gBAAgB,CAAC,cAAc,EAAE,QAAQ,IAAI,gBAAgB,CAAC,YAAY,EAAE,QAAQ,IAAI,EAAE;AAC/G,QAAQ,SAAS,EAAE,gBAAgB,CAAC,cAAc,EAAE,MAAM,IAAI,gBAAgB,CAAC,YAAY,EAAE,MAAM,IAAI,EAAE;AACzG,QAAQ,YAAY,EAAE,gBAAgB,CAAC,OAAO,IAAI,gBAAgB,CAAC,YAAY,EAAE,OAAO,IAAI,EAAE;AAC9F,QAAQ,QAAQ,EAAE,gBAAgB,CAAC,cAAc,EAAE,KAAK,IAAI;AAC5D,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,GAAG,gBAAgB,CAAC,MAAM,GAAG,gBAAgB,CAAC,UAAU,EAAE,IAAI,IAAI,gBAAgB,CAAC,UAAU,EAAE,SAAS,IAAI;AAClK,OAAO;AACP,MAAM,SAAS,EAAE,gBAAgB,CAAC,SAAS,GAAG,gBAAgB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,MAAM;AACxF,QAAQ,GAAG,IAAI;AACf,QAAQ,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI;AACzC,OAAO,CAAC,CAAC,GAAG;AACZ,KAAK;AACL;AACA,EAAE,IAAI,eAAe,GAAG,qBAAqB,EAAE;AAC/C,EAAE,SAAS,UAAU,CAAC,UAAU,EAAE;AAClC,IAAI,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC;AACrC,IAAI,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE;AAC5C,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,GAAG,EAAE;AACX,KAAK,CAAC;AACN;AACA,EAAE,MAAM,WAAW,GAAG,OAAO,EAAE,SAAS,GAAG,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,UAAU;AACrF,EAAE,GAAG,CAAC,SAAS,EAAE;AACjB,IAAI,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,IAAI,IAAI,SAAS,CAAC,QAAQ,CAAC;AAClD,IAAI,WAAW,EAAE,sDAAsD;AACvE,IAAI,QAAQ,EAAE,uEAAuE;AACrF,IAAI,GAAG,EAAE,CAAC,6CAA6C,EAAE,OAAO,EAAE,EAAE,CAAC;AACrE,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,yHAAyH,EAAE,WAAW,CAAC,OAAO,EAAE,IAAI,IAAI,SAAS,CAAC,CAAC,6DAA6D,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC,uBAAuB,CAAC;AACvS,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACjC,MAAM,qBAAqB,CAAC,UAAU,EAAE;AACxC,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,MAAM,CAAC,UAAU,EAAE;AAC7B,YAAY,OAAO,EAAE,OAAO;AAC5B,YAAY,IAAI,EAAE,MAAM;AACxB,YAAY,KAAK,EAAE,sBAAsB;AACzC,YAAY,QAAQ,EAAE,CAAC,UAAU,KAAK;AACtC,cAAc,QAAQ,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AACxD,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,8CAA8C,CAAC;AAChF,aAAa;AACb,YAAY,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACpC,WAAW,CAAC;AACZ,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC,OAAO,CAAC;AACR,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;AACzC,MAAM,qBAAqB,CAAC,UAAU,EAAE;AACxC,QAAQ,KAAK,EAAE,KAAK;AACpB,QAAQ,KAAK,EAAE,MAAM;AACrB,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACrC,UAAU,mBAAmB,CAAC,UAAU,EAAE;AAC1C,YAAY,QAAQ,EAAE,CAAC,UAAU,KAAK;AACtC,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC;AACxD,aAAa;AACb,YAAY,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACpC,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;AAC7C,UAAU,uBAAuB,CAAC,UAAU,EAAE,EAAE,CAAC;AACjD,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,4SAA4S,CAAC;AAC1U,UAAU,IAAI,eAAe,CAAC,UAAU,CAAC,gBAAgB,EAAE;AAC3D,YAAY,UAAU,CAAC,GAAG,IAAI,UAAU;AACxC,YAAY,OAAO,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AAC1D,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,yCAAyC,CAAC;AACzE,WAAW,MAAM;AACjB,YAAY,UAAU,CAAC,GAAG,IAAI,WAAW;AACzC,YAAY,GAAG,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AACtD,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,uCAAuC,CAAC;AACvE;AACA,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,yBAAyB,CAAC;AACvD,UAAU,uBAAuB,CAAC,UAAU,EAAE,EAAE,CAAC;AACjD,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,kRAAkR,CAAC;AAChT,UAAU,OAAO,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AACxD,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,4CAA4C,CAAC;AAC1E,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC,OAAO,CAAC;AACR,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACjC,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,uDAAuD,CAAC;AAC5E,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,sNAAsN,CAAC;AAC7O;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACnC,EAAE,GAAG,EAAE;AACP;;;;"}