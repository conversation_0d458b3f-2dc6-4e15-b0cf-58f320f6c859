{"version": 3, "file": "_server.ts-BKkID-ND.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/email/analytics/events/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../../chunks/prisma.js\";\nimport { l as logger } from \"../../../../../../chunks/logger.js\";\nasync function GET({ url }) {\n  try {\n    const timeRange = url.searchParams.get(\"timeRange\") || \"7d\";\n    const type = url.searchParams.get(\"type\") || \"all\";\n    const template = url.searchParams.get(\"template\") || \"all\";\n    const page = parseInt(url.searchParams.get(\"page\") || \"1\");\n    const limit = parseInt(url.searchParams.get(\"limit\") || \"100\");\n    const countOnly = url.searchParams.get(\"count\") === \"true\";\n    const startDateParam = url.searchParams.get(\"startDate\");\n    const endDateParam = url.searchParams.get(\"endDate\");\n    try {\n      await prisma.emailEvent.count();\n    } catch (error) {\n      logger.warn(\"EmailEvent table does not exist or is not accessible\");\n      if (countOnly) {\n        return json({ count: 0 });\n      }\n      return json([]);\n    }\n    const now = /* @__PURE__ */ new Date();\n    let startDate = /* @__PURE__ */ new Date();\n    let endDate = now;\n    if (startDateParam && endDateParam) {\n      startDate = new Date(startDateParam);\n      endDate = new Date(endDateParam);\n    } else {\n      switch (timeRange) {\n        case \"24h\":\n          startDate.setHours(now.getHours() - 24);\n          break;\n        case \"7d\":\n          startDate.setDate(now.getDate() - 7);\n          break;\n        case \"30d\":\n          startDate.setDate(now.getDate() - 30);\n          break;\n        case \"90d\":\n          startDate.setDate(now.getDate() - 90);\n          break;\n        default:\n          startDate.setDate(now.getDate() - 7);\n      }\n    }\n    const whereConditions = {\n      timestamp: {\n        gte: startDate,\n        lte: endDate\n      }\n    };\n    if (type !== \"all\") {\n      whereConditions.type = type;\n    }\n    if (template !== \"all\") {\n      whereConditions.templateName = template;\n    }\n    if (countOnly) {\n      const count = await prisma.emailEvent.count({\n        where: whereConditions\n      });\n      return json({ count });\n    }\n    const events = await prisma.emailEvent.findMany({\n      where: whereConditions,\n      orderBy: {\n        timestamp: \"desc\"\n      },\n      take: limit,\n      skip: (page - 1) * limit\n    });\n    return json(events);\n  } catch (error) {\n    logger.error(\"Error getting email events:\", error);\n    if (url.searchParams.get(\"count\") === \"true\") {\n      return json({ count: 0 }, { status: 200 });\n    }\n    return json([], { status: 200 });\n  }\n}\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;;AAGA,eAAe,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE;AAC5B,EAAE,IAAI;AACN,IAAI,MAAM,SAAS,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,IAAI;AAC/D,IAAI,MAAM,IAAI,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK;AACtD,IAAI,MAAM,QAAQ,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,KAAK;AAC9D,IAAI,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC;AAC9D,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC;AAClE,IAAI,MAAM,SAAS,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,MAAM;AAC9D,IAAI,MAAM,cAAc,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC;AAC5D,IAAI,MAAM,YAAY,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC;AACxD,IAAI,IAAI;AACR,MAAM,MAAM,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE;AACrC,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,MAAM,CAAC,IAAI,CAAC,sDAAsD,CAAC;AACzE,MAAM,IAAI,SAAS,EAAE;AACrB,QAAQ,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;AACjC;AACA,MAAM,OAAO,IAAI,CAAC,EAAE,CAAC;AACrB;AACA,IAAI,MAAM,GAAG,mBAAmB,IAAI,IAAI,EAAE;AAC1C,IAAI,IAAI,SAAS,mBAAmB,IAAI,IAAI,EAAE;AAC9C,IAAI,IAAI,OAAO,GAAG,GAAG;AACrB,IAAI,IAAI,cAAc,IAAI,YAAY,EAAE;AACxC,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC;AAC1C,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC;AACtC,KAAK,MAAM;AACX,MAAM,QAAQ,SAAS;AACvB,QAAQ,KAAK,KAAK;AAClB,UAAU,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC;AACjD,UAAU;AACV,QAAQ,KAAK,IAAI;AACjB,UAAU,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AAC9C,UAAU;AACV,QAAQ,KAAK,KAAK;AAClB,UAAU,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC;AAC/C,UAAU;AACV,QAAQ,KAAK,KAAK;AAClB,UAAU,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC;AAC/C,UAAU;AACV,QAAQ;AACR,UAAU,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AAC9C;AACA;AACA,IAAI,MAAM,eAAe,GAAG;AAC5B,MAAM,SAAS,EAAE;AACjB,QAAQ,GAAG,EAAE,SAAS;AACtB,QAAQ,GAAG,EAAE;AACb;AACA,KAAK;AACL,IAAI,IAAI,IAAI,KAAK,KAAK,EAAE;AACxB,MAAM,eAAe,CAAC,IAAI,GAAG,IAAI;AACjC;AACA,IAAI,IAAI,QAAQ,KAAK,KAAK,EAAE;AAC5B,MAAM,eAAe,CAAC,YAAY,GAAG,QAAQ;AAC7C;AACA,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC;AAClD,QAAQ,KAAK,EAAE;AACf,OAAO,CAAC;AACR,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC;AAC5B;AACA,IAAI,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;AACpD,MAAM,KAAK,EAAE,eAAe;AAC5B,MAAM,OAAO,EAAE;AACf,QAAQ,SAAS,EAAE;AACnB,OAAO;AACP,MAAM,IAAI,EAAE,KAAK;AACjB,MAAM,IAAI,EAAE,CAAC,IAAI,GAAG,CAAC,IAAI;AACzB,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC;AACvB,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC;AACtD,IAAI,IAAI,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,MAAM,EAAE;AAClD,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAChD;AACA,IAAI,OAAO,IAAI,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpC;AACA;;;;"}