{"version": 3, "file": "_server.ts-BWnhlxjN.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/ai/interview/_id_/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../../chunks/prisma.js\";\nimport { OpenAI } from \"openai\";\nfunction getOpenAIClient() {\n  if (!process.env.OPENAI_API_KEY) {\n    throw new Error(\"OpenAI API key is not configured\");\n  }\n  return new OpenAI({\n    apiKey: process.env.OPENAI_API_KEY\n  });\n}\nconst GET = async ({ params, locals }) => {\n  if (!locals.user) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  const { id } = params;\n  try {\n    const session = await prisma.interviewCoachingSession.findUnique({\n      where: {\n        id,\n        userId: locals.user.id\n      }\n    });\n    if (!session) {\n      return json({ error: \"Session not found\" }, { status: 404 });\n    }\n    return json({ session });\n  } catch (error) {\n    console.error(\"Error fetching interview coaching session:\", error);\n    return json({ error: \"Failed to fetch interview coaching session\" }, { status: 500 });\n  }\n};\nconst POST = async ({ params, request, locals }) => {\n  if (!locals.user) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  const { id } = params;\n  try {\n    const { questionIndex, response } = await request.json();\n    if (questionIndex === void 0 || !response) {\n      return json({ error: \"Question index and response are required\" }, { status: 400 });\n    }\n    const session = await prisma.interviewCoachingSession.findUnique({\n      where: {\n        id,\n        userId: locals.user.id\n      }\n    });\n    if (!session) {\n      return json({ error: \"Session not found\" }, { status: 404 });\n    }\n    if (!session.questions[questionIndex]) {\n      return json({ error: \"Question not found\" }, { status: 404 });\n    }\n    const feedback = await generateFeedback(\n      session.questions[questionIndex].question,\n      response,\n      session.jobTitle\n    );\n    const updatedSession = await prisma.interviewCoachingSession.update({\n      where: {\n        id\n      },\n      data: {\n        responses: [\n          ...session.responses || [],\n          {\n            questionIndex,\n            response,\n            timestamp: (/* @__PURE__ */ new Date()).toISOString()\n          }\n        ],\n        feedback: [\n          ...session.feedback || [],\n          {\n            questionIndex,\n            feedback,\n            timestamp: (/* @__PURE__ */ new Date()).toISOString()\n          }\n        ],\n        status: \"in_progress\",\n        updatedAt: /* @__PURE__ */ new Date()\n      }\n    });\n    return json({\n      success: true,\n      feedback,\n      session: updatedSession\n    });\n  } catch (error) {\n    console.error(\"Error submitting interview response:\", error);\n    return json({ error: \"Failed to submit interview response\" }, { status: 500 });\n  }\n};\nconst PATCH = async ({ params, request, locals }) => {\n  if (!locals.user) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  const { id } = params;\n  try {\n    const { status } = await request.json();\n    if (!status) {\n      return json({ error: \"Status is required\" }, { status: 400 });\n    }\n    const updatedSession = await prisma.interviewCoachingSession.update({\n      where: {\n        id,\n        userId: locals.user.id\n      },\n      data: {\n        status,\n        updatedAt: /* @__PURE__ */ new Date()\n      }\n    });\n    return json({\n      success: true,\n      session: updatedSession\n    });\n  } catch (error) {\n    console.error(\"Error updating interview session:\", error);\n    return json({ error: \"Failed to update interview session\" }, { status: 500 });\n  }\n};\nasync function generateFeedback(question, response, jobTitle) {\n  try {\n    const prompt = `\n    Question: \"${question}\"\n    \n    Candidate's Response: \"${response}\"\n    \n    Please provide constructive feedback on this interview response for a ${jobTitle} position. \n    Evaluate the response based on:\n    1. Content and relevance\n    2. Structure and clarity\n    3. Specific examples and details\n    4. Areas for improvement\n    \n    Keep the feedback concise, constructive, and actionable.\n    `;\n    const openai = getOpenAIClient();\n    const aiResponse = await openai.chat.completions.create({\n      model: \"gpt-4\",\n      messages: [\n        {\n          role: \"system\",\n          content: \"You are an expert interview coach providing feedback on interview responses.\"\n        },\n        { role: \"user\", content: prompt }\n      ],\n      temperature: 0.7,\n      max_tokens: 1e3\n    });\n    return aiResponse.choices[0]?.message?.content || \"No feedback available.\";\n  } catch (error) {\n    console.error(\"Error generating feedback:\", error);\n    return \"Unable to generate feedback at this time. Please try again later.\";\n  }\n}\nexport {\n  GET,\n  PATCH,\n  POST\n};\n"], "names": [], "mappings": ";;;;;AAGA,SAAS,eAAe,GAAG;AAC3B,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE;AACnC,IAAI,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC;AACvD;AACA,EAAE,OAAO,IAAI,MAAM,CAAC;AACpB,IAAI,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC;AACxB,GAAG,CAAC;AACJ;AACK,MAAC,GAAG,GAAG,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK;AAC1C,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;AACpB,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;AACvB,EAAE,IAAI;AACN,IAAI,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,wBAAwB,CAAC,UAAU,CAAC;AACrE,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE;AACV,QAAQ,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC;AAC5B;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,OAAO,EAAE;AAClB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE;AACA,IAAI,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,CAAC;AAC5B,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC;AACtE,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,4CAA4C,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzF;AACA;AACK,MAAC,IAAI,GAAG,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AACpD,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;AACpB,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;AACvB,EAAE,IAAI;AACN,IAAI,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AAC5D,IAAI,IAAI,aAAa,KAAK,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE;AAC/C,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,0CAA0C,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzF;AACA,IAAI,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,wBAAwB,CAAC,UAAU,CAAC;AACrE,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE;AACV,QAAQ,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC;AAC5B;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,OAAO,EAAE;AAClB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE;AACA,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE;AAC3C,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oBAAoB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnE;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,gBAAgB;AAC3C,MAAM,OAAO,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,QAAQ;AAC/C,MAAM,QAAQ;AACd,MAAM,OAAO,CAAC;AACd,KAAK;AACL,IAAI,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,wBAAwB,CAAC,MAAM,CAAC;AACxE,MAAM,KAAK,EAAE;AACb,QAAQ;AACR,OAAO;AACP,MAAM,IAAI,EAAE;AACZ,QAAQ,SAAS,EAAE;AACnB,UAAU,GAAG,OAAO,CAAC,SAAS,IAAI,EAAE;AACpC,UAAU;AACV,YAAY,aAAa;AACzB,YAAY,QAAQ;AACpB,YAAY,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC/D;AACA,SAAS;AACT,QAAQ,QAAQ,EAAE;AAClB,UAAU,GAAG,OAAO,CAAC,QAAQ,IAAI,EAAE;AACnC,UAAU;AACV,YAAY,aAAa;AACzB,YAAY,QAAQ;AACpB,YAAY,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC/D;AACA,SAAS;AACT,QAAQ,MAAM,EAAE,aAAa;AAC7B,QAAQ,SAAS,kBAAkB,IAAI,IAAI;AAC3C;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,QAAQ;AACd,MAAM,OAAO,EAAE;AACf,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC;AAChE,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,qCAAqC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClF;AACA;AACK,MAAC,KAAK,GAAG,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AACrD,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;AACpB,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;AACvB,EAAE,IAAI;AACN,IAAI,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AAC3C,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oBAAoB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnE;AACA,IAAI,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,wBAAwB,CAAC,MAAM,CAAC;AACxE,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE;AACV,QAAQ,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC;AAC5B,OAAO;AACP,MAAM,IAAI,EAAE;AACZ,QAAQ,MAAM;AACd,QAAQ,SAAS,kBAAkB,IAAI,IAAI;AAC3C;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,OAAO,EAAE;AACf,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC;AAC7D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oCAAoC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjF;AACA;AACA,eAAe,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE;AAC9D,EAAE,IAAI;AACN,IAAI,MAAM,MAAM,GAAG;AACnB,eAAe,EAAE,QAAQ,CAAC;AAC1B;AACA,2BAA2B,EAAE,QAAQ,CAAC;AACtC;AACA,0EAA0E,EAAE,QAAQ,CAAC;AACrF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC;AACL,IAAI,MAAM,MAAM,GAAG,eAAe,EAAE;AACpC,IAAI,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;AAC5D,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,QAAQ,EAAE;AAChB,QAAQ;AACR,UAAU,IAAI,EAAE,QAAQ;AACxB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM;AACvC,OAAO;AACP,MAAM,WAAW,EAAE,GAAG;AACtB,MAAM,UAAU,EAAE;AAClB,KAAK,CAAC;AACN,IAAI,OAAO,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,IAAI,wBAAwB;AAC9E,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC;AACtD,IAAI,OAAO,mEAAmE;AAC9E;AACA;;;;"}