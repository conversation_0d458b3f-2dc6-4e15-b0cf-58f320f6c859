{"version": 3, "file": "_server.ts-rXCSeRhm.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/jobs/_id_/is-saved/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../../chunks/prisma.js\";\nconst GET = async ({ params, locals }) => {\n  const user = locals.user;\n  if (!user) {\n    return json({\n      success: true,\n      isSaved: false\n    });\n  }\n  try {\n    const jobId = params.id;\n    const savedJob = await prisma.savedJob.findUnique({\n      where: {\n        userId_jobId: {\n          userId: user.id,\n          jobId\n        }\n      }\n    });\n    return json({\n      success: true,\n      isSaved: !!savedJob\n    });\n  } catch (error) {\n    console.error(\"Error checking if job is saved:\", error);\n    return json({ error: \"Failed to check if job is saved\" }, { status: 500 });\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;AAEK,MAAC,GAAG,GAAG,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK;AAC1C,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,OAAO,EAAE;AACf,KAAK,CAAC;AACN;AACA,EAAE,IAAI;AACN,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,EAAE;AAC3B,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;AACtD,MAAM,KAAK,EAAE;AACb,QAAQ,YAAY,EAAE;AACtB,UAAU,MAAM,EAAE,IAAI,CAAC,EAAE;AACzB,UAAU;AACV;AACA;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,OAAO,EAAE,CAAC,CAAC;AACjB,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC;AAC3D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,iCAAiC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9E;AACA;;;;"}