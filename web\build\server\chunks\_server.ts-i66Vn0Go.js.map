{"version": 3, "file": "_server.ts-i66Vn0Go.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/help/categories/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nconst GET = async ({ url }) => {\n  try {\n    const includeArticleCount = url.searchParams.get(\"includeArticleCount\") === \"true\";\n    const parentOnly = url.searchParams.get(\"parentOnly\") === \"true\";\n    const filters = {};\n    if (parentOnly) {\n      filters.parentId = null;\n    }\n    const categories = await prisma.helpCategory.findMany({\n      where: filters,\n      orderBy: [\n        {\n          order: \"asc\"\n        },\n        {\n          name: \"asc\"\n        }\n      ],\n      include: {\n        children: true,\n        _count: includeArticleCount ? {\n          select: {\n            articles: true\n          }\n        } : void 0\n      }\n    });\n    const formattedCategories = categories.map((category) => ({\n      ...category,\n      articleCount: includeArticleCount ? category._count.articles : void 0,\n      _count: void 0\n    }));\n    return json(formattedCategories);\n  } catch (error) {\n    console.error(\"Error fetching help categories:\", error);\n    return json({ error: \"Failed to fetch help categories\" }, { status: 500 });\n  }\n};\nconst POST = async ({ request, locals }) => {\n  const user = locals.user;\n  if (!user || user.role !== \"ADMIN\") {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  try {\n    const { name, slug, description, icon, parentId, order } = await request.json();\n    const category = await prisma.helpCategory.create({\n      data: {\n        name,\n        slug,\n        description,\n        icon,\n        parentId,\n        order: order || 0\n      }\n    });\n    return json(category);\n  } catch (error) {\n    console.error(\"Error creating help category:\", error);\n    return json({ error: \"Failed to create help category\" }, { status: 500 });\n  }\n};\nexport {\n  GET,\n  POST\n};\n"], "names": [], "mappings": ";;;;AAEK,MAAC,GAAG,GAAG,OAAO,EAAE,GAAG,EAAE,KAAK;AAC/B,EAAE,IAAI;AACN,IAAI,MAAM,mBAAmB,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,qBAAqB,CAAC,KAAK,MAAM;AACtF,IAAI,MAAM,UAAU,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,YAAY,CAAC,KAAK,MAAM;AACpE,IAAI,MAAM,OAAO,GAAG,EAAE;AACtB,IAAI,IAAI,UAAU,EAAE;AACpB,MAAM,OAAO,CAAC,QAAQ,GAAG,IAAI;AAC7B;AACA,IAAI,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;AAC1D,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,OAAO,EAAE;AACf,QAAQ;AACR,UAAU,KAAK,EAAE;AACjB,SAAS;AACT,QAAQ;AACR,UAAU,IAAI,EAAE;AAChB;AACA,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,QAAQ,EAAE,IAAI;AACtB,QAAQ,MAAM,EAAE,mBAAmB,GAAG;AACtC,UAAU,MAAM,EAAE;AAClB,YAAY,QAAQ,EAAE;AACtB;AACA,SAAS,GAAG,KAAK;AACjB;AACA,KAAK,CAAC;AACN,IAAI,MAAM,mBAAmB,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,QAAQ,MAAM;AAC9D,MAAM,GAAG,QAAQ;AACjB,MAAM,YAAY,EAAE,mBAAmB,GAAG,QAAQ,CAAC,MAAM,CAAC,QAAQ,GAAG,KAAK,CAAC;AAC3E,MAAM,MAAM,EAAE,KAAK;AACnB,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,IAAI,CAAC,mBAAmB,CAAC;AACpC,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC;AAC3D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,iCAAiC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9E;AACA;AACK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AAC5C,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;AACtC,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACnF,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AACtD,MAAM,IAAI,EAAE;AACZ,QAAQ,IAAI;AACZ,QAAQ,IAAI;AACZ,QAAQ,WAAW;AACnB,QAAQ,IAAI;AACZ,QAAQ,QAAQ;AAChB,QAAQ,KAAK,EAAE,KAAK,IAAI;AACxB;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC;AACzB,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC;AACzD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,gCAAgC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7E;AACA;;;;"}