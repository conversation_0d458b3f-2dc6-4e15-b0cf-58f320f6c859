{"version": 3, "file": "_server.ts-CcIEn6Iv.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/resume/profile/_profileId_/_server.ts.js"], "sourcesContent": ["import { p as prisma } from \"../../../../../../chunks/prisma.js\";\nconst GET = async ({ params, locals }) => {\n  const user = locals.user;\n  if (!user) return new Response(\"Unauthorized\", { status: 401 });\n  const profileId = params.profileId;\n  const profile = await prisma.profile.findUnique({\n    where: { id: profileId },\n    include: {\n      data: true,\n      documents: {\n        include: {\n          document: true\n        }\n      }\n    }\n  });\n  if (!profile) {\n    return new Response(\"Profile not found\", { status: 404 });\n  }\n  return new Response(\n    JSON.stringify({\n      documents: profile.documents,\n      profileData: profile.data\n    }),\n    {\n      headers: { \"Content-Type\": \"application/json\" }\n    }\n  );\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;AACK,MAAC,GAAG,GAAG,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK;AAC1C,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE,EAAE,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS;AACpC,EAAE,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;AAClD,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;AAC5B,IAAI,OAAO,EAAE;AACb,MAAM,IAAI,EAAE,IAAI;AAChB,MAAM,SAAS,EAAE;AACjB,QAAQ,OAAO,EAAE;AACjB,UAAU,QAAQ,EAAE;AACpB;AACA;AACA;AACA,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,OAAO,IAAI,QAAQ,CAAC,mBAAmB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7D;AACA,EAAE,OAAO,IAAI,QAAQ;AACrB,IAAI,IAAI,CAAC,SAAS,CAAC;AACnB,MAAM,SAAS,EAAE,OAAO,CAAC,SAAS;AAClC,MAAM,WAAW,EAAE,OAAO,CAAC;AAC3B,KAAK,CAAC;AACN,IAAI;AACJ,MAAM,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB;AACnD;AACA,GAAG;AACH;;;;"}