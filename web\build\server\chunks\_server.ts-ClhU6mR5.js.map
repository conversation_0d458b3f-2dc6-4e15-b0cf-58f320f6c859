{"version": 3, "file": "_server.ts-ClhU6mR5.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/dashboard/usage/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../chunks/index.js\";\nimport { v as verifySessionToken } from \"../../../../chunks/auth.js\";\nimport { getUserFeatureUsageWithPlanLimits } from \"../../../../chunks/feature-usage.js\";\nimport { p as prisma } from \"../../../../chunks/prisma.js\";\nimport { b as getPlanById } from \"../../../../chunks/plan-sync.js\";\nconst GET = async ({ cookies, url }) => {\n  const token = cookies.get(\"auth_token\");\n  if (!token) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  const userData = await verifySessionToken(token);\n  if (!userData?.id) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  try {\n    const dataType = url.searchParams.get(\"type\") || \"all\";\n    if (dataType === \"resume\") {\n      const startOfMonth = /* @__PURE__ */ new Date();\n      startOfMonth.setDate(1);\n      startOfMonth.setHours(0, 0, 0, 0);\n      const used = await prisma.documentSubmission.count({\n        where: {\n          userId: userData.id,\n          createdAt: {\n            gte: startOfMonth\n          }\n        }\n      });\n      const userPlan = await getPlanById(userData.role || \"free\");\n      const resumeScannerFeature = userPlan?.features.find((f) => f.featureId === \"resume_scanner\");\n      const resumeScansLimit = resumeScannerFeature?.limits?.find(\n        (l) => l.limitId === \"resume_scans_per_month\"\n      );\n      const limit = resumeScansLimit ? resumeScansLimit.value === \"unlimited\" ? null : Number(resumeScansLimit.value) : null;\n      const remaining = limit !== null ? Math.max(0, limit - used) : null;\n      return json({ used, limit, remaining });\n    } else if (dataType === \"features\") {\n      const usageData = await getUserFeatureUsageWithPlanLimits(userData.id);\n      return json(usageData);\n    } else {\n      const startOfMonth = /* @__PURE__ */ new Date();\n      startOfMonth.setDate(1);\n      startOfMonth.setHours(0, 0, 0, 0);\n      const used = await prisma.documentSubmission.count({\n        where: {\n          userId: userData.id,\n          createdAt: {\n            gte: startOfMonth\n          }\n        }\n      });\n      const userPlan = await getPlanById(userData.role || \"free\");\n      const resumeScannerFeature = userPlan?.features.find((f) => f.featureId === \"resume_scanner\");\n      const resumeScansLimit = resumeScannerFeature?.limits?.find(\n        (l) => l.limitId === \"resume_scans_per_month\"\n      );\n      const limit = resumeScansLimit ? resumeScansLimit.value === \"unlimited\" ? null : Number(resumeScansLimit.value) : null;\n      const remaining = limit !== null ? Math.max(0, limit - used) : null;\n      const usageData = await getUserFeatureUsageWithPlanLimits(userData.id);\n      return json({\n        resume: { used, limit, remaining },\n        features: usageData\n      });\n    }\n  } catch (error) {\n    console.error(\"Error in usage API:\", error);\n    return json(\n      {\n        error: error.message || \"An error occurred while fetching usage data\"\n      },\n      { status: 500 }\n    );\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAKK,MAAC,GAAG,GAAG,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK;AACxC,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE;AACd,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AAClD,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,QAAQ,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK;AAC1D,IAAI,IAAI,QAAQ,KAAK,QAAQ,EAAE;AAC/B,MAAM,MAAM,YAAY,mBAAmB,IAAI,IAAI,EAAE;AACrD,MAAM,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC;AAC7B,MAAM,YAAY,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACvC,MAAM,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC;AACzD,QAAQ,KAAK,EAAE;AACf,UAAU,MAAM,EAAE,QAAQ,CAAC,EAAE;AAC7B,UAAU,SAAS,EAAE;AACrB,YAAY,GAAG,EAAE;AACjB;AACA;AACA,OAAO,CAAC;AACR,MAAM,MAAM,QAAQ,GAAG,MAAM,WAAW,CAAC,QAAQ,CAAC,IAAI,IAAI,MAAM,CAAC;AACjE,MAAM,MAAM,oBAAoB,GAAG,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,KAAK,gBAAgB,CAAC;AACnG,MAAM,MAAM,gBAAgB,GAAG,oBAAoB,EAAE,MAAM,EAAE,IAAI;AACjE,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,KAAK;AAC7B,OAAO;AACP,MAAM,MAAM,KAAK,GAAG,gBAAgB,GAAG,gBAAgB,CAAC,KAAK,KAAK,WAAW,GAAG,IAAI,GAAG,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,GAAG,IAAI;AAC5H,MAAM,MAAM,SAAS,GAAG,KAAK,KAAK,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI;AACzE,MAAM,OAAO,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AAC7C,KAAK,MAAM,IAAI,QAAQ,KAAK,UAAU,EAAE;AACxC,MAAM,MAAM,SAAS,GAAG,MAAM,iCAAiC,CAAC,QAAQ,CAAC,EAAE,CAAC;AAC5E,MAAM,OAAO,IAAI,CAAC,SAAS,CAAC;AAC5B,KAAK,MAAM;AACX,MAAM,MAAM,YAAY,mBAAmB,IAAI,IAAI,EAAE;AACrD,MAAM,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC;AAC7B,MAAM,YAAY,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACvC,MAAM,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC;AACzD,QAAQ,KAAK,EAAE;AACf,UAAU,MAAM,EAAE,QAAQ,CAAC,EAAE;AAC7B,UAAU,SAAS,EAAE;AACrB,YAAY,GAAG,EAAE;AACjB;AACA;AACA,OAAO,CAAC;AACR,MAAM,MAAM,QAAQ,GAAG,MAAM,WAAW,CAAC,QAAQ,CAAC,IAAI,IAAI,MAAM,CAAC;AACjE,MAAM,MAAM,oBAAoB,GAAG,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,KAAK,gBAAgB,CAAC;AACnG,MAAM,MAAM,gBAAgB,GAAG,oBAAoB,EAAE,MAAM,EAAE,IAAI;AACjE,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,KAAK;AAC7B,OAAO;AACP,MAAM,MAAM,KAAK,GAAG,gBAAgB,GAAG,gBAAgB,CAAC,KAAK,KAAK,WAAW,GAAG,IAAI,GAAG,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,GAAG,IAAI;AAC5H,MAAM,MAAM,SAAS,GAAG,KAAK,KAAK,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI;AACzE,MAAM,MAAM,SAAS,GAAG,MAAM,iCAAiC,CAAC,QAAQ,CAAC,EAAE,CAAC;AAC5E,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,MAAM,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE;AAC1C,QAAQ,QAAQ,EAAE;AAClB,OAAO,CAAC;AACR;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC;AAC/C,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,KAAK,EAAE,KAAK,CAAC,OAAO,IAAI;AAChC,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;;;;"}