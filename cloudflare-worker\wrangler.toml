# Cloudflare Worker configuration for static assets proxy
name = "hirli-static-assets"
main = "static-assets-proxy.js"
compatibility_date = "2024-01-01"

# R2 bucket bindings
[[r2_buckets]]
binding = "COMPANY_LOGOS_BUCKET"
bucket_name = "hirli-company-logos"

[[r2_buckets]]
binding = "RESUMES_BUCKET"
bucket_name = "hirli-resume-files"

[[r2_buckets]]
binding = "USER_BUCKET"
bucket_name = "hirli-user-images"

# Environment variables (if needed)
[vars]
ENVIRONMENT = "production"

# Custom domain configuration (add your domain here)
# routes = [
#   { pattern = "static.yourdomain.com/*", custom_domain = true }
# ]
