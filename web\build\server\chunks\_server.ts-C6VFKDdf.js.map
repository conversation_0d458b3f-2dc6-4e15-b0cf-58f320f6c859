{"version": 3, "file": "_server.ts-C6VFKDdf.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/email/audiences/contacts/import/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../../chunks/index.js\";\nimport { Resend } from \"resend\";\nimport { l as logger } from \"../../../../../../../chunks/logger.js\";\nimport { parse } from \"csv-parse/sync\";\nconst resend = process.env.RESEND_API_KEY ? new Resend(process.env.RESEND_API_KEY) : null;\nasync function POST({ request }) {\n  try {\n    const formData = await request.formData();\n    const audienceId = formData.get(\"audienceId\");\n    const file = formData.get(\"file\");\n    if (!audienceId) {\n      return json({ error: \"Audience ID is required\" }, { status: 400 });\n    }\n    if (!file || !(file instanceof File)) {\n      return json({ error: \"CSV file is required\" }, { status: 400 });\n    }\n    const fileContent = await file.text();\n    const records = parse(fileContent, {\n      columns: true,\n      skip_empty_lines: true,\n      trim: true\n    });\n    if (records.length === 0) {\n      return json({ error: \"CSV file is empty\" }, { status: 400 });\n    }\n    const validRecords = records.filter((record) => {\n      return record.email && typeof record.email === \"string\" && record.email.includes(\"@\");\n    });\n    if (validRecords.length === 0) {\n      return json({ error: \"No valid email addresses found in CSV\" }, { status: 400 });\n    }\n    const batchSize = 100;\n    const batches = [];\n    for (let i = 0; i < validRecords.length; i += batchSize) {\n      batches.push(validRecords.slice(i, i + batchSize));\n    }\n    let importedCount = 0;\n    let failedCount = 0;\n    if (!resend) {\n      logger.warn(\"Resend API key not available, returning mock import response\");\n      return json({\n        success: true,\n        total: validRecords.length,\n        imported: validRecords.length,\n        failed: 0,\n        mock: true\n      });\n    }\n    for (const batch of batches) {\n      try {\n        const contacts = batch.map((record) => ({\n          email: record.email,\n          first_name: record.first_name || record.firstName || \"\",\n          last_name: record.last_name || record.lastName || \"\",\n          data: {\n            ...record,\n            email: void 0,\n            first_name: void 0,\n            firstName: void 0,\n            last_name: void 0,\n            lastName: void 0\n          }\n        }));\n        const response = await resend.contacts.createBatch({\n          audienceId: audienceId.toString(),\n          contacts\n        });\n        if (response.error) {\n          logger.error(\"Error importing contacts batch:\", response.error);\n          failedCount += batch.length;\n        } else {\n          importedCount += batch.length;\n        }\n      } catch (error) {\n        logger.error(\"Error importing contacts batch:\", error);\n        failedCount += batch.length;\n      }\n    }\n    return json({\n      success: true,\n      total: validRecords.length,\n      imported: importedCount,\n      failed: failedCount\n    });\n  } catch (error) {\n    logger.error(\"Error importing contacts:\", error);\n    return json({ error: \"Failed to import contacts\" }, { status: 500 });\n  }\n}\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;AAIA,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,IAAI;AACzF,eAAe,IAAI,CAAC,EAAE,OAAO,EAAE,EAAE;AACjC,EAAE,IAAI;AACN,IAAI,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,QAAQ,EAAE;AAC7C,IAAI,MAAM,UAAU,GAAG,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC;AACjD,IAAI,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC;AACrC,IAAI,IAAI,CAAC,UAAU,EAAE;AACrB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,YAAY,IAAI,CAAC,EAAE;AAC1C,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,sBAAsB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACrE;AACA,IAAI,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;AACzC,IAAI,MAAM,OAAO,GAAG,KAAK,CAAC,WAAW,EAAE;AACvC,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,gBAAgB,EAAE,IAAI;AAC5B,MAAM,IAAI,EAAE;AACZ,KAAK,CAAC;AACN,IAAI,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;AAC9B,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE;AACA,IAAI,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK;AACpD,MAAM,OAAO,MAAM,CAAC,KAAK,IAAI,OAAO,MAAM,CAAC,KAAK,KAAK,QAAQ,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC;AAC3F,KAAK,CAAC;AACN,IAAI,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;AACnC,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uCAAuC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtF;AACA,IAAI,MAAM,SAAS,GAAG,GAAG;AACzB,IAAI,MAAM,OAAO,GAAG,EAAE;AACtB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,IAAI,SAAS,EAAE;AAC7D,MAAM,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC;AACxD;AACA,IAAI,IAAI,aAAa,GAAG,CAAC;AACzB,IAAI,IAAI,WAAW,GAAG,CAAC;AACvB,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,MAAM,CAAC,IAAI,CAAC,8DAA8D,CAAC;AACjF,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,KAAK,EAAE,YAAY,CAAC,MAAM;AAClC,QAAQ,QAAQ,EAAE,YAAY,CAAC,MAAM;AACrC,QAAQ,MAAM,EAAE,CAAC;AACjB,QAAQ,IAAI,EAAE;AACd,OAAO,CAAC;AACR;AACA,IAAI,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE;AACjC,MAAM,IAAI;AACV,QAAQ,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,MAAM;AAChD,UAAU,KAAK,EAAE,MAAM,CAAC,KAAK;AAC7B,UAAU,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,SAAS,IAAI,EAAE;AACjE,UAAU,SAAS,EAAE,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,QAAQ,IAAI,EAAE;AAC9D,UAAU,IAAI,EAAE;AAChB,YAAY,GAAG,MAAM;AACrB,YAAY,KAAK,EAAE,KAAK,CAAC;AACzB,YAAY,UAAU,EAAE,KAAK,CAAC;AAC9B,YAAY,SAAS,EAAE,KAAK,CAAC;AAC7B,YAAY,SAAS,EAAE,KAAK,CAAC;AAC7B,YAAY,QAAQ,EAAE,KAAK;AAC3B;AACA,SAAS,CAAC,CAAC;AACX,QAAQ,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC;AAC3D,UAAU,UAAU,EAAE,UAAU,CAAC,QAAQ,EAAE;AAC3C,UAAU;AACV,SAAS,CAAC;AACV,QAAQ,IAAI,QAAQ,CAAC,KAAK,EAAE;AAC5B,UAAU,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,QAAQ,CAAC,KAAK,CAAC;AACzE,UAAU,WAAW,IAAI,KAAK,CAAC,MAAM;AACrC,SAAS,MAAM;AACf,UAAU,aAAa,IAAI,KAAK,CAAC,MAAM;AACvC;AACA,OAAO,CAAC,OAAO,KAAK,EAAE;AACtB,QAAQ,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC;AAC9D,QAAQ,WAAW,IAAI,KAAK,CAAC,MAAM;AACnC;AACA;AACA,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,KAAK,EAAE,YAAY,CAAC,MAAM;AAChC,MAAM,QAAQ,EAAE,aAAa;AAC7B,MAAM,MAAM,EAAE;AACd,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC;AACpD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,2BAA2B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA;;;;"}