{"version": 3, "file": "_server.ts-CFU7NsUV.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/auth/resend-verification/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { randomBytes } from \"crypto\";\nimport { R as RedisConnection } from \"../../../../../chunks/redis.js\";\nconst BASE_URL = process.env.PUBLIC_BASE_URL || \"http://localhost:5173\";\nconst TEST_EMAIL = \"<EMAIL>\";\nconst EMAIL_QUEUE_KEY = \"email:queue\";\nconst POST = async ({ request }) => {\n  const { email } = await request.json();\n  if (!email) {\n    return json({ error: \"Email is required\" }, { status: 400 });\n  }\n  const user = await prisma.user.findUnique({\n    where: { email }\n  });\n  if (!user) {\n    return json({ success: true });\n  }\n  try {\n    const prefs = typeof user.preferences === \"string\" ? JSON.parse(user.preferences) : user.preferences;\n    if (prefs.emailVerified === true) {\n      return json(\n        {\n          error: \"Email already verified\",\n          message: \"Your email is already verified. You can sign in to your account.\"\n        },\n        { status: 400 }\n      );\n    }\n    const RATE_LIMIT_MINUTES = 5;\n    if (prefs.verificationExpires) {\n      const lastVerificationTime = new Date(prefs.verificationExpires).getTime() - 1e3 * 60 * 60 * 24;\n      const timeSinceLastEmail = Date.now() - lastVerificationTime;\n      const minutesSinceLastEmail = Math.floor(timeSinceLastEmail / (1e3 * 60));\n      if (minutesSinceLastEmail < RATE_LIMIT_MINUTES) {\n        const timeRemaining = RATE_LIMIT_MINUTES - minutesSinceLastEmail;\n        return json(\n          {\n            error: \"Rate limit exceeded\",\n            message: `Please wait ${timeRemaining} minute${timeRemaining !== 1 ? \"s\" : \"\"} before requesting another verification email`\n          },\n          { status: 429 }\n        );\n      }\n    }\n  } catch (error) {\n    console.error(\"Failed to parse user preferences:\", error);\n  }\n  const verificationToken = randomBytes(32).toString(\"hex\");\n  const verificationExpires = new Date(Date.now() + 1e3 * 60 * 60 * 24);\n  try {\n    let prefs = {};\n    if (typeof user.preferences === \"string\") {\n      prefs = JSON.parse(user.preferences) || {};\n    } else if (user.preferences && typeof user.preferences === \"object\") {\n      prefs = { ...user.preferences };\n    }\n    prefs.emailVerified = false;\n    prefs.verificationToken = verificationToken;\n    prefs.verificationExpires = verificationExpires.toISOString();\n    await prisma.user.update({\n      where: { id: user.id },\n      data: {\n        preferences: prefs\n      }\n    });\n  } catch (error) {\n    console.error(\"Failed to update user preferences:\", error);\n    return json({ error: \"Failed to generate verification token\" }, { status: 500 });\n  }\n  const verificationUrl = `${BASE_URL}/auth/verify?token=${verificationToken}`;\n  console.log(`Verification URL for ${email}: ${verificationUrl}`);\n  try {\n    if (!RedisConnection) {\n      console.error(\"Redis client not available\");\n    } else {\n      const id = `email_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;\n      const emailJob = {\n        id,\n        template: \"verification\",\n        to: [email],\n        data: {\n          firstName: user.name || email.split(\"@\")[0],\n          verificationUrl,\n          expiresInMinutes: 60 * 24\n          // 24 hours\n        },\n        options: {\n          category: \"transactional\",\n          priority: 1,\n          // Highest priority\n          retries: 3,\n          tags: [{ name: \"action\", value: \"verification\" }]\n        },\n        createdAt: (/* @__PURE__ */ new Date()).toISOString()\n      };\n      await RedisConnection.zadd(\n        EMAIL_QUEUE_KEY,\n        1,\n        // Highest priority\n        JSON.stringify(emailJob)\n      );\n      console.log(`Verification email queued in Redis: ${id} (to ${email})`);\n      const SEND_TEST_COPIES = process.env.SEND_TEST_EMAIL_COPIES === \"true\";\n      if (SEND_TEST_COPIES && email.toLowerCase() !== TEST_EMAIL.toLowerCase()) {\n        const testEmailId = `email_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;\n        const testEmailJob = {\n          id: testEmailId,\n          template: \"verification\",\n          to: [TEST_EMAIL],\n          data: {\n            firstName: \"Test Copy\",\n            verificationUrl,\n            expiresInMinutes: 60 * 24\n            // 24 hours\n          },\n          options: {\n            category: \"transactional\",\n            priority: 1,\n            // Highest priority\n            retries: 3,\n            tags: [\n              { name: \"action\", value: \"verification\" },\n              { name: \"test\", value: \"true\" }\n            ]\n          },\n          createdAt: (/* @__PURE__ */ new Date()).toISOString()\n        };\n        await RedisConnection.zadd(\n          EMAIL_QUEUE_KEY,\n          1,\n          // Highest priority\n          JSON.stringify(testEmailJob)\n        );\n        console.log(`Test verification email queued in Redis: ${testEmailId} (to ${TEST_EMAIL})`);\n      }\n      await RedisConnection.publish(\"email:process\", \"process_now\");\n      console.log(\"Published process_now message to email:process channel\");\n    }\n  } catch (error) {\n    console.error(\"Failed to queue verification email:\", error);\n  }\n  return json({\n    success: true,\n    message: \"Verification email sent successfully\"\n  });\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;AAIA,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,uBAAuB;AACvE,MAAM,UAAU,GAAG,wCAAwC;AAC3D,MAAM,eAAe,GAAG,aAAa;AAChC,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK;AACpC,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACxC,EAAE,IAAI,CAAC,KAAK,EAAE;AACd,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAChE;AACA,EAAE,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAC5C,IAAI,KAAK,EAAE,EAAE,KAAK;AAClB,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;AAClC;AACA,EAAE,IAAI;AACN,IAAI,MAAM,KAAK,GAAG,OAAO,IAAI,CAAC,WAAW,KAAK,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,WAAW;AACxG,IAAI,IAAI,KAAK,CAAC,aAAa,KAAK,IAAI,EAAE;AACtC,MAAM,OAAO,IAAI;AACjB,QAAQ;AACR,UAAU,KAAK,EAAE,wBAAwB;AACzC,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ,EAAE,MAAM,EAAE,GAAG;AACrB,OAAO;AACP;AACA,IAAI,MAAM,kBAAkB,GAAG,CAAC;AAChC,IAAI,IAAI,KAAK,CAAC,mBAAmB,EAAE;AACnC,MAAM,MAAM,oBAAoB,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,OAAO,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACrG,MAAM,MAAM,kBAAkB,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,oBAAoB;AAClE,MAAM,MAAM,qBAAqB,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,IAAI,GAAG,GAAG,EAAE,CAAC,CAAC;AAC/E,MAAM,IAAI,qBAAqB,GAAG,kBAAkB,EAAE;AACtD,QAAQ,MAAM,aAAa,GAAG,kBAAkB,GAAG,qBAAqB;AACxE,QAAQ,OAAO,IAAI;AACnB,UAAU;AACV,YAAY,KAAK,EAAE,qBAAqB;AACxC,YAAY,OAAO,EAAE,CAAC,YAAY,EAAE,aAAa,CAAC,OAAO,EAAE,aAAa,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,6CAA6C;AACvI,WAAW;AACX,UAAU,EAAE,MAAM,EAAE,GAAG;AACvB,SAAS;AACT;AACA;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC;AAC7D;AACA,EAAE,MAAM,iBAAiB,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC;AAC3D,EAAE,MAAM,mBAAmB,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACvE,EAAE,IAAI;AACN,IAAI,IAAI,KAAK,GAAG,EAAE;AAClB,IAAI,IAAI,OAAO,IAAI,CAAC,WAAW,KAAK,QAAQ,EAAE;AAC9C,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE;AAChD,KAAK,MAAM,IAAI,IAAI,CAAC,WAAW,IAAI,OAAO,IAAI,CAAC,WAAW,KAAK,QAAQ,EAAE;AACzE,MAAM,KAAK,GAAG,EAAE,GAAG,IAAI,CAAC,WAAW,EAAE;AACrC;AACA,IAAI,KAAK,CAAC,aAAa,GAAG,KAAK;AAC/B,IAAI,KAAK,CAAC,iBAAiB,GAAG,iBAAiB;AAC/C,IAAI,KAAK,CAAC,mBAAmB,GAAG,mBAAmB,CAAC,WAAW,EAAE;AACjE,IAAI,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AAC7B,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;AAC5B,MAAM,IAAI,EAAE;AACZ,QAAQ,WAAW,EAAE;AACrB;AACA,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC;AAC9D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uCAAuC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpF;AACA,EAAE,MAAM,eAAe,GAAG,CAAC,EAAE,QAAQ,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,CAAC;AAC9E,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,KAAK,CAAC,EAAE,EAAE,eAAe,CAAC,CAAC,CAAC;AAClE,EAAE,IAAI;AACN,IAAI,IAAI,CAAC,eAAe,EAAE;AAC1B,MAAM,OAAO,CAAC,KAAK,CAAC,4BAA4B,CAAC;AACjD,KAAK,MAAM;AACX,MAAM,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AACrF,MAAM,MAAM,QAAQ,GAAG;AACvB,QAAQ,EAAE;AACV,QAAQ,QAAQ,EAAE,cAAc;AAChC,QAAQ,EAAE,EAAE,CAAC,KAAK,CAAC;AACnB,QAAQ,IAAI,EAAE;AACd,UAAU,SAAS,EAAE,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACrD,UAAU,eAAe;AACzB,UAAU,gBAAgB,EAAE,EAAE,GAAG;AACjC;AACA,SAAS;AACT,QAAQ,OAAO,EAAE;AACjB,UAAU,QAAQ,EAAE,eAAe;AACnC,UAAU,QAAQ,EAAE,CAAC;AACrB;AACA,UAAU,OAAO,EAAE,CAAC;AACpB,UAAU,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,cAAc,EAAE;AAC1D,SAAS;AACT,QAAQ,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC3D,OAAO;AACP,MAAM,MAAM,eAAe,CAAC,IAAI;AAChC,QAAQ,eAAe;AACvB,QAAQ,CAAC;AACT;AACA,QAAQ,IAAI,CAAC,SAAS,CAAC,QAAQ;AAC/B,OAAO;AACP,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,oCAAoC,EAAE,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;AAC5E,MAAM,MAAM,gBAAgB,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,KAAK,MAAM;AAC5E,MAAM,IAAI,gBAAgB,IAAI,KAAK,CAAC,WAAW,EAAE,KAAK,UAAU,CAAC,WAAW,EAAE,EAAE;AAChF,QAAQ,MAAM,WAAW,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AAChG,QAAQ,MAAM,YAAY,GAAG;AAC7B,UAAU,EAAE,EAAE,WAAW;AACzB,UAAU,QAAQ,EAAE,cAAc;AAClC,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC;AAC1B,UAAU,IAAI,EAAE;AAChB,YAAY,SAAS,EAAE,WAAW;AAClC,YAAY,eAAe;AAC3B,YAAY,gBAAgB,EAAE,EAAE,GAAG;AACnC;AACA,WAAW;AACX,UAAU,OAAO,EAAE;AACnB,YAAY,QAAQ,EAAE,eAAe;AACrC,YAAY,QAAQ,EAAE,CAAC;AACvB;AACA,YAAY,OAAO,EAAE,CAAC;AACtB,YAAY,IAAI,EAAE;AAClB,cAAc,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,cAAc,EAAE;AACvD,cAAc,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM;AAC3C;AACA,WAAW;AACX,UAAU,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC7D,SAAS;AACT,QAAQ,MAAM,eAAe,CAAC,IAAI;AAClC,UAAU,eAAe;AACzB,UAAU,CAAC;AACX;AACA,UAAU,IAAI,CAAC,SAAS,CAAC,YAAY;AACrC,SAAS;AACT,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,yCAAyC,EAAE,WAAW,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;AACjG;AACA,MAAM,MAAM,eAAe,CAAC,OAAO,CAAC,eAAe,EAAE,aAAa,CAAC;AACnE,MAAM,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC;AAC3E;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC;AAC/D;AACA,EAAE,OAAO,IAAI,CAAC;AACd,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,OAAO,EAAE;AACb,GAAG,CAAC;AACJ;;;;"}