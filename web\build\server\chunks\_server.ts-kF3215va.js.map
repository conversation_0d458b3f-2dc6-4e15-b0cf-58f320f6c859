{"version": 3, "file": "_server.ts-kF3215va.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/applications/status/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { l as logger } from \"../../../../../chunks/logger.js\";\nconst GET = async () => {\n  try {\n    const status = {\n      operational: true,\n      dailyApplications: 180,\n      successRate: 97.8,\n      // percentage\n      averageProcessingTime: 3.2\n      // seconds\n    };\n    return json({\n      ...status,\n      timestamp: (/* @__PURE__ */ new Date()).toISOString()\n    });\n  } catch (error) {\n    logger.error(\"Error checking application system status:\", error);\n    return json(\n      {\n        error: \"Failed to check application system status\",\n        timestamp: (/* @__PURE__ */ new Date()).toISOString()\n      },\n      { status: 500 }\n    );\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;AAEK,MAAC,GAAG,GAAG,YAAY;AACxB,EAAE,IAAI;AACN,IAAI,MAAM,MAAM,GAAG;AACnB,MAAM,WAAW,EAAE,IAAI;AACvB,MAAM,iBAAiB,EAAE,GAAG;AAC5B,MAAM,WAAW,EAAE,IAAI;AACvB;AACA,MAAM,qBAAqB,EAAE;AAC7B;AACA,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,GAAG,MAAM;AACf,MAAM,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AACzD,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC;AACpE,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,KAAK,EAAE,2CAA2C;AAC1D,QAAQ,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC3D,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;;;;"}