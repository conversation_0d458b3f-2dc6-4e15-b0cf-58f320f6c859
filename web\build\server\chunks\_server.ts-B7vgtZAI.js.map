{"version": 3, "file": "_server.ts-B7vgtZAI.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/ai/resume/suggestions/_id_/apply/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../../../../../../chunks/auth.js\";\nconst POST = async ({ params, cookies }) => {\n  try {\n    const token = cookies.get(\"auth_token\");\n    if (!token) {\n      return json({ error: \"Unauthorized\" }, { status: 401 });\n    }\n    const tokenData = verifySessionToken(token);\n    if (!tokenData || !tokenData.id) {\n      return json({ error: \"Invalid token\" }, { status: 401 });\n    }\n    const userId = tokenData.id;\n    const suggestionId = params.id;\n    if (!suggestionId) {\n      return json({ error: \"Missing suggestion ID\" }, { status: 400 });\n    }\n    const suggestion = await prisma.resumeAISuggestion.findFirst({\n      where: {\n        id: suggestionId,\n        userId\n      },\n      include: {\n        resume: true\n      }\n    });\n    if (!suggestion) {\n      return json({ error: \"Suggestion not found or access denied\" }, { status: 404 });\n    }\n    await prisma.resumeAISuggestion.update({\n      where: { id: suggestionId },\n      data: { applied: true }\n    });\n    return json({ success: true, message: \"Suggestion applied successfully\" });\n  } catch (error) {\n    console.error(\"Error applying resume suggestion:\", error);\n    return json({ error: \"Internal server error\" }, { status: 500 });\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;AAGK,MAAC,IAAI,GAAG,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK;AAC5C,EAAE,IAAI;AACN,IAAI,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AAC3C,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7D;AACA,IAAI,MAAM,SAAS,GAAG,kBAAkB,CAAC,KAAK,CAAC;AAC/C,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE;AACrC,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9D;AACA,IAAI,MAAM,MAAM,GAAG,SAAS,CAAC,EAAE;AAC/B,IAAI,MAAM,YAAY,GAAG,MAAM,CAAC,EAAE;AAClC,IAAI,IAAI,CAAC,YAAY,EAAE;AACvB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtE;AACA,IAAI,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,kBAAkB,CAAC,SAAS,CAAC;AACjE,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE,YAAY;AACxB,QAAQ;AACR,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,MAAM,EAAE;AAChB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,UAAU,EAAE;AACrB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uCAAuC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtF;AACA,IAAI,MAAM,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC;AAC3C,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE;AACjC,MAAM,IAAI,EAAE,EAAE,OAAO,EAAE,IAAI;AAC3B,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,iCAAiC,EAAE,CAAC;AAC9E,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC;AAC7D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpE;AACA;;;;"}