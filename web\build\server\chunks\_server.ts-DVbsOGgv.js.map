{"version": 3, "file": "_server.ts-DVbsOGgv.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/_server.ts.js"], "sourcesContent": ["import \"../../../chunks/index.js\";\nimport { broadcastMessage } from \"../../../chunks/websocket.js\";\nconst GET = async () => {\n  return new Response(\n    JSON.stringify({\n      status: \"WebSocket server is running\",\n      timestamp: (/* @__PURE__ */ new Date()).toISOString()\n    }),\n    {\n      status: 200,\n      headers: {\n        \"Content-Type\": \"application/json\"\n      }\n    }\n  );\n};\nconst POST = async ({ request }) => {\n  try {\n    const data = await request.json();\n    broadcastMessage(data);\n    return new Response(JSON.stringify({ success: true, message: \"Broadcast sent\" }), {\n      status: 200,\n      headers: {\n        \"Content-Type\": \"application/json\"\n      }\n    });\n  } catch (error) {\n    return new Response(\n      JSON.stringify({\n        success: false,\n        error: error.message\n      }),\n      {\n        status: 400,\n        headers: {\n          \"Content-Type\": \"application/json\"\n        }\n      }\n    );\n  }\n};\nexport {\n  GET,\n  POST\n};\n"], "names": [], "mappings": ";;;;AAEK,MAAC,GAAG,GAAG,YAAY;AACxB,EAAE,OAAO,IAAI,QAAQ;AACrB,IAAI,IAAI,CAAC,SAAS,CAAC;AACnB,MAAM,MAAM,EAAE,6BAA6B;AAC3C,MAAM,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AACzD,KAAK,CAAC;AACN,IAAI;AACJ,MAAM,MAAM,EAAE,GAAG;AACjB,MAAM,OAAO,EAAE;AACf,QAAQ,cAAc,EAAE;AACxB;AACA;AACA,GAAG;AACH;AACK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK;AACpC,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,gBAAgB,CAAC,IAAI,CAAC;AAC1B,IAAI,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,gBAAgB,EAAE,CAAC,EAAE;AACtF,MAAM,MAAM,EAAE,GAAG;AACjB,MAAM,OAAO,EAAE;AACf,QAAQ,cAAc,EAAE;AACxB;AACA,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,IAAI,QAAQ;AACvB,MAAM,IAAI,CAAC,SAAS,CAAC;AACrB,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,KAAK,EAAE,KAAK,CAAC;AACrB,OAAO,CAAC;AACR,MAAM;AACN,QAAQ,MAAM,EAAE,GAAG;AACnB,QAAQ,OAAO,EAAE;AACjB,UAAU,cAAc,EAAE;AAC1B;AACA;AACA,KAAK;AACL;AACA;;;;"}