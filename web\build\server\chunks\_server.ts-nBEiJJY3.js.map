{"version": 3, "file": "_server.ts-nBEiJJY3.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/email/audiences/_id_/contacts/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../../chunks/index.js\";\nimport { Resend } from \"resend\";\nimport { l as logger } from \"../../../../../../../chunks/logger.js\";\nconst resend = process.env.RESEND_API_KEY ? new Resend(process.env.RESEND_API_KEY) : null;\nasync function GET({ params }) {\n  try {\n    const { id } = params;\n    if (!id) {\n      return json({ error: \"Audience ID is required\" }, { status: 400 });\n    }\n    if (!resend) {\n      logger.warn(\"Resend API key not available, returning mock contacts data\");\n      return json([\n        { id: \"mock-1\", email: \"<EMAIL>\", first_name: \"Test\", last_name: \"User\" },\n        { id: \"mock-2\", email: \"<EMAIL>\", first_name: \"<PERSON><PERSON>\", last_name: \"<PERSON>\" }\n      ]);\n    }\n    const response = await resend.contacts.list({ audienceId: id });\n    if (response.error) {\n      logger.error(\"Error fetching audience contacts:\", response.error);\n      return json({ error: response.error.message }, { status: 500 });\n    }\n    return json(response.data || []);\n  } catch (error) {\n    logger.error(\"Error fetching audience contacts:\", error);\n    return json({ error: \"Failed to fetch audience contacts\" }, { status: 500 });\n  }\n}\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;AAGA,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,IAAI;AACzF,eAAe,GAAG,CAAC,EAAE,MAAM,EAAE,EAAE;AAC/B,EAAE,IAAI;AACN,IAAI,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;AACzB,IAAI,IAAI,CAAC,EAAE,EAAE;AACb,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,MAAM,CAAC,IAAI,CAAC,4DAA4D,CAAC;AAC/E,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,mBAAmB,EAAE,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE;AAC3F,QAAQ,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,mBAAmB,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS;AAC9F,OAAO,CAAC;AACR;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC;AACnE,IAAI,IAAI,QAAQ,CAAC,KAAK,EAAE;AACxB,MAAM,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,QAAQ,CAAC,KAAK,CAAC;AACvE,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACrE;AACA,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE,CAAC;AACpC,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC;AAC5D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mCAAmC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAChF;AACA;;;;"}