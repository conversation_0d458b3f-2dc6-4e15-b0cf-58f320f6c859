{"version": 3, "file": "_server.ts-Btkq1kQh.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/auth/forgot-password/_server.ts.js"], "sourcesContent": ["import { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { j as json } from \"../../../../../chunks/index.js\";\nimport { s as sendResetPasswordEmail } from \"../../../../../chunks/email.js\";\nimport { randomBytes } from \"crypto\";\nconst POST = async ({ request }) => {\n  const { email } = await request.json();\n  if (!email) {\n    return new Response(\"Email is required\", { status: 400 });\n  }\n  const user = await prisma.user.findUnique({\n    where: { email }\n  });\n  if (!user) {\n    return json({ success: true });\n  }\n  const resetToken = randomBytes(32).toString(\"hex\");\n  const expires = new Date(Date.now() + 1e3 * 60 * 30);\n  await prisma.passwordResetToken.upsert({\n    where: { id: user.id },\n    update: { token: resetToken, expiresAt: expires },\n    create: {\n      id: user.id,\n      email: user.email,\n      token: resetToken,\n      expiresAt: expires\n    }\n  });\n  try {\n    const resetUrl = `${process.env.PUBLIC_BASE_URL || \"http://localhost:5173\"}/auth/reset-password?token=${resetToken}`;\n    const testEmail = \"<EMAIL>\";\n    await sendResetPasswordEmail(user.email, resetUrl, {\n      firstName: user.name || user.email.split(\"@\")[0],\n      // Use name or part of email\n      expiresInMinutes: 30,\n      token: resetToken\n    });\n    if (user.email.toLowerCase() !== testEmail.toLowerCase()) {\n      console.log(`Sending test password reset email to ${testEmail}`);\n      await sendResetPasswordEmail(testEmail, resetUrl, {\n        firstName: \"Christopher (Test Copy)\",\n        expiresInMinutes: 30,\n        token: resetToken\n      });\n    }\n  } catch (err) {\n    console.error(\"Failed to send reset email:\", err);\n    return new Response(\"Failed to send reset email\", { status: 500 });\n  }\n  return json({ success: true });\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;AAIK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK;AACpC,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACxC,EAAE,IAAI,CAAC,KAAK,EAAE;AACd,IAAI,OAAO,IAAI,QAAQ,CAAC,mBAAmB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7D;AACA,EAAE,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAC5C,IAAI,KAAK,EAAE,EAAE,KAAK;AAClB,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;AAClC;AACA,EAAE,MAAM,UAAU,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC;AACpD,EAAE,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;AACtD,EAAE,MAAM,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC;AACzC,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;AAC1B,IAAI,MAAM,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE;AACrD,IAAI,MAAM,EAAE;AACZ,MAAM,EAAE,EAAE,IAAI,CAAC,EAAE;AACjB,MAAM,KAAK,EAAE,IAAI,CAAC,KAAK;AACvB,MAAM,KAAK,EAAE,UAAU;AACvB,MAAM,SAAS,EAAE;AACjB;AACA,GAAG,CAAC;AACJ,EAAE,IAAI;AACN,IAAI,MAAM,QAAQ,GAAG,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,uBAAuB,CAAC,2BAA2B,EAAE,UAAU,CAAC,CAAC;AACxH,IAAI,MAAM,SAAS,GAAG,wCAAwC;AAC9D,IAAI,MAAM,sBAAsB,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,EAAE;AACvD,MAAM,SAAS,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACtD;AACA,MAAM,gBAAgB,EAAE,EAAE;AAC1B,MAAM,KAAK,EAAE;AACb,KAAK,CAAC;AACN,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,SAAS,CAAC,WAAW,EAAE,EAAE;AAC9D,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,qCAAqC,EAAE,SAAS,CAAC,CAAC,CAAC;AACtE,MAAM,MAAM,sBAAsB,CAAC,SAAS,EAAE,QAAQ,EAAE;AACxD,QAAQ,SAAS,EAAE,yBAAyB;AAC5C,QAAQ,gBAAgB,EAAE,EAAE;AAC5B,QAAQ,KAAK,EAAE;AACf,OAAO,CAAC;AACR;AACA,GAAG,CAAC,OAAO,GAAG,EAAE;AAChB,IAAI,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,GAAG,CAAC;AACrD,IAAI,OAAO,IAAI,QAAQ,CAAC,4BAA4B,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtE;AACA,EAAE,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;AAChC;;;;"}