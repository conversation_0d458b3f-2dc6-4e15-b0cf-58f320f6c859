#!/usr/bin/env tsx
// cron/scripts/checkExperienceRequirements.ts - Check and update job experience requirements

import { logger } from "../utils/logger";
import { getPrismaClient } from "../utils/prismaClient";
import {
  extractYearsOfExperience,
  getMaxYearsOfExperience,
} from "../lib/search/extractJobRequirements";

interface JobUpdateResult {
  id: string;
  title: string;
  company: string;
  previousExperience: number | null;
  newExperience: number;
  updated: boolean;
}

async function checkExperienceRequirements() {
  logger.info("🚀 Starting experience requirements check");

  const prisma = await getPrismaClient("cron");

  try {
    // Get jobs that have descriptions for analysis
    const jobs = await prisma.jobListing.findMany({
      where: {
        description: { not: null },
        isActive: true,
      },
      select: {
        id: true,
        title: true,
        description: true,
        company: true,
      },
      take: 100, // Process in batches
    });

    logger.info(
      `📊 Found ${jobs.length} jobs to check for experience requirements`
    );

    if (jobs.length === 0) {
      logger.info("✅ No jobs need experience requirements updates");
      return;
    }

    const results: JobUpdateResult[] = [];
    let updated = 0;
    let skipped = 0;
    let errors = 0;

    for (const job of jobs) {
      try {
        if (!job.description) {
          logger.warn(`⚠️ Job ${job.id} has no description, skipping`);
          skipped++;
          continue;
        }

        // Extract experience requirements from job description
        const experienceRequirements = extractYearsOfExperience(
          job.description
        );
        const maxExperience = getMaxYearsOfExperience(experienceRequirements);

        const result: JobUpdateResult = {
          id: job.id,
          title: job.title,
          company: job.company || "Unknown",
          previousExperience: 0, // Since we don't have this field in the schema
          newExperience: maxExperience,
          updated: false,
        };

        // For now, just log the extracted experience requirements
        // TODO: Add experienceRequired field to jobListing schema if needed
        logger.info(
          `📊 Analyzed "${job.title}" at ${job.company}: ${maxExperience} years experience required`
        );

        updated++;
        result.updated = true;

        results.push(result);

        // Add a small delay to avoid overwhelming the database
        await new Promise((resolve) => setTimeout(resolve, 10));
      } catch (error) {
        errors++;
        logger.error(`❌ Error processing job ${job.id}:`, error);
      }
    }

    // Summary
    logger.info(`📊 Experience requirements check completed:`);
    logger.info(`   • Jobs processed: ${jobs.length}`);
    logger.info(`   • Jobs updated: ${updated}`);
    logger.info(`   • Jobs skipped: ${skipped}`);
    logger.info(`   • Errors: ${errors}`);

    // Log some statistics
    if (results.length > 0) {
      const experienceStats = results.reduce(
        (acc, result) => {
          const exp = result.newExperience;
          acc[exp] = (acc[exp] || 0) + 1;
          return acc;
        },
        {} as Record<number, number>
      );

      logger.info(`📊 Experience distribution:`);
      Object.entries(experienceStats)
        .sort(([a], [b]) => parseInt(a) - parseInt(b))
        .forEach(([years, count]) => {
          logger.info(`   • ${years} years: ${count} jobs`);
        });
    }
  } catch (error) {
    logger.error("❌ Error in experience requirements check:", error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
checkExperienceRequirements()
  .then(() => {
    logger.info("✅ Experience requirements check completed successfully");
    process.exit(0);
  })
  .catch((error) => {
    logger.error("❌ Experience requirements check failed:", error);
    process.exit(1);
  });

export { checkExperienceRequirements };
