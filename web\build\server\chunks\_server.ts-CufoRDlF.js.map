{"version": 3, "file": "_server.ts-CufoRDlF.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/email/audiences/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { Resend } from \"resend\";\nimport { l as logger } from \"../../../../../chunks/logger.js\";\nimport { d as private_env } from \"../../../../../chunks/shared-server.js\";\nconst resend = private_env.RESEND_API_KEY ? new Resend(private_env.RESEND_API_KEY) : null;\nasync function GET() {\n  try {\n    if (!resend) {\n      logger.warn(\"Resend API key not configured, returning sample audience data\");\n      return json([\n        {\n          id: \"sample-audience-1\",\n          name: \"Sample Audience\",\n          created_at: (/* @__PURE__ */ new Date()).toISOString()\n        }\n      ]);\n    }\n    const response = await resend.audiences.list();\n    if (response.error) {\n      logger.error(\"Error fetching audiences:\", response.error);\n      return json({ error: response.error.message }, { status: 500 });\n    }\n    if (response.data && Array.isArray(response.data)) {\n      return json(response.data);\n    } else if (response.data && response.data.data && Array.isArray(response.data.data)) {\n      return json(response.data.data);\n    } else {\n      return json([]);\n    }\n  } catch (error) {\n    logger.error(\"Error fetching audiences:\", error);\n    return json({ error: \"Failed to fetch audiences\" }, { status: 500 });\n  }\n}\nasync function POST({ request }) {\n  try {\n    const body = await request.json();\n    const { name } = body;\n    if (!name) {\n      return json({ error: \"Name is required\" }, { status: 400 });\n    }\n    if (!resend) {\n      logger.warn(\"Resend API key not configured, creating sample audience\");\n      return json({\n        id: `sample-audience-${Date.now()}`,\n        name,\n        created_at: (/* @__PURE__ */ new Date()).toISOString()\n      }, { status: 201 });\n    }\n    const response = await resend.audiences.create({ name });\n    if (response.error) {\n      logger.error(\"Error creating audience:\", response.error);\n      return json({ error: response.error.message }, { status: 500 });\n    }\n    if (response.data && typeof response.data === \"object\") {\n      if (response.data.data && typeof response.data.data === \"object\") {\n        return json(response.data.data, { status: 201 });\n      } else {\n        return json(response.data, { status: 201 });\n      }\n    } else {\n      return json({ error: \"Unexpected response format\" }, { status: 500 });\n    }\n  } catch (error) {\n    logger.error(\"Error creating audience:\", error);\n    return json({ error: \"Failed to create audience\" }, { status: 500 });\n  }\n}\nasync function DELETE({ url }) {\n  try {\n    const id = url.searchParams.get(\"id\");\n    if (!id) {\n      return json({ error: \"Audience ID is required\" }, { status: 400 });\n    }\n    if (!resend) {\n      logger.warn(\"Resend API key not configured, simulating audience deletion\");\n      return json({ success: true });\n    }\n    const response = await resend.audiences.remove(id);\n    if (response.error) {\n      logger.error(\"Error deleting audience:\", response.error);\n      return json({ error: response.error.message }, { status: 500 });\n    }\n    return json({ success: true });\n  } catch (error) {\n    logger.error(\"Error deleting audience:\", error);\n    return json({ error: \"Failed to delete audience\" }, { status: 500 });\n  }\n}\nexport {\n  DELETE,\n  GET,\n  POST\n};\n"], "names": [], "mappings": ";;;;;AAIA,MAAM,MAAM,GAAG,WAAW,CAAC,cAAc,GAAG,IAAI,MAAM,CAAC,WAAW,CAAC,cAAc,CAAC,GAAG,IAAI;AACzF,eAAe,GAAG,GAAG;AACrB,EAAE,IAAI;AACN,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,MAAM,CAAC,IAAI,CAAC,+DAA+D,CAAC;AAClF,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ;AACR,UAAU,EAAE,EAAE,mBAAmB;AACjC,UAAU,IAAI,EAAE,iBAAiB;AACjC,UAAU,UAAU,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC9D;AACA,OAAO,CAAC;AACR;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE;AAClD,IAAI,IAAI,QAAQ,CAAC,KAAK,EAAE;AACxB,MAAM,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,QAAQ,CAAC,KAAK,CAAC;AAC/D,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACrE;AACA,IAAI,IAAI,QAAQ,CAAC,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AACvD,MAAM,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AAChC,KAAK,MAAM,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AACzF,MAAM,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;AACrC,KAAK,MAAM;AACX,MAAM,OAAO,IAAI,CAAC,EAAE,CAAC;AACrB;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC;AACpD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,2BAA2B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA;AACA,eAAe,IAAI,CAAC,EAAE,OAAO,EAAE,EAAE;AACjC,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI;AACzB,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,kBAAkB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE;AACA,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,MAAM,CAAC,IAAI,CAAC,yDAAyD,CAAC;AAC5E,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,EAAE,EAAE,CAAC,gBAAgB,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;AAC3C,QAAQ,IAAI;AACZ,QAAQ,UAAU,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC5D,OAAO,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzB;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC;AAC5D,IAAI,IAAI,QAAQ,CAAC,KAAK,EAAE;AACxB,MAAM,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,QAAQ,CAAC,KAAK,CAAC;AAC9D,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACrE;AACA,IAAI,IAAI,QAAQ,CAAC,IAAI,IAAI,OAAO,QAAQ,CAAC,IAAI,KAAK,QAAQ,EAAE;AAC5D,MAAM,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;AACxE,QAAQ,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxD,OAAO,MAAM;AACb,QAAQ,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnD;AACA,KAAK,MAAM;AACX,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,4BAA4B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3E;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC;AACnD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,2BAA2B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA;AACA,eAAe,MAAM,CAAC,EAAE,GAAG,EAAE,EAAE;AAC/B,EAAE,IAAI;AACN,IAAI,MAAM,EAAE,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC;AACzC,IAAI,IAAI,CAAC,EAAE,EAAE;AACb,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,MAAM,CAAC,IAAI,CAAC,6DAA6D,CAAC;AAChF,MAAM,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;AACpC;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;AACtD,IAAI,IAAI,QAAQ,CAAC,KAAK,EAAE;AACxB,MAAM,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,QAAQ,CAAC,KAAK,CAAC;AAC9D,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACrE;AACA,IAAI,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;AAClC,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC;AACnD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,2BAA2B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA;;;;"}