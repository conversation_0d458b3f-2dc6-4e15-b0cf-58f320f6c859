{"version": 3, "file": "_page.svelte-z1RaVAed.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/legal/_slug_/_page.svelte.js"], "sourcesContent": ["import { V as escape_html, N as bind_props, y as pop, w as push } from \"../../../../chunks/index3.js\";\nimport { S as SEO } from \"../../../../chunks/SEO.js\";\nimport { P as PortableText } from \"../../../../chunks/PortableText.js\";\nfunction _page($$payload, $$props) {\n  push();\n  let data = $$props[\"data\"];\n  const { legalPage } = data;\n  const formattedDate = legalPage?.updatedAt ? new Date(legalPage.updatedAt).toLocaleDateString(\"en-US\", {\n    month: \"long\",\n    day: \"numeric\",\n    year: \"numeric\"\n  }) : null;\n  SEO($$payload, {\n    title: `${legalPage.title} | Hirli`,\n    description: legalPage.description,\n    keywords: `${legalPage.title.toLowerCase()}, legal, Hirli`\n  });\n  $$payload.out += `<!----> <div class=\"max-w-none\"><h1 class=\"mb-4 text-2xl font-bold\">${escape_html(legalPage.title)}</h1> `;\n  if (formattedDate) {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<p class=\"mb-6 text-gray-500\">Last updated: ${escape_html(formattedDate)}</p>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--> `;\n  if (legalPage.content) {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<div class=\"prose max-w-none\">`;\n    PortableText($$payload, { value: legalPage.content });\n    $$payload.out += `<!----></div>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<div class=\"prose max-w-none\"><p class=\"mb-6 rounded-lg border border-amber-200 bg-amber-50 p-4 text-amber-700\">This content is not yet available in the CMS. Please add content for this page in Sanity.</p> <p>This is a placeholder for the ${escape_html(legalPage.title)} page. The actual content should be added in the\n        Sanity CMS.</p> <p>To add content to this page:</p> <ol><li>Go to your Sanity Studio</li> <li>Find or create a page with the slug \"${escape_html(legalPage.slug)}\"</li> <li>Add your content using the rich text editor</li> <li>Publish the changes</li></ol></div>`;\n  }\n  $$payload.out += `<!--]--></div>`;\n  bind_props($$props, { data });\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;AAGA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC;AAC5B,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI;AAC5B,EAAE,MAAM,aAAa,GAAG,SAAS,EAAE,SAAS,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,kBAAkB,CAAC,OAAO,EAAE;AACzG,IAAI,KAAK,EAAE,MAAM;AACjB,IAAI,GAAG,EAAE,SAAS;AAClB,IAAI,IAAI,EAAE;AACV,GAAG,CAAC,GAAG,IAAI;AACX,EAAE,GAAG,CAAC,SAAS,EAAE;AACjB,IAAI,KAAK,EAAE,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC;AACvC,IAAI,WAAW,EAAE,SAAS,CAAC,WAAW;AACtC,IAAI,QAAQ,EAAE,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,cAAc;AAC7D,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,oEAAoE,EAAE,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AAC9H,EAAE,IAAI,aAAa,EAAE;AACrB,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,4CAA4C,EAAE,WAAW,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC;AACpG,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AAC9B,EAAE,IAAI,SAAS,CAAC,OAAO,EAAE;AACzB,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,8BAA8B,CAAC;AACrD,IAAI,YAAY,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,SAAS,CAAC,OAAO,EAAE,CAAC;AACzD,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACpC,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,+OAA+O,EAAE,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AACpS,2IAA2I,EAAE,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,mGAAmG,CAAC;AAC7Q;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACnC,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,CAAC;AAC/B,EAAE,GAAG,EAAE;AACP;;;;"}