{"version": 3, "file": "_server.ts-BFBGDKLn.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/admin/users/_userId_/plan/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../../../../../chunks/auth.js\";\nimport { b as getPlanById } from \"../../../../../../../chunks/plan-sync.js\";\nconst POST = async ({ cookies, params, request }) => {\n  const token = cookies.get(\"auth_token\");\n  if (!token) return new Response(\"Unauthorized\", { status: 401 });\n  const userData = verifySessionToken(token);\n  if (!userData?.id) return new Response(\"Unauthorized\", { status: 401 });\n  const adminUser = await prisma.user.findUnique({\n    where: { id: userData.id },\n    select: { isAdmin: true, role: true }\n  });\n  if (!adminUser || !adminUser.isAdmin && adminUser.role !== \"admin\") {\n    return new Response(\"Unauthorized\", { status: 401 });\n  }\n  const { userId } = params;\n  if (!userId) {\n    return new Response(\"User ID is required\", { status: 400 });\n  }\n  try {\n    const { planId } = await request.json();\n    if (!planId) {\n      return new Response(\"Plan ID is required\", { status: 400 });\n    }\n    const plan = await getPlanById(planId);\n    if (!plan && planId !== \"free\") {\n      return new Response(\"Invalid plan ID\", { status: 400 });\n    }\n    const updatedUser = await prisma.user.update({\n      where: { id: userId },\n      data: { role: planId }\n    });\n    await prisma.userActivityLog.create({\n      data: {\n        userId,\n        action: \"PLAN_CHANGED\",\n        details: JSON.stringify({\n          oldPlan: updatedUser.role,\n          newPlan: planId,\n          changedBy: userData.id,\n          changedByAdmin: true\n        })\n      }\n    });\n    return json({\n      success: true,\n      message: `User plan updated to ${planId}`,\n      user: {\n        id: updatedUser.id,\n        email: updatedUser.email,\n        role: updatedUser.role\n      }\n    });\n  } catch (error) {\n    console.error(\"Error updating user plan:\", error);\n    return new Response(`Failed to update user plan: ${error.message}`, { status: 500 });\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAIK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK;AACrD,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE,EAAE,MAAM,QAAQ,GAAG,kBAAkB,CAAC,KAAK,CAAC;AAC5C,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE,EAAE,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AACjD,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE;AAC9B,IAAI,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AACvC,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,OAAO,IAAI,SAAS,CAAC,IAAI,KAAK,OAAO,EAAE;AACtE,IAAI,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxD;AACA,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;AAC3B,EAAE,IAAI,CAAC,MAAM,EAAE;AACf,IAAI,OAAO,IAAI,QAAQ,CAAC,qBAAqB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC/D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AAC3C,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,OAAO,IAAI,QAAQ,CAAC,qBAAqB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,WAAW,CAAC,MAAM,CAAC;AAC1C,IAAI,IAAI,CAAC,IAAI,IAAI,MAAM,KAAK,MAAM,EAAE;AACpC,MAAM,OAAO,IAAI,QAAQ,CAAC,iBAAiB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7D;AACA,IAAI,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AACjD,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;AAC3B,MAAM,IAAI,EAAE,EAAE,IAAI,EAAE,MAAM;AAC1B,KAAK,CAAC;AACN,IAAI,MAAM,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC;AACxC,MAAM,IAAI,EAAE;AACZ,QAAQ,MAAM;AACd,QAAQ,MAAM,EAAE,cAAc;AAC9B,QAAQ,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC;AAChC,UAAU,OAAO,EAAE,WAAW,CAAC,IAAI;AACnC,UAAU,OAAO,EAAE,MAAM;AACzB,UAAU,SAAS,EAAE,QAAQ,CAAC,EAAE;AAChC,UAAU,cAAc,EAAE;AAC1B,SAAS;AACT;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,OAAO,EAAE,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;AAC/C,MAAM,IAAI,EAAE;AACZ,QAAQ,EAAE,EAAE,WAAW,CAAC,EAAE;AAC1B,QAAQ,KAAK,EAAE,WAAW,CAAC,KAAK;AAChC,QAAQ,IAAI,EAAE,WAAW,CAAC;AAC1B;AACA,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC;AACrD,IAAI,OAAO,IAAI,QAAQ,CAAC,CAAC,4BAA4B,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxF;AACA;;;;"}