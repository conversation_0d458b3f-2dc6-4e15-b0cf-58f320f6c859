{"version": 3, "file": "_server.ts-CAy-I_xk.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/ai/resume/suggestions/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../../../../chunks/auth.js\";\nconst POST = async ({ request, cookies }) => {\n  try {\n    const token = cookies.get(\"auth_token\");\n    if (!token) {\n      return json({ error: \"Unauthorized\" }, { status: 401 });\n    }\n    const tokenData = verifySessionToken(token);\n    if (!tokenData || !tokenData.id) {\n      return json({ error: \"Invalid token\" }, { status: 401 });\n    }\n    const userId = tokenData.id;\n    const body = await request.json();\n    const { resumeId, section, content } = body;\n    if (!resumeId || !section || !content) {\n      return json(\n        { error: \"Missing required fields\", required: [\"resumeId\", \"section\", \"content\"] },\n        { status: 400 }\n      );\n    }\n    const resume = await prisma.resume.findFirst({\n      where: {\n        id: resumeId,\n        document: {\n          userId\n        }\n      }\n    });\n    if (!resume) {\n      return json({ error: \"Resume not found or access denied\" }, { status: 404 });\n    }\n    const user = await prisma.user.findUnique({\n      where: { id: userId },\n      include: {\n        subscriptions: {\n          where: { status: \"active\" },\n          include: {\n            plan: {\n              include: {\n                features: {\n                  where: { featureId: \"resume_ai\" }\n                }\n              }\n            }\n          }\n        }\n      }\n    });\n    const hasAccess = user?.subscriptions.some(\n      (sub) => sub.plan.features.some((feature) => feature.featureId === \"resume_ai\")\n    );\n    if (!hasAccess && process.env.NODE_ENV === \"production\") {\n      return json({ error: \"Feature not available in your plan\" }, { status: 403 });\n    }\n    const suggestions = generateMockSuggestions(section, content);\n    const savedSuggestions = await Promise.all(\n      suggestions.map(\n        (suggestion) => prisma.resumeAISuggestion.create({\n          data: {\n            resumeId,\n            userId,\n            section,\n            originalContent: content,\n            suggestion: suggestion.suggestion,\n            reasoning: suggestion.reasoning,\n            applied: false\n          }\n        })\n      )\n    );\n    return json({ success: true, suggestions: savedSuggestions });\n  } catch (error) {\n    console.error(\"Error generating resume suggestions:\", error);\n    return json({ error: \"Internal server error\" }, { status: 500 });\n  }\n};\nfunction generateMockSuggestions(section, content) {\n  switch (section) {\n    case \"summary\":\n      return [\n        {\n          suggestion: \"Results-driven software engineer with 5+ years of experience developing scalable web applications. Proficient in JavaScript, TypeScript, and React with a strong focus on performance optimization and clean code practices.\",\n          reasoning: \"Adds specific years of experience and highlights key technical skills\"\n        },\n        {\n          suggestion: \"Innovative software developer specializing in full-stack web development with expertise in modern JavaScript frameworks. Demonstrated success in delivering high-quality applications that improve user experience and business metrics.\",\n          reasoning: \"Emphasizes impact and business value\"\n        }\n      ];\n    case \"experience\":\n      return [\n        {\n          suggestion: content.replace(/Responsible for/g, \"Led\") + \" Increased team productivity by 20% through implementation of automated testing.\",\n          reasoning: \"Uses stronger action verbs and adds quantifiable achievements\"\n        },\n        {\n          suggestion: content.replace(/Worked on/g, \"Developed and deployed\") + \" Reduced page load time by 40% through code optimization.\",\n          reasoning: \"Adds specific metrics and uses more impactful language\"\n        }\n      ];\n    case \"skills\":\n      return [\n        {\n          suggestion: content + \", CI/CD, Docker, Kubernetes\",\n          reasoning: \"Adds relevant DevOps skills that are in high demand\"\n        },\n        {\n          suggestion: content.replace(\"JavaScript\", \"JavaScript (ES6+), TypeScript\"),\n          reasoning: \"Specifies JavaScript version and adds TypeScript for more relevance\"\n        }\n      ];\n    default:\n      return [\n        {\n          suggestion: content + \" (Enhanced with more specific details)\",\n          reasoning: \"Added more specific details to make content more impactful\"\n        }\n      ];\n  }\n}\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;AAGK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AAC7C,EAAE,IAAI;AACN,IAAI,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AAC3C,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7D;AACA,IAAI,MAAM,SAAS,GAAG,kBAAkB,CAAC,KAAK,CAAC;AAC/C,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE;AACrC,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9D;AACA,IAAI,MAAM,MAAM,GAAG,SAAS,CAAC,EAAE;AAC/B,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,IAAI;AAC/C,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,EAAE;AAC3C,MAAM,OAAO,IAAI;AACjB,QAAQ,EAAE,KAAK,EAAE,yBAAyB,EAAE,QAAQ,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,SAAS,CAAC,EAAE;AAC1F,QAAQ,EAAE,MAAM,EAAE,GAAG;AACrB,OAAO;AACP;AACA,IAAI,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC;AACjD,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE,QAAQ;AACpB,QAAQ,QAAQ,EAAE;AAClB,UAAU;AACV;AACA;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mCAAmC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClF;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAC9C,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;AAC3B,MAAM,OAAO,EAAE;AACf,QAAQ,aAAa,EAAE;AACvB,UAAU,KAAK,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE;AACrC,UAAU,OAAO,EAAE;AACnB,YAAY,IAAI,EAAE;AAClB,cAAc,OAAO,EAAE;AACvB,gBAAgB,QAAQ,EAAE;AAC1B,kBAAkB,KAAK,EAAE,EAAE,SAAS,EAAE,WAAW;AACjD;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,CAAC;AACN,IAAI,MAAM,SAAS,GAAG,IAAI,EAAE,aAAa,CAAC,IAAI;AAC9C,MAAM,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,SAAS,KAAK,WAAW;AACpF,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE;AAC7D,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oCAAoC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnF;AACA,IAAI,MAAM,WAAW,GAAG,uBAAuB,CAAC,OAAO,EAAE,OAAO,CAAC;AACjE,IAAI,MAAM,gBAAgB,GAAG,MAAM,OAAO,CAAC,GAAG;AAC9C,MAAM,WAAW,CAAC,GAAG;AACrB,QAAQ,CAAC,UAAU,KAAK,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC;AACzD,UAAU,IAAI,EAAE;AAChB,YAAY,QAAQ;AACpB,YAAY,MAAM;AAClB,YAAY,OAAO;AACnB,YAAY,eAAe,EAAE,OAAO;AACpC,YAAY,UAAU,EAAE,UAAU,CAAC,UAAU;AAC7C,YAAY,SAAS,EAAE,UAAU,CAAC,SAAS;AAC3C,YAAY,OAAO,EAAE;AACrB;AACA,SAAS;AACT;AACA,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,gBAAgB,EAAE,CAAC;AACjE,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC;AAChE,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpE;AACA;AACA,SAAS,uBAAuB,CAAC,OAAO,EAAE,OAAO,EAAE;AACnD,EAAE,QAAQ,OAAO;AACjB,IAAI,KAAK,SAAS;AAClB,MAAM,OAAO;AACb,QAAQ;AACR,UAAU,UAAU,EAAE,8NAA8N;AACpP,UAAU,SAAS,EAAE;AACrB,SAAS;AACT,QAAQ;AACR,UAAU,UAAU,EAAE,0OAA0O;AAChQ,UAAU,SAAS,EAAE;AACrB;AACA,OAAO;AACP,IAAI,KAAK,YAAY;AACrB,MAAM,OAAO;AACb,QAAQ;AACR,UAAU,UAAU,EAAE,OAAO,CAAC,OAAO,CAAC,kBAAkB,EAAE,KAAK,CAAC,GAAG,kFAAkF;AACrJ,UAAU,SAAS,EAAE;AACrB,SAAS;AACT,QAAQ;AACR,UAAU,UAAU,EAAE,OAAO,CAAC,OAAO,CAAC,YAAY,EAAE,wBAAwB,CAAC,GAAG,2DAA2D;AAC3I,UAAU,SAAS,EAAE;AACrB;AACA,OAAO;AACP,IAAI,KAAK,QAAQ;AACjB,MAAM,OAAO;AACb,QAAQ;AACR,UAAU,UAAU,EAAE,OAAO,GAAG,6BAA6B;AAC7D,UAAU,SAAS,EAAE;AACrB,SAAS;AACT,QAAQ;AACR,UAAU,UAAU,EAAE,OAAO,CAAC,OAAO,CAAC,YAAY,EAAE,+BAA+B,CAAC;AACpF,UAAU,SAAS,EAAE;AACrB;AACA,OAAO;AACP,IAAI;AACJ,MAAM,OAAO;AACb,QAAQ;AACR,UAAU,UAAU,EAAE,OAAO,GAAG,wCAAwC;AACxE,UAAU,SAAS,EAAE;AACrB;AACA,OAAO;AACP;AACA;;;;"}