{"version": 3, "file": "_server.ts-awDSlQgK.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/auth/reset-password/_server.ts.js"], "sourcesContent": ["import { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport bcrypt__default from \"bcryptjs\";\nimport { j as json } from \"../../../../../chunks/index.js\";\nimport { a as sendPasswordChangedEmail } from \"../../../../../chunks/email.js\";\nconst POST = async ({ request }) => {\n  const { token, password } = await request.json();\n  if (!token || !password) {\n    return json({ error: \"Missing token or password\" }, { status: 400 });\n  }\n  const resetToken = await prisma.passwordResetToken.findUnique({\n    where: { token }\n  });\n  if (!resetToken || resetToken.expiresAt < /* @__PURE__ */ new Date()) {\n    return json({ error: \"Invalid or expired token\" }, { status: 400 });\n  }\n  const hashed = await bcrypt__default.hash(password, 10);\n  await prisma.user.update({\n    where: { id: resetToken.userId },\n    data: { passwordHash: hashed }\n  });\n  await prisma.passwordResetToken.delete({\n    where: { token }\n  });\n  try {\n    const baseUrl = process.env.PUBLIC_BASE_URL || \"http://localhost:5173\";\n    const testEmail = \"<EMAIL>\";\n    await sendPasswordChangedEmail(resetToken.email, {\n      firstName: resetToken.email.split(\"@\")[0],\n      // Use part of email as name\n      loginUrl: `${baseUrl}/auth/sign-in`\n    });\n    if (resetToken.email.toLowerCase() !== testEmail.toLowerCase()) {\n      console.log(`Sending test password changed email to ${testEmail}`);\n      await sendPasswordChangedEmail(testEmail, {\n        firstName: \"Christopher (Test Copy)\",\n        loginUrl: `${baseUrl}/auth/sign-in`\n      });\n    }\n  } catch (error) {\n    console.error(\"Failed to send password changed notification:\", error);\n  }\n  return json({ success: true });\n};\nexport {\n  POST\n};\n"], "names": ["bcrypt__default"], "mappings": ";;;;;;;;;AAIK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK;AACpC,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AAClD,EAAE,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,EAAE;AAC3B,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,2BAA2B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA,EAAE,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,kBAAkB,CAAC,UAAU,CAAC;AAChE,IAAI,KAAK,EAAE,EAAE,KAAK;AAClB,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,SAAS,mBAAmB,IAAI,IAAI,EAAE,EAAE;AACxE,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,0BAA0B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACvE;AACA,EAAE,MAAM,MAAM,GAAG,MAAMA,wBAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;AACzD,EAAE,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AAC3B,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU,CAAC,MAAM,EAAE;AACpC,IAAI,IAAI,EAAE,EAAE,YAAY,EAAE,MAAM;AAChC,GAAG,CAAC;AACJ,EAAE,MAAM,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC;AACzC,IAAI,KAAK,EAAE,EAAE,KAAK;AAClB,GAAG,CAAC;AACJ,EAAE,IAAI;AACN,IAAI,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,uBAAuB;AAC1E,IAAI,MAAM,SAAS,GAAG,wCAAwC;AAC9D,IAAI,MAAM,wBAAwB,CAAC,UAAU,CAAC,KAAK,EAAE;AACrD,MAAM,SAAS,EAAE,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/C;AACA,MAAM,QAAQ,EAAE,CAAC,EAAE,OAAO,CAAC,aAAa;AACxC,KAAK,CAAC;AACN,IAAI,IAAI,UAAU,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,SAAS,CAAC,WAAW,EAAE,EAAE;AACpE,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,uCAAuC,EAAE,SAAS,CAAC,CAAC,CAAC;AACxE,MAAM,MAAM,wBAAwB,CAAC,SAAS,EAAE;AAChD,QAAQ,SAAS,EAAE,yBAAyB;AAC5C,QAAQ,QAAQ,EAAE,CAAC,EAAE,OAAO,CAAC,aAAa;AAC1C,OAAO,CAAC;AACR;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,+CAA+C,EAAE,KAAK,CAAC;AACzE;AACA,EAAE,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;AAChC;;;;"}