{"version": 3, "file": "_server.ts-C8sLd6eZ.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/auth/login/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport * as bcrypt from \"bcryptjs\";\nimport { c as createSessionToken } from \"../../../../../chunks/auth.js\";\nconst POST = async ({ request, cookies }) => {\n  try {\n    const { email, password, remember } = await request.json();\n    if (!email || !password) {\n      return json({ error: \"Email and password are required\" }, { status: 400 });\n    }\n    const user = await prisma.user.findUnique({\n      where: { email: email.toLowerCase() }\n    });\n    if (!user) {\n      return json({ error: \"Invalid credentials\" }, { status: 401 });\n    }\n    if (!user.passwordHash) {\n      return json(\n        {\n          error: \"Invalid credentials\",\n          message: \"This account doesn't have a password. Try signing in with Google or LinkedIn.\"\n        },\n        { status: 401 }\n      );\n    }\n    const valid = await bcrypt.compare(password, user.passwordHash);\n    if (!valid) {\n      return json({ error: \"Invalid credentials\" }, { status: 401 });\n    }\n    const preferences = typeof user.preferences === \"string\" ? JSON.parse(user.preferences || \"{}\") : user.preferences || {};\n    const isVerifiedInField = !!user.emailVerified;\n    const isVerifiedInPrefs = !!preferences.emailVerified;\n    if (!isVerifiedInField && !isVerifiedInPrefs) {\n      console.log(\"Email not verified:\", email);\n      return json(\n        {\n          error: \"Email not verified\",\n          message: \"Please verify your email address before signing in. Check your inbox for a verification email.\",\n          verified: false\n        },\n        { status: 403 }\n      );\n    }\n    console.log(\"Email verified:\", email);\n    const token = await createSessionToken(user, request);\n    const maxAge = remember ? 60 * 60 * 24 * 30 : 60 * 60 * 24 * 7;\n    cookies.set(\"auth_token\", token, {\n      path: \"/\",\n      httpOnly: true,\n      sameSite: \"lax\",\n      secure: process.env.NODE_ENV === \"production\",\n      maxAge\n    });\n    console.log(`Setting auth_token cookie with maxAge: ${maxAge}s`);\n    return json({\n      success: true,\n      message: \"Login successful\",\n      user: {\n        id: user.id,\n        email: user.email,\n        name: user.name\n      }\n    });\n  } catch (error) {\n    console.error(\"Login error:\", error);\n    return json(\n      {\n        error: \"Server error\",\n        message: \"An unexpected error occurred. Please try again.\"\n      },\n      { status: 500 }\n    );\n  }\n};\nexport {\n  POST\n};\n"], "names": ["bcrypt"], "mappings": ";;;;;;;;;;AAIK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AAC7C,EAAE,IAAI;AACN,IAAI,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AAC9D,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,EAAE;AAC7B,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,iCAAiC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAChF;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAC9C,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,WAAW,EAAE;AACzC,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,qBAAqB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpE;AACA,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AAC5B,MAAM,OAAO,IAAI;AACjB,QAAQ;AACR,UAAU,KAAK,EAAE,qBAAqB;AACtC,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ,EAAE,MAAM,EAAE,GAAG;AACrB,OAAO;AACP;AACA,IAAI,MAAM,KAAK,GAAG,MAAMA,eAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC;AACnE,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,qBAAqB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpE;AACA,IAAI,MAAM,WAAW,GAAG,OAAO,IAAI,CAAC,WAAW,KAAK,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,WAAW,IAAI,EAAE;AAC5H,IAAI,MAAM,iBAAiB,GAAG,CAAC,CAAC,IAAI,CAAC,aAAa;AAClD,IAAI,MAAM,iBAAiB,GAAG,CAAC,CAAC,WAAW,CAAC,aAAa;AACzD,IAAI,IAAI,CAAC,iBAAiB,IAAI,CAAC,iBAAiB,EAAE;AAClD,MAAM,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,KAAK,CAAC;AAC/C,MAAM,OAAO,IAAI;AACjB,QAAQ;AACR,UAAU,KAAK,EAAE,oBAAoB;AACrC,UAAU,OAAO,EAAE,gGAAgG;AACnH,UAAU,QAAQ,EAAE;AACpB,SAAS;AACT,QAAQ,EAAE,MAAM,EAAE,GAAG;AACrB,OAAO;AACP;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,KAAK,CAAC;AACzC,IAAI,MAAM,KAAK,GAAG,MAAM,kBAAkB,CAAC,IAAI,EAAE,OAAO,CAAC;AACzD,IAAI,MAAM,MAAM,GAAG,QAAQ,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAClE,IAAI,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,KAAK,EAAE;AACrC,MAAM,IAAI,EAAE,GAAG;AACf,MAAM,QAAQ,EAAE,IAAI;AACpB,MAAM,QAAQ,EAAE,KAAK;AACrB,MAAM,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;AACnD,MAAM;AACN,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,uCAAuC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;AACpE,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,OAAO,EAAE,kBAAkB;AACjC,MAAM,IAAI,EAAE;AACZ,QAAQ,EAAE,EAAE,IAAI,CAAC,EAAE;AACnB,QAAQ,KAAK,EAAE,IAAI,CAAC,KAAK;AACzB,QAAQ,IAAI,EAAE,IAAI,CAAC;AACnB;AACA,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC;AACxC,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,KAAK,EAAE,cAAc;AAC7B,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;;;;"}