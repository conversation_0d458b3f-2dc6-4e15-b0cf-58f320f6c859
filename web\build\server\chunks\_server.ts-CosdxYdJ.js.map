{"version": 3, "file": "_server.ts-CosdxYdJ.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/email/broadcasts/cancel/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { l as logger } from \"../../../../../../chunks/logger.js\";\nasync function POST({ request }) {\n  try {\n    const data = await request.json();\n    logger.info(\"Cancelling broadcast:\", data);\n    if (!data.broadcastId) {\n      return json({ error: \"Broadcast ID is required\" }, { status: 400 });\n    }\n    return json({ success: true, message: \"Broadcast cancelled successfully\" });\n  } catch (error) {\n    logger.error(\"Error cancelling broadcast:\", error);\n    return json({ error: \"Failed to cancel broadcast\" }, { status: 500 });\n  }\n}\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;AAEA,eAAe,IAAI,CAAC,EAAE,OAAO,EAAE,EAAE;AACjC,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,MAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE,IAAI,CAAC;AAC9C,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;AAC3B,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,0BAA0B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE;AACA,IAAI,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,kCAAkC,EAAE,CAAC;AAC/E,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC;AACtD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,4BAA4B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE;AACA;;;;"}