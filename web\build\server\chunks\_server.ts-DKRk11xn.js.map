{"version": 3, "file": "_server.ts-DKRk11xn.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/documents/_id_/parse/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../../chunks/prisma.js\";\nconst POST = async ({ params, request, locals, fetch }) => {\n  const { id } = params;\n  try {\n    const document = await prisma.document.findUnique({\n      where: { id },\n      include: {\n        resume: true\n      }\n    });\n    if (!document) {\n      return json({ error: \"Document not found\" }, { status: 404 });\n    }\n    if (document.type === \"resume\" && document.resume) {\n      console.log(`Document ${id} is a resume, redirecting to resume parsing endpoint`);\n      const response = await fetch(`/api/resume/${document.resume.id}/parse`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          // Forward any request body data\n          ...request.headers.get(\"content-type\")?.includes(\"application/json\") ? await request.json() : {}\n        })\n      });\n      const result = await response.json();\n      return json(result, { status: response.status });\n    }\n    const updatedDocument = await prisma.document.update({\n      where: { id },\n      data: {\n        isParsed: false,\n        parsedAt: null\n      }\n    });\n    return json({\n      success: true,\n      message: \"Document parsing started\",\n      document: updatedDocument\n    });\n  } catch (error) {\n    console.error(\"Error starting document parsing:\", error);\n    return json(\n      {\n        error: \"Failed to start document parsing\",\n        details: error instanceof Error ? error.message : String(error)\n      },\n      { status: 500 }\n    );\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;AAEK,MAAC,IAAI,GAAG,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK;AAC3D,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;AACvB,EAAE,IAAI;AACN,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;AACtD,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE;AACnB,MAAM,OAAO,EAAE;AACf,QAAQ,MAAM,EAAE;AAChB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oBAAoB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnE;AACA,IAAI,IAAI,QAAQ,CAAC,IAAI,KAAK,QAAQ,IAAI,QAAQ,CAAC,MAAM,EAAE;AACvD,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,oDAAoD,CAAC,CAAC;AACvF,MAAM,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,CAAC,YAAY,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE;AAC9E,QAAQ,MAAM,EAAE,MAAM;AACtB,QAAQ,OAAO,EAAE;AACjB,UAAU,cAAc,EAAE;AAC1B,SAAS;AACT,QAAQ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;AAC7B;AACA,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,QAAQ,CAAC,kBAAkB,CAAC,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE,GAAG;AACxG,SAAS;AACT,OAAO,CAAC;AACR,MAAM,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE;AAC1C,MAAM,OAAO,IAAI,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,QAAQ,CAAC,MAAM,EAAE,CAAC;AACtD;AACA,IAAI,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;AACzD,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE;AACnB,MAAM,IAAI,EAAE;AACZ,QAAQ,QAAQ,EAAE,KAAK;AACvB,QAAQ,QAAQ,EAAE;AAClB;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,OAAO,EAAE,0BAA0B;AACzC,MAAM,QAAQ,EAAE;AAChB,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC;AAC5D,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,KAAK,EAAE,kCAAkC;AACjD,QAAQ,OAAO,EAAE,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC,KAAK;AACtE,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;;;;"}