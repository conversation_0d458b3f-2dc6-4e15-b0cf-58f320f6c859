{"version": 3, "file": "_server.ts-GJbwYEo_.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/user/set-admin/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { l as logger } from \"../../../../../chunks/logger.js\";\nimport { d as dev } from \"../../../../../chunks/index4.js\";\nasync function POST({ request, locals }) {\n  try {\n    const currentUserId = locals.user?.id;\n    if (!currentUserId) {\n      return json({ error: \"Not authenticated\" }, { status: 401 });\n    }\n    const currentUser = await prisma.user.findUnique({\n      where: { id: currentUserId },\n      select: { isAdmin: true }\n    });\n    if (!dev && !currentUser?.isAdmin) {\n      return json({ error: \"Not authorized\" }, { status: 403 });\n    }\n    const body = await request.json();\n    const { email, isAdmin } = body;\n    if (!email) {\n      return json({ error: \"Email is required\" }, { status: 400 });\n    }\n    const user = await prisma.user.update({\n      where: { email },\n      data: { isAdmin: isAdmin === true },\n      select: {\n        id: true,\n        email: true,\n        name: true,\n        isAdmin: true\n      }\n    });\n    return json(user);\n  } catch (error) {\n    logger.error(\"Error setting admin status:\", error);\n    return json({ error: \"Failed to set admin status\" }, { status: 500 });\n  }\n}\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;AAIA,eAAe,IAAI,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE;AACzC,EAAE,IAAI;AACN,IAAI,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,EAAE,EAAE;AACzC,IAAI,IAAI,CAAC,aAAa,EAAE;AACxB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE;AACA,IAAI,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AACrD,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,aAAa,EAAE;AAClC,MAAM,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI;AAC7B,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE;AACvC,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,gBAAgB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC/D;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,IAAI;AACnC,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AAC1C,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE;AACtB,MAAM,IAAI,EAAE,EAAE,OAAO,EAAE,OAAO,KAAK,IAAI,EAAE;AACzC,MAAM,MAAM,EAAE;AACd,QAAQ,EAAE,EAAE,IAAI;AAChB,QAAQ,KAAK,EAAE,IAAI;AACnB,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,OAAO,EAAE;AACjB;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC;AACrB,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC;AACtD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,4BAA4B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE;AACA;;;;"}