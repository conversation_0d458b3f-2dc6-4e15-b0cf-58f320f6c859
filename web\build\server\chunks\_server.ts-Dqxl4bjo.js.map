{"version": 3, "file": "_server.ts-Dqxl4bjo.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/submit/_server.ts.js"], "sourcesContent": ["import { Roles } from \"../../../../chunks/roles.js\";\nimport { e as error } from \"../../../../chunks/index.js\";\nasync function canSubmitResume(user) {\n  const { PrismaClient } = await import(\"@prisma/client\");\n  const prisma = new PrismaClient();\n  const roleConfig = Roles[user.role];\n  if (!roleConfig) return false;\n  const monthlyLimit = roleConfig.limits.resumesPerMonth;\n  if (monthlyLimit === null) return true;\n  const startOfMonth = /* @__PURE__ */ new Date();\n  startOfMonth.setDate(1);\n  startOfMonth.setHours(0, 0, 0, 0);\n  const count = await prisma.resumeSubmission.count({\n    where: {\n      userId: user.id,\n      createdAt: {\n        gte: startOfMonth\n      }\n    }\n  });\n  await prisma.$disconnect();\n  return count < monthlyLimit;\n}\nconst POST = async ({ locals, request }) => {\n  const user = await locals.getUser();\n  if (!user) throw error(401, \"Unauthorized\");\n  const allowed = await canSubmitResume(user);\n  if (!allowed) {\n    throw error(403, \"Upgrade to submit more resumes.\");\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;AAEA,eAAe,eAAe,CAAC,IAAI,EAAE;AACrC,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,MAAM,OAAO,gBAAgB,CAAC;AACzD,EAAE,MAAM,MAAM,GAAG,IAAI,YAAY,EAAE;AACnC,EAAE,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;AACrC,EAAE,IAAI,CAAC,UAAU,EAAE,OAAO,KAAK;AAC/B,EAAE,MAAM,YAAY,GAAG,UAAU,CAAC,MAAM,CAAC,eAAe;AACxD,EAAE,IAAI,YAAY,KAAK,IAAI,EAAE,OAAO,IAAI;AACxC,EAAE,MAAM,YAAY,mBAAmB,IAAI,IAAI,EAAE;AACjD,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC;AACzB,EAAE,YAAY,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACnC,EAAE,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC;AACpD,IAAI,KAAK,EAAE;AACX,MAAM,MAAM,EAAE,IAAI,CAAC,EAAE;AACrB,MAAM,SAAS,EAAE;AACjB,QAAQ,GAAG,EAAE;AACb;AACA;AACA,GAAG,CAAC;AACJ,EAAE,MAAM,MAAM,CAAC,WAAW,EAAE;AAC5B,EAAE,OAAO,KAAK,GAAG,YAAY;AAC7B;AACK,MAAC,IAAI,GAAG,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK;AAC5C,EAAE,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,OAAO,EAAE;AACrC,EAAE,IAAI,CAAC,IAAI,EAAE,MAAM,KAAK,CAAC,GAAG,EAAE,cAAc,CAAC;AAC7C,EAAE,MAAM,OAAO,GAAG,MAAM,eAAe,CAAC,IAAI,CAAC;AAC7C,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,MAAM,KAAK,CAAC,GAAG,EAAE,iCAAiC,CAAC;AACvD;AACA;;;;"}