{"version": 3, "file": "_server.ts-o3ltphBj.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/resume/scanner/status/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { l as logger } from \"../../../../../../chunks/logger.js\";\nconst GET = async () => {\n  try {\n    const status = {\n      operational: true,\n      queueSize: 2,\n      averageScanTime: 1.8,\n      // seconds\n      dailyScans: 85,\n      accuracyRate: 96.2\n      // percentage\n    };\n    return json({\n      ...status,\n      timestamp: (/* @__PURE__ */ new Date()).toISOString()\n    });\n  } catch (error) {\n    logger.error(\"Error checking resume scanner status:\", error);\n    return json(\n      {\n        error: \"Failed to check resume scanner status\",\n        timestamp: (/* @__PURE__ */ new Date()).toISOString()\n      },\n      { status: 500 }\n    );\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;AAEK,MAAC,GAAG,GAAG,YAAY;AACxB,EAAE,IAAI;AACN,IAAI,MAAM,MAAM,GAAG;AACnB,MAAM,WAAW,EAAE,IAAI;AACvB,MAAM,SAAS,EAAE,CAAC;AAClB,MAAM,eAAe,EAAE,GAAG;AAC1B;AACA,MAAM,UAAU,EAAE,EAAE;AACpB,MAAM,YAAY,EAAE;AACpB;AACA,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,GAAG,MAAM;AACf,MAAM,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AACzD,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC;AAChE,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,KAAK,EAAE,uCAAuC;AACtD,QAAQ,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC3D,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;;;;"}