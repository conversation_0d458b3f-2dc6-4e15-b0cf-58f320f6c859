{"version": 3, "file": "_server.ts-HKvmIiny.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/automation/ws/_server.ts.js"], "sourcesContent": ["import { createClient } from \"redis\";\nimport { g as getUser<PERSON>rom<PERSON>oken } from \"../../../../../chunks/auth.js\";\nconst GET = async ({ cookies, request }) => {\n  const user = getUserFromToken(cookies);\n  if (!user) {\n    return new Response(\"Unauthorized\", { status: 401 });\n  }\n  const { socket, response } = Bun.upgradeWebSocket(request);\n  const redisUrl = process.env.NODE_ENV === \"production\" ? \"rediss://red-cvmu1me3jp1c738ve7ig:<EMAIL>:6379\" : \"redis://localhost:6379\";\n  const redis = createClient({\n    url: redisUrl\n  });\n  const subscriber = redis.duplicate();\n  socket.onopen = async () => {\n    console.log(\"WebSocket connection opened\");\n    try {\n      await redis.connect();\n      await subscriber.connect();\n      await subscriber.subscribe(\"automation:status\", async (message) => {\n        try {\n          const data = JSON.parse(message);\n          if (data.userId === user.id) {\n            socket.send(\n              JSON.stringify({\n                type: \"status\",\n                data\n              })\n            );\n          }\n        } catch (error) {\n          console.error(\"Error processing message:\", error);\n        }\n      });\n      await subscriber.subscribe(\"automation:jobs\", (message) => {\n        try {\n          const data = JSON.parse(message);\n          if (data.userId === user.id) {\n            socket.send(\n              JSON.stringify({\n                type: \"jobs\",\n                data\n              })\n            );\n          }\n        } catch (error) {\n          console.error(\"Error processing message:\", error);\n        }\n      });\n      console.log(\"Subscribed to Redis channels\");\n    } catch (error) {\n      console.error(\"Error connecting to Redis:\", error);\n      socket.close();\n    }\n  };\n  socket.onclose = async () => {\n    console.log(\"WebSocket connection closed\");\n    try {\n      await subscriber.quit();\n      await redis.quit();\n    } catch (error) {\n      console.error(\"Error closing Redis connections:\", error);\n    }\n  };\n  socket.onerror = (error) => {\n    console.error(\"WebSocket error:\", error);\n  };\n  socket.onmessage = (event) => {\n    try {\n      const message = JSON.parse(event.data);\n      console.log(\"Received message:\", message);\n    } catch (error) {\n      console.error(\"Error processing client message:\", error);\n    }\n  };\n  return response;\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;;;;;;AAEK,MAAC,GAAG,GAAG,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AAC5C,EAAE,MAAM,IAAI,GAAG,gBAAgB,CAAC,OAAO,CAAC;AACxC,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxD;AACA,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC,gBAAgB,CAAC,OAAO,CAAC;AAC5D,EAAE,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,GAAG,oGAAoG,GAAG,wBAAwB;AAC1L,EAAE,MAAM,KAAK,GAAG,YAAY,CAAC;AAC7B,IAAI,GAAG,EAAE;AACT,GAAG,CAAC;AACJ,EAAE,MAAM,UAAU,GAAG,KAAK,CAAC,SAAS,EAAE;AACtC,EAAE,MAAM,CAAC,MAAM,GAAG,YAAY;AAC9B,IAAI,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC;AAC9C,IAAI,IAAI;AACR,MAAM,MAAM,KAAK,CAAC,OAAO,EAAE;AAC3B,MAAM,MAAM,UAAU,CAAC,OAAO,EAAE;AAChC,MAAM,MAAM,UAAU,CAAC,SAAS,CAAC,mBAAmB,EAAE,OAAO,OAAO,KAAK;AACzE,QAAQ,IAAI;AACZ,UAAU,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;AAC1C,UAAU,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,EAAE,EAAE;AACvC,YAAY,MAAM,CAAC,IAAI;AACvB,cAAc,IAAI,CAAC,SAAS,CAAC;AAC7B,gBAAgB,IAAI,EAAE,QAAQ;AAC9B,gBAAgB;AAChB,eAAe;AACf,aAAa;AACb;AACA,SAAS,CAAC,OAAO,KAAK,EAAE;AACxB,UAAU,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC;AAC3D;AACA,OAAO,CAAC;AACR,MAAM,MAAM,UAAU,CAAC,SAAS,CAAC,iBAAiB,EAAE,CAAC,OAAO,KAAK;AACjE,QAAQ,IAAI;AACZ,UAAU,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;AAC1C,UAAU,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,EAAE,EAAE;AACvC,YAAY,MAAM,CAAC,IAAI;AACvB,cAAc,IAAI,CAAC,SAAS,CAAC;AAC7B,gBAAgB,IAAI,EAAE,MAAM;AAC5B,gBAAgB;AAChB,eAAe;AACf,aAAa;AACb;AACA,SAAS,CAAC,OAAO,KAAK,EAAE;AACxB,UAAU,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC;AAC3D;AACA,OAAO,CAAC;AACR,MAAM,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC;AACjD,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC;AACxD,MAAM,MAAM,CAAC,KAAK,EAAE;AACpB;AACA,GAAG;AACH,EAAE,MAAM,CAAC,OAAO,GAAG,YAAY;AAC/B,IAAI,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC;AAC9C,IAAI,IAAI;AACR,MAAM,MAAM,UAAU,CAAC,IAAI,EAAE;AAC7B,MAAM,MAAM,KAAK,CAAC,IAAI,EAAE;AACxB,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC;AAC9D;AACA,GAAG;AACH,EAAE,MAAM,CAAC,OAAO,GAAG,CAAC,KAAK,KAAK;AAC9B,IAAI,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC;AAC5C,GAAG;AACH,EAAE,MAAM,CAAC,SAAS,GAAG,CAAC,KAAK,KAAK;AAChC,IAAI,IAAI;AACR,MAAM,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC;AAC5C,MAAM,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,OAAO,CAAC;AAC/C,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC;AAC9D;AACA,GAAG;AACH,EAAE,OAAO,QAAQ;AACjB;;;;"}