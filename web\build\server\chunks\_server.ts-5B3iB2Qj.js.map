{"version": 3, "file": "_server.ts-5B3iB2Qj.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/admin/initialize-features/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { v as verifySessionToken } from \"../../../../../chunks/auth.js\";\nimport { i as initializeFeatureData } from \"../../../../../chunks/initialize-features.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nconst POST = async ({ cookies }) => {\n  const token = cookies.get(\"auth_token\");\n  if (!token) return new Response(\"Unauthorized\", { status: 401 });\n  const userData = verifySessionToken(token);\n  if (!userData?.id) return new Response(\"Unauthorized\", { status: 401 });\n  if (userData.role !== \"admin\") {\n    return new Response(\"Forbidden - Admin access required\", { status: 403 });\n  }\n  try {\n    console.log(\"Starting feature data initialization...\");\n    const result = await initializeFeatureData();\n    console.log(\"Feature initialization result:\", result);\n    if (result.success) {\n      console.log(`Successfully created ${result.features} features and ${result.limits} limits`);\n      try {\n        const feature = await prisma.feature.findFirst({\n          where: {\n            limits: {\n              some: {}\n            }\n          },\n          include: {\n            limits: {\n              take: 1\n            }\n          }\n        });\n        if (feature && feature.limits.length > 0) {\n          const limit = feature.limits[0];\n          const now = /* @__PURE__ */ new Date();\n          const period = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, \"0\")}`;\n          const existingUsage = await prisma.featureUsage.findFirst({\n            where: {\n              userId: userData.id,\n              featureId: feature.id,\n              limitId: limit.id,\n              period\n            }\n          });\n          if (!existingUsage) {\n            await prisma.featureUsage.create({\n              data: {\n                userId: userData.id,\n                featureId: feature.id,\n                limitId: limit.id,\n                used: 5,\n                period\n              }\n            });\n            console.log(\n              `Created test usage record for user ${userData.id}, feature ${feature.name}, limit ${limit.name}`\n            );\n          } else {\n            await prisma.featureUsage.update({\n              where: { id: existingUsage.id },\n              data: {\n                used: 5,\n                updatedAt: /* @__PURE__ */ new Date()\n              }\n            });\n            console.log(\n              `Updated test usage record for user ${userData.id}, feature ${feature.name}, limit ${limit.name}`\n            );\n          }\n        }\n      } catch (usageError) {\n        console.error(\"Error creating test usage record:\", usageError);\n      }\n      return json({\n        success: true,\n        message: \"Feature data initialized successfully\",\n        results: {\n          features: result.features,\n          limits: result.limits\n        }\n      });\n    } else {\n      console.error(\"Feature initialization failed:\", result.error);\n      return json(\n        {\n          success: false,\n          error: result.error || \"Failed to initialize feature data\"\n        },\n        { status: 500 }\n      );\n    }\n  } catch (error) {\n    console.error(\"Error initializing feature data:\", error);\n    if (error instanceof Error) {\n      console.error(\"Error name:\", error.name);\n      console.error(\"Error message:\", error.message);\n      console.error(\"Error stack:\", error.stack);\n    }\n    return json(\n      {\n        success: false,\n        error: error.message || \"Failed to initialize feature data\"\n      },\n      { status: 500 }\n    );\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAIK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK;AACpC,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE,EAAE,MAAM,QAAQ,GAAG,kBAAkB,CAAC,KAAK,CAAC;AAC5C,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE,EAAE,IAAI,QAAQ,CAAC,IAAI,KAAK,OAAO,EAAE;AACjC,IAAI,OAAO,IAAI,QAAQ,CAAC,mCAAmC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7E;AACA,EAAE,IAAI;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC;AAC1D,IAAI,MAAM,MAAM,GAAG,MAAM,qBAAqB,EAAE;AAChD,IAAI,OAAO,CAAC,GAAG,CAAC,gCAAgC,EAAE,MAAM,CAAC;AACzD,IAAI,IAAI,MAAM,CAAC,OAAO,EAAE;AACxB,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,MAAM,CAAC,QAAQ,CAAC,cAAc,EAAE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACjG,MAAM,IAAI;AACV,QAAQ,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;AACvD,UAAU,KAAK,EAAE;AACjB,YAAY,MAAM,EAAE;AACpB,cAAc,IAAI,EAAE;AACpB;AACA,WAAW;AACX,UAAU,OAAO,EAAE;AACnB,YAAY,MAAM,EAAE;AACpB,cAAc,IAAI,EAAE;AACpB;AACA;AACA,SAAS,CAAC;AACV,QAAQ,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;AAClD,UAAU,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;AACzC,UAAU,MAAM,GAAG,mBAAmB,IAAI,IAAI,EAAE;AAChD,UAAU,MAAM,MAAM,GAAG,CAAC,EAAE,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAC9F,UAAU,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC;AACpE,YAAY,KAAK,EAAE;AACnB,cAAc,MAAM,EAAE,QAAQ,CAAC,EAAE;AACjC,cAAc,SAAS,EAAE,OAAO,CAAC,EAAE;AACnC,cAAc,OAAO,EAAE,KAAK,CAAC,EAAE;AAC/B,cAAc;AACd;AACA,WAAW,CAAC;AACZ,UAAU,IAAI,CAAC,aAAa,EAAE;AAC9B,YAAY,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AAC7C,cAAc,IAAI,EAAE;AACpB,gBAAgB,MAAM,EAAE,QAAQ,CAAC,EAAE;AACnC,gBAAgB,SAAS,EAAE,OAAO,CAAC,EAAE;AACrC,gBAAgB,OAAO,EAAE,KAAK,CAAC,EAAE;AACjC,gBAAgB,IAAI,EAAE,CAAC;AACvB,gBAAgB;AAChB;AACA,aAAa,CAAC;AACd,YAAY,OAAO,CAAC,GAAG;AACvB,cAAc,CAAC,mCAAmC,EAAE,QAAQ,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC;AAC9G,aAAa;AACb,WAAW,MAAM;AACjB,YAAY,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AAC7C,cAAc,KAAK,EAAE,EAAE,EAAE,EAAE,aAAa,CAAC,EAAE,EAAE;AAC7C,cAAc,IAAI,EAAE;AACpB,gBAAgB,IAAI,EAAE,CAAC;AACvB,gBAAgB,SAAS,kBAAkB,IAAI,IAAI;AACnD;AACA,aAAa,CAAC;AACd,YAAY,OAAO,CAAC,GAAG;AACvB,cAAc,CAAC,mCAAmC,EAAE,QAAQ,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC;AAC9G,aAAa;AACb;AACA;AACA,OAAO,CAAC,OAAO,UAAU,EAAE;AAC3B,QAAQ,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,UAAU,CAAC;AACtE;AACA,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,OAAO,EAAE,uCAAuC;AACxD,QAAQ,OAAO,EAAE;AACjB,UAAU,QAAQ,EAAE,MAAM,CAAC,QAAQ;AACnC,UAAU,MAAM,EAAE,MAAM,CAAC;AACzB;AACA,OAAO,CAAC;AACR,KAAK,MAAM;AACX,MAAM,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,MAAM,CAAC,KAAK,CAAC;AACnE,MAAM,OAAO,IAAI;AACjB,QAAQ;AACR,UAAU,OAAO,EAAE,KAAK;AACxB,UAAU,KAAK,EAAE,MAAM,CAAC,KAAK,IAAI;AACjC,SAAS;AACT,QAAQ,EAAE,MAAM,EAAE,GAAG;AACrB,OAAO;AACP;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC;AAC5D,IAAI,IAAI,KAAK,YAAY,KAAK,EAAE;AAChC,MAAM,OAAO,CAAC,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,IAAI,CAAC;AAC9C,MAAM,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,OAAO,CAAC;AACpD,MAAM,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,KAAK,CAAC;AAChD;AACA,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,KAAK,EAAE,KAAK,CAAC,OAAO,IAAI;AAChC,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;;;;"}