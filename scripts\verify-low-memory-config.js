#!/usr/bin/env node

/**
 * Verify Low Memory Configuration Script
 * This script checks if all the low-memory environment variables are properly set
 */

console.log('🔍 Verifying Low Memory Configuration...\n');

const requiredEnvVars = {
  // Circuit Breaker Settings
  'CIRCUIT_BREAKER_MEMORY_THRESHOLD': '50',
  'CIRCUIT_BREAKER_CPU_THRESHOLD': '50',
  'CIRCUIT_BREAKER_DEGRADED_MEMORY_THRESHOLD': '30',
  'CIRCUIT_BREAKER_DEGRADED_CPU_THRESHOLD': '30',
  
  // Job Processing Settings
  'ENRICH_JOBS_BATCH_SIZE': '5',
  'ENRICH_JOBS_CONCURRENCY': '1',
  'ENRICH_JOBS_MAX_MEMORY': '400',
  'ENRICH_JOBS_WARNING_MEMORY': '300',
  'ENRICH_JOBS_MAX_PER_RUN': '20',
  
  // Feature Toggles
  'DISABLE_HTML_PREVIEWS': 'true',
  'DISABLE_SCREENSHOTS': 'true',
  'DISABLE_FILE_STORAGE': 'true',
  
  // Scraper Settings
  'SCRAPER_MAX_WORKERS': '1',
  'SCRAPER_BATCH_SIZE': '2',
  'JOB_DETAILS_BATCH_SIZE': '1',
  'JOB_DETAILS_MAX_JOBS': '20'
};

let allCorrect = true;
let warnings = [];

console.log('📋 Environment Variable Check:');
console.log('================================');

for (const [envVar, expectedValue] of Object.entries(requiredEnvVars)) {
  const actualValue = process.env[envVar];
  
  if (actualValue === expectedValue) {
    console.log(`✅ ${envVar}: ${actualValue}`);
  } else if (actualValue) {
    console.log(`⚠️  ${envVar}: ${actualValue} (expected: ${expectedValue})`);
    warnings.push(`${envVar} is set to "${actualValue}" but should be "${expectedValue}"`);
  } else {
    console.log(`❌ ${envVar}: NOT SET (expected: ${expectedValue})`);
    allCorrect = false;
  }
}

// Check Node.js memory limit
console.log('\n🧠 Node.js Memory Configuration:');
console.log('=================================');

const nodeOptions = process.env.NODE_OPTIONS;
if (nodeOptions && nodeOptions.includes('--max-old-space-size=400')) {
  console.log(`✅ NODE_OPTIONS: ${nodeOptions}`);
} else if (nodeOptions) {
  console.log(`⚠️  NODE_OPTIONS: ${nodeOptions} (should include --max-old-space-size=400)`);
  warnings.push('NODE_OPTIONS should include --max-old-space-size=400');
} else {
  console.log(`❌ NODE_OPTIONS: NOT SET (should be --max-old-space-size=400)`);
  allCorrect = false;
}

// Summary
console.log('\n📊 Configuration Summary:');
console.log('=========================');

if (allCorrect && warnings.length === 0) {
  console.log('🎉 All low-memory configuration settings are correct!');
  console.log('🚀 Your system should now operate within memory constraints.');
} else {
  if (!allCorrect) {
    console.log('❌ Some required environment variables are missing.');
    console.log('📝 Please check your render.yaml or environment configuration.');
  }
  
  if (warnings.length > 0) {
    console.log('\n⚠️  Warnings:');
    warnings.forEach(warning => console.log(`   - ${warning}`));
  }
}

console.log('\n💡 Expected behavior with these settings:');
console.log('   - Memory usage should stay below 50%');
console.log('   - HTML previews will be disabled');
console.log('   - Smaller batch processing (5 jobs at a time)');
console.log('   - Single-threaded processing for stability');

process.exit(allCorrect && warnings.length === 0 ? 0 : 1);
