{"version": 3, "file": "_server.ts-CIGcmnOm.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/admin/plans/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../../../chunks/auth.js\";\nimport { i as initializePlansInDatabase, g as getPlansFromDatabase, s as syncPlan, a as syncPlanWithStripeAndUpdateDb } from \"../../../../../chunks/plan-sync.js\";\nconst GET = async ({ cookies }) => {\n  const token = cookies.get(\"auth_token\");\n  if (!token) return new Response(\"Unauthorized\", { status: 401 });\n  const userData = await verifySessionToken(token);\n  if (!userData?.id) return new Response(\"Unauthorized\", { status: 401 });\n  const user = await prisma.user.findUnique({\n    where: { id: userData.id },\n    select: { isAdmin: true, role: true }\n  });\n  if (!user || !user.isAdmin && user.role !== \"admin\") {\n    return new Response(\"Unauthorized\", { status: 401 });\n  }\n  try {\n    await initializePlansInDatabase();\n    const plans = await getPlansFromDatabase();\n    return json(plans);\n  } catch (error) {\n    console.error(\"Error loading plans:\", error);\n    return new Response(`Failed to load plans: ${error.message}`, { status: 500 });\n  }\n};\nconst POST = async ({ cookies, request }) => {\n  const token = cookies.get(\"auth_token\");\n  if (!token) return new Response(\"Unauthorized\", { status: 401 });\n  const userData = await verifySessionToken(token);\n  if (!userData?.id) return new Response(\"Unauthorized\", { status: 401 });\n  const user = await prisma.user.findUnique({\n    where: { id: userData.id },\n    select: { isAdmin: true, role: true }\n  });\n  if (!user || !user.isAdmin && user.role !== \"admin\") {\n    return new Response(\"Unauthorized\", { status: 401 });\n  }\n  try {\n    await initializePlansInDatabase();\n    const requestData = await request.json();\n    const { action, syncWithStripe } = requestData;\n    if (action === \"sync_all\") {\n      const plans2 = await getPlansFromDatabase();\n      for (const plan of plans2) {\n        await syncPlan(plan);\n        if (syncWithStripe && (plan.monthlyPrice > 0 || plan.annualPrice > 0)) {\n          await syncPlanWithStripeAndUpdateDb(plan.id);\n        }\n      }\n      return json({ message: \"Plans synced successfully\" });\n    } else if (action === \"sync_with_stripe\" && requestData.planId) {\n      const updatedPlan = await syncPlanWithStripeAndUpdateDb(requestData.planId);\n      return json({ plan: updatedPlan, message: \"Plan synced with Stripe successfully\" });\n    }\n    if (action === \"update_plan\") {\n      const { plan } = requestData;\n      if (!plan || !plan.id) {\n        return new Response(\"Invalid plan data\", { status: 400 });\n      }\n      const updatedPlan = await syncPlan(plan);\n      return json({ success: true, plan: updatedPlan, message: \"Plan updated successfully\" });\n    }\n    if (action === \"update_feature\") {\n      const { planId, featureId, accessLevel, limits } = requestData;\n      if (!planId || !featureId || !accessLevel) {\n        return new Response(\"Invalid feature data\", { status: 400 });\n      }\n      try {\n        const plan = await prisma.plan.findUnique({\n          where: { id: planId },\n          include: {\n            features: {\n              include: {\n                limits: true\n              }\n            }\n          }\n        });\n        if (!plan) {\n          return new Response(`Plan not found: ${planId}`, { status: 404 });\n        }\n        const existingFeature = plan.features.find((f) => f.featureId === featureId);\n        if (existingFeature) {\n          await prisma.planFeature.update({\n            where: { id: existingFeature.id },\n            data: { accessLevel }\n          });\n          if (accessLevel === \"limited\" && limits) {\n            for (const limit of limits) {\n              const existingLimit = existingFeature.limits.find((l) => l.limitId === limit.limitId);\n              if (existingLimit) {\n                await prisma.planFeatureLimit.update({\n                  where: { id: existingLimit.id },\n                  data: { value: limit.value.toString() }\n                });\n              } else {\n                await prisma.planFeatureLimit.create({\n                  data: {\n                    planFeatureId: existingFeature.id,\n                    limitId: limit.limitId,\n                    value: limit.value.toString()\n                  }\n                });\n              }\n            }\n            const limitIdsToKeep = limits.map((l) => l.limitId);\n            for (const existingLimit of existingFeature.limits) {\n              if (!limitIdsToKeep.includes(existingLimit.limitId)) {\n                await prisma.planFeatureLimit.delete({\n                  where: { id: existingLimit.id }\n                });\n              }\n            }\n          } else {\n            await prisma.planFeatureLimit.deleteMany({\n              where: { planFeatureId: existingFeature.id }\n            });\n          }\n        } else {\n          const newFeature = await prisma.planFeature.create({\n            data: {\n              planId,\n              featureId,\n              accessLevel\n            }\n          });\n          if (accessLevel === \"limited\" && limits) {\n            for (const limit of limits) {\n              await prisma.planFeatureLimit.create({\n                data: {\n                  planFeatureId: newFeature.id,\n                  limitId: limit.limitId,\n                  value: limit.value.toString()\n                }\n              });\n            }\n          }\n        }\n        const updatedPlan = await prisma.plan.findUnique({\n          where: { id: planId },\n          include: {\n            features: {\n              include: {\n                limits: true\n              }\n            }\n          }\n        });\n        return json({\n          success: true,\n          plan: updatedPlan,\n          message: `Feature ${featureId} updated successfully for plan ${planId}`\n        });\n      } catch (error) {\n        console.error(\"Error updating feature:\", error);\n        return new Response(`Failed to update feature: ${error.message}`, { status: 500 });\n      }\n    }\n    const { plans } = requestData;\n    if (!Array.isArray(plans)) {\n      return new Response(\"Invalid plans data\", { status: 400 });\n    }\n    for (const plan of plans) {\n      console.log(`Updating plan: ${plan.id}`);\n      await prisma.plan.update({\n        where: { id: plan.id },\n        data: {\n          name: plan.name,\n          description: plan.description,\n          section: plan.section,\n          monthlyPrice: plan.monthlyPrice,\n          annualPrice: plan.annualPrice,\n          stripePriceMonthlyId: plan.stripePriceMonthlyId,\n          stripePriceYearlyId: plan.stripePriceYearlyId,\n          popular: plan.popular || false\n        }\n      });\n      const existingPlanFeatures = await prisma.planFeature.findMany({\n        where: { planId: plan.id },\n        include: { limits: true }\n      });\n      for (const planFeature of plan.features) {\n        const existingPlanFeature = existingPlanFeatures.find(\n          (f) => f.featureId === planFeature.featureId\n        );\n        if (existingPlanFeature) {\n          await prisma.planFeature.update({\n            where: { id: existingPlanFeature.id },\n            data: { accessLevel: planFeature.accessLevel }\n          });\n          if (planFeature.accessLevel === \"limited\" && planFeature.limits) {\n            for (const limitValue of planFeature.limits) {\n              const existingLimit = existingPlanFeature.limits.find(\n                (l) => l.limitId === limitValue.limitId\n              );\n              if (existingLimit) {\n                await prisma.planFeatureLimit.update({\n                  where: { id: existingLimit.id },\n                  data: { value: limitValue.value.toString() }\n                });\n              } else {\n                await prisma.planFeatureLimit.create({\n                  data: {\n                    planFeatureId: existingPlanFeature.id,\n                    limitId: limitValue.limitId,\n                    value: limitValue.value.toString()\n                  }\n                });\n              }\n            }\n            const limitIdsToKeep = planFeature.limits.map((l) => l.limitId);\n            const limitsToRemove = existingPlanFeature.limits.filter(\n              (l) => !limitIdsToKeep.includes(l.limitId)\n            );\n            for (const limitToRemove of limitsToRemove) {\n              await prisma.planFeatureLimit.delete({\n                where: { id: limitToRemove.id }\n              });\n            }\n          } else {\n            for (const limitToRemove of existingPlanFeature.limits) {\n              await prisma.planFeatureLimit.delete({\n                where: { id: limitToRemove.id }\n              });\n            }\n          }\n        } else {\n          const newPlanFeature = await prisma.planFeature.create({\n            data: {\n              planId: plan.id,\n              featureId: planFeature.featureId,\n              accessLevel: planFeature.accessLevel\n            }\n          });\n          if (planFeature.accessLevel === \"limited\" && planFeature.limits) {\n            for (const limitValue of planFeature.limits) {\n              await prisma.planFeatureLimit.create({\n                data: {\n                  planFeatureId: newPlanFeature.id,\n                  limitId: limitValue.limitId,\n                  value: limitValue.value.toString()\n                }\n              });\n            }\n          }\n        }\n      }\n      const featureIdsToKeep = plan.features.map((f) => f.featureId);\n      const featuresToRemove = existingPlanFeatures.filter(\n        (f) => !featureIdsToKeep.includes(f.featureId)\n      );\n      for (const featureToRemove of featuresToRemove) {\n        await prisma.planFeatureLimit.deleteMany({\n          where: { planFeatureId: featureToRemove.id }\n        });\n        await prisma.planFeature.delete({\n          where: { id: featureToRemove.id }\n        });\n      }\n    }\n    return json({ success: true });\n  } catch (error) {\n    console.error(\"Error saving plans:\", error);\n    return new Response(`Failed to save plans: ${error.message}`, { status: 500 });\n  }\n};\nexport {\n  GET,\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAIK,MAAC,GAAG,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK;AACnC,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE,EAAE,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AAClD,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE,EAAE,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAC5C,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE;AAC9B,IAAI,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AACvC,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;AACvD,IAAI,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxD;AACA,EAAE,IAAI;AACN,IAAI,MAAM,yBAAyB,EAAE;AACrC,IAAI,MAAM,KAAK,GAAG,MAAM,oBAAoB,EAAE;AAC9C,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC;AACtB,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC;AAChD,IAAI,OAAO,IAAI,QAAQ,CAAC,CAAC,sBAAsB,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClF;AACA;AACK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AAC7C,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE,EAAE,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AAClD,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE,EAAE,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAC5C,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE;AAC9B,IAAI,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AACvC,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;AACvD,IAAI,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxD;AACA,EAAE,IAAI;AACN,IAAI,MAAM,yBAAyB,EAAE;AACrC,IAAI,MAAM,WAAW,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AAC5C,IAAI,MAAM,EAAE,MAAM,EAAE,cAAc,EAAE,GAAG,WAAW;AAClD,IAAI,IAAI,MAAM,KAAK,UAAU,EAAE;AAC/B,MAAM,MAAM,MAAM,GAAG,MAAM,oBAAoB,EAAE;AACjD,MAAM,KAAK,MAAM,IAAI,IAAI,MAAM,EAAE;AACjC,QAAQ,MAAM,QAAQ,CAAC,IAAI,CAAC;AAC5B,QAAQ,IAAI,cAAc,KAAK,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;AAC/E,UAAU,MAAM,6BAA6B,CAAC,IAAI,CAAC,EAAE,CAAC;AACtD;AACA;AACA,MAAM,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,2BAA2B,EAAE,CAAC;AAC3D,KAAK,MAAM,IAAI,MAAM,KAAK,kBAAkB,IAAI,WAAW,CAAC,MAAM,EAAE;AACpE,MAAM,MAAM,WAAW,GAAG,MAAM,6BAA6B,CAAC,WAAW,CAAC,MAAM,CAAC;AACjF,MAAM,OAAO,IAAI,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,sCAAsC,EAAE,CAAC;AACzF;AACA,IAAI,IAAI,MAAM,KAAK,aAAa,EAAE;AAClC,MAAM,MAAM,EAAE,IAAI,EAAE,GAAG,WAAW;AAClC,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;AAC7B,QAAQ,OAAO,IAAI,QAAQ,CAAC,mBAAmB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE;AACA,MAAM,MAAM,WAAW,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC;AAC9C,MAAM,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,2BAA2B,EAAE,CAAC;AAC7F;AACA,IAAI,IAAI,MAAM,KAAK,gBAAgB,EAAE;AACrC,MAAM,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,WAAW;AACpE,MAAM,IAAI,CAAC,MAAM,IAAI,CAAC,SAAS,IAAI,CAAC,WAAW,EAAE;AACjD,QAAQ,OAAO,IAAI,QAAQ,CAAC,sBAAsB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpE;AACA,MAAM,IAAI;AACV,QAAQ,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAClD,UAAU,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;AAC/B,UAAU,OAAO,EAAE;AACnB,YAAY,QAAQ,EAAE;AACtB,cAAc,OAAO,EAAE;AACvB,gBAAgB,MAAM,EAAE;AACxB;AACA;AACA;AACA,SAAS,CAAC;AACV,QAAQ,IAAI,CAAC,IAAI,EAAE;AACnB,UAAU,OAAO,IAAI,QAAQ,CAAC,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3E;AACA,QAAQ,MAAM,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,KAAK,SAAS,CAAC;AACpF,QAAQ,IAAI,eAAe,EAAE;AAC7B,UAAU,MAAM,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;AAC1C,YAAY,KAAK,EAAE,EAAE,EAAE,EAAE,eAAe,CAAC,EAAE,EAAE;AAC7C,YAAY,IAAI,EAAE,EAAE,WAAW;AAC/B,WAAW,CAAC;AACZ,UAAU,IAAI,WAAW,KAAK,SAAS,IAAI,MAAM,EAAE;AACnD,YAAY,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;AACxC,cAAc,MAAM,aAAa,GAAG,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,KAAK,KAAK,CAAC,OAAO,CAAC;AACnG,cAAc,IAAI,aAAa,EAAE;AACjC,gBAAgB,MAAM,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;AACrD,kBAAkB,KAAK,EAAE,EAAE,EAAE,EAAE,aAAa,CAAC,EAAE,EAAE;AACjD,kBAAkB,IAAI,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE;AACvD,iBAAiB,CAAC;AAClB,eAAe,MAAM;AACrB,gBAAgB,MAAM,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;AACrD,kBAAkB,IAAI,EAAE;AACxB,oBAAoB,aAAa,EAAE,eAAe,CAAC,EAAE;AACrD,oBAAoB,OAAO,EAAE,KAAK,CAAC,OAAO;AAC1C,oBAAoB,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,QAAQ;AAC/C;AACA,iBAAiB,CAAC;AAClB;AACA;AACA,YAAY,MAAM,cAAc,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC;AAC/D,YAAY,KAAK,MAAM,aAAa,IAAI,eAAe,CAAC,MAAM,EAAE;AAChE,cAAc,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE;AACnE,gBAAgB,MAAM,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;AACrD,kBAAkB,KAAK,EAAE,EAAE,EAAE,EAAE,aAAa,CAAC,EAAE;AAC/C,iBAAiB,CAAC;AAClB;AACA;AACA,WAAW,MAAM;AACjB,YAAY,MAAM,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC;AACrD,cAAc,KAAK,EAAE,EAAE,aAAa,EAAE,eAAe,CAAC,EAAE;AACxD,aAAa,CAAC;AACd;AACA,SAAS,MAAM;AACf,UAAU,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;AAC7D,YAAY,IAAI,EAAE;AAClB,cAAc,MAAM;AACpB,cAAc,SAAS;AACvB,cAAc;AACd;AACA,WAAW,CAAC;AACZ,UAAU,IAAI,WAAW,KAAK,SAAS,IAAI,MAAM,EAAE;AACnD,YAAY,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;AACxC,cAAc,MAAM,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;AACnD,gBAAgB,IAAI,EAAE;AACtB,kBAAkB,aAAa,EAAE,UAAU,CAAC,EAAE;AAC9C,kBAAkB,OAAO,EAAE,KAAK,CAAC,OAAO;AACxC,kBAAkB,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,QAAQ;AAC7C;AACA,eAAe,CAAC;AAChB;AACA;AACA;AACA,QAAQ,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AACzD,UAAU,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;AAC/B,UAAU,OAAO,EAAE;AACnB,YAAY,QAAQ,EAAE;AACtB,cAAc,OAAO,EAAE;AACvB,gBAAgB,MAAM,EAAE;AACxB;AACA;AACA;AACA,SAAS,CAAC;AACV,QAAQ,OAAO,IAAI,CAAC;AACpB,UAAU,OAAO,EAAE,IAAI;AACvB,UAAU,IAAI,EAAE,WAAW;AAC3B,UAAU,OAAO,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC,+BAA+B,EAAE,MAAM,CAAC;AAChF,SAAS,CAAC;AACV,OAAO,CAAC,OAAO,KAAK,EAAE;AACtB,QAAQ,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC;AACvD,QAAQ,OAAO,IAAI,QAAQ,CAAC,CAAC,0BAA0B,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1F;AACA;AACA,IAAI,MAAM,EAAE,KAAK,EAAE,GAAG,WAAW;AACjC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAC/B,MAAM,OAAO,IAAI,QAAQ,CAAC,oBAAoB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAChE;AACA,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;AAC9B,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AAC9C,MAAM,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AAC/B,QAAQ,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;AAC9B,QAAQ,IAAI,EAAE;AACd,UAAU,IAAI,EAAE,IAAI,CAAC,IAAI;AACzB,UAAU,WAAW,EAAE,IAAI,CAAC,WAAW;AACvC,UAAU,OAAO,EAAE,IAAI,CAAC,OAAO;AAC/B,UAAU,YAAY,EAAE,IAAI,CAAC,YAAY;AACzC,UAAU,WAAW,EAAE,IAAI,CAAC,WAAW;AACvC,UAAU,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;AACzD,UAAU,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;AACvD,UAAU,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI;AACnC;AACA,OAAO,CAAC;AACR,MAAM,MAAM,oBAAoB,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;AACrE,QAAQ,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;AAClC,QAAQ,OAAO,EAAE,EAAE,MAAM,EAAE,IAAI;AAC/B,OAAO,CAAC;AACR,MAAM,KAAK,MAAM,WAAW,IAAI,IAAI,CAAC,QAAQ,EAAE;AAC/C,QAAQ,MAAM,mBAAmB,GAAG,oBAAoB,CAAC,IAAI;AAC7D,UAAU,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,KAAK,WAAW,CAAC;AAC7C,SAAS;AACT,QAAQ,IAAI,mBAAmB,EAAE;AACjC,UAAU,MAAM,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;AAC1C,YAAY,KAAK,EAAE,EAAE,EAAE,EAAE,mBAAmB,CAAC,EAAE,EAAE;AACjD,YAAY,IAAI,EAAE,EAAE,WAAW,EAAE,WAAW,CAAC,WAAW;AACxD,WAAW,CAAC;AACZ,UAAU,IAAI,WAAW,CAAC,WAAW,KAAK,SAAS,IAAI,WAAW,CAAC,MAAM,EAAE;AAC3E,YAAY,KAAK,MAAM,UAAU,IAAI,WAAW,CAAC,MAAM,EAAE;AACzD,cAAc,MAAM,aAAa,GAAG,mBAAmB,CAAC,MAAM,CAAC,IAAI;AACnE,gBAAgB,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,KAAK,UAAU,CAAC;AAChD,eAAe;AACf,cAAc,IAAI,aAAa,EAAE;AACjC,gBAAgB,MAAM,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;AACrD,kBAAkB,KAAK,EAAE,EAAE,EAAE,EAAE,aAAa,CAAC,EAAE,EAAE;AACjD,kBAAkB,IAAI,EAAE,EAAE,KAAK,EAAE,UAAU,CAAC,KAAK,CAAC,QAAQ,EAAE;AAC5D,iBAAiB,CAAC;AAClB,eAAe,MAAM;AACrB,gBAAgB,MAAM,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;AACrD,kBAAkB,IAAI,EAAE;AACxB,oBAAoB,aAAa,EAAE,mBAAmB,CAAC,EAAE;AACzD,oBAAoB,OAAO,EAAE,UAAU,CAAC,OAAO;AAC/C,oBAAoB,KAAK,EAAE,UAAU,CAAC,KAAK,CAAC,QAAQ;AACpD;AACA,iBAAiB,CAAC;AAClB;AACA;AACA,YAAY,MAAM,cAAc,GAAG,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC;AAC3E,YAAY,MAAM,cAAc,GAAG,mBAAmB,CAAC,MAAM,CAAC,MAAM;AACpE,cAAc,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO;AACvD,aAAa;AACb,YAAY,KAAK,MAAM,aAAa,IAAI,cAAc,EAAE;AACxD,cAAc,MAAM,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;AACnD,gBAAgB,KAAK,EAAE,EAAE,EAAE,EAAE,aAAa,CAAC,EAAE;AAC7C,eAAe,CAAC;AAChB;AACA,WAAW,MAAM;AACjB,YAAY,KAAK,MAAM,aAAa,IAAI,mBAAmB,CAAC,MAAM,EAAE;AACpE,cAAc,MAAM,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;AACnD,gBAAgB,KAAK,EAAE,EAAE,EAAE,EAAE,aAAa,CAAC,EAAE;AAC7C,eAAe,CAAC;AAChB;AACA;AACA,SAAS,MAAM;AACf,UAAU,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;AACjE,YAAY,IAAI,EAAE;AAClB,cAAc,MAAM,EAAE,IAAI,CAAC,EAAE;AAC7B,cAAc,SAAS,EAAE,WAAW,CAAC,SAAS;AAC9C,cAAc,WAAW,EAAE,WAAW,CAAC;AACvC;AACA,WAAW,CAAC;AACZ,UAAU,IAAI,WAAW,CAAC,WAAW,KAAK,SAAS,IAAI,WAAW,CAAC,MAAM,EAAE;AAC3E,YAAY,KAAK,MAAM,UAAU,IAAI,WAAW,CAAC,MAAM,EAAE;AACzD,cAAc,MAAM,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;AACnD,gBAAgB,IAAI,EAAE;AACtB,kBAAkB,aAAa,EAAE,cAAc,CAAC,EAAE;AAClD,kBAAkB,OAAO,EAAE,UAAU,CAAC,OAAO;AAC7C,kBAAkB,KAAK,EAAE,UAAU,CAAC,KAAK,CAAC,QAAQ;AAClD;AACA,eAAe,CAAC;AAChB;AACA;AACA;AACA;AACA,MAAM,MAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC;AACpE,MAAM,MAAM,gBAAgB,GAAG,oBAAoB,CAAC,MAAM;AAC1D,QAAQ,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS;AACrD,OAAO;AACP,MAAM,KAAK,MAAM,eAAe,IAAI,gBAAgB,EAAE;AACtD,QAAQ,MAAM,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC;AACjD,UAAU,KAAK,EAAE,EAAE,aAAa,EAAE,eAAe,CAAC,EAAE;AACpD,SAAS,CAAC;AACV,QAAQ,MAAM,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;AACxC,UAAU,KAAK,EAAE,EAAE,EAAE,EAAE,eAAe,CAAC,EAAE;AACzC,SAAS,CAAC;AACV;AACA;AACA,IAAI,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;AAClC,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC;AAC/C,IAAI,OAAO,IAAI,QAAQ,CAAC,CAAC,sBAAsB,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClF;AACA;;;;"}