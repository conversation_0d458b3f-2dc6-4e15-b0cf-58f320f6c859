{"version": 3, "file": "_server.ts-DLjPh16S.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/email/analytics/export/_server.ts.js"], "sourcesContent": ["import { l as logger } from \"../../../../../../chunks/logger.js\";\nimport { p as prisma } from \"../../../../../../chunks/prisma.js\";\nasync function GET({ url, setHeaders }) {\n  try {\n    const timeRange = url.searchParams.get(\"timeRange\") || \"7d\";\n    const type = url.searchParams.get(\"type\") || \"all\";\n    const template = url.searchParams.get(\"template\") || \"all\";\n    const startDateParam = url.searchParams.get(\"startDate\");\n    const endDateParam = url.searchParams.get(\"endDate\");\n    try {\n      await prisma.emailEvent.count();\n    } catch (error) {\n      logger.warn(\"EmailEvent table does not exist or is not accessible\");\n      const headers2 = [\"ID\", \"Email\", \"Event Type\", \"Timestamp\", \"Template\", \"Category\"];\n      const csv2 = headers2.join(\",\");\n      setHeaders({\n        \"Content-Type\": \"text/csv\",\n        \"Content-Disposition\": `attachment; filename=\"email-analytics-empty-${(/* @__PURE__ */ new Date()).toISOString().split(\"T\")[0]}.csv\"`\n      });\n      return new Response(csv2);\n    }\n    const now = /* @__PURE__ */ new Date();\n    let startDate = /* @__PURE__ */ new Date();\n    let endDate = now;\n    if (startDateParam && endDateParam) {\n      startDate = new Date(startDateParam);\n      endDate = new Date(endDateParam);\n    } else {\n      switch (timeRange) {\n        case \"24h\":\n          startDate.setHours(now.getHours() - 24);\n          break;\n        case \"7d\":\n          startDate.setDate(now.getDate() - 7);\n          break;\n        case \"30d\":\n          startDate.setDate(now.getDate() - 30);\n          break;\n        case \"90d\":\n          startDate.setDate(now.getDate() - 90);\n          break;\n        default:\n          startDate.setDate(now.getDate() - 7);\n      }\n    }\n    const whereConditions = {\n      timestamp: {\n        gte: startDate,\n        lte: endDate\n      }\n    };\n    if (type !== \"all\") {\n      whereConditions.type = type;\n    }\n    if (template !== \"all\") {\n      whereConditions.templateName = template;\n    }\n    const events = await prisma.emailEvent.findMany({\n      where: whereConditions,\n      orderBy: {\n        timestamp: \"desc\"\n      },\n      select: {\n        id: true,\n        email: true,\n        type: true,\n        timestamp: true,\n        templateName: true,\n        category: true\n      }\n    });\n    const headers = [\"ID\", \"Email\", \"Event Type\", \"Timestamp\", \"Template\", \"Category\"];\n    const rows = events.map((event) => [\n      event.id,\n      event.email,\n      event.type,\n      event.timestamp.toISOString(),\n      event.templateName || \"\",\n      event.category || \"\"\n    ]);\n    const csv = [\n      headers.join(\",\"),\n      ...rows.map((row) => row.map((cell) => `\"${String(cell).replace(/\"/g, '\"\"')}\"`).join(\",\"))\n    ].join(\"\\n\");\n    setHeaders({\n      \"Content-Type\": \"text/csv\",\n      \"Content-Disposition\": `attachment; filename=\"email-analytics-${(/* @__PURE__ */ new Date()).toISOString().split(\"T\")[0]}.csv\"`\n    });\n    return new Response(csv);\n  } catch (error) {\n    logger.error(\"Error exporting email analytics:\", error);\n    const headers = [\"ID\", \"Email\", \"Event Type\", \"Timestamp\", \"Template\", \"Category\"];\n    const csv = headers.join(\",\");\n    setHeaders({\n      \"Content-Type\": \"text/csv\",\n      \"Content-Disposition\": `attachment; filename=\"email-analytics-error-${(/* @__PURE__ */ new Date()).toISOString().split(\"T\")[0]}.csv\"`\n    });\n    return new Response(csv);\n  }\n}\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;AAEA,eAAe,GAAG,CAAC,EAAE,GAAG,EAAE,UAAU,EAAE,EAAE;AACxC,EAAE,IAAI;AACN,IAAI,MAAM,SAAS,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,IAAI;AAC/D,IAAI,MAAM,IAAI,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK;AACtD,IAAI,MAAM,QAAQ,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,KAAK;AAC9D,IAAI,MAAM,cAAc,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC;AAC5D,IAAI,MAAM,YAAY,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC;AACxD,IAAI,IAAI;AACR,MAAM,MAAM,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE;AACrC,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,MAAM,CAAC,IAAI,CAAC,sDAAsD,CAAC;AACzE,MAAM,MAAM,QAAQ,GAAG,CAAC,IAAI,EAAE,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,CAAC;AACzF,MAAM,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC;AACrC,MAAM,UAAU,CAAC;AACjB,QAAQ,cAAc,EAAE,UAAU;AAClC,QAAQ,qBAAqB,EAAE,CAAC,4CAA4C,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK;AAC5I,OAAO,CAAC;AACR,MAAM,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC;AAC/B;AACA,IAAI,MAAM,GAAG,mBAAmB,IAAI,IAAI,EAAE;AAC1C,IAAI,IAAI,SAAS,mBAAmB,IAAI,IAAI,EAAE;AAC9C,IAAI,IAAI,OAAO,GAAG,GAAG;AACrB,IAAI,IAAI,cAAc,IAAI,YAAY,EAAE;AACxC,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC;AAC1C,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC;AACtC,KAAK,MAAM;AACX,MAAM,QAAQ,SAAS;AACvB,QAAQ,KAAK,KAAK;AAClB,UAAU,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC;AACjD,UAAU;AACV,QAAQ,KAAK,IAAI;AACjB,UAAU,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AAC9C,UAAU;AACV,QAAQ,KAAK,KAAK;AAClB,UAAU,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC;AAC/C,UAAU;AACV,QAAQ,KAAK,KAAK;AAClB,UAAU,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC;AAC/C,UAAU;AACV,QAAQ;AACR,UAAU,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AAC9C;AACA;AACA,IAAI,MAAM,eAAe,GAAG;AAC5B,MAAM,SAAS,EAAE;AACjB,QAAQ,GAAG,EAAE,SAAS;AACtB,QAAQ,GAAG,EAAE;AACb;AACA,KAAK;AACL,IAAI,IAAI,IAAI,KAAK,KAAK,EAAE;AACxB,MAAM,eAAe,CAAC,IAAI,GAAG,IAAI;AACjC;AACA,IAAI,IAAI,QAAQ,KAAK,KAAK,EAAE;AAC5B,MAAM,eAAe,CAAC,YAAY,GAAG,QAAQ;AAC7C;AACA,IAAI,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;AACpD,MAAM,KAAK,EAAE,eAAe;AAC5B,MAAM,OAAO,EAAE;AACf,QAAQ,SAAS,EAAE;AACnB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,EAAE,EAAE,IAAI;AAChB,QAAQ,KAAK,EAAE,IAAI;AACnB,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,SAAS,EAAE,IAAI;AACvB,QAAQ,YAAY,EAAE,IAAI;AAC1B,QAAQ,QAAQ,EAAE;AAClB;AACA,KAAK,CAAC;AACN,IAAI,MAAM,OAAO,GAAG,CAAC,IAAI,EAAE,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,CAAC;AACtF,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK;AACvC,MAAM,KAAK,CAAC,EAAE;AACd,MAAM,KAAK,CAAC,KAAK;AACjB,MAAM,KAAK,CAAC,IAAI;AAChB,MAAM,KAAK,CAAC,SAAS,CAAC,WAAW,EAAE;AACnC,MAAM,KAAK,CAAC,YAAY,IAAI,EAAE;AAC9B,MAAM,KAAK,CAAC,QAAQ,IAAI;AACxB,KAAK,CAAC;AACN,IAAI,MAAM,GAAG,GAAG;AAChB,MAAM,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC;AACvB,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;AAC/F,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;AAChB,IAAI,UAAU,CAAC;AACf,MAAM,cAAc,EAAE,UAAU;AAChC,MAAM,qBAAqB,EAAE,CAAC,sCAAsC,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK;AACpI,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,QAAQ,CAAC,GAAG,CAAC;AAC5B,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC;AAC3D,IAAI,MAAM,OAAO,GAAG,CAAC,IAAI,EAAE,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,CAAC;AACtF,IAAI,MAAM,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC;AACjC,IAAI,UAAU,CAAC;AACf,MAAM,cAAc,EAAE,UAAU;AAChC,MAAM,qBAAqB,EAAE,CAAC,4CAA4C,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK;AAC1I,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,QAAQ,CAAC,GAAG,CAAC;AAC5B;AACA;;;;"}