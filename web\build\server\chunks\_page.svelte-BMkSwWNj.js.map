{"version": 3, "file": "_page.svelte-BMkSwWNj.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/settings/make-admin/_page.svelte.js"], "sourcesContent": ["import \"clsx\";\nimport { y as pop, w as push } from \"../../../../../chunks/index3.js\";\nimport \"../../../../../chunks/button.js\";\nimport \"../../../../../chunks/Toaster.svelte_svelte_type_style_lang.js\";\nfunction _page($$payload, $$props) {\n  push();\n  $$payload.out += `<div class=\"container mx-auto py-8\"><div class=\"mx-auto max-w-md\"><h1 class=\"mb-6 text-2xl font-bold\">Admin Access</h1> `;\n  {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<div class=\"my-8 flex justify-center\"><div class=\"border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent\"></div></div>`;\n  }\n  $$payload.out += `<!--]--></div></div>`;\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;AAIA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,wHAAwH,CAAC;AAC7I,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,8IAA8I,CAAC;AACrK;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC;AACzC,EAAE,GAAG,EAAE;AACP;;;;"}