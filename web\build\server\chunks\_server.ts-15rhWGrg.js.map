{"version": 3, "file": "_server.ts-15rhWGrg.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/help/search/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nconst GET = async ({ url, locals }) => {\n  try {\n    const query = url.searchParams.get(\"q\") || \"\";\n    const limit = parseInt(url.searchParams.get(\"limit\") || \"10\");\n    const page = parseInt(url.searchParams.get(\"page\") || \"1\");\n    const skip = (page - 1) * limit;\n    const user = locals.user;\n    if (!query || query.length < 2) {\n      return json({\n        articles: [],\n        pagination: {\n          total: 0,\n          page,\n          limit,\n          pages: 0\n        }\n      });\n    }\n    const searchQuery = {\n      OR: [\n        {\n          title: {\n            contains: query,\n            mode: \"insensitive\"\n          }\n        },\n        {\n          content: {\n            contains: query,\n            mode: \"insensitive\"\n          }\n        },\n        {\n          excerpt: {\n            contains: query,\n            mode: \"insensitive\"\n          }\n        },\n        {\n          category: {\n            name: {\n              contains: query,\n              mode: \"insensitive\"\n            }\n          }\n        },\n        {\n          tags: {\n            some: {\n              name: {\n                contains: query,\n                mode: \"insensitive\"\n              }\n            }\n          }\n        }\n      ],\n      published: true\n    };\n    const [articles, totalCount] = await Promise.all([\n      prisma.helpArticle.findMany({\n        where: searchQuery,\n        orderBy: [\n          {\n            title: {\n              // If title contains the exact query, prioritize it\n              similarity: query\n            }\n          },\n          {\n            viewCount: \"desc\"\n          }\n        ],\n        skip,\n        take: limit,\n        include: {\n          category: true,\n          tags: true\n        }\n      }),\n      prisma.helpArticle.count({\n        where: searchQuery\n      })\n    ]);\n    if (query.trim()) {\n      await prisma.helpSearch.create({\n        data: {\n          query: query.trim(),\n          userId: user?.id,\n          resultCount: totalCount\n        }\n      });\n    }\n    return json({\n      articles,\n      pagination: {\n        total: totalCount,\n        page,\n        limit,\n        pages: Math.ceil(totalCount / limit)\n      }\n    });\n  } catch (error) {\n    console.error(\"Error searching help articles:\", error);\n    return json({ error: \"Failed to search help articles\" }, { status: 500 });\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;AAEK,MAAC,GAAG,GAAG,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK;AACvC,EAAE,IAAI;AACN,IAAI,MAAM,KAAK,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE;AACjD,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC;AACjE,IAAI,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC;AAC9D,IAAI,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK;AACnC,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC5B,IAAI,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AACpC,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,QAAQ,EAAE,EAAE;AACpB,QAAQ,UAAU,EAAE;AACpB,UAAU,KAAK,EAAE,CAAC;AAClB,UAAU,IAAI;AACd,UAAU,KAAK;AACf,UAAU,KAAK,EAAE;AACjB;AACA,OAAO,CAAC;AACR;AACA,IAAI,MAAM,WAAW,GAAG;AACxB,MAAM,EAAE,EAAE;AACV,QAAQ;AACR,UAAU,KAAK,EAAE;AACjB,YAAY,QAAQ,EAAE,KAAK;AAC3B,YAAY,IAAI,EAAE;AAClB;AACA,SAAS;AACT,QAAQ;AACR,UAAU,OAAO,EAAE;AACnB,YAAY,QAAQ,EAAE,KAAK;AAC3B,YAAY,IAAI,EAAE;AAClB;AACA,SAAS;AACT,QAAQ;AACR,UAAU,OAAO,EAAE;AACnB,YAAY,QAAQ,EAAE,KAAK;AAC3B,YAAY,IAAI,EAAE;AAClB;AACA,SAAS;AACT,QAAQ;AACR,UAAU,QAAQ,EAAE;AACpB,YAAY,IAAI,EAAE;AAClB,cAAc,QAAQ,EAAE,KAAK;AAC7B,cAAc,IAAI,EAAE;AACpB;AACA;AACA,SAAS;AACT,QAAQ;AACR,UAAU,IAAI,EAAE;AAChB,YAAY,IAAI,EAAE;AAClB,cAAc,IAAI,EAAE;AACpB,gBAAgB,QAAQ,EAAE,KAAK;AAC/B,gBAAgB,IAAI,EAAE;AACtB;AACA;AACA;AACA;AACA,OAAO;AACP,MAAM,SAAS,EAAE;AACjB,KAAK;AACL,IAAI,MAAM,CAAC,QAAQ,EAAE,UAAU,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;AACrD,MAAM,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;AAClC,QAAQ,KAAK,EAAE,WAAW;AAC1B,QAAQ,OAAO,EAAE;AACjB,UAAU;AACV,YAAY,KAAK,EAAE;AACnB;AACA,cAAc,UAAU,EAAE;AAC1B;AACA,WAAW;AACX,UAAU;AACV,YAAY,SAAS,EAAE;AACvB;AACA,SAAS;AACT,QAAQ,IAAI;AACZ,QAAQ,IAAI,EAAE,KAAK;AACnB,QAAQ,OAAO,EAAE;AACjB,UAAU,QAAQ,EAAE,IAAI;AACxB,UAAU,IAAI,EAAE;AAChB;AACA,OAAO,CAAC;AACR,MAAM,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC;AAC/B,QAAQ,KAAK,EAAE;AACf,OAAO;AACP,KAAK,CAAC;AACN,IAAI,IAAI,KAAK,CAAC,IAAI,EAAE,EAAE;AACtB,MAAM,MAAM,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC;AACrC,QAAQ,IAAI,EAAE;AACd,UAAU,KAAK,EAAE,KAAK,CAAC,IAAI,EAAE;AAC7B,UAAU,MAAM,EAAE,IAAI,EAAE,EAAE;AAC1B,UAAU,WAAW,EAAE;AACvB;AACA,OAAO,CAAC;AACR;AACA,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,QAAQ;AACd,MAAM,UAAU,EAAE;AAClB,QAAQ,KAAK,EAAE,UAAU;AACzB,QAAQ,IAAI;AACZ,QAAQ,KAAK;AACb,QAAQ,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,KAAK;AAC3C;AACA,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC;AAC1D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,gCAAgC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7E;AACA;;;;"}