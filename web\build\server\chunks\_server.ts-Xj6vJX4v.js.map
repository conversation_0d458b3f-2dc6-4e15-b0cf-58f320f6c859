{"version": 3, "file": "_server.ts-Xj6vJX4v.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/billing/delete-payment-method/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../../../chunks/auth.js\";\nconst POST = async ({ cookies, request }) => {\n  const token = cookies.get(\"auth_token\");\n  const isProd = process.env.NODE_ENV === \"production\";\n  const { paymentMethodId } = await request.json();\n  if (!token) return new Response(\"Unauthorized\", { status: 401 });\n  if (!paymentMethodId) return new Response(\"Payment method ID is required\", { status: 400 });\n  const userData = await verifySessionToken(token);\n  if (!userData?.id) return new Response(\"Unauthorized\", { status: 401 });\n  const stripeSecret = isProd ? process.env.STRIPE_SECRET_KEY_LIVE || \"sk_live_placeholder\" : process.env.STRIPE_SECRET_KEY_TEST || \"sk_test_placeholder\";\n  const Stripe = (await import(\"stripe\")).default;\n  const stripe = new Stripe(stripeSecret, {\n    apiVersion: \"2025-04-30.basil\"\n  });\n  let user = await prisma.user.findUnique({ where: { id: userData.id } });\n  if (!user) return new Response(\"User not found\", { status: 404 });\n  if (!user.stripeCustomerId) return new Response(\"No customer found\", { status: 404 });\n  try {\n    const customer = await stripe.customers.retrieve(user.stripeCustomerId);\n    if (customer && !(\"deleted\" in customer) && customer.invoice_settings?.default_payment_method === paymentMethodId) {\n      const paymentMethods = await stripe.paymentMethods.list({\n        customer: user.stripeCustomerId,\n        type: \"card\"\n      });\n      if (paymentMethods.data.length <= 1) {\n        return new Response(\n          \"Cannot delete the default payment method. Please add another payment method first.\",\n          { status: 400 }\n        );\n      }\n      const newDefaultMethod = paymentMethods.data.find((method) => method.id !== paymentMethodId);\n      if (newDefaultMethod) {\n        await stripe.customers.update(user.stripeCustomerId, {\n          invoice_settings: {\n            default_payment_method: newDefaultMethod.id\n          }\n        });\n      }\n    }\n    await stripe.paymentMethods.detach(paymentMethodId);\n    const updatedPaymentMethodsResponse = await stripe.paymentMethods.list({\n      customer: user.stripeCustomerId,\n      type: \"card\"\n    });\n    const updatedCustomer = await stripe.customers.retrieve(user.stripeCustomerId);\n    let defaultPaymentMethod = null;\n    if (updatedCustomer && !(\"deleted\" in updatedCustomer) && updatedCustomer.invoice_settings?.default_payment_method) {\n      defaultPaymentMethod = updatedCustomer.invoice_settings.default_payment_method;\n    }\n    const updatedPaymentMethods = updatedPaymentMethodsResponse.data.map((method) => ({\n      ...method,\n      isDefault: method.id === defaultPaymentMethod\n    }));\n    return json({\n      success: true,\n      message: \"Payment method deleted\",\n      paymentMethods: updatedPaymentMethods\n    });\n  } catch (error) {\n    console.error(\"Error deleting payment method:\", error);\n    return new Response(\"Failed to delete payment method\", { status: 500 });\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;AAGK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AAC7C,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;AACtD,EAAE,MAAM,EAAE,eAAe,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AAClD,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE,EAAE,IAAI,CAAC,eAAe,EAAE,OAAO,IAAI,QAAQ,CAAC,+BAA+B,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7F,EAAE,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AAClD,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE,EAAE,MAAM,YAAY,GAAG,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,qBAAqB,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,qBAAqB;AACzJ,EAAE,MAAM,MAAM,GAAG,CAAC,MAAM,OAAO,+BAAQ,CAAC,EAAE,OAAO;AACjD,EAAE,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,YAAY,EAAE;AAC1C,IAAI,UAAU,EAAE;AAChB,GAAG,CAAC;AACJ,EAAE,IAAI,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE,EAAE,CAAC;AACzE,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,QAAQ,CAAC,gBAAgB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnE,EAAE,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,OAAO,IAAI,QAAQ,CAAC,mBAAmB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACvF,EAAE,IAAI;AACN,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC;AAC3E,IAAI,IAAI,QAAQ,IAAI,EAAE,SAAS,IAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,gBAAgB,EAAE,sBAAsB,KAAK,eAAe,EAAE;AACvH,MAAM,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC;AAC9D,QAAQ,QAAQ,EAAE,IAAI,CAAC,gBAAgB;AACvC,QAAQ,IAAI,EAAE;AACd,OAAO,CAAC;AACR,MAAM,IAAI,cAAc,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE;AAC3C,QAAQ,OAAO,IAAI,QAAQ;AAC3B,UAAU,oFAAoF;AAC9F,UAAU,EAAE,MAAM,EAAE,GAAG;AACvB,SAAS;AACT;AACA,MAAM,MAAM,gBAAgB,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,EAAE,KAAK,eAAe,CAAC;AAClG,MAAM,IAAI,gBAAgB,EAAE;AAC5B,QAAQ,MAAM,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE;AAC7D,UAAU,gBAAgB,EAAE;AAC5B,YAAY,sBAAsB,EAAE,gBAAgB,CAAC;AACrD;AACA,SAAS,CAAC;AACV;AACA;AACA,IAAI,MAAM,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,eAAe,CAAC;AACvD,IAAI,MAAM,6BAA6B,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC;AAC3E,MAAM,QAAQ,EAAE,IAAI,CAAC,gBAAgB;AACrC,MAAM,IAAI,EAAE;AACZ,KAAK,CAAC;AACN,IAAI,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC;AAClF,IAAI,IAAI,oBAAoB,GAAG,IAAI;AACnC,IAAI,IAAI,eAAe,IAAI,EAAE,SAAS,IAAI,eAAe,CAAC,IAAI,eAAe,CAAC,gBAAgB,EAAE,sBAAsB,EAAE;AACxH,MAAM,oBAAoB,GAAG,eAAe,CAAC,gBAAgB,CAAC,sBAAsB;AACpF;AACA,IAAI,MAAM,qBAAqB,GAAG,6BAA6B,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,MAAM;AACtF,MAAM,GAAG,MAAM;AACf,MAAM,SAAS,EAAE,MAAM,CAAC,EAAE,KAAK;AAC/B,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,OAAO,EAAE,wBAAwB;AACvC,MAAM,cAAc,EAAE;AACtB,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC;AAC1D,IAAI,OAAO,IAAI,QAAQ,CAAC,iCAAiC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3E;AACA;;;;"}