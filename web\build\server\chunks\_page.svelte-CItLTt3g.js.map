{"version": 3, "file": "_page.svelte-CItLTt3g.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/blog/_page.svelte.js"], "sourcesContent": ["import { U as ensure_array_like, R as attr, V as escape_html, N as bind_props, y as pop, w as push } from \"../../../chunks/index3.js\";\nimport { S as SEO } from \"../../../chunks/SEO.js\";\nimport { C as Card } from \"../../../chunks/card.js\";\nimport { u as urlFor } from \"../../../chunks/sanityClient.js\";\nimport { P as PostCard } from \"../../../chunks/PostCard.js\";\nimport { A as Arrow_right } from \"../../../chunks/arrow-right.js\";\nfunction _page($$payload, $$props) {\n  push();\n  let data = $$props[\"data\"];\n  const { posts = [] } = data;\n  function formatDate(dateStr) {\n    const date = new Date(dateStr);\n    return date.toLocaleDateString(\"en-US\", {\n      year: \"numeric\",\n      month: \"long\",\n      day: \"numeric\"\n    });\n  }\n  const each_array = ensure_array_like(posts.slice(1));\n  SEO($$payload, {\n    title: \"Newsroom | Hirli Blog\",\n    description: \"Stay updated with the latest career advice, job search tips, and industry insights from <PERSON><PERSON><PERSON>'s team of experts.\",\n    keywords: \"career blog, job search tips, hiring trends, resume advice, interview preparation, Hirli newsroom\"\n  });\n  $$payload.out += `<!----> <div class=\"container mx-auto px-4 py-16\"><div><div class=\"mb-12 text-center\"><h1 class=\"mb-4 text-4xl font-bold\">Blog</h1> <p class=\"text-muted-foreground mx-auto max-w-2xl text-lg\">Insights, advice, and updates from the Hirli team to help you navigate your career journey.</p></div> `;\n  if (posts.length > 0) {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<div class=\"mb-16\">`;\n    Card($$payload, {\n      class: \"overflow-hidden border shadow-md\",\n      children: ($$payload2) => {\n        $$payload2.out += `<div class=\"grid md:grid-cols-2\"><div class=\"bg-muted aspect-video overflow-hidden\"><img${attr(\"src\", urlFor(posts[0].mainImage, { width: 800, height: 600, fit: \"crop\" }))}${attr(\"alt\", posts[0].title)} class=\"h-full w-full object-cover\"/></div> <div class=\"flex flex-col justify-center p-8\"><div class=\"text-muted-foreground mb-2 flex items-center gap-2 text-sm\">`;\n        if (posts[0].author) {\n          $$payload2.out += \"<!--[-->\";\n          $$payload2.out += `<span>${escape_html(posts[0].author.name)}</span> <span>•</span>`;\n        } else {\n          $$payload2.out += \"<!--[!-->\";\n        }\n        $$payload2.out += `<!--]--> <span>${escape_html(formatDate(posts[0].publishedAt))}</span> `;\n        if (posts[0].categories && posts[0].categories.length > 0) {\n          $$payload2.out += \"<!--[-->\";\n          $$payload2.out += `<span>•</span> <span>${escape_html(posts[0].categories[0].title)}</span>`;\n        } else {\n          $$payload2.out += \"<!--[!-->\";\n        }\n        $$payload2.out += `<!--]--></div> <h2 class=\"mb-4 text-2xl font-bold\">${escape_html(posts[0].title)}</h2> <p class=\"text-muted-foreground mb-6\">${escape_html(posts[0].excerpt)}</p> <div class=\"mt-auto\"><a${attr(\"href\", `/blog/${posts[0].slug.current}`)} class=\"text-primary inline-flex items-center font-medium hover:underline\">Read Article `;\n        Arrow_right($$payload2, { class: \"ml-1 h-4 w-4\" });\n        $$payload2.out += `<!----></a></div></div></div>`;\n      },\n      $$slots: { default: true }\n    });\n    $$payload.out += `<!----></div>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--> <div class=\"grid gap-8 md:grid-cols-2 lg:grid-cols-3\"><!--[-->`;\n  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n    let post = each_array[$$index];\n    PostCard($$payload, { post });\n  }\n  $$payload.out += `<!--]--></div></div></div>`;\n  bind_props($$props, { data });\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAMA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC;AAC5B,EAAE,MAAM,EAAE,KAAK,GAAG,EAAE,EAAE,GAAG,IAAI;AAC7B,EAAE,SAAS,UAAU,CAAC,OAAO,EAAE;AAC/B,IAAI,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC;AAClC,IAAI,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE;AAC5C,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,KAAK,EAAE,MAAM;AACnB,MAAM,GAAG,EAAE;AACX,KAAK,CAAC;AACN;AACA,EAAE,MAAM,UAAU,GAAG,iBAAiB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACtD,EAAE,GAAG,CAAC,SAAS,EAAE;AACjB,IAAI,KAAK,EAAE,uBAAuB;AAClC,IAAI,WAAW,EAAE,kHAAkH;AACnI,IAAI,QAAQ,EAAE;AACd,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,qSAAqS,CAAC;AAC1T,EAAE,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AACxB,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC;AAC1C,IAAI,IAAI,CAAC,SAAS,EAAE;AACpB,MAAM,KAAK,EAAE,kCAAkC;AAC/C,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,wFAAwF,EAAE,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,kKAAkK,CAAC;AACxY,QAAQ,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;AAC7B,UAAU,UAAU,CAAC,GAAG,IAAI,UAAU;AACtC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,sBAAsB,CAAC;AAC9F,SAAS,MAAM;AACf,UAAU,UAAU,CAAC,GAAG,IAAI,WAAW;AACvC;AACA,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,eAAe,EAAE,WAAW,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC;AACnG,QAAQ,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;AACnE,UAAU,UAAU,CAAC,GAAG,IAAI,UAAU;AACtC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,qBAAqB,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC;AACtG,SAAS,MAAM;AACf,UAAU,UAAU,CAAC,GAAG,IAAI,WAAW;AACvC;AACA,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,mDAAmD,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,4CAA4C,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,4BAA4B,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,wFAAwF,CAAC;AAC9V,QAAQ,WAAW,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AAC1D,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,6BAA6B,CAAC;AACzD,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B,KAAK,CAAC;AACN,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACpC,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,uEAAuE,CAAC;AAC5F,EAAE,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACrF,IAAI,IAAI,IAAI,GAAG,UAAU,CAAC,OAAO,CAAC;AAClC,IAAI,QAAQ,CAAC,SAAS,EAAE,EAAE,IAAI,EAAE,CAAC;AACjC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,0BAA0B,CAAC;AAC/C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,CAAC;AAC/B,EAAE,GAAG,EAAE;AACP;;;;"}