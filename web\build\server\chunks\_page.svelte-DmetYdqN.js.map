{"version": 3, "file": "_page.svelte-DmetYdqN.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/settings/security/_page.svelte.js"], "sourcesContent": ["import { Y as fallback, O as copy_payload, P as assign_payload, N as bind_props, y as pop, w as push, U as ensure_array_like, V as escape_html, R as attr, aa as store_mutate, _ as store_get, a1 as unsubscribe_stores } from \"../../../../../chunks/index3.js\";\nimport { R as Root$1, T as Tabs_list, a as Tabs_content } from \"../../../../../chunks/index9.js\";\nimport { S as SEO } from \"../../../../../chunks/SEO.js\";\nimport { C as Card } from \"../../../../../chunks/card.js\";\nimport { C as Card_content } from \"../../../../../chunks/card-content.js\";\nimport { R as Root, d as Dialog_overlay, P as Portal, D as Dialog_content } from \"../../../../../chunks/index7.js\";\nimport { a as toast } from \"../../../../../chunks/Toaster.svelte_svelte_type_style_lang.js\";\nimport \"@simplewebauthn/browser\";\nimport { L as Label } from \"../../../../../chunks/label.js\";\nimport { I as Input } from \"../../../../../chunks/input.js\";\nimport { P as Plus } from \"../../../../../chunks/plus.js\";\nimport { F as Fingerprint, K as Key, S as Smartphone, C as Computer } from \"../../../../../chunks/smartphone.js\";\nimport { T as Trash_2 } from \"../../../../../chunks/trash-2.js\";\nimport { I as Info } from \"../../../../../chunks/info.js\";\nimport { D as Dialog_header, a as Dialog_title, b as Dialog_description, c as Dialog_footer } from \"../../../../../chunks/dialog-description.js\";\nimport \"../../../../../chunks/client.js\";\nimport \"clsx\";\nimport \"ts-deepmerge\";\nimport { s as superForm } from \"../../../../../chunks/superForm.js\";\nimport \"../../../../../chunks/index.js\";\nimport \"../../../../../chunks/formData.js\";\nimport \"memoize-weak\";\nimport { a as zodClient } from \"../../../../../chunks/zod.js\";\nimport { z } from \"zod\";\nimport { B as Button } from \"../../../../../chunks/button.js\";\nimport { L as Lock } from \"../../../../../chunks/lock.js\";\nimport { L as Log_out } from \"../../../../../chunks/log-out.js\";\nimport { G as Globe } from \"../../../../../chunks/globe.js\";\nimport { M as Map_pin } from \"../../../../../chunks/map-pin.js\";\nimport { C as Clock } from \"../../../../../chunks/clock.js\";\nimport { M as Monitor } from \"../../../../../chunks/monitor.js\";\nimport { L as Laptop } from \"../../../../../chunks/laptop.js\";\nimport { T as Tabs_trigger } from \"../../../../../chunks/tabs-trigger.js\";\nfunction Passkeys($$payload, $$props) {\n  push();\n  let passkeys = fallback($$props[\"passkeys\"], () => [], true);\n  let showAddDialog = false;\n  let showRemoveDialog = false;\n  let passkeyName = \"\";\n  let isRegistering = false;\n  function formatDate(dateString) {\n    if (!dateString) {\n      return \"Unknown date\";\n    }\n    try {\n      const date = new Date(dateString);\n      return date.toLocaleDateString() + \" \" + date.toLocaleTimeString();\n    } catch (e) {\n      console.error(\"Error formatting date:\", e);\n      return \"Invalid date\";\n    }\n  }\n  {\n    if (!passkeys) {\n      console.error(\"Passkeys is null or undefined\");\n      passkeys = [];\n    } else if (!Array.isArray(passkeys)) {\n      console.error(\"Passkeys is not an array:\", passkeys);\n      passkeys = [];\n    } else {\n      console.log(\"Passkeys component has\", passkeys.length, \"passkeys\");\n      if (passkeys.length > 0) {\n        console.log(\"First passkey:\", passkeys[0]);\n      }\n    }\n  }\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    $$payload2.out += `<div class=\"border-border mb-4 flex items-center justify-between border-b p-4\"><div class=\"flex flex-col\"><h4 class=\"text-md font-normal\">Passkeys</h4> <p class=\"text-muted-foreground text-sm\">Manage passkeys for passwordless sign-in.</p></div> <button type=\"button\" class=\"focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-10 items-center justify-center gap-2 whitespace-nowrap rounded-md border px-4 py-2 text-sm font-medium shadow-sm transition-colors disabled:pointer-events-none disabled:opacity-50\">`;\n    Plus($$payload2, { class: \"h-4 w-4\" });\n    $$payload2.out += `<!----> Add Passkey</button></div> <div class=\"space-y-6 p-6 pt-0\">`;\n    if (passkeys.length === 0) {\n      $$payload2.out += \"<!--[-->\";\n      $$payload2.out += `<div class=\"rounded-lg border border-dashed p-8 text-center\">`;\n      Fingerprint($$payload2, {\n        class: \"text-muted-foreground mx-auto mb-4 h-10 w-10\"\n      });\n      $$payload2.out += `<!----> <h3 class=\"mb-2 text-lg font-medium\">No passkeys added yet</h3> <p class=\"text-muted-foreground mb-4 text-sm\">Add a passkey to sign in without a password using your device's biometric authentication or\n        PIN.</p> <div class=\"mx-auto mb-6 max-w-md\"><div class=\"text-muted-foreground mb-2 text-sm font-medium\">Benefits of passkeys:</div> <ul class=\"text-muted-foreground list-disc space-y-1 pl-6 text-left text-sm\"><li>No passwords to remember or type</li> <li>More secure than passwords</li> <li>Can't be phished or stolen in data breaches</li> <li>Works across your devices</li></ul></div> <button type=\"button\" class=\"focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground mx-auto inline-flex h-10 items-center justify-center gap-2 whitespace-nowrap rounded-md border px-4 py-2 text-sm font-medium shadow-sm transition-colors disabled:pointer-events-none disabled:opacity-50\">`;\n      Plus($$payload2, { class: \"h-4 w-4\" });\n      $$payload2.out += `<!----> Add Your First Passkey</button></div>`;\n    } else {\n      $$payload2.out += \"<!--[!-->\";\n      const each_array = ensure_array_like(passkeys);\n      $$payload2.out += `<div class=\"space-y-4\"><!--[-->`;\n      for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n        let passkey = each_array[$$index];\n        Card($$payload2, {\n          children: ($$payload3) => {\n            Card_content($$payload3, {\n              class: \"flex items-center justify-between rounded-lg\",\n              children: ($$payload4) => {\n                $$payload4.out += `<div class=\"flex w-full items-center gap-4\">`;\n                Key($$payload4, { class: \"text-primary h-8 w-8\" });\n                $$payload4.out += `<!----> <div><p class=\"font-medium\">${escape_html(passkey.name || \"Unnamed Passkey\")}</p> <p class=\"text-muted-foreground text-xs\">Created: ${escape_html(formatDate(passkey.createdAt))}</p> <p class=\"text-muted-foreground text-xs\">Last used: ${escape_html(formatDate(passkey.lastUsed))}</p> `;\n                if (passkey.id) {\n                  $$payload4.out += \"<!--[-->\";\n                  $$payload4.out += `<p class=\"text-muted-foreground text-xs\">ID: ${escape_html(typeof passkey.id === \"string\" && passkey.id.length > 10 ? passkey.id.substring(0, 10) + \"...\" : passkey.id)}</p>`;\n                } else {\n                  $$payload4.out += \"<!--[!-->\";\n                }\n                $$payload4.out += `<!--]--></div></div> <button type=\"button\" class=\"text-destructive hover:text-destructive/90 hover:bg-destructive/10 inline-flex h-8 w-8 items-center justify-center rounded-md p-0\">`;\n                Trash_2($$payload4, { class: \"h-4 w-4\" });\n                $$payload4.out += `<!----></button>`;\n              },\n              $$slots: { default: true }\n            });\n          },\n          $$slots: { default: true }\n        });\n      }\n      $$payload2.out += `<!--]--></div>`;\n    }\n    $$payload2.out += `<!--]--> <div class=\"bg-muted/40 mt-12 rounded-lg border p-4\"><div class=\"flex items-start gap-4\">`;\n    Info($$payload2, { class: \"text-primary mt-1 h-6 w-6\" });\n    $$payload2.out += `<!----> <div><h3 class=\"font-medium\">About Passkeys</h3> <p class=\"text-muted-foreground text-sm\">Passkeys are a more secure alternative to passwords. They use biometric authentication\n          (like fingerprint or face recognition) or a device PIN to sign you in without having to\n          remember or type a password.</p> <p class=\"text-muted-foreground mt-2 text-sm\">Your passkey is stored securely on your device and can't be phished or stolen in a data\n          breach.</p> <p class=\"text-muted-foreground mt-2 text-sm\"><strong>Tip:</strong> Add passkeys to multiple devices to ensure you can always sign in, even\n          if one device is lost or unavailable.</p> <p class=\"text-muted-foreground mt-2 text-sm\"><a href=\"https://passkeys.com/what-are-passkeys/\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"text-primary hover:underline\">Learn more about passkeys</a></p></div></div></div></div> `;\n    Root($$payload2, {\n      get open() {\n        return showAddDialog;\n      },\n      set open($$value) {\n        showAddDialog = $$value;\n        $$settled = false;\n      },\n      children: ($$payload3) => {\n        Dialog_overlay($$payload3, {});\n        $$payload3.out += `<!----> `;\n        Portal($$payload3, {\n          children: ($$payload4) => {\n            Dialog_content($$payload4, {\n              class: \"p-0 sm:max-w-[425px]\",\n              children: ($$payload5) => {\n                Dialog_header($$payload5, {\n                  class: \"border-border gap-1 border-b p-4\",\n                  children: ($$payload6) => {\n                    Dialog_title($$payload6, {\n                      children: ($$payload7) => {\n                        $$payload7.out += `<!---->Add Passkey`;\n                      },\n                      $$slots: { default: true }\n                    });\n                    $$payload6.out += `<!----> `;\n                    Dialog_description($$payload6, {\n                      children: ($$payload7) => {\n                        $$payload7.out += `<!---->Create a new passkey for passwordless sign-in.`;\n                      },\n                      $$slots: { default: true }\n                    });\n                    $$payload6.out += `<!---->`;\n                  },\n                  $$slots: { default: true }\n                });\n                $$payload5.out += `<!----> <div class=\"grid gap-4 px-4\"><div class=\"space-y-2\">`;\n                Label($$payload5, {\n                  for: \"passkey-name\",\n                  class: \"text-sm font-medium leading-none\",\n                  children: ($$payload6) => {\n                    $$payload6.out += `<!---->Passkey Name`;\n                  },\n                  $$slots: { default: true }\n                });\n                $$payload5.out += `<!----> `;\n                Input($$payload5, {\n                  id: \"passkey-name\",\n                  type: \"text\",\n                  placeholder: \"e.g., Work Laptop, Personal Phone\",\n                  class: \"border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm\",\n                  get value() {\n                    return passkeyName;\n                  },\n                  set value($$value) {\n                    passkeyName = $$value;\n                    $$settled = false;\n                  }\n                });\n                $$payload5.out += `<!----> <p class=\"text-muted-foreground text-xs\">Give your passkey a name to help you identify it later</p></div> <div class=\"bg-muted rounded-md p-3 text-sm\"><p class=\"font-medium\">What happens next?</p> <ul class=\"text-muted-foreground mt-2 list-disc pl-5 text-xs\"><li>Your device will prompt you to create a passkey</li> <li>You may need to use your fingerprint, face, or device PIN</li> <li>This passkey will be stored securely on your current device</li></ul></div></div> `;\n                Dialog_footer($$payload5, {\n                  class: \"border-border border-t p-2\",\n                  children: ($$payload6) => {\n                    $$payload6.out += `<button type=\"button\" class=\"focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-10 items-center justify-center whitespace-nowrap rounded-md border px-4 py-2 text-sm font-medium shadow-sm transition-colors disabled:pointer-events-none disabled:opacity-50\"${attr(\"disabled\", isRegistering, true)}>Cancel</button> <button type=\"button\" class=\"bg-primary text-primary-foreground hover:bg-primary/90 focus-visible:ring-ring inline-flex h-10 items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\"${attr(\"disabled\", isRegistering, true)}>${escape_html(\"Create Passkey\")}</button>`;\n                  },\n                  $$slots: { default: true }\n                });\n                $$payload5.out += `<!---->`;\n              },\n              $$slots: { default: true }\n            });\n          }\n        });\n        $$payload3.out += `<!---->`;\n      },\n      $$slots: { default: true }\n    });\n    $$payload2.out += `<!----> `;\n    Root($$payload2, {\n      get open() {\n        return showRemoveDialog;\n      },\n      set open($$value) {\n        showRemoveDialog = $$value;\n        $$settled = false;\n      },\n      children: ($$payload3) => {\n        Dialog_overlay($$payload3, {});\n        $$payload3.out += `<!----> `;\n        Portal($$payload3, {\n          children: ($$payload4) => {\n            Dialog_content($$payload4, {\n              class: \"sm:max-w-[425px]\",\n              children: ($$payload5) => {\n                Dialog_header($$payload5, {\n                  children: ($$payload6) => {\n                    Dialog_title($$payload6, {\n                      children: ($$payload7) => {\n                        $$payload7.out += `<!---->Remove Passkey`;\n                      },\n                      $$slots: { default: true }\n                    });\n                    $$payload6.out += `<!----> `;\n                    Dialog_description($$payload6, {\n                      children: ($$payload7) => {\n                        $$payload7.out += `<!---->Are you sure you want to remove this passkey? This action cannot be undone.`;\n                      },\n                      $$slots: { default: true }\n                    });\n                    $$payload6.out += `<!---->`;\n                  },\n                  $$slots: { default: true }\n                });\n                $$payload5.out += `<!----> <div class=\"py-4\">`;\n                {\n                  $$payload5.out += \"<!--[!-->\";\n                }\n                $$payload5.out += `<!--]--></div> `;\n                Dialog_footer($$payload5, {\n                  children: ($$payload6) => {\n                    $$payload6.out += `<button type=\"button\" class=\"focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-10 items-center justify-center whitespace-nowrap rounded-md border px-4 py-2 text-sm font-medium shadow-sm transition-colors disabled:pointer-events-none disabled:opacity-50\">Cancel</button> <button type=\"button\" class=\"bg-destructive text-destructive-foreground hover:bg-destructive/90 focus-visible:ring-ring inline-flex h-10 items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\">Remove Passkey</button>`;\n                  },\n                  $$slots: { default: true }\n                });\n                $$payload5.out += `<!---->`;\n              },\n              $$slots: { default: true }\n            });\n          }\n        });\n        $$payload3.out += `<!---->`;\n      },\n      $$slots: { default: true }\n    });\n    $$payload2.out += `<!---->`;\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  bind_props($$props, { passkeys });\n  pop();\n}\nfunction Password_change($$payload, $$props) {\n  push();\n  var $$store_subs;\n  const passwordSchema = z.object({\n    currentPassword: z.string().min(1, \"Current password is required\"),\n    newPassword: z.string().min(8, \"Password must be at least 8 characters\"),\n    confirmPassword: z.string().min(1, \"Please confirm your password\")\n  }).refine((data) => data.newPassword === data.confirmPassword, {\n    message: \"Passwords do not match\",\n    path: [\"confirmPassword\"]\n  });\n  let passwordForm = $$props[\"passwordForm\"];\n  const form = superForm(passwordForm, {\n    validators: zodClient(passwordSchema),\n    dataType: \"json\",\n    onUpdated: ({ form: form2, result }) => {\n      if (form2.valid && result.type === \"success\") {\n        toast.success(\"Password updated successfully\");\n        resetForm();\n      }\n    },\n    onError: () => {\n      toast.error(\"Failed to update password\");\n    }\n  });\n  const { form: formData, enhance, errors, submitting } = form;\n  function resetForm() {\n    formData.update((f) => ({\n      ...f,\n      currentPassword: \"\",\n      newPassword: \"\",\n      confirmPassword: \"\"\n    }));\n  }\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    $$payload2.out += `<form method=\"POST\" action=\"?/changePassword\"><div class=\"flex flex-col justify-between\"><div class=\"border-border flex flex-col border-b p-4\"><h4 class=\"text-md font-normal\">Change Password</h4> <p class=\"text-muted-foreground text-sm\">Update your account password.</p></div> <div class=\"flex flex-col gap-6 p-4\"><div class=\"space-y-4\"><div class=\"space-y-2\"><label for=\"currentPassword\" class=\"text-sm font-medium leading-none\">Current Password</label> `;\n    Input($$payload2, {\n      id: \"currentPassword\",\n      type: \"password\",\n      placeholder: \"Enter your current password\",\n      get value() {\n        return store_get($$store_subs ??= {}, \"$formData\", formData).currentPassword;\n      },\n      set value($$value) {\n        store_mutate($$store_subs ??= {}, \"$formData\", formData, store_get($$store_subs ??= {}, \"$formData\", formData).currentPassword = $$value);\n        $$settled = false;\n      }\n    });\n    $$payload2.out += `<!----> `;\n    if (store_get($$store_subs ??= {}, \"$errors\", errors).currentPassword) {\n      $$payload2.out += \"<!--[-->\";\n      $$payload2.out += `<p class=\"text-destructive text-sm\">${escape_html(store_get($$store_subs ??= {}, \"$errors\", errors).currentPassword)}</p>`;\n    } else {\n      $$payload2.out += \"<!--[!-->\";\n    }\n    $$payload2.out += `<!--]--></div> <div class=\"space-y-2\"><label for=\"newPassword\" class=\"text-sm font-medium leading-none\">New Password</label> `;\n    Input($$payload2, {\n      id: \"newPassword\",\n      type: \"password\",\n      placeholder: \"Enter your new password\",\n      get value() {\n        return store_get($$store_subs ??= {}, \"$formData\", formData).newPassword;\n      },\n      set value($$value) {\n        store_mutate($$store_subs ??= {}, \"$formData\", formData, store_get($$store_subs ??= {}, \"$formData\", formData).newPassword = $$value);\n        $$settled = false;\n      }\n    });\n    $$payload2.out += `<!----> `;\n    if (store_get($$store_subs ??= {}, \"$errors\", errors).newPassword) {\n      $$payload2.out += \"<!--[-->\";\n      $$payload2.out += `<p class=\"text-destructive text-sm\">${escape_html(store_get($$store_subs ??= {}, \"$errors\", errors).newPassword)}</p>`;\n    } else {\n      $$payload2.out += \"<!--[!-->\";\n    }\n    $$payload2.out += `<!--]--></div> <div class=\"space-y-2\"><label for=\"confirmPassword\" class=\"text-sm font-medium leading-none\">Confirm New Password</label> `;\n    Input($$payload2, {\n      id: \"confirmPassword\",\n      type: \"password\",\n      placeholder: \"Confirm your new password\",\n      get value() {\n        return store_get($$store_subs ??= {}, \"$formData\", formData).confirmPassword;\n      },\n      set value($$value) {\n        store_mutate($$store_subs ??= {}, \"$formData\", formData, store_get($$store_subs ??= {}, \"$formData\", formData).confirmPassword = $$value);\n        $$settled = false;\n      }\n    });\n    $$payload2.out += `<!----> `;\n    if (store_get($$store_subs ??= {}, \"$errors\", errors).confirmPassword) {\n      $$payload2.out += \"<!--[-->\";\n      $$payload2.out += `<p class=\"text-destructive text-sm\">${escape_html(store_get($$store_subs ??= {}, \"$errors\", errors).confirmPassword)}</p>`;\n    } else {\n      $$payload2.out += \"<!--[!-->\";\n    }\n    $$payload2.out += `<!--]--></div></div> <div class=\"bg-muted/40 rounded-lg border p-4\"><div class=\"flex items-start gap-4\">`;\n    Lock($$payload2, { class: \"text-primary mt-1 h-5 w-5\" });\n    $$payload2.out += `<!----> <div><h4 class=\"font-medium\">Password Security Tips</h4> <ul class=\"text-muted-foreground mt-2 list-disc space-y-1 pl-5 text-xs\"><li>Use at least 8 characters</li> <li>Include uppercase and lowercase letters</li> <li>Add numbers and special characters</li> <li>Avoid using personal information</li> <li>Don't reuse passwords across different sites</li></ul></div></div></div></div> <div class=\"flex justify-end p-6\">`;\n    Button($$payload2, {\n      type: \"submit\",\n      disabled: store_get($$store_subs ??= {}, \"$submitting\", submitting),\n      children: ($$payload3) => {\n        $$payload3.out += `<!---->${escape_html(store_get($$store_subs ??= {}, \"$submitting\", submitting) ? \"Updating...\" : \"Update Password\")}`;\n      },\n      $$slots: { default: true }\n    });\n    $$payload2.out += `<!----></div></div></form>`;\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  if ($$store_subs) unsubscribe_stores($$store_subs);\n  bind_props($$props, { passwordForm });\n  pop();\n}\nfunction Sessions($$payload, $$props) {\n  push();\n  let sessions = fallback($$props[\"sessions\"], () => [], true);\n  let showLogoutAllDialog = false;\n  function formatRelativeTime(dateString) {\n    const date = new Date(dateString);\n    const now = /* @__PURE__ */ new Date();\n    const diffMs = now.getTime() - date.getTime();\n    const diffSec = Math.round(diffMs / 1e3);\n    const diffMin = Math.round(diffSec / 60);\n    const diffHour = Math.round(diffMin / 60);\n    const diffDay = Math.round(diffHour / 24);\n    if (diffSec < 60) {\n      return \"just now\";\n    } else if (diffMin < 60) {\n      return `${diffMin} minute${diffMin > 1 ? \"s\" : \"\"} ago`;\n    } else if (diffHour < 24) {\n      return `${diffHour} hour${diffHour > 1 ? \"s\" : \"\"} ago`;\n    } else if (diffDay < 30) {\n      return `${diffDay} day${diffDay > 1 ? \"s\" : \"\"} ago`;\n    } else {\n      return date.toLocaleDateString();\n    }\n  }\n  function getDeviceIcon(device) {\n    if (device.toLowerCase().includes(\"iphone\") || device.toLowerCase().includes(\"android\") || device.toLowerCase().includes(\"mobile\")) {\n      return Smartphone;\n    } else if (device.toLowerCase().includes(\"ipad\") || device.toLowerCase().includes(\"tablet\")) {\n      return Monitor;\n    } else {\n      return Laptop;\n    }\n  }\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    const each_array = ensure_array_like(sessions);\n    $$payload2.out += `<div class=\"border-border flex flex-col justify-between border-b p-4\"><div class=\"flex flex-col\"><h4 class=\"text-md font-normal\">Active Sessions</h4> <p class=\"text-muted-foreground text-sm\">Manage your active sessions across devices.</p></div> `;\n    if (sessions.filter((s) => !s.isCurrent).length > 0) {\n      $$payload2.out += \"<!--[-->\";\n      $$payload2.out += `<button class=\"ring-offset-background focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-10 items-center justify-center gap-2 whitespace-nowrap rounded-md border px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\">`;\n      Log_out($$payload2, { class: \"h-4 w-4\" });\n      $$payload2.out += `<!----> Log Out All Other Sessions</button>`;\n    } else {\n      $$payload2.out += \"<!--[!-->\";\n    }\n    $$payload2.out += `<!--]--></div> <div class=\"p-4\"><div class=\"space-y-4\"><!--[-->`;\n    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n      let session = each_array[$$index];\n      Card($$payload2, {\n        children: ($$payload3) => {\n          Card_content($$payload3, {\n            class: \"border-border flex items-center justify-between rounded-lg\",\n            children: ($$payload4) => {\n              $$payload4.out += `<div class=\"flex items-start gap-4\"><!---->`;\n              getDeviceIcon(session.device)?.($$payload4, { class: \"text-primary mt-1 h-8 w-8\" });\n              $$payload4.out += `<!----> <div class=\"space-y-1\"><div class=\"flex items-center\"><p class=\"font-medium\">${escape_html(session.device)}</p> `;\n              if (session.isCurrent) {\n                $$payload4.out += \"<!--[-->\";\n                $$payload4.out += `<span class=\"bg-success/20 text-success ml-2 rounded-full px-2 py-0.5 text-xs font-medium\">Current</span>`;\n              } else {\n                $$payload4.out += \"<!--[!-->\";\n              }\n              $$payload4.out += `<!--]--></div> <div class=\"text-muted-foreground flex items-center gap-4 text-sm\"><div class=\"flex items-center gap-1\">`;\n              Globe($$payload4, { class: \"h-3.5 w-3.5\" });\n              $$payload4.out += `<!----> <span>${escape_html(session.browser)} on ${escape_html(session.os)}</span></div> <div class=\"flex items-center gap-1\">`;\n              Map_pin($$payload4, { class: \"h-3.5 w-3.5\" });\n              $$payload4.out += `<!----> <span>${escape_html(session.location)}</span></div></div> <div class=\"text-muted-foreground flex items-center gap-1 text-xs\">`;\n              Clock($$payload4, { class: \"h-3 w-3\" });\n              $$payload4.out += `<!----> <span>Last active: ${escape_html(formatRelativeTime(session.lastActive))}</span></div> `;\n              if (session.ip) {\n                $$payload4.out += \"<!--[-->\";\n                $$payload4.out += `<div class=\"text-muted-foreground text-xs\">IP: ${escape_html(session.ip)}</div>`;\n              } else {\n                $$payload4.out += \"<!--[!-->\";\n              }\n              $$payload4.out += `<!--]--></div></div> `;\n              if (!session.isCurrent) {\n                $$payload4.out += \"<!--[-->\";\n                $$payload4.out += `<button class=\"ring-offset-background focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-9 items-center justify-center gap-2 rounded-md border px-3 text-xs font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\">`;\n                Log_out($$payload4, { class: \"h-4 w-4\" });\n                $$payload4.out += `<!----> Log Out</button>`;\n              } else {\n                $$payload4.out += \"<!--[!-->\";\n              }\n              $$payload4.out += `<!--]-->`;\n            },\n            $$slots: { default: true }\n          });\n        },\n        $$slots: { default: true }\n      });\n    }\n    $$payload2.out += `<!--]--></div></div> `;\n    Root($$payload2, {\n      get open() {\n        return showLogoutAllDialog;\n      },\n      set open($$value) {\n        showLogoutAllDialog = $$value;\n        $$settled = false;\n      },\n      children: ($$payload3) => {\n        Dialog_overlay($$payload3, {});\n        $$payload3.out += `<!----> `;\n        Portal($$payload3, {\n          children: ($$payload4) => {\n            Dialog_content($$payload4, {\n              class: \"sm:max-w-[425px]\",\n              children: ($$payload5) => {\n                Dialog_header($$payload5, {\n                  children: ($$payload6) => {\n                    Dialog_title($$payload6, {\n                      children: ($$payload7) => {\n                        $$payload7.out += `<!---->Log Out All Other Sessions`;\n                      },\n                      $$slots: { default: true }\n                    });\n                    $$payload6.out += `<!----> `;\n                    Dialog_description($$payload6, {\n                      children: ($$payload7) => {\n                        $$payload7.out += `<!---->Are you sure you want to log out all other sessions? You will remain logged in on this\n          device.`;\n                      },\n                      $$slots: { default: true }\n                    });\n                    $$payload6.out += `<!---->`;\n                  },\n                  $$slots: { default: true }\n                });\n                $$payload5.out += `<!----> <div class=\"py-4\"><p class=\"text-muted-foreground text-sm\">This will log you out from ${escape_html(sessions.filter((s) => !s.isCurrent).length)} other device(s).</p></div> `;\n                Dialog_footer($$payload5, {\n                  children: ($$payload6) => {\n                    $$payload6.out += `<button type=\"button\" class=\"ring-offset-background focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-10 items-center justify-center rounded-md border px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\">Cancel</button> <button type=\"button\" class=\"bg-destructive text-destructive-foreground hover:bg-destructive/90 focus-visible:ring-ring inline-flex h-10 items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\">Log Out All Other Sessions</button>`;\n                  },\n                  $$slots: { default: true }\n                });\n                $$payload5.out += `<!---->`;\n              },\n              $$slots: { default: true }\n            });\n          }\n        });\n        $$payload3.out += `<!---->`;\n      },\n      $$slots: { default: true }\n    });\n    $$payload2.out += `<!---->`;\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  bind_props($$props, { sessions });\n  pop();\n}\nfunction _page($$payload, $$props) {\n  push();\n  let data = $$props[\"data\"];\n  console.log(\"Security page data:\", data);\n  console.log(\"Passkeys from data:\", data.passkeys);\n  console.log(\"User data from server:\", data.userData);\n  const tabs = [\n    {\n      id: \"password\",\n      label: \"Password\",\n      icon: Lock\n    },\n    // Two-factor and phone tabs temporarily disabled\n    // { id: 'two-factor', label: 'Two-Factor', icon: Shield },\n    // { id: 'phone', label: 'Phone', icon: Smartphone },\n    { id: \"passkeys\", label: \"Passkeys\", icon: Key },\n    {\n      id: \"sessions\",\n      label: \"Sessions\",\n      icon: Computer\n    }\n  ];\n  let activeTab = \"password\";\n  SEO($$payload, {\n    title: \"Security Settings | Hirli\",\n    description: \"Manage your account security settings, including active sessions, two-factor authentication, and connected devices.\",\n    keywords: \"account security, two-factor authentication, 2FA, sessions, devices, login history\",\n    url: \"https://hirli.com/dashboard/settings/security\"\n  });\n  $$payload.out += `<!----> <div class=\"flex flex-row justify-between p-6\"><div class=\"flex flex-col\"><h2 class=\"text-lg font-semibold\">Security</h2> <p class=\"text-foreground/80\">Manage your account security settings, including password, two-factor authentication, and\n      more.</p></div></div> <div class=\"grid gap-6\"><div>`;\n  Root$1($$payload, {\n    value: activeTab,\n    onValueChange: (value) => activeTab = value,\n    children: ($$payload2) => {\n      const each_array_1 = ensure_array_like(tabs);\n      Tabs_list($$payload2, {\n        class: \"w-full\",\n        children: ($$payload3) => {\n          const each_array = ensure_array_like(tabs);\n          $$payload3.out += `<!--[-->`;\n          for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n            let tab = each_array[$$index];\n            Tabs_trigger($$payload3, {\n              value: tab.id,\n              children: ($$payload4) => {\n                $$payload4.out += `<!---->`;\n                tab.icon?.($$payload4, { class: \"h-4 w-4\" });\n                $$payload4.out += `<!----> <span>${escape_html(tab.label)}</span>`;\n              },\n              $$slots: { default: true }\n            });\n          }\n          $$payload3.out += `<!--]-->`;\n        },\n        $$slots: { default: true }\n      });\n      $$payload2.out += `<!----> <!--[-->`;\n      for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {\n        let tab = each_array_1[$$index_1];\n        Tabs_content($$payload2, {\n          value: tab.id,\n          children: ($$payload3) => {\n            if (tab.id === \"password\") {\n              $$payload3.out += \"<!--[-->\";\n              Password_change($$payload3, { passwordForm: data.passwordForm });\n            } else if (tab.id === \"passkeys\") {\n              $$payload3.out += \"<!--[1-->\";\n              Passkeys($$payload3, { passkeys: data.passkeys });\n            } else if (tab.id === \"sessions\") {\n              $$payload3.out += \"<!--[2-->\";\n              Sessions($$payload3, { sessions: data.sessions });\n            } else {\n              $$payload3.out += \"<!--[!-->\";\n            }\n            $$payload3.out += `<!--]-->`;\n          },\n          $$slots: { default: true }\n        });\n      }\n      $$payload2.out += `<!--]-->`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----></div></div>`;\n  bind_props($$props, { data });\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": ["Root", "z.object", "z.string", "Root$1"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCA,SAAS,QAAQ,CAAC,SAAS,EAAE,OAAO,EAAE;AACtC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC;AAC9D,EAAE,IAAI,aAAa,GAAG,KAAK;AAC3B,EAAE,IAAI,gBAAgB,GAAG,KAAK;AAC9B,EAAE,IAAI,WAAW,GAAG,EAAE;AACtB,EAAE,IAAI,aAAa,GAAG,KAAK;AAC3B,EAAE,SAAS,UAAU,CAAC,UAAU,EAAE;AAClC,IAAI,IAAI,CAAC,UAAU,EAAE;AACrB,MAAM,OAAO,cAAc;AAC3B;AACA,IAAI,IAAI;AACR,MAAM,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC;AACvC,MAAM,OAAO,IAAI,CAAC,kBAAkB,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,kBAAkB,EAAE;AACxE,KAAK,CAAC,OAAO,CAAC,EAAE;AAChB,MAAM,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,CAAC,CAAC;AAChD,MAAM,OAAO,cAAc;AAC3B;AACA;AACA,EAAE;AACF,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,OAAO,CAAC,KAAK,CAAC,+BAA+B,CAAC;AACpD,MAAM,QAAQ,GAAG,EAAE;AACnB,KAAK,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AACzC,MAAM,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,QAAQ,CAAC;AAC1D,MAAM,QAAQ,GAAG,EAAE;AACnB,KAAK,MAAM;AACX,MAAM,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE,QAAQ,CAAC,MAAM,EAAE,UAAU,CAAC;AACxE,MAAM,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;AAC/B,QAAQ,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;AAClD;AACA;AACA;AACA,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,qjBAAqjB,CAAC;AAC7kB,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AAC1C,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,mEAAmE,CAAC;AAC3F,IAAI,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;AAC/B,MAAM,UAAU,CAAC,GAAG,IAAI,UAAU;AAClC,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,6DAA6D,CAAC;AACvF,MAAM,WAAW,CAAC,UAAU,EAAE;AAC9B,QAAQ,KAAK,EAAE;AACf,OAAO,CAAC;AACR,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC;AACzB,ktBAAktB,CAAC;AACntB,MAAM,IAAI,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AAC5C,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,6CAA6C,CAAC;AACvE,KAAK,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,IAAI,WAAW;AACnC,MAAM,MAAM,UAAU,GAAG,iBAAiB,CAAC,QAAQ,CAAC;AACpD,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,+BAA+B,CAAC;AACzD,MAAM,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACzF,QAAQ,IAAI,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;AACzC,QAAQ,IAAI,CAAC,UAAU,EAAE;AACzB,UAAU,QAAQ,EAAE,CAAC,UAAU,KAAK;AACpC,YAAY,YAAY,CAAC,UAAU,EAAE;AACrC,cAAc,KAAK,EAAE,8CAA8C;AACnE,cAAc,QAAQ,EAAE,CAAC,UAAU,KAAK;AACxC,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,4CAA4C,CAAC;AAChF,gBAAgB,GAAG,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,sBAAsB,EAAE,CAAC;AAClE,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,oCAAoC,EAAE,WAAW,CAAC,OAAO,CAAC,IAAI,IAAI,iBAAiB,CAAC,CAAC,uDAAuD,EAAE,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,yDAAyD,EAAE,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC;AACvU,gBAAgB,IAAI,OAAO,CAAC,EAAE,EAAE;AAChC,kBAAkB,UAAU,CAAC,GAAG,IAAI,UAAU;AAC9C,kBAAkB,UAAU,CAAC,GAAG,IAAI,CAAC,6CAA6C,EAAE,WAAW,CAAC,OAAO,OAAO,CAAC,EAAE,KAAK,QAAQ,IAAI,OAAO,CAAC,EAAE,CAAC,MAAM,GAAG,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC;AAClN,iBAAiB,MAAM;AACvB,kBAAkB,UAAU,CAAC,GAAG,IAAI,WAAW;AAC/C;AACA,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,qLAAqL,CAAC;AACzN,gBAAgB,OAAO,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AACzD,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC;AACpD,eAAe;AACf,cAAc,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACtC,aAAa,CAAC;AACd,WAAW;AACX,UAAU,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAClC,SAAS,CAAC;AACV;AACA,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACxC;AACA,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,kGAAkG,CAAC;AAC1H,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,2BAA2B,EAAE,CAAC;AAC5D,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC;AACvB;AACA;AACA;AACA,6RAA6R,CAAC;AAC9R,IAAIA,MAAI,CAAC,UAAU,EAAE;AACrB,MAAM,IAAI,IAAI,GAAG;AACjB,QAAQ,OAAO,aAAa;AAC5B,OAAO;AACP,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;AACxB,QAAQ,aAAa,GAAG,OAAO;AAC/B,QAAQ,SAAS,GAAG,KAAK;AACzB,OAAO;AACP,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,cAAc,CAAC,UAAU,EAAE,EAAE,CAAC;AACtC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACpC,QAAQ,MAAM,CAAC,UAAU,EAAE;AAC3B,UAAU,QAAQ,EAAE,CAAC,UAAU,KAAK;AACpC,YAAY,cAAc,CAAC,UAAU,EAAE;AACvC,cAAc,KAAK,EAAE,sBAAsB;AAC3C,cAAc,QAAQ,EAAE,CAAC,UAAU,KAAK;AACxC,gBAAgB,aAAa,CAAC,UAAU,EAAE;AAC1C,kBAAkB,KAAK,EAAE,kCAAkC;AAC3D,kBAAkB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC5C,oBAAoB,YAAY,CAAC,UAAU,EAAE;AAC7C,sBAAsB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChD,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;AAC9D,uBAAuB;AACvB,sBAAsB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9C,qBAAqB,CAAC;AACtB,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChD,oBAAoB,kBAAkB,CAAC,UAAU,EAAE;AACnD,sBAAsB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChD,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,qDAAqD,CAAC;AACjG,uBAAuB;AACvB,sBAAsB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9C,qBAAqB,CAAC;AACtB,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/C,mBAAmB;AACnB,kBAAkB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC1C,iBAAiB,CAAC;AAClB,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,4DAA4D,CAAC;AAChG,gBAAgB,KAAK,CAAC,UAAU,EAAE;AAClC,kBAAkB,GAAG,EAAE,cAAc;AACrC,kBAAkB,KAAK,EAAE,kCAAkC;AAC3D,kBAAkB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC5C,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC;AAC3D,mBAAmB;AACnB,kBAAkB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC1C,iBAAiB,CAAC;AAClB,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC5C,gBAAgB,KAAK,CAAC,UAAU,EAAE;AAClC,kBAAkB,EAAE,EAAE,cAAc;AACpC,kBAAkB,IAAI,EAAE,MAAM;AAC9B,kBAAkB,WAAW,EAAE,mCAAmC;AAClE,kBAAkB,KAAK,EAAE,qJAAqJ;AAC9K,kBAAkB,IAAI,KAAK,GAAG;AAC9B,oBAAoB,OAAO,WAAW;AACtC,mBAAmB;AACnB,kBAAkB,IAAI,KAAK,CAAC,OAAO,EAAE;AACrC,oBAAoB,WAAW,GAAG,OAAO;AACzC,oBAAoB,SAAS,GAAG,KAAK;AACrC;AACA,iBAAiB,CAAC;AAClB,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,6dAA6d,CAAC;AACjgB,gBAAgB,aAAa,CAAC,UAAU,EAAE;AAC1C,kBAAkB,KAAK,EAAE,4BAA4B;AACrD,kBAAkB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC5C,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,yTAAyT,EAAE,IAAI,CAAC,UAAU,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC,kWAAkW,EAAE,IAAI,CAAC,UAAU,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,gBAAgB,CAAC,CAAC,SAAS,CAAC;AAC7zB,mBAAmB;AACnB,kBAAkB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC1C,iBAAiB,CAAC;AAClB,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC3C,eAAe;AACf,cAAc,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACtC,aAAa,CAAC;AACd;AACA,SAAS,CAAC;AACV,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChC,IAAIA,MAAI,CAAC,UAAU,EAAE;AACrB,MAAM,IAAI,IAAI,GAAG;AACjB,QAAQ,OAAO,gBAAgB;AAC/B,OAAO;AACP,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;AACxB,QAAQ,gBAAgB,GAAG,OAAO;AAClC,QAAQ,SAAS,GAAG,KAAK;AACzB,OAAO;AACP,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,cAAc,CAAC,UAAU,EAAE,EAAE,CAAC;AACtC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACpC,QAAQ,MAAM,CAAC,UAAU,EAAE;AAC3B,UAAU,QAAQ,EAAE,CAAC,UAAU,KAAK;AACpC,YAAY,cAAc,CAAC,UAAU,EAAE;AACvC,cAAc,KAAK,EAAE,kBAAkB;AACvC,cAAc,QAAQ,EAAE,CAAC,UAAU,KAAK;AACxC,gBAAgB,aAAa,CAAC,UAAU,EAAE;AAC1C,kBAAkB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC5C,oBAAoB,YAAY,CAAC,UAAU,EAAE;AAC7C,sBAAsB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChD,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC;AACjE,uBAAuB;AACvB,sBAAsB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9C,qBAAqB,CAAC;AACtB,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChD,oBAAoB,kBAAkB,CAAC,UAAU,EAAE;AACnD,sBAAsB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChD,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,kFAAkF,CAAC;AAC9H,uBAAuB;AACvB,sBAAsB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9C,qBAAqB,CAAC;AACtB,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/C,mBAAmB;AACnB,kBAAkB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC1C,iBAAiB,CAAC;AAClB,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,0BAA0B,CAAC;AAC9D,gBAAgB;AAChB,kBAAkB,UAAU,CAAC,GAAG,IAAI,WAAW;AAC/C;AACA,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;AACnD,gBAAgB,aAAa,CAAC,UAAU,EAAE;AAC1C,kBAAkB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC5C,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,+rBAA+rB,CAAC;AACvuB,mBAAmB;AACnB,kBAAkB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC1C,iBAAiB,CAAC;AAClB,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC3C,eAAe;AACf,cAAc,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACtC,aAAa,CAAC;AACd;AACA,SAAS,CAAC;AACV,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,QAAQ,EAAE,CAAC;AACnC,EAAE,GAAG,EAAE;AACP;AACA,SAAS,eAAe,CAAC,SAAS,EAAE,OAAO,EAAE;AAC7C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,YAAY;AAClB,EAAE,MAAM,cAAc,GAAGC,UAAQ,CAAC;AAClC,IAAI,eAAe,EAAEC,UAAQ,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,8BAA8B,CAAC;AACtE,IAAI,WAAW,EAAEA,UAAQ,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,wCAAwC,CAAC;AAC5E,IAAI,eAAe,EAAEA,UAAQ,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,8BAA8B;AACrE,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,eAAe,EAAE;AACjE,IAAI,OAAO,EAAE,wBAAwB;AACrC,IAAI,IAAI,EAAE,CAAC,iBAAiB;AAC5B,GAAG,CAAC;AACJ,EAAE,IAAI,YAAY,GAAG,OAAO,CAAC,cAAc,CAAC;AAC5C,EAAE,MAAM,IAAI,GAAG,SAAS,CAAC,YAAY,EAAE;AACvC,IAAI,UAAU,EAAE,SAAS,CAAC,cAAc,CAAC;AACzC,IAAI,QAAQ,EAAE,MAAM;AACpB,IAAI,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK;AAC5C,MAAM,IAAI,KAAK,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS,EAAE;AACpD,QAAQ,KAAK,CAAC,OAAO,CAAC,+BAA+B,CAAC;AACtD,QAAQ,SAAS,EAAE;AACnB;AACA,KAAK;AACL,IAAI,OAAO,EAAE,MAAM;AACnB,MAAM,KAAK,CAAC,KAAK,CAAC,2BAA2B,CAAC;AAC9C;AACA,GAAG,CAAC;AACJ,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI;AAC9D,EAAE,SAAS,SAAS,GAAG;AACvB,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM;AAC5B,MAAM,GAAG,CAAC;AACV,MAAM,eAAe,EAAE,EAAE;AACzB,MAAM,WAAW,EAAE,EAAE;AACrB,MAAM,eAAe,EAAE;AACvB,KAAK,CAAC,CAAC;AACP;AACA,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,ucAAuc,CAAC;AAC/d,IAAI,KAAK,CAAC,UAAU,EAAE;AACtB,MAAM,EAAE,EAAE,iBAAiB;AAC3B,MAAM,IAAI,EAAE,UAAU;AACtB,MAAM,WAAW,EAAE,6BAA6B;AAChD,MAAM,IAAI,KAAK,GAAG;AAClB,QAAQ,OAAO,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC,eAAe;AACpF,OAAO;AACP,MAAM,IAAI,KAAK,CAAC,OAAO,EAAE;AACzB,QAAQ,YAAY,CAAC,YAAY,KAAK,EAAE,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC,eAAe,GAAG,OAAO,CAAC;AACjJ,QAAQ,SAAS,GAAG,KAAK;AACzB;AACA,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChC,IAAI,IAAI,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,eAAe,EAAE;AAC3E,MAAM,UAAU,CAAC,GAAG,IAAI,UAAU;AAClC,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,oCAAoC,EAAE,WAAW,CAAC,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC;AACnJ,KAAK,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,IAAI,WAAW;AACnC;AACA,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,6HAA6H,CAAC;AACrJ,IAAI,KAAK,CAAC,UAAU,EAAE;AACtB,MAAM,EAAE,EAAE,aAAa;AACvB,MAAM,IAAI,EAAE,UAAU;AACtB,MAAM,WAAW,EAAE,yBAAyB;AAC5C,MAAM,IAAI,KAAK,GAAG;AAClB,QAAQ,OAAO,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC,WAAW;AAChF,OAAO;AACP,MAAM,IAAI,KAAK,CAAC,OAAO,EAAE;AACzB,QAAQ,YAAY,CAAC,YAAY,KAAK,EAAE,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC,WAAW,GAAG,OAAO,CAAC;AAC7I,QAAQ,SAAS,GAAG,KAAK;AACzB;AACA,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChC,IAAI,IAAI,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,WAAW,EAAE;AACvE,MAAM,UAAU,CAAC,GAAG,IAAI,UAAU;AAClC,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,oCAAoC,EAAE,WAAW,CAAC,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;AAC/I,KAAK,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,IAAI,WAAW;AACnC;AACA,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,yIAAyI,CAAC;AACjK,IAAI,KAAK,CAAC,UAAU,EAAE;AACtB,MAAM,EAAE,EAAE,iBAAiB;AAC3B,MAAM,IAAI,EAAE,UAAU;AACtB,MAAM,WAAW,EAAE,2BAA2B;AAC9C,MAAM,IAAI,KAAK,GAAG;AAClB,QAAQ,OAAO,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC,eAAe;AACpF,OAAO;AACP,MAAM,IAAI,KAAK,CAAC,OAAO,EAAE;AACzB,QAAQ,YAAY,CAAC,YAAY,KAAK,EAAE,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC,eAAe,GAAG,OAAO,CAAC;AACjJ,QAAQ,SAAS,GAAG,KAAK;AACzB;AACA,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChC,IAAI,IAAI,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,eAAe,EAAE;AAC3E,MAAM,UAAU,CAAC,GAAG,IAAI,UAAU;AAClC,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,oCAAoC,EAAE,WAAW,CAAC,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC;AACnJ,KAAK,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,IAAI,WAAW;AACnC;AACA,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,wGAAwG,CAAC;AAChI,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,2BAA2B,EAAE,CAAC;AAC5D,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,waAAwa,CAAC;AAChc,IAAI,MAAM,CAAC,UAAU,EAAE;AACvB,MAAM,IAAI,EAAE,QAAQ;AACpB,MAAM,QAAQ,EAAE,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,aAAa,EAAE,UAAU,CAAC;AACzE,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,aAAa,EAAE,UAAU,CAAC,GAAG,aAAa,GAAG,iBAAiB,CAAC,CAAC,CAAC;AAChJ,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,0BAA0B,CAAC;AAClD;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,IAAI,YAAY,EAAE,kBAAkB,CAAC,YAAY,CAAC;AACpD,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,YAAY,EAAE,CAAC;AACvC,EAAE,GAAG,EAAE;AACP;AACA,SAAS,QAAQ,CAAC,SAAS,EAAE,OAAO,EAAE;AACtC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC;AAC9D,EAAE,IAAI,mBAAmB,GAAG,KAAK;AACjC,EAAE,SAAS,kBAAkB,CAAC,UAAU,EAAE;AAC1C,IAAI,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC;AACrC,IAAI,MAAM,GAAG,mBAAmB,IAAI,IAAI,EAAE;AAC1C,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE;AACjD,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC;AAC5C,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,CAAC;AAC5C,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,CAAC;AAC7C,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;AAC7C,IAAI,IAAI,OAAO,GAAG,EAAE,EAAE;AACtB,MAAM,OAAO,UAAU;AACvB,KAAK,MAAM,IAAI,OAAO,GAAG,EAAE,EAAE;AAC7B,MAAM,OAAO,CAAC,EAAE,OAAO,CAAC,OAAO,EAAE,OAAO,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC;AAC7D,KAAK,MAAM,IAAI,QAAQ,GAAG,EAAE,EAAE;AAC9B,MAAM,OAAO,CAAC,EAAE,QAAQ,CAAC,KAAK,EAAE,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC;AAC7D,KAAK,MAAM,IAAI,OAAO,GAAG,EAAE,EAAE;AAC7B,MAAM,OAAO,CAAC,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC;AAC1D,KAAK,MAAM;AACX,MAAM,OAAO,IAAI,CAAC,kBAAkB,EAAE;AACtC;AACA;AACA,EAAE,SAAS,aAAa,CAAC,MAAM,EAAE;AACjC,IAAI,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;AACxI,MAAM,OAAO,UAAU;AACvB,KAAK,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;AACjG,MAAM,OAAO,OAAO;AACpB,KAAK,MAAM;AACX,MAAM,OAAO,MAAM;AACnB;AACA;AACA,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,MAAM,UAAU,GAAG,iBAAiB,CAAC,QAAQ,CAAC;AAClD,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,qPAAqP,CAAC;AAC7Q,IAAI,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;AACzD,MAAM,UAAU,CAAC,GAAG,IAAI,UAAU;AAClC,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,2YAA2Y,CAAC;AACra,MAAM,OAAO,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AAC/C,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,2CAA2C,CAAC;AACrE,KAAK,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,IAAI,WAAW;AACnC;AACA,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,+DAA+D,CAAC;AACvF,IAAI,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACvF,MAAM,IAAI,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;AACvC,MAAM,IAAI,CAAC,UAAU,EAAE;AACvB,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,YAAY,CAAC,UAAU,EAAE;AACnC,YAAY,KAAK,EAAE,4DAA4D;AAC/E,YAAY,QAAQ,EAAE,CAAC,UAAU,KAAK;AACtC,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,2CAA2C,CAAC;AAC7E,cAAc,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,UAAU,EAAE,EAAE,KAAK,EAAE,2BAA2B,EAAE,CAAC;AACjG,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,qFAAqF,EAAE,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC;AAC1J,cAAc,IAAI,OAAO,CAAC,SAAS,EAAE;AACrC,gBAAgB,UAAU,CAAC,GAAG,IAAI,UAAU;AAC5C,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,yGAAyG,CAAC;AAC7I,eAAe,MAAM;AACrB,gBAAgB,UAAU,CAAC,GAAG,IAAI,WAAW;AAC7C;AACA,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,uHAAuH,CAAC;AACzJ,cAAc,KAAK,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC;AACzD,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,cAAc,EAAE,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,mDAAmD,CAAC;AAChK,cAAc,OAAO,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC;AAC3D,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,cAAc,EAAE,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,uFAAuF,CAAC;AACvK,cAAc,KAAK,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AACrD,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,2BAA2B,EAAE,WAAW,CAAC,kBAAkB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,cAAc,CAAC;AACjI,cAAc,IAAI,OAAO,CAAC,EAAE,EAAE;AAC9B,gBAAgB,UAAU,CAAC,GAAG,IAAI,UAAU;AAC5C,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,+CAA+C,EAAE,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC;AACnH,eAAe,MAAM;AACrB,gBAAgB,UAAU,CAAC,GAAG,IAAI,WAAW;AAC7C;AACA,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC;AACvD,cAAc,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;AACtC,gBAAgB,UAAU,CAAC,GAAG,IAAI,UAAU;AAC5C,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,mXAAmX,CAAC;AACvZ,gBAAgB,OAAO,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AACzD,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,wBAAwB,CAAC;AAC5D,eAAe,MAAM;AACrB,gBAAgB,UAAU,CAAC,GAAG,IAAI,WAAW;AAC7C;AACA,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC1C,aAAa;AACb,YAAY,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACpC,WAAW,CAAC;AACZ,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC,OAAO,CAAC;AACR;AACA,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC;AAC7C,IAAIF,MAAI,CAAC,UAAU,EAAE;AACrB,MAAM,IAAI,IAAI,GAAG;AACjB,QAAQ,OAAO,mBAAmB;AAClC,OAAO;AACP,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;AACxB,QAAQ,mBAAmB,GAAG,OAAO;AACrC,QAAQ,SAAS,GAAG,KAAK;AACzB,OAAO;AACP,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,cAAc,CAAC,UAAU,EAAE,EAAE,CAAC;AACtC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACpC,QAAQ,MAAM,CAAC,UAAU,EAAE;AAC3B,UAAU,QAAQ,EAAE,CAAC,UAAU,KAAK;AACpC,YAAY,cAAc,CAAC,UAAU,EAAE;AACvC,cAAc,KAAK,EAAE,kBAAkB;AACvC,cAAc,QAAQ,EAAE,CAAC,UAAU,KAAK;AACxC,gBAAgB,aAAa,CAAC,UAAU,EAAE;AAC1C,kBAAkB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC5C,oBAAoB,YAAY,CAAC,UAAU,EAAE;AAC7C,sBAAsB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChD,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,iCAAiC,CAAC;AAC7E,uBAAuB;AACvB,sBAAsB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9C,qBAAqB,CAAC;AACtB,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChD,oBAAoB,kBAAkB,CAAC,UAAU,EAAE;AACnD,sBAAsB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChD,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC;AAC3C,iBAAiB,CAAC;AAClB,uBAAuB;AACvB,sBAAsB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9C,qBAAqB,CAAC;AACtB,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/C,mBAAmB;AACnB,kBAAkB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC1C,iBAAiB,CAAC;AAClB,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,8FAA8F,EAAE,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC,4BAA4B,CAAC;AACzN,gBAAgB,aAAa,CAAC,UAAU,EAAE;AAC1C,kBAAkB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC5C,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,kxBAAkxB,CAAC;AAC1zB,mBAAmB;AACnB,kBAAkB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC1C,iBAAiB,CAAC;AAClB,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC3C,eAAe;AACf,cAAc,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACtC,aAAa,CAAC;AACd;AACA,SAAS,CAAC;AACV,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,QAAQ,EAAE,CAAC;AACnC,EAAE,GAAG,EAAE;AACP;AACA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC;AAC5B,EAAE,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAAC;AAC1C,EAAE,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAAC,QAAQ,CAAC;AACnD,EAAE,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAAC,QAAQ,CAAC;AACtD,EAAE,MAAM,IAAI,GAAG;AACf,IAAI;AACJ,MAAM,EAAE,EAAE,UAAU;AACpB,MAAM,KAAK,EAAE,UAAU;AACvB,MAAM,IAAI,EAAE;AACZ,KAAK;AACL;AACA;AACA;AACA,IAAI,EAAE,EAAE,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE;AACpD,IAAI;AACJ,MAAM,EAAE,EAAE,UAAU;AACpB,MAAM,KAAK,EAAE,UAAU;AACvB,MAAM,IAAI,EAAE;AACZ;AACA,GAAG;AACH,EAAE,IAAI,SAAS,GAAG,UAAU;AAC5B,EAAE,GAAG,CAAC,SAAS,EAAE;AACjB,IAAI,KAAK,EAAE,2BAA2B;AACtC,IAAI,WAAW,EAAE,qHAAqH;AACtI,IAAI,QAAQ,EAAE,oFAAoF;AAClG,IAAI,GAAG,EAAE;AACT,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC;AACpB,yDAAyD,CAAC;AAC1D,EAAEG,IAAM,CAAC,SAAS,EAAE;AACpB,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,aAAa,EAAE,CAAC,KAAK,KAAK,SAAS,GAAG,KAAK;AAC/C,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,MAAM,YAAY,GAAG,iBAAiB,CAAC,IAAI,CAAC;AAClD,MAAM,SAAS,CAAC,UAAU,EAAE;AAC5B,QAAQ,KAAK,EAAE,QAAQ;AACvB,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,MAAM,UAAU,GAAG,iBAAiB,CAAC,IAAI,CAAC;AACpD,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACtC,UAAU,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AAC7F,YAAY,IAAI,GAAG,GAAG,UAAU,CAAC,OAAO,CAAC;AACzC,YAAY,YAAY,CAAC,UAAU,EAAE;AACrC,cAAc,KAAK,EAAE,GAAG,CAAC,EAAE;AAC3B,cAAc,QAAQ,EAAE,CAAC,UAAU,KAAK;AACxC,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC3C,gBAAgB,GAAG,CAAC,IAAI,GAAG,UAAU,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AAC5D,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,cAAc,EAAE,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC;AAClF,eAAe;AACf,cAAc,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACtC,aAAa,CAAC;AACd;AACA,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACtC,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC,OAAO,CAAC;AACR,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC;AAC1C,MAAM,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,QAAQ,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,QAAQ,EAAE,SAAS,EAAE,EAAE;AACjG,QAAQ,IAAI,GAAG,GAAG,YAAY,CAAC,SAAS,CAAC;AACzC,QAAQ,YAAY,CAAC,UAAU,EAAE;AACjC,UAAU,KAAK,EAAE,GAAG,CAAC,EAAE;AACvB,UAAU,QAAQ,EAAE,CAAC,UAAU,KAAK;AACpC,YAAY,IAAI,GAAG,CAAC,EAAE,KAAK,UAAU,EAAE;AACvC,cAAc,UAAU,CAAC,GAAG,IAAI,UAAU;AAC1C,cAAc,eAAe,CAAC,UAAU,EAAE,EAAE,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC;AAC9E,aAAa,MAAM,IAAI,GAAG,CAAC,EAAE,KAAK,UAAU,EAAE;AAC9C,cAAc,UAAU,CAAC,GAAG,IAAI,WAAW;AAC3C,cAAc,QAAQ,CAAC,UAAU,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC;AAC/D,aAAa,MAAM,IAAI,GAAG,CAAC,EAAE,KAAK,UAAU,EAAE;AAC9C,cAAc,UAAU,CAAC,GAAG,IAAI,WAAW;AAC3C,cAAc,QAAQ,CAAC,UAAU,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC;AAC/D,aAAa,MAAM;AACnB,cAAc,UAAU,CAAC,GAAG,IAAI,WAAW;AAC3C;AACA,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACxC,WAAW;AACX,UAAU,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAClC,SAAS,CAAC;AACV;AACA,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAClC,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC;AACxC,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,CAAC;AAC/B,EAAE,GAAG,EAAE;AACP;;;;"}