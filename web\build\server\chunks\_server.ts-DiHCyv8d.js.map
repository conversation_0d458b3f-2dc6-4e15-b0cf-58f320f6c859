{"version": 3, "file": "_server.ts-DiHCyv8d.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/auth/logout/_server.ts.js"], "sourcesContent": ["import \"../../../../../chunks/index.js\";\nconst POST = async ({ cookies }) => {\n  const isProd = process.env.NODE_ENV === \"production\";\n  cookies.delete(\"auth_token\", {\n    path: \"/\",\n    httpOnly: true,\n    sameSite: \"lax\",\n    secure: isProd\n  });\n  return new Response(null, { status: 200 });\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": "AACK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK;AACpC,EAAE,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;AACtD,EAAE,OAAO,CAAC,MAAM,CAAC,YAAY,EAAE;AAC/B,IAAI,IAAI,EAAE,GAAG;AACb,IAAI,QAAQ,EAAE,IAAI;AAClB,IAAI,QAAQ,EAAE,KAAK;AACnB,IAAI,MAAM,EAAE;AACZ,GAAG,CAAC;AACJ,EAAE,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC5C;;;;"}