{"version": 3, "file": "_server.ts-DD4jdJ0l.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/push/_action_/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { g as getUserFromToken } from \"../../../../../chunks/auth.js\";\nconst GET = () => {\n  return json({ publicKey: process.env.VAPID_PUBLIC_KEY });\n};\nconst POST = async ({ params, request, cookies }) => {\n  try {\n    const user = await getUserFromToken(cookies);\n    if (!user) return json({ error: \"Unauthorized\" }, { status: 401 });\n    const action = params.action;\n    if (action === \"subscribe\") {\n      const { sub } = await request.json();\n      await prisma.pushSubscription.upsert({\n        where: { userId_endpoint: { userId: user.id, endpoint: sub.endpoint } },\n        update: {\n          endpoint: sub.endpoint\n        },\n        create: {\n          userId: user.id,\n          endpoint: sub.endpoint\n        }\n      });\n      await prisma.notificationSettings.upsert({\n        where: { userId: user.id },\n        update: { pushEnabled: true },\n        create: {\n          userId: user.id,\n          pushEnabled: true,\n          emailEnabled: true,\n          browserEnabled: true,\n          jobMatchEnabled: true,\n          applicationStatusEnabled: true,\n          automationEnabled: true\n        }\n      });\n      return new Response(null, { status: 204 });\n    }\n    if (action === \"unsubscribe\") {\n      await prisma.pushSubscription.deleteMany({ where: { userId: user.id } });\n      await prisma.notificationSettings.upsert({\n        where: { userId: user.id },\n        update: { pushEnabled: false },\n        create: {\n          userId: user.id,\n          pushEnabled: false,\n          emailEnabled: true,\n          browserEnabled: true,\n          jobMatchEnabled: true,\n          applicationStatusEnabled: true,\n          automationEnabled: true\n        }\n      });\n      return new Response(null, { status: 204 });\n    }\n    if (action === \"send\") {\n      const { subscription, payload } = await request.json();\n      const webpush = (await import(\"../../../../../chunks/index5.js\").then((n) => n.i)).default;\n      webpush.setVapidDetails(\n        \"mailto:<EMAIL>\",\n        process.env.VAPID_PUBLIC_KEY,\n        process.env.VAPID_PRIVATE_KEY\n      );\n      try {\n        const res = await webpush.sendNotification(subscription, payload);\n        return new Response(null, { status: res.statusCode });\n      } catch (err) {\n        console.error(\"Push send error:\", err);\n        return new Response(\"Push failed\", { status: 500 });\n      }\n    }\n    return new Response(\"Not found\", { status: 404 });\n  } catch (err) {\n    console.error(\"Push route error:\", err);\n    return json({ error: err.message }, { status: 500 });\n  }\n};\nexport {\n  GET,\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;AAGK,MAAC,GAAG,GAAG,MAAM;AAClB,EAAE,OAAO,IAAI,CAAC,EAAE,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;AAC1D;AACK,MAAC,IAAI,GAAG,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AACrD,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,gBAAgB,CAAC,OAAO,CAAC;AAChD,IAAI,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtE,IAAI,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM;AAChC,IAAI,IAAI,MAAM,KAAK,WAAW,EAAE;AAChC,MAAM,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AAC1C,MAAM,MAAM,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;AAC3C,QAAQ,KAAK,EAAE,EAAE,eAAe,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,GAAG,CAAC,QAAQ,EAAE,EAAE;AAC/E,QAAQ,MAAM,EAAE;AAChB,UAAU,QAAQ,EAAE,GAAG,CAAC;AACxB,SAAS;AACT,QAAQ,MAAM,EAAE;AAChB,UAAU,MAAM,EAAE,IAAI,CAAC,EAAE;AACzB,UAAU,QAAQ,EAAE,GAAG,CAAC;AACxB;AACA,OAAO,CAAC;AACR,MAAM,MAAM,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC;AAC/C,QAAQ,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;AAClC,QAAQ,MAAM,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE;AACrC,QAAQ,MAAM,EAAE;AAChB,UAAU,MAAM,EAAE,IAAI,CAAC,EAAE;AACzB,UAAU,WAAW,EAAE,IAAI;AAC3B,UAAU,YAAY,EAAE,IAAI;AAC5B,UAAU,cAAc,EAAE,IAAI;AAC9B,UAAU,eAAe,EAAE,IAAI;AAC/B,UAAU,wBAAwB,EAAE,IAAI;AACxC,UAAU,iBAAiB,EAAE;AAC7B;AACA,OAAO,CAAC;AACR,MAAM,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAChD;AACA,IAAI,IAAI,MAAM,KAAK,aAAa,EAAE;AAClC,MAAM,MAAM,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC;AAC9E,MAAM,MAAM,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC;AAC/C,QAAQ,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;AAClC,QAAQ,MAAM,EAAE,EAAE,WAAW,EAAE,KAAK,EAAE;AACtC,QAAQ,MAAM,EAAE;AAChB,UAAU,MAAM,EAAE,IAAI,CAAC,EAAE;AACzB,UAAU,WAAW,EAAE,KAAK;AAC5B,UAAU,YAAY,EAAE,IAAI;AAC5B,UAAU,cAAc,EAAE,IAAI;AAC9B,UAAU,eAAe,EAAE,IAAI;AAC/B,UAAU,wBAAwB,EAAE,IAAI;AACxC,UAAU,iBAAiB,EAAE;AAC7B;AACA,OAAO,CAAC;AACR,MAAM,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAChD;AACA,IAAI,IAAI,MAAM,KAAK,MAAM,EAAE;AAC3B,MAAM,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AAC5D,MAAM,MAAM,OAAO,GAAG,CAAC,MAAM,OAAO,sBAAiC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO;AAChG,MAAM,OAAO,CAAC,eAAe;AAC7B,QAAQ,sBAAsB;AAC9B,QAAQ,OAAO,CAAC,GAAG,CAAC,gBAAgB;AACpC,QAAQ,OAAO,CAAC,GAAG,CAAC;AACpB,OAAO;AACP,MAAM,IAAI;AACV,QAAQ,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,gBAAgB,CAAC,YAAY,EAAE,OAAO,CAAC;AACzE,QAAQ,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,GAAG,CAAC,UAAU,EAAE,CAAC;AAC7D,OAAO,CAAC,OAAO,GAAG,EAAE;AACpB,QAAQ,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAE,GAAG,CAAC;AAC9C,QAAQ,OAAO,IAAI,QAAQ,CAAC,aAAa,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA;AACA,IAAI,OAAO,IAAI,QAAQ,CAAC,WAAW,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACrD,GAAG,CAAC,OAAO,GAAG,EAAE;AAChB,IAAI,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,GAAG,CAAC;AAC3C,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxD;AACA;;;;"}