import { p as prisma } from './prisma-Cit_HrSw.js';
import { d as determineDocumentSource } from './documentSource-DXqQzUYc.js';
import { e as error } from './index-Ddp2AB5f.js';
import '@prisma/client';

const load = async ({ params, locals }) => {
  const { id } = params;
  if (!locals.user) {
    throw error(401, "Unauthorized");
  }
  try {
    const document = await prisma.document.findUnique({
      where: { id },
      include: {
        resume: true
      }
    });
    if (!document || document.userId !== locals.user.id) {
      throw error(404, "Document not found");
    }
    const source = determineDocumentSource(document);
    return {
      id,
      document: {
        ...document,
        source
      }
    };
  } catch (e) {
    console.error("Error loading document:", e);
    throw error(500, "Failed to load document");
  }
};

var _page_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 33;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-4Uo51tPk.js')).default;
const server_id = "src/routes/dashboard/documents/[id]/+page.server.ts";
const imports = ["_app/immutable/nodes/33.CLJKbDou.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/B-Xjo-Yt.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/FN1sk3P2.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/DjPYYl4Z.js","_app/immutable/chunks/C6g8ubaU.js","_app/immutable/chunks/B1K98fMG.js","_app/immutable/chunks/ncUU1dSD.js","_app/immutable/chunks/5V1tIHTN.js","_app/immutable/chunks/DM07Bv7T.js","_app/immutable/chunks/DMTMHyMa.js","_app/immutable/chunks/CzsE_FAw.js","_app/immutable/chunks/DuGukytH.js","_app/immutable/chunks/Cdn-N1RY.js","_app/immutable/chunks/BkJY4La4.js","_app/immutable/chunks/DETxXRrJ.js","_app/immutable/chunks/GwmmX_iF.js","_app/immutable/chunks/D50jIuLr.js","_app/immutable/chunks/BnikQ10_.js","_app/immutable/chunks/BvdI7LR8.js","_app/immutable/chunks/DMoa_yM9.js","_app/immutable/chunks/BfX7a-t9.js","_app/immutable/chunks/BosuxZz1.js","_app/immutable/chunks/CnMg5bH0.js","_app/immutable/chunks/DuoUhxYL.js","_app/immutable/chunks/Bd3zs5C6.js","_app/immutable/chunks/CIOgxH3l.js","_app/immutable/chunks/XESq6qWN.js","_app/immutable/chunks/OOsIR5sE.js","_app/immutable/chunks/BaVT73bJ.js","_app/immutable/chunks/DT9WCdWY.js","_app/immutable/chunks/Bpi49Nrf.js","_app/immutable/chunks/Cb-3cdbh.js","_app/immutable/chunks/DX6rZLP_.js","_app/immutable/chunks/BJIrNhIJ.js","_app/immutable/chunks/BKLOCbjP.js","_app/immutable/chunks/CSGDlQPw.js","_app/immutable/chunks/C1FmrZbK.js","_app/immutable/chunks/BPvdPoic.js","_app/immutable/chunks/zNKWipEG.js","_app/immutable/chunks/BBa424ah.js","_app/immutable/chunks/D4f2twK-.js","_app/immutable/chunks/C3w0v0gR.js","_app/immutable/chunks/w80wGXGd.js","_app/immutable/chunks/tr-scC-m.js","_app/immutable/chunks/BIUPxhhl.js","_app/immutable/chunks/CTQ8y7hr.js"];
const stylesheets = ["_app/immutable/assets/Toaster.DKF17Rty.css"];
const fonts = [];

export { component, fonts, imports, index, _page_server_ts as server, server_id, stylesheets };
//# sourceMappingURL=33-BAClqn6i.js.map
