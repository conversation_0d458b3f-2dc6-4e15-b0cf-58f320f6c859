{"version": 3, "file": "_server.ts-CPR3SMTU.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/resume/create/_server.ts.js"], "sourcesContent": ["import { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { j as json } from \"../../../../../chunks/index.js\";\nimport { e as ensureUniqueDocumentName } from \"../../../../../chunks/documentNameUniqueness.js\";\nimport { d as determineDocumentSource } from \"../../../../../chunks/documentSource.js\";\nconst POST = async ({ request, locals }) => {\n  try {\n    const user = locals.user;\n    console.log(\"User from locals:\", user);\n    const userId = user?.id || \"98489dfa-3bde-47e7-81d0-31866951ddfe\";\n    console.log(\"Creating new resume for user:\", userId);\n    const data = await request.json();\n    let label = data.label || \"New Resume\";\n    label = await ensureUniqueDocumentName(label, userId, \"resume\");\n    const document = await prisma.document.create({\n      data: {\n        label,\n        fileUrl: \"/placeholder.pdf\",\n        // Use a placeholder URL since fileUrl is required\n        filePath: null,\n        // Will be generated later\n        fileName: null,\n        // Will be set when file is generated\n        type: \"resume\",\n        contentType: \"application/pdf\",\n        storageType: \"local\",\n        isDefault: false,\n        source: data.source || \"created\",\n        // Connect to the user instead of just providing the userId\n        user: {\n          connect: { id: userId }\n        },\n        // Only connect to profile if profileId is provided\n        ...data.profileId ? { profile: { connect: { id: data.profileId } } } : {},\n        // Only connect to team if teamId is provided\n        ...data.teamId ? { team: { connect: { id: data.teamId } } } : {}\n      }\n    });\n    const resume = await prisma.resume.create({\n      data: {\n        documentId: document.id,\n        isParsed: false,\n        parsedData: {},\n        rawText: \"\"\n      }\n    });\n    console.log(\"Resume created successfully:\", resume.id);\n    const source = determineDocumentSource(document);\n    return json({\n      success: true,\n      id: resume.id,\n      documentId: document.id,\n      source,\n      // Include the determined source in the response\n      message: \"Resume created successfully\"\n    });\n  } catch (error) {\n    console.error(\"Error creating resume:\", error);\n    return json({ error: \"Failed to create resume\", details: String(error) }, { status: 500 });\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;AAIK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AAC5C,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC5B,IAAI,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC;AAC1C,IAAI,MAAM,MAAM,GAAG,IAAI,EAAE,EAAE,IAAI,sCAAsC;AACrE,IAAI,OAAO,CAAC,GAAG,CAAC,+BAA+B,EAAE,MAAM,CAAC;AACxD,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,YAAY;AAC1C,IAAI,KAAK,GAAG,MAAM,wBAAwB,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC;AACnE,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;AAClD,MAAM,IAAI,EAAE;AACZ,QAAQ,KAAK;AACb,QAAQ,OAAO,EAAE,kBAAkB;AACnC;AACA,QAAQ,QAAQ,EAAE,IAAI;AACtB;AACA,QAAQ,QAAQ,EAAE,IAAI;AACtB;AACA,QAAQ,IAAI,EAAE,QAAQ;AACtB,QAAQ,WAAW,EAAE,iBAAiB;AACtC,QAAQ,WAAW,EAAE,OAAO;AAC5B,QAAQ,SAAS,EAAE,KAAK;AACxB,QAAQ,MAAM,EAAE,IAAI,CAAC,MAAM,IAAI,SAAS;AACxC;AACA,QAAQ,IAAI,EAAE;AACd,UAAU,OAAO,EAAE,EAAE,EAAE,EAAE,MAAM;AAC/B,SAAS;AACT;AACA,QAAQ,GAAG,IAAI,CAAC,SAAS,GAAG,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,SAAS,EAAE,EAAE,EAAE,GAAG,EAAE;AACjF;AACA,QAAQ,GAAG,IAAI,CAAC,MAAM,GAAG,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,GAAG;AACtE;AACA,KAAK,CAAC;AACN,IAAI,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;AAC9C,MAAM,IAAI,EAAE;AACZ,QAAQ,UAAU,EAAE,QAAQ,CAAC,EAAE;AAC/B,QAAQ,QAAQ,EAAE,KAAK;AACvB,QAAQ,UAAU,EAAE,EAAE;AACtB,QAAQ,OAAO,EAAE;AACjB;AACA,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,MAAM,CAAC,EAAE,CAAC;AAC1D,IAAI,MAAM,MAAM,GAAG,uBAAuB,CAAC,QAAQ,CAAC;AACpD,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,EAAE,EAAE,MAAM,CAAC,EAAE;AACnB,MAAM,UAAU,EAAE,QAAQ,CAAC,EAAE;AAC7B,MAAM,MAAM;AACZ;AACA,MAAM,OAAO,EAAE;AACf,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC;AAClD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9F;AACA;;;;"}