{"version": 3, "file": "_server.ts-CV1WqGQD.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/ai/interview/sessions/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../../../../chunks/auth.js\";\nconst GET = async ({ cookies, locals }) => {\n  try {\n    const token = cookies.get(\"auth_token\");\n    if (!token && !locals.user) {\n      return json({ error: \"Unauthorized\" }, { status: 401 });\n    }\n    const userId = locals.user?.id || (token ? verifySessionToken(token)?.id : null);\n    if (!userId) {\n      return json({ error: \"Invalid token or user not found\" }, { status: 401 });\n    }\n    const sessions = await prisma.interviewCoachingSession.findMany({\n      where: {\n        userId\n      },\n      orderBy: {\n        createdAt: \"desc\"\n      }\n    });\n    const transformedSessions = sessions.map((session) => ({\n      id: session.id,\n      jobTitle: session.jobTitle,\n      company: session.company,\n      status: session.status,\n      questions: session.questions,\n      responses: session.responses,\n      feedback: session.feedback,\n      sessionDate: session.createdAt,\n      updatedAt: session.updatedAt\n    }));\n    return json({ sessions: transformedSessions });\n  } catch (error) {\n    console.error(\"Error fetching interview sessions:\", error);\n    return json({ error: \"Failed to fetch interview sessions\" }, { status: 500 });\n  }\n};\nconst POST = async ({ request, cookies }) => {\n  try {\n    const token = cookies.get(\"auth_token\");\n    if (!token) {\n      return json({ error: \"Unauthorized\" }, { status: 401 });\n    }\n    const tokenData = verifySessionToken(token);\n    if (!tokenData?.id) {\n      return json({ error: \"Invalid token\" }, { status: 401 });\n    }\n    const userId = tokenData.id;\n    const body = await request.json();\n    const { applicationId, jobTitle, industry } = body;\n    if (!applicationId || !jobTitle) {\n      return json(\n        { error: \"Missing required fields\", required: [\"applicationId\", \"jobTitle\"] },\n        { status: 400 }\n      );\n    }\n    const application = await prisma.application.findFirst({\n      where: {\n        id: applicationId,\n        userId\n      }\n    });\n    if (!application) {\n      return json({ error: \"Application not found or access denied\" }, { status: 404 });\n    }\n    const user = await prisma.user.findUnique({\n      where: { id: userId },\n      include: {\n        subscriptions: {\n          where: { status: \"active\" },\n          include: {\n            plan: {\n              include: {\n                features: {\n                  where: { featureId: \"ai_interview_coach\" }\n                }\n              }\n            }\n          }\n        },\n        featureUsage: {\n          where: {\n            featureId: \"ai_interview_coach\",\n            limitId: \"ai_interview_sessions_monthly\"\n          }\n        }\n      }\n    });\n    const hasAccess = user?.subscriptions.some(\n      (sub) => sub.plan.features.some((feature) => feature.featureId === \"ai_interview_coach\")\n    );\n    if (!hasAccess && process.env.NODE_ENV === \"production\") {\n      return json({ error: \"Feature not available in your plan\" }, { status: 403 });\n    }\n    const usageLimit = user?.subscriptions.flatMap((sub) => sub.plan.features).find((feature) => feature.featureId === \"ai_interview_coach\")?.limits?.find((limit) => limit.limitId === \"ai_interview_sessions_monthly\")?.value;\n    const currentUsage = user?.featureUsage.find(\n      (usage) => usage.featureId === \"ai_interview_coach\" && usage.limitId === \"ai_interview_sessions_monthly\"\n    )?.usage ?? 0;\n    if (usageLimit && currentUsage >= parseInt(usageLimit) && process.env.NODE_ENV === \"production\") {\n      return json({ error: \"Monthly usage limit reached\" }, { status: 403 });\n    }\n    if (applicationId) {\n      const application2 = await prisma.application.findFirst({\n        where: {\n          id: applicationId,\n          userId\n        }\n      });\n      if (!application2) {\n        return json({ error: \"Application not found or access denied\" }, { status: 404 });\n      }\n    }\n    const questions = generateInterviewQuestions(jobTitle, industry);\n    const session = await prisma.interviewCoachingSession.create({\n      data: {\n        userId,\n        applicationId,\n        jobTitle,\n        company: body.company ?? null,\n        status: \"in_progress\",\n        questions\n      }\n    });\n    await prisma.featureUsage.upsert({\n      where: {\n        userId_featureId_limitId: {\n          userId,\n          featureId: \"ai_interview_coach\",\n          limitId: \"ai_interview_sessions_monthly\"\n        }\n      },\n      update: {\n        usage: { increment: 1 }\n      },\n      create: {\n        userId,\n        featureId: \"ai_interview_coach\",\n        limitId: \"ai_interview_sessions_monthly\",\n        usage: 1\n      }\n    });\n    return json({ success: true, session });\n  } catch (error) {\n    console.error(\"Error creating interview coaching session:\", error);\n    return json({ error: \"Internal server error\" }, { status: 500 });\n  }\n};\nfunction generateInterviewQuestions(jobTitle, industry) {\n  const baseQuestions = [\n    {\n      question: \"Tell me about yourself and your background.\",\n      category: \"general\",\n      difficulty: 3\n    },\n    {\n      question: \"What are your greatest strengths and weaknesses?\",\n      category: \"general\",\n      difficulty: 4\n    },\n    {\n      question: \"Why are you interested in this position?\",\n      category: \"general\",\n      difficulty: 3\n    },\n    {\n      question: \"Describe a challenging situation at work and how you handled it.\",\n      category: \"behavioral\",\n      difficulty: 6\n    },\n    {\n      question: \"Where do you see yourself in 5 years?\",\n      category: \"general\",\n      difficulty: 4\n    }\n  ];\n  let technicalQuestions = [];\n  if (jobTitle.toLowerCase().includes(\"software\") || jobTitle.toLowerCase().includes(\"developer\") || jobTitle.toLowerCase().includes(\"engineer\")) {\n    technicalQuestions = [\n      {\n        question: \"Explain the difference between a stack and a queue.\",\n        category: \"technical\",\n        difficulty: 5\n      },\n      {\n        question: \"What is your experience with version control systems?\",\n        category: \"technical\",\n        difficulty: 4\n      },\n      {\n        question: \"Describe a project where you had to optimize performance. What techniques did you use?\",\n        category: \"technical\",\n        difficulty: 7\n      },\n      {\n        question: \"How do you approach debugging a complex issue?\",\n        category: \"technical\",\n        difficulty: 6\n      },\n      {\n        question: \"Explain the concept of dependency injection.\",\n        category: \"technical\",\n        difficulty: 8\n      }\n    ];\n  } else if (jobTitle.toLowerCase().includes(\"manager\") || jobTitle.toLowerCase().includes(\"lead\")) {\n    technicalQuestions = [\n      {\n        question: \"How do you prioritize tasks for your team?\",\n        category: \"leadership\",\n        difficulty: 6\n      },\n      {\n        question: \"Describe your approach to managing underperforming team members.\",\n        category: \"leadership\",\n        difficulty: 7\n      },\n      {\n        question: \"How do you handle conflicts within your team?\",\n        category: \"leadership\",\n        difficulty: 6\n      },\n      {\n        question: \"What metrics do you use to measure team success?\",\n        category: \"leadership\",\n        difficulty: 7\n      },\n      {\n        question: \"How do you ensure your team meets deadlines?\",\n        category: \"leadership\",\n        difficulty: 5\n      }\n    ];\n  } else if (jobTitle.toLowerCase().includes(\"marketing\")) {\n    technicalQuestions = [\n      {\n        question: \"How do you measure the success of a marketing campaign?\",\n        category: \"marketing\",\n        difficulty: 5\n      },\n      {\n        question: \"Describe a successful marketing campaign you developed.\",\n        category: \"marketing\",\n        difficulty: 6\n      },\n      {\n        question: \"How do you stay updated with the latest marketing trends?\",\n        category: \"marketing\",\n        difficulty: 4\n      },\n      {\n        question: \"What tools do you use for marketing analytics?\",\n        category: \"marketing\",\n        difficulty: 5\n      },\n      {\n        question: \"How would you approach marketing to a new demographic?\",\n        category: \"marketing\",\n        difficulty: 7\n      }\n    ];\n  }\n  return [...baseQuestions, ...technicalQuestions];\n}\nexport {\n  GET,\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;AAGK,MAAC,GAAG,GAAG,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AAC3C,EAAE,IAAI;AACN,IAAI,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AAC3C,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;AAChC,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7D;AACA,IAAI,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,EAAE,EAAE,KAAK,KAAK,GAAG,kBAAkB,CAAC,KAAK,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC;AACpF,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,iCAAiC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAChF;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,wBAAwB,CAAC,QAAQ,CAAC;AACpE,MAAM,KAAK,EAAE;AACb,QAAQ;AACR,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,SAAS,EAAE;AACnB;AACA,KAAK,CAAC;AACN,IAAI,MAAM,mBAAmB,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,MAAM;AAC3D,MAAM,EAAE,EAAE,OAAO,CAAC,EAAE;AACpB,MAAM,QAAQ,EAAE,OAAO,CAAC,QAAQ;AAChC,MAAM,OAAO,EAAE,OAAO,CAAC,OAAO;AAC9B,MAAM,MAAM,EAAE,OAAO,CAAC,MAAM;AAC5B,MAAM,SAAS,EAAE,OAAO,CAAC,SAAS;AAClC,MAAM,SAAS,EAAE,OAAO,CAAC,SAAS;AAClC,MAAM,QAAQ,EAAE,OAAO,CAAC,QAAQ;AAChC,MAAM,WAAW,EAAE,OAAO,CAAC,SAAS;AACpC,MAAM,SAAS,EAAE,OAAO,CAAC;AACzB,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,IAAI,CAAC,EAAE,QAAQ,EAAE,mBAAmB,EAAE,CAAC;AAClD,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC;AAC9D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oCAAoC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjF;AACA;AACK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AAC7C,EAAE,IAAI;AACN,IAAI,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AAC3C,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7D;AACA,IAAI,MAAM,SAAS,GAAG,kBAAkB,CAAC,KAAK,CAAC;AAC/C,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE,EAAE;AACxB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9D;AACA,IAAI,MAAM,MAAM,GAAG,SAAS,CAAC,EAAE;AAC/B,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,IAAI;AACtD,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,QAAQ,EAAE;AACrC,MAAM,OAAO,IAAI;AACjB,QAAQ,EAAE,KAAK,EAAE,yBAAyB,EAAE,QAAQ,EAAE,CAAC,eAAe,EAAE,UAAU,CAAC,EAAE;AACrF,QAAQ,EAAE,MAAM,EAAE,GAAG;AACrB,OAAO;AACP;AACA,IAAI,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC;AAC3D,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE,aAAa;AACzB,QAAQ;AACR;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,WAAW,EAAE;AACtB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,wCAAwC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACvF;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAC9C,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;AAC3B,MAAM,OAAO,EAAE;AACf,QAAQ,aAAa,EAAE;AACvB,UAAU,KAAK,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE;AACrC,UAAU,OAAO,EAAE;AACnB,YAAY,IAAI,EAAE;AAClB,cAAc,OAAO,EAAE;AACvB,gBAAgB,QAAQ,EAAE;AAC1B,kBAAkB,KAAK,EAAE,EAAE,SAAS,EAAE,oBAAoB;AAC1D;AACA;AACA;AACA;AACA,SAAS;AACT,QAAQ,YAAY,EAAE;AACtB,UAAU,KAAK,EAAE;AACjB,YAAY,SAAS,EAAE,oBAAoB;AAC3C,YAAY,OAAO,EAAE;AACrB;AACA;AACA;AACA,KAAK,CAAC;AACN,IAAI,MAAM,SAAS,GAAG,IAAI,EAAE,aAAa,CAAC,IAAI;AAC9C,MAAM,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,SAAS,KAAK,oBAAoB;AAC7F,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE;AAC7D,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oCAAoC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnF;AACA,IAAI,MAAM,UAAU,GAAG,IAAI,EAAE,aAAa,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,SAAS,KAAK,oBAAoB,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,OAAO,KAAK,+BAA+B,CAAC,EAAE,KAAK;AAC/N,IAAI,MAAM,YAAY,GAAG,IAAI,EAAE,YAAY,CAAC,IAAI;AAChD,MAAM,CAAC,KAAK,KAAK,KAAK,CAAC,SAAS,KAAK,oBAAoB,IAAI,KAAK,CAAC,OAAO,KAAK;AAC/E,KAAK,EAAE,KAAK,IAAI,CAAC;AACjB,IAAI,IAAI,UAAU,IAAI,YAAY,IAAI,QAAQ,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE;AACrG,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,6BAA6B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC5E;AACA,IAAI,IAAI,aAAa,EAAE;AACvB,MAAM,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC;AAC9D,QAAQ,KAAK,EAAE;AACf,UAAU,EAAE,EAAE,aAAa;AAC3B,UAAU;AACV;AACA,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,YAAY,EAAE;AACzB,QAAQ,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,wCAAwC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzF;AACA;AACA,IAAI,MAAM,SAAS,GAAG,0BAA0B,CAAC,QAAQ,EAAE,QAAQ,CAAC;AACpE,IAAI,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,wBAAwB,CAAC,MAAM,CAAC;AACjE,MAAM,IAAI,EAAE;AACZ,QAAQ,MAAM;AACd,QAAQ,aAAa;AACrB,QAAQ,QAAQ;AAChB,QAAQ,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,IAAI;AACrC,QAAQ,MAAM,EAAE,aAAa;AAC7B,QAAQ;AACR;AACA,KAAK,CAAC;AACN,IAAI,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AACrC,MAAM,KAAK,EAAE;AACb,QAAQ,wBAAwB,EAAE;AAClC,UAAU,MAAM;AAChB,UAAU,SAAS,EAAE,oBAAoB;AACzC,UAAU,OAAO,EAAE;AACnB;AACA,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,KAAK,EAAE,EAAE,SAAS,EAAE,CAAC;AAC7B,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,MAAM;AACd,QAAQ,SAAS,EAAE,oBAAoB;AACvC,QAAQ,OAAO,EAAE,+BAA+B;AAChD,QAAQ,KAAK,EAAE;AACf;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;AAC3C,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC;AACtE,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpE;AACA;AACA,SAAS,0BAA0B,CAAC,QAAQ,EAAE,QAAQ,EAAE;AACxD,EAAE,MAAM,aAAa,GAAG;AACxB,IAAI;AACJ,MAAM,QAAQ,EAAE,6CAA6C;AAC7D,MAAM,QAAQ,EAAE,SAAS;AACzB,MAAM,UAAU,EAAE;AAClB,KAAK;AACL,IAAI;AACJ,MAAM,QAAQ,EAAE,kDAAkD;AAClE,MAAM,QAAQ,EAAE,SAAS;AACzB,MAAM,UAAU,EAAE;AAClB,KAAK;AACL,IAAI;AACJ,MAAM,QAAQ,EAAE,0CAA0C;AAC1D,MAAM,QAAQ,EAAE,SAAS;AACzB,MAAM,UAAU,EAAE;AAClB,KAAK;AACL,IAAI;AACJ,MAAM,QAAQ,EAAE,kEAAkE;AAClF,MAAM,QAAQ,EAAE,YAAY;AAC5B,MAAM,UAAU,EAAE;AAClB,KAAK;AACL,IAAI;AACJ,MAAM,QAAQ,EAAE,uCAAuC;AACvD,MAAM,QAAQ,EAAE,SAAS;AACzB,MAAM,UAAU,EAAE;AAClB;AACA,GAAG;AACH,EAAE,IAAI,kBAAkB,GAAG,EAAE;AAC7B,EAAE,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;AAClJ,IAAI,kBAAkB,GAAG;AACzB,MAAM;AACN,QAAQ,QAAQ,EAAE,qDAAqD;AACvE,QAAQ,QAAQ,EAAE,WAAW;AAC7B,QAAQ,UAAU,EAAE;AACpB,OAAO;AACP,MAAM;AACN,QAAQ,QAAQ,EAAE,uDAAuD;AACzE,QAAQ,QAAQ,EAAE,WAAW;AAC7B,QAAQ,UAAU,EAAE;AACpB,OAAO;AACP,MAAM;AACN,QAAQ,QAAQ,EAAE,wFAAwF;AAC1G,QAAQ,QAAQ,EAAE,WAAW;AAC7B,QAAQ,UAAU,EAAE;AACpB,OAAO;AACP,MAAM;AACN,QAAQ,QAAQ,EAAE,gDAAgD;AAClE,QAAQ,QAAQ,EAAE,WAAW;AAC7B,QAAQ,UAAU,EAAE;AACpB,OAAO;AACP,MAAM;AACN,QAAQ,QAAQ,EAAE,8CAA8C;AAChE,QAAQ,QAAQ,EAAE,WAAW;AAC7B,QAAQ,UAAU,EAAE;AACpB;AACA,KAAK;AACL,GAAG,MAAM,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;AACpG,IAAI,kBAAkB,GAAG;AACzB,MAAM;AACN,QAAQ,QAAQ,EAAE,4CAA4C;AAC9D,QAAQ,QAAQ,EAAE,YAAY;AAC9B,QAAQ,UAAU,EAAE;AACpB,OAAO;AACP,MAAM;AACN,QAAQ,QAAQ,EAAE,kEAAkE;AACpF,QAAQ,QAAQ,EAAE,YAAY;AAC9B,QAAQ,UAAU,EAAE;AACpB,OAAO;AACP,MAAM;AACN,QAAQ,QAAQ,EAAE,+CAA+C;AACjE,QAAQ,QAAQ,EAAE,YAAY;AAC9B,QAAQ,UAAU,EAAE;AACpB,OAAO;AACP,MAAM;AACN,QAAQ,QAAQ,EAAE,kDAAkD;AACpE,QAAQ,QAAQ,EAAE,YAAY;AAC9B,QAAQ,UAAU,EAAE;AACpB,OAAO;AACP,MAAM;AACN,QAAQ,QAAQ,EAAE,8CAA8C;AAChE,QAAQ,QAAQ,EAAE,YAAY;AAC9B,QAAQ,UAAU,EAAE;AACpB;AACA,KAAK;AACL,GAAG,MAAM,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;AAC3D,IAAI,kBAAkB,GAAG;AACzB,MAAM;AACN,QAAQ,QAAQ,EAAE,yDAAyD;AAC3E,QAAQ,QAAQ,EAAE,WAAW;AAC7B,QAAQ,UAAU,EAAE;AACpB,OAAO;AACP,MAAM;AACN,QAAQ,QAAQ,EAAE,yDAAyD;AAC3E,QAAQ,QAAQ,EAAE,WAAW;AAC7B,QAAQ,UAAU,EAAE;AACpB,OAAO;AACP,MAAM;AACN,QAAQ,QAAQ,EAAE,2DAA2D;AAC7E,QAAQ,QAAQ,EAAE,WAAW;AAC7B,QAAQ,UAAU,EAAE;AACpB,OAAO;AACP,MAAM;AACN,QAAQ,QAAQ,EAAE,gDAAgD;AAClE,QAAQ,QAAQ,EAAE,WAAW;AAC7B,QAAQ,UAAU,EAAE;AACpB,OAAO;AACP,MAAM;AACN,QAAQ,QAAQ,EAAE,wDAAwD;AAC1E,QAAQ,QAAQ,EAAE,WAAW;AAC7B,QAAQ,UAAU,EAAE;AACpB;AACA,KAAK;AACL;AACA,EAAE,OAAO,CAAC,GAAG,aAAa,EAAE,GAAG,kBAAkB,CAAC;AAClD;;;;"}