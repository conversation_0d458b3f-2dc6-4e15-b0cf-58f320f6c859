{"version": 3, "file": "_server.ts-Co8NMMfI.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/admin/users/_userId_/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../../../../chunks/auth.js\";\nconst GET = async ({ cookies, params }) => {\n  const token = cookies.get(\"auth_token\");\n  if (!token) return new Response(\"Unauthorized\", { status: 401 });\n  const userData = verifySessionToken(token);\n  if (!userData?.id) return new Response(\"Unauthorized\", { status: 401 });\n  const adminUser = await prisma.user.findUnique({\n    where: { id: userData.id },\n    select: { isAdmin: true, role: true }\n  });\n  if (!adminUser || !adminUser.isAdmin && adminUser.role !== \"admin\") {\n    return new Response(\"Unauthorized\", { status: 401 });\n  }\n  const { userId } = params;\n  if (!userId) {\n    return new Response(\"User ID is required\", { status: 400 });\n  }\n  try {\n    const user = await prisma.user.findUnique({\n      where: { id: userId },\n      select: {\n        id: true,\n        email: true,\n        name: true,\n        role: true,\n        isAdmin: true,\n        createdAt: true,\n        updatedAt: true,\n        stripeCustomerId: true,\n        subscriptions: {\n          orderBy: { createdAt: \"desc\" },\n          take: 1\n        }\n      }\n    });\n    if (!user) {\n      return new Response(\"User not found\", { status: 404 });\n    }\n    const subscription = user.subscriptions[0];\n    const formattedUser = {\n      id: user.id,\n      email: user.email,\n      name: user.name,\n      role: user.role,\n      isAdmin: user.isAdmin,\n      createdAt: user.createdAt,\n      updatedAt: user.updatedAt,\n      stripeCustomerId: user.stripeCustomerId,\n      subscription: subscription ? {\n        id: subscription.id,\n        status: subscription.status,\n        currentPeriodStart: subscription.currentPeriodStart,\n        currentPeriodEnd: subscription.currentPeriodEnd,\n        cancelAtPeriodEnd: subscription.cancelAtPeriodEnd\n      } : null\n    };\n    return json(formattedUser);\n  } catch (error) {\n    console.error(\"Error fetching user:\", error);\n    return new Response(`Failed to fetch user: ${error.message}`, { status: 500 });\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;;;;;;AAGK,MAAC,GAAG,GAAG,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AAC3C,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE,EAAE,MAAM,QAAQ,GAAG,kBAAkB,CAAC,KAAK,CAAC;AAC5C,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE,EAAE,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AACjD,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE;AAC9B,IAAI,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AACvC,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,OAAO,IAAI,SAAS,CAAC,IAAI,KAAK,OAAO,EAAE;AACtE,IAAI,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxD;AACA,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;AAC3B,EAAE,IAAI,CAAC,MAAM,EAAE;AACf,IAAI,OAAO,IAAI,QAAQ,CAAC,qBAAqB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC/D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAC9C,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;AAC3B,MAAM,MAAM,EAAE;AACd,QAAQ,EAAE,EAAE,IAAI;AAChB,QAAQ,KAAK,EAAE,IAAI;AACnB,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,SAAS,EAAE,IAAI;AACvB,QAAQ,SAAS,EAAE,IAAI;AACvB,QAAQ,gBAAgB,EAAE,IAAI;AAC9B,QAAQ,aAAa,EAAE;AACvB,UAAU,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;AACxC,UAAU,IAAI,EAAE;AAChB;AACA;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,OAAO,IAAI,QAAQ,CAAC,gBAAgB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC5D;AACA,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;AAC9C,IAAI,MAAM,aAAa,GAAG;AAC1B,MAAM,EAAE,EAAE,IAAI,CAAC,EAAE;AACjB,MAAM,KAAK,EAAE,IAAI,CAAC,KAAK;AACvB,MAAM,IAAI,EAAE,IAAI,CAAC,IAAI;AACrB,MAAM,IAAI,EAAE,IAAI,CAAC,IAAI;AACrB,MAAM,OAAO,EAAE,IAAI,CAAC,OAAO;AAC3B,MAAM,SAAS,EAAE,IAAI,CAAC,SAAS;AAC/B,MAAM,SAAS,EAAE,IAAI,CAAC,SAAS;AAC/B,MAAM,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;AAC7C,MAAM,YAAY,EAAE,YAAY,GAAG;AACnC,QAAQ,EAAE,EAAE,YAAY,CAAC,EAAE;AAC3B,QAAQ,MAAM,EAAE,YAAY,CAAC,MAAM;AACnC,QAAQ,kBAAkB,EAAE,YAAY,CAAC,kBAAkB;AAC3D,QAAQ,gBAAgB,EAAE,YAAY,CAAC,gBAAgB;AACvD,QAAQ,iBAAiB,EAAE,YAAY,CAAC;AACxC,OAAO,GAAG;AACV,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC;AAC9B,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC;AAChD,IAAI,OAAO,IAAI,QAAQ,CAAC,CAAC,sBAAsB,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClF;AACA;;;;"}