{"version": 3, "file": "_server.ts-TyLb9xN8.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/auth/verify-token/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { c as createSessionToken } from \"../../../../../chunks/auth.js\";\nimport { R as RedisConnection } from \"../../../../../chunks/redis.js\";\nconst BASE_URL = process.env.PUBLIC_BASE_URL || \"http://localhost:5173\";\nconst TEST_EMAIL = \"<EMAIL>\";\nconst EMAIL_QUEUE_KEY = \"email:queue\";\nconst POST = async ({ request, cookies }) => {\n  const { token } = await request.json();\n  if (!token) {\n    return json({ error: \"Verification token is required\" }, { status: 400 });\n  }\n  const users = await prisma.user.findMany();\n  const user = users.find((u) => {\n    try {\n      const prefs = typeof u.preferences === \"string\" ? JSON.parse(u.preferences) : u.preferences;\n      return prefs.verificationToken === token && new Date(prefs.verificationExpires) > /* @__PURE__ */ new Date();\n    } catch (e) {\n      console.error(\"Error parsing preferences:\", e);\n      return false;\n    }\n  });\n  if (!user) {\n    return json({ error: \"Invalid or expired verification token\" }, { status: 400 });\n  }\n  try {\n    let prefs = {};\n    if (typeof user.preferences === \"string\") {\n      prefs = JSON.parse(user.preferences) || {};\n    } else if (user.preferences && typeof user.preferences === \"object\") {\n      prefs = { ...user.preferences };\n    }\n    prefs.emailVerified = true;\n    delete prefs.verificationToken;\n    delete prefs.verificationExpires;\n    await prisma.user.update({\n      where: { id: user.id },\n      data: {\n        preferences: prefs\n      }\n    });\n  } catch (error) {\n    console.error(\"Failed to update user preferences:\", error);\n    return json({ error: \"Failed to verify email\" }, { status: 500 });\n  }\n  console.log(`User ${user.email} has been verified successfully`);\n  try {\n    if (!RedisConnection) {\n      console.error(\"Redis client not available\");\n    } else {\n      const id = `email_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;\n      const loginUrl = `${BASE_URL}/auth/sign-in`;\n      const dashboardUrl = `${BASE_URL}/dashboard`;\n      const emailJob = {\n        id,\n        template: \"welcome\",\n        to: [user.email],\n        data: {\n          firstName: user.name || user.email.split(\"@\")[0],\n          loginUrl,\n          dashboardUrl\n        },\n        options: {\n          category: \"transactional\",\n          priority: 2,\n          // High priority\n          retries: 3,\n          tags: [{ name: \"action\", value: \"welcome\" }]\n        },\n        createdAt: (/* @__PURE__ */ new Date()).toISOString()\n      };\n      await RedisConnection.zadd(\n        EMAIL_QUEUE_KEY,\n        2,\n        // High priority\n        JSON.stringify(emailJob)\n      );\n      console.log(`Welcome email queued in Redis: ${id} (to ${user.email})`);\n      if (user.email.toLowerCase() !== TEST_EMAIL.toLowerCase()) {\n        const testEmailId = `email_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;\n        const testEmailJob = {\n          id: testEmailId,\n          template: \"welcome\",\n          to: [TEST_EMAIL],\n          data: {\n            firstName: \"Christopher (Test Copy)\",\n            loginUrl,\n            dashboardUrl\n          },\n          options: {\n            category: \"transactional\",\n            priority: 2,\n            // High priority\n            retries: 3,\n            tags: [\n              { name: \"action\", value: \"welcome\" },\n              { name: \"test\", value: \"true\" }\n            ]\n          },\n          createdAt: (/* @__PURE__ */ new Date()).toISOString()\n        };\n        await RedisConnection.zadd(\n          EMAIL_QUEUE_KEY,\n          2,\n          // High priority\n          JSON.stringify(testEmailJob)\n        );\n        console.log(`Test welcome email queued in Redis: ${testEmailId} (to ${TEST_EMAIL})`);\n      }\n      await RedisConnection.publish(\"email:process\", \"process_now\");\n      console.log(\"Published process_now message to email:process channel\");\n    }\n  } catch (error) {\n    console.error(\"Failed to queue welcome email:\", error);\n  }\n  const sessionToken = await createSessionToken({\n    email: user.email,\n    name: user.name || \"\",\n    image: user.image || \"\",\n    role: user.role,\n    id: user.id\n  });\n  cookies.set(\"auth_token\", sessionToken, {\n    path: \"/\",\n    httpOnly: true,\n    sameSite: \"lax\",\n    secure: process.env.NODE_ENV === \"production\",\n    maxAge: 60 * 60 * 24 * 30\n    // 30 days\n  });\n  return json({\n    success: true,\n    message: \"Email verified successfully\",\n    redirectTo: \"/dashboard\"\n  });\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,uBAAuB;AACvE,MAAM,UAAU,GAAG,wCAAwC;AAC3D,MAAM,eAAe,GAAG,aAAa;AAChC,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AAC7C,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACxC,EAAE,IAAI,CAAC,KAAK,EAAE;AACd,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,gCAAgC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7E;AACA,EAAE,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE;AAC5C,EAAE,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK;AACjC,IAAI,IAAI;AACR,MAAM,MAAM,KAAK,GAAG,OAAO,CAAC,CAAC,WAAW,KAAK,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,WAAW;AACjG,MAAM,OAAO,KAAK,CAAC,iBAAiB,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,mBAAmB,IAAI,IAAI,EAAE;AAClH,KAAK,CAAC,OAAO,CAAC,EAAE;AAChB,MAAM,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,CAAC,CAAC;AACpD,MAAM,OAAO,KAAK;AAClB;AACA,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uCAAuC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpF;AACA,EAAE,IAAI;AACN,IAAI,IAAI,KAAK,GAAG,EAAE;AAClB,IAAI,IAAI,OAAO,IAAI,CAAC,WAAW,KAAK,QAAQ,EAAE;AAC9C,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE;AAChD,KAAK,MAAM,IAAI,IAAI,CAAC,WAAW,IAAI,OAAO,IAAI,CAAC,WAAW,KAAK,QAAQ,EAAE;AACzE,MAAM,KAAK,GAAG,EAAE,GAAG,IAAI,CAAC,WAAW,EAAE;AACrC;AACA,IAAI,KAAK,CAAC,aAAa,GAAG,IAAI;AAC9B,IAAI,OAAO,KAAK,CAAC,iBAAiB;AAClC,IAAI,OAAO,KAAK,CAAC,mBAAmB;AACpC,IAAI,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AAC7B,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;AAC5B,MAAM,IAAI,EAAE;AACZ,QAAQ,WAAW,EAAE;AACrB;AACA,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC;AAC9D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,wBAAwB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACrE;AACA,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;AAClE,EAAE,IAAI;AACN,IAAI,IAAI,CAAC,eAAe,EAAE;AAC1B,MAAM,OAAO,CAAC,KAAK,CAAC,4BAA4B,CAAC;AACjD,KAAK,MAAM;AACX,MAAM,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AACrF,MAAM,MAAM,QAAQ,GAAG,CAAC,EAAE,QAAQ,CAAC,aAAa,CAAC;AACjD,MAAM,MAAM,YAAY,GAAG,CAAC,EAAE,QAAQ,CAAC,UAAU,CAAC;AAClD,MAAM,MAAM,QAAQ,GAAG;AACvB,QAAQ,EAAE;AACV,QAAQ,QAAQ,EAAE,SAAS;AAC3B,QAAQ,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;AACxB,QAAQ,IAAI,EAAE;AACd,UAAU,SAAS,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC1D,UAAU,QAAQ;AAClB,UAAU;AACV,SAAS;AACT,QAAQ,OAAO,EAAE;AACjB,UAAU,QAAQ,EAAE,eAAe;AACnC,UAAU,QAAQ,EAAE,CAAC;AACrB;AACA,UAAU,OAAO,EAAE,CAAC;AACpB,UAAU,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE;AACrD,SAAS;AACT,QAAQ,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC3D,OAAO;AACP,MAAM,MAAM,eAAe,CAAC,IAAI;AAChC,QAAQ,eAAe;AACvB,QAAQ,CAAC;AACT;AACA,QAAQ,IAAI,CAAC,SAAS,CAAC,QAAQ;AAC/B,OAAO;AACP,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,+BAA+B,EAAE,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC5E,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,UAAU,CAAC,WAAW,EAAE,EAAE;AACjE,QAAQ,MAAM,WAAW,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AAChG,QAAQ,MAAM,YAAY,GAAG;AAC7B,UAAU,EAAE,EAAE,WAAW;AACzB,UAAU,QAAQ,EAAE,SAAS;AAC7B,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC;AAC1B,UAAU,IAAI,EAAE;AAChB,YAAY,SAAS,EAAE,yBAAyB;AAChD,YAAY,QAAQ;AACpB,YAAY;AACZ,WAAW;AACX,UAAU,OAAO,EAAE;AACnB,YAAY,QAAQ,EAAE,eAAe;AACrC,YAAY,QAAQ,EAAE,CAAC;AACvB;AACA,YAAY,OAAO,EAAE,CAAC;AACtB,YAAY,IAAI,EAAE;AAClB,cAAc,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE;AAClD,cAAc,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM;AAC3C;AACA,WAAW;AACX,UAAU,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC7D,SAAS;AACT,QAAQ,MAAM,eAAe,CAAC,IAAI;AAClC,UAAU,eAAe;AACzB,UAAU,CAAC;AACX;AACA,UAAU,IAAI,CAAC,SAAS,CAAC,YAAY;AACrC,SAAS;AACT,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,oCAAoC,EAAE,WAAW,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;AAC5F;AACA,MAAM,MAAM,eAAe,CAAC,OAAO,CAAC,eAAe,EAAE,aAAa,CAAC;AACnE,MAAM,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC;AAC3E;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC;AAC1D;AACA,EAAE,MAAM,YAAY,GAAG,MAAM,kBAAkB,CAAC;AAChD,IAAI,KAAK,EAAE,IAAI,CAAC,KAAK;AACrB,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,EAAE;AACzB,IAAI,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,EAAE;AAC3B,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI;AACnB,IAAI,EAAE,EAAE,IAAI,CAAC;AACb,GAAG,CAAC;AACJ,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,YAAY,EAAE;AAC1C,IAAI,IAAI,EAAE,GAAG;AACb,IAAI,QAAQ,EAAE,IAAI;AAClB,IAAI,QAAQ,EAAE,KAAK;AACnB,IAAI,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;AACjD,IAAI,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;AAC3B;AACA,GAAG,CAAC;AACJ,EAAE,OAAO,IAAI,CAAC;AACd,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,OAAO,EAAE,6BAA6B;AAC1C,IAAI,UAAU,EAAE;AAChB,GAAG,CAAC;AACJ;;;;"}