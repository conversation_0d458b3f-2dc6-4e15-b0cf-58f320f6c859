{"version": 3, "file": "_page.svelte-DjBE9WWK.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/legal/_page.svelte.js"], "sourcesContent": ["import { V as escape_html, U as ensure_array_like, R as attr, N as bind_props } from \"../../../chunks/index3.js\";\nimport { S as SEO } from \"../../../chunks/SEO.js\";\nimport { P as PortableText } from \"../../../chunks/PortableText.js\";\nimport { F as File_text } from \"../../../chunks/file-text.js\";\nimport { S as Scale, A as Accessibility } from \"../../../chunks/scale.js\";\nimport { G as Globe } from \"../../../chunks/globe.js\";\nimport { D as Database } from \"../../../chunks/database.js\";\nimport { C as Cookie } from \"../../../chunks/cookie.js\";\nimport { S as Shield } from \"../../../chunks/shield.js\";\nfunction _page($$payload, $$props) {\n  let data = $$props[\"data\"];\n  const { legalPage } = data;\n  function getIconComponent(iconName) {\n    switch (iconName) {\n      case \"Shield\":\n        return Shield;\n      case \"FileText\":\n        return File_text;\n      case \"Cookie\":\n        return Cookie;\n      case \"Accessibility\":\n        return Accessibility;\n      case \"Database\":\n        return Database;\n      case \"Globe\":\n        return Globe;\n      case \"Scale\":\n        return Scale;\n      default:\n        return File_text;\n    }\n  }\n  SEO($$payload, {\n    title: legalPage?.title ? `${legalPage.title} | Hirli` : \"Legal Center | Hirli\",\n    description: legalPage?.description || \"Access Hirli's legal documents, policies, and compliance information.\",\n    keywords: \"legal, terms of service, privacy policy, cookie policy, accessibility, data security, GDPR, legal notices\"\n  });\n  $$payload.out += `<!----> <div class=\"max-w-none\"><h1 class=\"mb-4 text-2xl font-bold\">${escape_html(legalPage?.title || \"Legal Center\")}</h1> `;\n  if (legalPage?.content) {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<div class=\"mb-6\">`;\n    PortableText($$payload, { value: legalPage.content });\n    $$payload.out += `<!----></div>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<p class=\"mb-6 text-gray-700\">Welcome to Hirli's Legal Center. Here you'll find all our legal documents, policies, and\n      compliance information. We're committed to transparency and protecting your rights while using\n      our services.</p>`;\n  }\n  $$payload.out += `<!--]--> `;\n  if (legalPage?.legalPages && legalPage.legalPages.length > 0) {\n    $$payload.out += \"<!--[-->\";\n    const each_array = ensure_array_like(legalPage.legalPages);\n    $$payload.out += `<div class=\"not-prose mt-8 grid grid-cols-1 gap-6 md:grid-cols-2\"><!--[-->`;\n    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n      let page = each_array[$$index];\n      $$payload.out += `<a${attr(\"href\", `/legal/${page.slug}`)} class=\"bg-card hover:bg-muted/50 group flex flex-col rounded-xl border p-6 shadow-sm transition-colors\"><div class=\"mb-4 flex items-center gap-3\"><div class=\"bg-primary/10 text-primary flex h-10 w-10 items-center justify-center rounded-full\"><!---->`;\n      getIconComponent(page.icon)?.($$payload, { class: \"h-5 w-5\" });\n      $$payload.out += `<!----></div> <h2 class=\"text-xl font-semibold\">${escape_html(page.title)}</h2></div> <p class=\"text-muted-foreground\">${escape_html(page.description)}</p></a>`;\n    }\n    $$payload.out += `<!--]--></div>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<div class=\"mt-8 rounded-lg border bg-amber-50 p-6\"><p class=\"text-amber-800\">No legal pages found in Sanity CMS. Please make sure you have created legal pages with the\n        appropriate slugs.</p></div>`;\n  }\n  $$payload.out += `<!--]--> `;\n  if (legalPage?.legalContact) {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<div class=\"mt-12 rounded-lg border bg-gray-50 p-6\"><p class=\"mb-4 text-gray-700\">${escape_html(legalPage.legalContact.message || \"If you have questions about our legal documents, please contact our legal team.\")}</p> <a${attr(\"href\", `mailto:${legalPage.legalContact.email || \"<EMAIL>\"}`)} class=\"text-primary font-medium hover:underline\">${escape_html(legalPage.legalContact.email || \"<EMAIL>\")}</a></div>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--></div>`;\n  bind_props($$props, { data });\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AASA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC;AAC5B,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI;AAC5B,EAAE,SAAS,gBAAgB,CAAC,QAAQ,EAAE;AACtC,IAAI,QAAQ,QAAQ;AACpB,MAAM,KAAK,QAAQ;AACnB,QAAQ,OAAO,MAAM;AACrB,MAAM,KAAK,UAAU;AACrB,QAAQ,OAAO,SAAS;AACxB,MAAM,KAAK,QAAQ;AACnB,QAAQ,OAAO,MAAM;AACrB,MAAM,KAAK,eAAe;AAC1B,QAAQ,OAAO,aAAa;AAC5B,MAAM,KAAK,UAAU;AACrB,QAAQ,OAAO,QAAQ;AACvB,MAAM,KAAK,OAAO;AAClB,QAAQ,OAAO,KAAK;AACpB,MAAM,KAAK,OAAO;AAClB,QAAQ,OAAO,KAAK;AACpB,MAAM;AACN,QAAQ,OAAO,SAAS;AACxB;AACA;AACA,EAAE,GAAG,CAAC,SAAS,EAAE;AACjB,IAAI,KAAK,EAAE,SAAS,EAAE,KAAK,GAAG,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,sBAAsB;AACnF,IAAI,WAAW,EAAE,SAAS,EAAE,WAAW,IAAI,uEAAuE;AAClH,IAAI,QAAQ,EAAE;AACd,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,oEAAoE,EAAE,WAAW,CAAC,SAAS,EAAE,KAAK,IAAI,cAAc,CAAC,CAAC,MAAM,CAAC;AACjJ,EAAE,IAAI,SAAS,EAAE,OAAO,EAAE;AAC1B,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;AACzC,IAAI,YAAY,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,SAAS,CAAC,OAAO,EAAE,CAAC;AACzD,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACpC,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC;AACtB;AACA,uBAAuB,CAAC;AACxB;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AAC9B,EAAE,IAAI,SAAS,EAAE,UAAU,IAAI,SAAS,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;AAChE,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,MAAM,UAAU,GAAG,iBAAiB,CAAC,SAAS,CAAC,UAAU,CAAC;AAC9D,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,0EAA0E,CAAC;AACjG,IAAI,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACvF,MAAM,IAAI,IAAI,GAAG,UAAU,CAAC,OAAO,CAAC;AACpC,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,0PAA0P,CAAC;AAC3T,MAAM,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,SAAS,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AACpE,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,gDAAgD,EAAE,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,6CAA6C,EAAE,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC;AACxL;AACA,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACrC,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC;AACtB,oCAAoC,CAAC;AACrC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AAC9B,EAAE,IAAI,SAAS,EAAE,YAAY,EAAE;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,kFAAkF,EAAE,WAAW,CAAC,SAAS,CAAC,YAAY,CAAC,OAAO,IAAI,iFAAiF,CAAC,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,SAAS,CAAC,YAAY,CAAC,KAAK,IAAI,iBAAiB,CAAC,CAAC,CAAC,CAAC,kDAAkD,EAAE,WAAW,CAAC,SAAS,CAAC,YAAY,CAAC,KAAK,IAAI,iBAAiB,CAAC,CAAC,UAAU,CAAC;AAC9b,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACnC,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,CAAC;AAC/B;;;;"}