{"version": 3, "file": "_server.ts-D32AVaz3.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/resume/_id_/optimize/_server.ts.js"], "sourcesContent": ["import { p as prisma } from \"../../../../../../chunks/prisma.js\";\nimport { j as json } from \"../../../../../../chunks/index.js\";\nimport { g as getRedisClient } from \"../../../../../../chunks/redis.js\";\nlet redis = null;\nif (!(process.env.NODE_ENV === \"production\" && true && true)) {\n  getRedisClient().then((client) => {\n    redis = client;\n    if (redis) {\n      console.log(\"Redis client initialized for resume optimization\");\n    } else {\n      console.log(\"Redis client not available for resume optimization\");\n    }\n  }).catch((error) => {\n    console.error(`Failed to initialize Redis: ${error}`);\n    redis = null;\n  });\n}\nconst STREAM_NAME = \"optimize-resume\";\nconst GROUP_NAME = \"optimize-group\";\nasync function ensureGroup() {\n  if (!redis) {\n    console.log(\"Redis not available, skipping stream group creation\");\n    return false;\n  }\n  try {\n    await redis.xgroup(\"CREATE\", STREAM_NAME, GROUP_NAME, \"$\", \"MKSTREAM\");\n    console.log(`[redis] Stream group '${GROUP_NAME}' created.`);\n    return true;\n  } catch (err) {\n    if (!err.message.includes(\"BUSYGROUP\")) {\n      console.error(\"Failed to create Redis stream group:\", err);\n      return false;\n    }\n    return true;\n  }\n}\nconst POST = async ({ params }) => {\n  try {\n    console.log(\"Optimizing resume with ID:\", params.id);\n    const resume = await prisma.resume.findUnique({\n      where: { id: params.id },\n      include: {\n        document: true\n      }\n    });\n    if (!resume) {\n      return json({ error: \"Resume not found\" }, { status: 404 });\n    }\n    await prisma.resume.update({\n      where: { id: params.id },\n      data: {\n        // isOptimized: false,  // Uncomment if this field exists in your schema\n        // optimizedAt: null,   // Uncomment if this field exists in your schema\n        updatedAt: /* @__PURE__ */ new Date()\n        // Update the timestamp\n      }\n    });\n    let addedToQueue = false;\n    if (redis) {\n      try {\n        await ensureGroup();\n        await redis.xadd(\n          STREAM_NAME,\n          \"*\",\n          \"job\",\n          JSON.stringify({\n            resumeId: resume.id,\n            documentId: resume.documentId,\n            filePath: resume.document.filePath,\n            fileUrl: resume.document.fileUrl\n          })\n        );\n        await redis.publish(\n          \"optimize-resume:events\",\n          JSON.stringify({ action: \"new_job\", resumeId: resume.id })\n        );\n        addedToQueue = true;\n        console.log(\"Resume added to optimization queue and event published\");\n      } catch (redisError) {\n        console.error(\"Redis error:\", redisError);\n      }\n    }\n    return json({\n      success: true,\n      message: addedToQueue ? \"Resume added to optimization queue\" : \"Resume marked for optimization but not added to queue (Redis unavailable)\"\n    });\n  } catch (error) {\n    console.error(\"Error adding resume to optimization queue:\", error);\n    return json(\n      { error: \"Failed to add resume to optimization queue\", details: String(error) },\n      { status: 500 }\n    );\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;AAGA,IAAI,KAAK,GAAG,IAAI;AAChB,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,IAAI,IAAI,IAAI,IAAI,CAAC,EAAE;AAC9D,EAAE,cAAc,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK;AACpC,IAAI,KAAK,GAAG,MAAM;AAClB,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC;AACrE,KAAK,MAAM;AACX,MAAM,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC;AACvE;AACA,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,KAAK;AACtB,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC,CAAC;AACzD,IAAI,KAAK,GAAG,IAAI;AAChB,GAAG,CAAC;AACJ;AACA,MAAM,WAAW,GAAG,iBAAiB;AACrC,MAAM,UAAU,GAAG,gBAAgB;AACnC,eAAe,WAAW,GAAG;AAC7B,EAAE,IAAI,CAAC,KAAK,EAAE;AACd,IAAI,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC;AACtE,IAAI,OAAO,KAAK;AAChB;AACA,EAAE,IAAI;AACN,IAAI,MAAM,KAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,EAAE,UAAU,CAAC;AAC1E,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,sBAAsB,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC;AAChE,IAAI,OAAO,IAAI;AACf,GAAG,CAAC,OAAO,GAAG,EAAE;AAChB,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;AAC5C,MAAM,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,GAAG,CAAC;AAChE,MAAM,OAAO,KAAK;AAClB;AACA,IAAI,OAAO,IAAI;AACf;AACA;AACK,MAAC,IAAI,GAAG,OAAO,EAAE,MAAM,EAAE,KAAK;AACnC,EAAE,IAAI;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE,MAAM,CAAC,EAAE,CAAC;AACxD,IAAI,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;AAClD,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE;AAC9B,MAAM,OAAO,EAAE;AACf,QAAQ,QAAQ,EAAE;AAClB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,kBAAkB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE;AACA,IAAI,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;AAC/B,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE;AAC9B,MAAM,IAAI,EAAE;AACZ;AACA;AACA,QAAQ,SAAS,kBAAkB,IAAI,IAAI;AAC3C;AACA;AACA,KAAK,CAAC;AACN,IAAI,IAAI,YAAY,GAAG,KAAK;AAC5B,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,IAAI;AACV,QAAQ,MAAM,WAAW,EAAE;AAC3B,QAAQ,MAAM,KAAK,CAAC,IAAI;AACxB,UAAU,WAAW;AACrB,UAAU,GAAG;AACb,UAAU,KAAK;AACf,UAAU,IAAI,CAAC,SAAS,CAAC;AACzB,YAAY,QAAQ,EAAE,MAAM,CAAC,EAAE;AAC/B,YAAY,UAAU,EAAE,MAAM,CAAC,UAAU;AACzC,YAAY,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,QAAQ;AAC9C,YAAY,OAAO,EAAE,MAAM,CAAC,QAAQ,CAAC;AACrC,WAAW;AACX,SAAS;AACT,QAAQ,MAAM,KAAK,CAAC,OAAO;AAC3B,UAAU,wBAAwB;AAClC,UAAU,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,CAAC,EAAE,EAAE;AACnE,SAAS;AACT,QAAQ,YAAY,GAAG,IAAI;AAC3B,QAAQ,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC;AAC7E,OAAO,CAAC,OAAO,UAAU,EAAE;AAC3B,QAAQ,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,UAAU,CAAC;AACjD;AACA;AACA,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,OAAO,EAAE,YAAY,GAAG,oCAAoC,GAAG;AACrE,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC;AACtE,IAAI,OAAO,IAAI;AACf,MAAM,EAAE,KAAK,EAAE,4CAA4C,EAAE,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;AACrF,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;;;;"}