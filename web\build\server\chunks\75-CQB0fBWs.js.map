{"version": 3, "file": "75-CQB0fBWs.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/help/category/_slug_/_page.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/75.js"], "sourcesContent": ["import { e as error } from \"../../../../../chunks/index.js\";\nimport { d as getHelpArticlesByCategory, g as getHelpArticles } from \"../../../../../chunks/client2.js\";\nconst load = async ({ params }) => {\n  const { slug } = params;\n  try {\n    const categoryArticles = await getHelpArticlesByCategory(slug);\n    if (!categoryArticles || categoryArticles.length === 0) {\n      throw error(404, \"Category not found or has no articles\");\n    }\n    const categoryName = getCategoryName(slug);\n    const categoryIcon = getCategoryIcon(slug);\n    const allArticles = await getHelpArticles();\n    const articlesByCategory = allArticles.reduce((acc, article) => {\n      if (!acc[article.category]) {\n        acc[article.category] = {\n          name: getCategoryName(article.category),\n          slug: article.category,\n          icon: getCategoryIcon(article.category),\n          articles: []\n        };\n      }\n      acc[article.category].articles.push({\n        id: article._id,\n        title: article.title,\n        slug: article.slug.current\n      });\n      return acc;\n    }, {});\n    const categories = Object.values(articlesByCategory).sort(\n      (a, b) => a.name.localeCompare(b.name)\n    );\n    const formattedArticles = categoryArticles.map((article) => ({\n      ...article,\n      id: article._id,\n      slug: article.slug.current,\n      excerpt: article.description,\n      category: {\n        id: article.category,\n        name: getCategoryName(article.category),\n        slug: article.category,\n        icon: getCategoryIcon(article.category)\n      },\n      tags: article.tags?.map((tag) => ({\n        id: tag,\n        name: tag,\n        slug: tag.toLowerCase().replace(/\\s+/g, \"-\")\n      })) || []\n    }));\n    return {\n      category: {\n        name: categoryName,\n        slug,\n        icon: categoryIcon\n      },\n      articles: formattedArticles,\n      categories\n    };\n  } catch (err) {\n    console.error(`Error loading category ${slug}:`, err);\n    throw error(404, \"Category not found\");\n  }\n};\nfunction getCategoryName(slug) {\n  const categoryMap = {\n    \"getting-started\": \"Getting Started\",\n    \"auto-apply\": \"Using Auto Apply\",\n    \"account-billing\": \"Account & Billing\",\n    troubleshooting: \"Troubleshooting\",\n    \"privacy-security\": \"Privacy & Security\"\n  };\n  return categoryMap[slug] || slug;\n}\nfunction getCategoryIcon(slug) {\n  const iconMap = {\n    \"getting-started\": \"BookOpen\",\n    \"auto-apply\": \"FileText\",\n    \"account-billing\": \"CreditCard\",\n    troubleshooting: \"HelpCircle\",\n    \"privacy-security\": \"Shield\"\n  };\n  return iconMap[slug] || \"HelpCircle\";\n}\nexport {\n  load\n};\n", "import * as server from '../entries/pages/help/category/_slug_/_page.server.ts.js';\n\nexport const index = 75;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/help/category/_slug_/_page.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/help/category/[slug]/+page.server.ts\";\nexport const imports = [\"_app/immutable/nodes/75.BXojEoWw.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/u21ee2wt.js\",\"_app/immutable/chunks/C3w0v0gR.js\",\"_app/immutable/chunks/BvdI7LR8.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/Btcx8l8F.js\",\"_app/immutable/chunks/C6g8ubaU.js\",\"_app/immutable/chunks/B-Xjo-Yt.js\",\"_app/immutable/chunks/DR0R8QKE.js\",\"_app/immutable/chunks/nZgk9enP.js\",\"_app/immutable/chunks/FN1sk3P2.js\",\"_app/immutable/chunks/CfcZq63z.js\",\"_app/immutable/chunks/5V1tIHTN.js\",\"_app/immutable/chunks/Cf6rS4LV.js\",\"_app/immutable/chunks/ncUU1dSD.js\",\"_app/immutable/chunks/yW0TxTga.js\",\"_app/immutable/chunks/BBa424ah.js\",\"_app/immutable/chunks/D4f2twK-.js\",\"_app/immutable/chunks/w80wGXGd.js\",\"_app/immutable/chunks/jRvHGFcG.js\",\"_app/immutable/chunks/CGtH72Kl.js\",\"_app/immutable/chunks/C1FmrZbK.js\",\"_app/immutable/chunks/3WmhYGjL.js\",\"_app/immutable/chunks/BaVT73bJ.js\",\"_app/immutable/chunks/BfX7a-t9.js\",\"_app/immutable/chunks/DT9WCdWY.js\",\"_app/immutable/chunks/Bpi49Nrf.js\",\"_app/immutable/chunks/OOsIR5sE.js\",\"_app/immutable/chunks/Cb-3cdbh.js\",\"_app/immutable/chunks/DX6rZLP_.js\",\"_app/immutable/chunks/CIOgxH3l.js\",\"_app/immutable/chunks/DuoUhxYL.js\",\"_app/immutable/chunks/CnMg5bH0.js\",\"_app/immutable/chunks/BJIrNhIJ.js\",\"_app/immutable/chunks/D-o7ybA5.js\",\"_app/immutable/chunks/XESq6qWN.js\",\"_app/immutable/chunks/Bd3zs5C6.js\",\"_app/immutable/chunks/C2MdR6K0.js\",\"_app/immutable/chunks/hQ6uUXJy.js\",\"_app/immutable/chunks/Cs0qIT7f.js\",\"_app/immutable/chunks/CBdr9r-W.js\",\"_app/immutable/chunks/BPr9JIwg.js\",\"_app/immutable/chunks/OXTnUuEm.js\",\"_app/immutable/chunks/BwkAotBa.js\",\"_app/immutable/chunks/C8-oZ3V_.js\",\"_app/immutable/chunks/CsOU4yHs.js\",\"_app/immutable/chunks/BJwwRUaF.js\",\"_app/immutable/chunks/DuGukytH.js\",\"_app/immutable/chunks/BkJY4La4.js\",\"_app/immutable/chunks/DETxXRrJ.js\",\"_app/immutable/chunks/GwmmX_iF.js\",\"_app/immutable/chunks/D50jIuLr.js\",\"_app/immutable/chunks/DaBofrVv.js\",\"_app/immutable/chunks/DM07Bv7T.js\",\"_app/immutable/chunks/ChqRiddM.js\",\"_app/immutable/chunks/CxmsTEaf.js\",\"_app/immutable/chunks/rNI1Perp.js\",\"_app/immutable/chunks/DDpHsKo4.js\",\"_app/immutable/chunks/B-l1ubNa.js\",\"_app/immutable/chunks/DVGNPJty.js\",\"_app/immutable/chunks/Ce6y1v79.js\",\"_app/immutable/chunks/BV675lZR.js\",\"_app/immutable/chunks/B_tyjpYb.js\",\"_app/immutable/chunks/mCB4pHNc.js\",\"_app/immutable/chunks/BnV6AXQp.js\",\"_app/immutable/chunks/Ce4BqqU6.js\",\"_app/immutable/chunks/hA0h0kTo.js\",\"_app/immutable/chunks/1gTNXEeM.js\",\"_app/immutable/chunks/C3y1xd2Y.js\",\"_app/immutable/chunks/BM9SsHQg.js\",\"_app/immutable/chunks/eW6QhNR3.js\",\"_app/immutable/chunks/CIPPbbaT.js\",\"_app/immutable/chunks/iTqMWrIH.js\",\"_app/immutable/chunks/DfWpXjG9.js\",\"_app/immutable/chunks/DxcWIogY.js\",\"_app/immutable/chunks/BLiq6Dlm.js\",\"_app/immutable/chunks/CDnvByek.js\",\"_app/immutable/chunks/C2AK_5VT.js\",\"_app/immutable/chunks/ITUnHPIu.js\",\"_app/immutable/chunks/DZCYCPd3.js\",\"_app/immutable/chunks/A-1J-2PQ.js\",\"_app/immutable/chunks/whJ0cJ1Q.js\",\"_app/immutable/chunks/Bx0dWF_O.js\",\"_app/immutable/chunks/JqDL1wc2.js\",\"_app/immutable/chunks/CXUk17vb.js\",\"_app/immutable/chunks/BNEH2jqx.js\",\"_app/immutable/chunks/BBNNmnYR.js\",\"_app/immutable/chunks/DkmCSZhC.js\",\"_app/immutable/chunks/-vfp2Q9I.js\",\"_app/immutable/chunks/CKg8MWp_.js\",\"_app/immutable/chunks/DW7T7T22.js\",\"_app/immutable/chunks/D6Qh9vtB.js\",\"_app/immutable/chunks/BAIxhb6t.js\",\"_app/immutable/chunks/BIQwBPm4.js\",\"_app/immutable/chunks/-SpbofVw.js\",\"_app/immutable/chunks/BxlgRp1U.js\",\"_app/immutable/chunks/lZwfPN85.js\",\"_app/immutable/chunks/bEtmAhPN.js\",\"_app/immutable/chunks/DLZV8qTT.js\",\"_app/immutable/chunks/Dt_Sfkn6.js\",\"_app/immutable/chunks/BRdyUBC_.js\",\"_app/immutable/chunks/6BxQgNmX.js\",\"_app/immutable/chunks/tr-scC-m.js\",\"_app/immutable/chunks/DdoUfFy4.js\",\"_app/immutable/chunks/8b74MdfD.js\",\"_app/immutable/chunks/zNKWipEG.js\",\"_app/immutable/chunks/6UJoWgvL.js\",\"_app/immutable/chunks/7AwcL9ec.js\",\"_app/immutable/chunks/CY_6SfHi.js\",\"_app/immutable/chunks/BEVim9wJ.js\",\"_app/immutable/chunks/D1zde6Ej.js\",\"_app/immutable/chunks/DQB68x0Z.js\",\"_app/immutable/chunks/CqJi5rQC.js\",\"_app/immutable/chunks/BuYRPDDz.js\",\"_app/immutable/chunks/w9xFoQXV.js\",\"_app/immutable/chunks/CLdCqm7k.js\",\"_app/immutable/chunks/iDciRV2n.js\",\"_app/immutable/chunks/DRGimm5x.js\",\"_app/immutable/chunks/Cl1ZeFOf.js\",\"_app/immutable/chunks/CrpvsheG.js\",\"_app/immutable/chunks/BhzFx1Wy.js\",\"_app/immutable/chunks/DHNQRrgO.js\",\"_app/immutable/chunks/CHsAkgDv.js\",\"_app/immutable/chunks/yPulTJ2h.js\",\"_app/immutable/chunks/CwgkX8t9.js\",\"_app/immutable/chunks/DSDNnczY.js\",\"_app/immutable/chunks/BQS6hE8b.js\",\"_app/immutable/chunks/QtAhPN2H.js\",\"_app/immutable/chunks/lirlZJ-b.js\",\"_app/immutable/chunks/2KCyzleV.js\",\"_app/immutable/chunks/aemnuA_0.js\",\"_app/immutable/chunks/BBh-2PfQ.js\",\"_app/immutable/chunks/tjBMsfLi.js\",\"_app/immutable/chunks/DOf_JqyE.js\",\"_app/immutable/chunks/DvO_AOqy.js\",\"_app/immutable/chunks/DR5zc253.js\",\"_app/immutable/chunks/qwsZpUIl.js\",\"_app/immutable/chunks/BMRJMPdn.js\",\"_app/immutable/chunks/CTQ8y7hr.js\",\"_app/immutable/chunks/BHzYYMdu.js\",\"_app/immutable/chunks/D871oxnv.js\",\"_app/immutable/chunks/BoNCRmBc.js\",\"_app/immutable/chunks/BAawoUIy.js\",\"_app/immutable/chunks/D8pQCLOH.js\",\"_app/immutable/chunks/FAbXdqfL.js\",\"_app/immutable/chunks/DumgozFE.js\",\"_app/immutable/chunks/CYoZicO9.js\",\"_app/immutable/chunks/CZ8wIJN8.js\",\"_app/immutable/chunks/BNVswwUK.js\",\"_app/immutable/chunks/C33xR25f.js\",\"_app/immutable/chunks/Bpd96RWU.js\",\"_app/immutable/chunks/Csk_I0QV.js\",\"_app/immutable/chunks/CTO_B1Jk.js\",\"_app/immutable/chunks/G5Oo-PmU.js\",\"_app/immutable/chunks/CzSntoiK.js\",\"_app/immutable/chunks/B_6ivTD3.js\",\"_app/immutable/chunks/BSHZ37s_.js\",\"_app/immutable/chunks/PxawOV43.js\",\"_app/immutable/chunks/CnpHcmx3.js\",\"_app/immutable/chunks/1zwBog76.js\",\"_app/immutable/chunks/BIUPxhhl.js\"];\nexport const stylesheets = [\"_app/immutable/assets/scroll-area.bHHIbcsu.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;AAEA,MAAM,IAAI,GAAG,OAAO,EAAE,MAAM,EAAE,KAAK;AACnC,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;AACzB,EAAE,IAAI;AACN,IAAI,MAAM,gBAAgB,GAAG,MAAM,yBAAyB,CAAC,IAAI,CAAC;AAClE,IAAI,IAAI,CAAC,gBAAgB,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;AAC5D,MAAM,MAAM,KAAK,CAAC,GAAG,EAAE,uCAAuC,CAAC;AAC/D;AACA,IAAI,MAAM,YAAY,GAAG,eAAe,CAAC,IAAI,CAAC;AAC9C,IAAI,MAAM,YAAY,GAAG,eAAe,CAAC,IAAI,CAAC;AAC9C,IAAI,MAAM,WAAW,GAAG,MAAM,eAAe,EAAE;AAC/C,IAAI,MAAM,kBAAkB,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,OAAO,KAAK;AACpE,MAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AAClC,QAAQ,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG;AAChC,UAAU,IAAI,EAAE,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC;AACjD,UAAU,IAAI,EAAE,OAAO,CAAC,QAAQ;AAChC,UAAU,IAAI,EAAE,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC;AACjD,UAAU,QAAQ,EAAE;AACpB,SAAS;AACT;AACA,MAAM,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC;AAC1C,QAAQ,EAAE,EAAE,OAAO,CAAC,GAAG;AACvB,QAAQ,KAAK,EAAE,OAAO,CAAC,KAAK;AAC5B,QAAQ,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC;AAC3B,OAAO,CAAC;AACR,MAAM,OAAO,GAAG;AAChB,KAAK,EAAE,EAAE,CAAC;AACV,IAAI,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,IAAI;AAC7D,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI;AAC3C,KAAK;AACL,IAAI,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,OAAO,MAAM;AACjE,MAAM,GAAG,OAAO;AAChB,MAAM,EAAE,EAAE,OAAO,CAAC,GAAG;AACrB,MAAM,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,OAAO;AAChC,MAAM,OAAO,EAAE,OAAO,CAAC,WAAW;AAClC,MAAM,QAAQ,EAAE;AAChB,QAAQ,EAAE,EAAE,OAAO,CAAC,QAAQ;AAC5B,QAAQ,IAAI,EAAE,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC;AAC/C,QAAQ,IAAI,EAAE,OAAO,CAAC,QAAQ;AAC9B,QAAQ,IAAI,EAAE,eAAe,CAAC,OAAO,CAAC,QAAQ;AAC9C,OAAO;AACP,MAAM,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,GAAG,MAAM;AACxC,QAAQ,EAAE,EAAE,GAAG;AACf,QAAQ,IAAI,EAAE,GAAG;AACjB,QAAQ,IAAI,EAAE,GAAG,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG;AACnD,OAAO,CAAC,CAAC,IAAI;AACb,KAAK,CAAC,CAAC;AACP,IAAI,OAAO;AACX,MAAM,QAAQ,EAAE;AAChB,QAAQ,IAAI,EAAE,YAAY;AAC1B,QAAQ,IAAI;AACZ,QAAQ,IAAI,EAAE;AACd,OAAO;AACP,MAAM,QAAQ,EAAE,iBAAiB;AACjC,MAAM;AACN,KAAK;AACL,GAAG,CAAC,OAAO,GAAG,EAAE;AAChB,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC;AACzD,IAAI,MAAM,KAAK,CAAC,GAAG,EAAE,oBAAoB,CAAC;AAC1C;AACA,CAAC;AACD,SAAS,eAAe,CAAC,IAAI,EAAE;AAC/B,EAAE,MAAM,WAAW,GAAG;AACtB,IAAI,iBAAiB,EAAE,iBAAiB;AACxC,IAAI,YAAY,EAAE,kBAAkB;AACpC,IAAI,iBAAiB,EAAE,mBAAmB;AAC1C,IAAI,eAAe,EAAE,iBAAiB;AACtC,IAAI,kBAAkB,EAAE;AACxB,GAAG;AACH,EAAE,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,IAAI;AAClC;AACA,SAAS,eAAe,CAAC,IAAI,EAAE;AAC/B,EAAE,MAAM,OAAO,GAAG;AAClB,IAAI,iBAAiB,EAAE,UAAU;AACjC,IAAI,YAAY,EAAE,UAAU;AAC5B,IAAI,iBAAiB,EAAE,YAAY;AACnC,IAAI,eAAe,EAAE,YAAY;AACjC,IAAI,kBAAkB,EAAE;AACxB,GAAG;AACH,EAAE,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,YAAY;AACtC;;;;;;;AC/EY,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAAuD,CAAC,EAAE;AAErH,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACz4L,MAAC,WAAW,GAAG,CAAC,gDAAgD;AAChE,MAAC,KAAK,GAAG;;;;"}