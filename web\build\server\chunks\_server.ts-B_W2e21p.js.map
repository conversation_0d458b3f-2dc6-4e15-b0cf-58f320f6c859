{"version": 3, "file": "_server.ts-B_W2e21p.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/automation/runs/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { g as getUserFromToken } from \"../../../../../chunks/auth.js\";\nimport { createClient } from \"redis\";\nconst POST = async ({ request, cookies }) => {\n  const userPromise = getUserFromToken(cookies);\n  const user = await userPromise;\n  if (!user) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  try {\n    const {\n      profileId,\n      keywords,\n      location,\n      maxJobsToApply = 10,\n      minMatchScore = 70,\n      autoApplyEnabled = false,\n      salaryMin,\n      salaryMax,\n      experienceLevelMin,\n      experienceLevelMax,\n      jobTypes = [],\n      remotePreference = \"any\",\n      companySizePreference = [],\n      excludeCompanies = [],\n      preferredCompanies = [],\n      specifications = {}\n    } = await request.json();\n    if (!profileId) {\n      return json({ error: \"Profile ID is required\" }, { status: 400 });\n    }\n    let profile;\n    try {\n      profile = await prisma.profile.findFirst({\n        where: {\n          id: profileId,\n          OR: [\n            { userId: user.id },\n            {\n              team: {\n                members: {\n                  some: { userId: user.id }\n                }\n              }\n            }\n          ]\n        },\n        include: {\n          data: true,\n          documents: {\n            include: {\n              document: true\n            }\n          }\n        }\n      });\n    } catch (profileError) {\n      console.error(\"Error finding profile:\", profileError);\n      const errorMessage = profileError instanceof Error ? profileError.message : \"Unknown error\";\n      return json({ error: \"Error finding profile\", details: errorMessage }, { status: 500 });\n    }\n    if (!profile) {\n      return json({ error: \"Profile not found\" }, { status: 404 });\n    }\n    const automationRun = await prisma.automationRun.create({\n      data: {\n        userId: user.id,\n        profileId,\n        keywords: Array.isArray(keywords) ? keywords.join(\", \") : keywords || \"\",\n        location: Array.isArray(location) ? location.join(\", \") : location || \"\",\n        maxJobsToApply,\n        minMatchScore,\n        autoApplyEnabled,\n        salaryMin,\n        salaryMax,\n        experienceLevelMin,\n        experienceLevelMax,\n        jobTypes: jobTypes || [],\n        remotePreference,\n        companySizePreference: companySizePreference || [],\n        excludeCompanies: excludeCompanies || [],\n        preferredCompanies: preferredCompanies || [],\n        specifications: specifications || {\n          advancedFiltering: true,\n          profileMatchingEnabled: true,\n          intelligentScoring: true\n        },\n        status: \"pending\",\n        progress: 0\n      },\n      include: {\n        profile: {\n          include: {\n            data: true\n          }\n        }\n      }\n    });\n    try {\n      try {\n        const redisUrl = process.env.NODE_ENV === \"production\" ? \"rediss://red-cvmu1me3jp1c738ve7ig:<EMAIL>:6379\" : \"redis://localhost:6379\";\n        const redis = createClient({\n          url: redisUrl\n        });\n        await redis.connect();\n        await redis.xadd(\n          \"automation::stream\",\n          \"*\",\n          \"job\",\n          JSON.stringify({\n            runId: automationRun.id,\n            profileId: profile.id,\n            userId: user.id,\n            keywords: keywords || \"\",\n            location: location || \"\",\n            maxJobsToApply,\n            minMatchScore,\n            autoApplyEnabled,\n            salaryMin,\n            salaryMax,\n            experienceLevelMin,\n            experienceLevelMax,\n            jobTypes,\n            remotePreference,\n            companySizePreference,\n            excludeCompanies,\n            preferredCompanies,\n            specifications,\n            resumeId: null,\n            // We'll handle resume selection in the worker\n            timestamp: (/* @__PURE__ */ new Date()).toISOString()\n          })\n        );\n        console.log(`[automation] Added job to automation::stream for run ${automationRun.id}`);\n        await redis.publish(\n          \"automation::status\",\n          JSON.stringify({\n            runId: automationRun.id,\n            status: \"pending\",\n            timestamp: (/* @__PURE__ */ new Date()).toISOString()\n          })\n        );\n        try {\n          const { createNotification } = await import(\"../../../../../chunks/notifications.js\");\n          await createNotification({\n            userId: user.id,\n            title: \"Automation Run Started\",\n            message: `Your automation run has been started and is now processing.`,\n            url: `/dashboard/automation/${automationRun.id}`,\n            type: \"info\",\n            data: {\n              automationRunId: automationRun.id,\n              status: \"started\"\n            }\n          });\n          console.log(`[automation] Sent start notification to user ${user.id}`);\n        } catch (notificationError) {\n          console.error(\"Error sending start notification:\", notificationError);\n        }\n        await redis.disconnect();\n        console.log(\"Sent job search request to Redis\");\n      } catch (redisError) {\n        console.error(\"Redis error, falling back to mock worker:\", redisError);\n        try {\n          const response = await fetch(\"http://localhost:3001/trigger\", {\n            method: \"POST\",\n            headers: {\n              \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n              runId: automationRun.id,\n              profileId: profile.id,\n              userId: user.id,\n              keywords: keywords || \"\",\n              location: location || \"\",\n              maxJobsToApply,\n              minMatchScore,\n              autoApplyEnabled,\n              salaryMin,\n              salaryMax,\n              experienceLevelMin,\n              experienceLevelMax,\n              jobTypes,\n              remotePreference,\n              companySizePreference,\n              excludeCompanies,\n              preferredCompanies,\n              specifications,\n              resumeId: null\n              // We'll handle resume selection in the worker\n            })\n          });\n          if (!response.ok) {\n            throw new Error(`Mock worker returned ${response.status}: ${response.statusText}`);\n          }\n          console.log(\"Sent job search request to mock worker\");\n        } catch (mockError) {\n          console.error(\"Mock worker error:\", mockError);\n        }\n      }\n    } catch (redisError) {\n      console.error(\"Redis error:\", redisError);\n    }\n    return json(automationRun);\n  } catch (error) {\n    console.error(\"Error creating automation run:\", error);\n    const errorMessage = error instanceof Error ? error.message : \"Unknown error\";\n    const errorStack = error instanceof Error ? error.stack : \"\";\n    console.error(\"Error details:\", { message: errorMessage, stack: errorStack });\n    return json(\n      {\n        error: \"Failed to create automation run\",\n        details: errorMessage\n      },\n      { status: 500 }\n    );\n  }\n};\nconst GET = async ({ cookies }) => {\n  const userPromise = getUserFromToken(cookies);\n  const user = await userPromise;\n  if (!user) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  try {\n    const automationRuns = await prisma.automationRun.findMany({\n      where: {\n        OR: [\n          { userId: user.id },\n          {\n            profile: {\n              team: {\n                members: {\n                  some: { userId: user.id }\n                }\n              }\n            }\n          }\n        ]\n      },\n      include: {\n        profile: {\n          include: {\n            data: true,\n            documents: {\n              where: {\n                type: \"resume\"\n              }\n            }\n          }\n        }\n      },\n      orderBy: {\n        createdAt: \"desc\"\n      }\n    });\n    return json(automationRuns);\n  } catch (error) {\n    console.error(\"Error fetching automation runs:\", error);\n    return json({ error: \"Failed to fetch automation runs\" }, { status: 500 });\n  }\n};\nexport {\n  GET,\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAIK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AAC7C,EAAE,MAAM,WAAW,GAAG,gBAAgB,CAAC,OAAO,CAAC;AAC/C,EAAE,MAAM,IAAI,GAAG,MAAM,WAAW;AAChC,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,IAAI;AACN,IAAI,MAAM;AACV,MAAM,SAAS;AACf,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,cAAc,GAAG,EAAE;AACzB,MAAM,aAAa,GAAG,EAAE;AACxB,MAAM,gBAAgB,GAAG,KAAK;AAC9B,MAAM,SAAS;AACf,MAAM,SAAS;AACf,MAAM,kBAAkB;AACxB,MAAM,kBAAkB;AACxB,MAAM,QAAQ,GAAG,EAAE;AACnB,MAAM,gBAAgB,GAAG,KAAK;AAC9B,MAAM,qBAAqB,GAAG,EAAE;AAChC,MAAM,gBAAgB,GAAG,EAAE;AAC3B,MAAM,kBAAkB,GAAG,EAAE;AAC7B,MAAM,cAAc,GAAG;AACvB,KAAK,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AAC5B,IAAI,IAAI,CAAC,SAAS,EAAE;AACpB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,wBAAwB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACvE;AACA,IAAI,IAAI,OAAO;AACf,IAAI,IAAI;AACR,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;AAC/C,QAAQ,KAAK,EAAE;AACf,UAAU,EAAE,EAAE,SAAS;AACvB,UAAU,EAAE,EAAE;AACd,YAAY,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;AAC/B,YAAY;AACZ,cAAc,IAAI,EAAE;AACpB,gBAAgB,OAAO,EAAE;AACzB,kBAAkB,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE;AACzC;AACA;AACA;AACA;AACA,SAAS;AACT,QAAQ,OAAO,EAAE;AACjB,UAAU,IAAI,EAAE,IAAI;AACpB,UAAU,SAAS,EAAE;AACrB,YAAY,OAAO,EAAE;AACrB,cAAc,QAAQ,EAAE;AACxB;AACA;AACA;AACA,OAAO,CAAC;AACR,KAAK,CAAC,OAAO,YAAY,EAAE;AAC3B,MAAM,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,YAAY,CAAC;AAC3D,MAAM,MAAM,YAAY,GAAG,YAAY,YAAY,KAAK,GAAG,YAAY,CAAC,OAAO,GAAG,eAAe;AACjG,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,OAAO,EAAE,YAAY,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7F;AACA,IAAI,IAAI,CAAC,OAAO,EAAE;AAClB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE;AACA,IAAI,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;AAC5D,MAAM,IAAI,EAAE;AACZ,QAAQ,MAAM,EAAE,IAAI,CAAC,EAAE;AACvB,QAAQ,SAAS;AACjB,QAAQ,QAAQ,EAAE,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,QAAQ,IAAI,EAAE;AAChF,QAAQ,QAAQ,EAAE,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,QAAQ,IAAI,EAAE;AAChF,QAAQ,cAAc;AACtB,QAAQ,aAAa;AACrB,QAAQ,gBAAgB;AACxB,QAAQ,SAAS;AACjB,QAAQ,SAAS;AACjB,QAAQ,kBAAkB;AAC1B,QAAQ,kBAAkB;AAC1B,QAAQ,QAAQ,EAAE,QAAQ,IAAI,EAAE;AAChC,QAAQ,gBAAgB;AACxB,QAAQ,qBAAqB,EAAE,qBAAqB,IAAI,EAAE;AAC1D,QAAQ,gBAAgB,EAAE,gBAAgB,IAAI,EAAE;AAChD,QAAQ,kBAAkB,EAAE,kBAAkB,IAAI,EAAE;AACpD,QAAQ,cAAc,EAAE,cAAc,IAAI;AAC1C,UAAU,iBAAiB,EAAE,IAAI;AACjC,UAAU,sBAAsB,EAAE,IAAI;AACtC,UAAU,kBAAkB,EAAE;AAC9B,SAAS;AACT,QAAQ,MAAM,EAAE,SAAS;AACzB,QAAQ,QAAQ,EAAE;AAClB,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,OAAO,EAAE;AACjB,UAAU,OAAO,EAAE;AACnB,YAAY,IAAI,EAAE;AAClB;AACA;AACA;AACA,KAAK,CAAC;AACN,IAAI,IAAI;AACR,MAAM,IAAI;AACV,QAAQ,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,GAAG,oGAAoG,GAAG,wBAAwB;AAChM,QAAQ,MAAM,KAAK,GAAG,YAAY,CAAC;AACnC,UAAU,GAAG,EAAE;AACf,SAAS,CAAC;AACV,QAAQ,MAAM,KAAK,CAAC,OAAO,EAAE;AAC7B,QAAQ,MAAM,KAAK,CAAC,IAAI;AACxB,UAAU,oBAAoB;AAC9B,UAAU,GAAG;AACb,UAAU,KAAK;AACf,UAAU,IAAI,CAAC,SAAS,CAAC;AACzB,YAAY,KAAK,EAAE,aAAa,CAAC,EAAE;AACnC,YAAY,SAAS,EAAE,OAAO,CAAC,EAAE;AACjC,YAAY,MAAM,EAAE,IAAI,CAAC,EAAE;AAC3B,YAAY,QAAQ,EAAE,QAAQ,IAAI,EAAE;AACpC,YAAY,QAAQ,EAAE,QAAQ,IAAI,EAAE;AACpC,YAAY,cAAc;AAC1B,YAAY,aAAa;AACzB,YAAY,gBAAgB;AAC5B,YAAY,SAAS;AACrB,YAAY,SAAS;AACrB,YAAY,kBAAkB;AAC9B,YAAY,kBAAkB;AAC9B,YAAY,QAAQ;AACpB,YAAY,gBAAgB;AAC5B,YAAY,qBAAqB;AACjC,YAAY,gBAAgB;AAC5B,YAAY,kBAAkB;AAC9B,YAAY,cAAc;AAC1B,YAAY,QAAQ,EAAE,IAAI;AAC1B;AACA,YAAY,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC/D,WAAW;AACX,SAAS;AACT,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,qDAAqD,EAAE,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC;AAC/F,QAAQ,MAAM,KAAK,CAAC,OAAO;AAC3B,UAAU,oBAAoB;AAC9B,UAAU,IAAI,CAAC,SAAS,CAAC;AACzB,YAAY,KAAK,EAAE,aAAa,CAAC,EAAE;AACnC,YAAY,MAAM,EAAE,SAAS;AAC7B,YAAY,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC/D,WAAW;AACX,SAAS;AACT,QAAQ,IAAI;AACZ,UAAU,MAAM,EAAE,kBAAkB,EAAE,GAAG,MAAM,OAAO,6BAAwC,CAAC;AAC/F,UAAU,MAAM,kBAAkB,CAAC;AACnC,YAAY,MAAM,EAAE,IAAI,CAAC,EAAE;AAC3B,YAAY,KAAK,EAAE,wBAAwB;AAC3C,YAAY,OAAO,EAAE,CAAC,2DAA2D,CAAC;AAClF,YAAY,GAAG,EAAE,CAAC,sBAAsB,EAAE,aAAa,CAAC,EAAE,CAAC,CAAC;AAC5D,YAAY,IAAI,EAAE,MAAM;AACxB,YAAY,IAAI,EAAE;AAClB,cAAc,eAAe,EAAE,aAAa,CAAC,EAAE;AAC/C,cAAc,MAAM,EAAE;AACtB;AACA,WAAW,CAAC;AACZ,UAAU,OAAO,CAAC,GAAG,CAAC,CAAC,6CAA6C,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AAChF,SAAS,CAAC,OAAO,iBAAiB,EAAE;AACpC,UAAU,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,iBAAiB,CAAC;AAC/E;AACA,QAAQ,MAAM,KAAK,CAAC,UAAU,EAAE;AAChC,QAAQ,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC;AACvD,OAAO,CAAC,OAAO,UAAU,EAAE;AAC3B,QAAQ,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,UAAU,CAAC;AAC9E,QAAQ,IAAI;AACZ,UAAU,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,+BAA+B,EAAE;AACxE,YAAY,MAAM,EAAE,MAAM;AAC1B,YAAY,OAAO,EAAE;AACrB,cAAc,cAAc,EAAE;AAC9B,aAAa;AACb,YAAY,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;AACjC,cAAc,KAAK,EAAE,aAAa,CAAC,EAAE;AACrC,cAAc,SAAS,EAAE,OAAO,CAAC,EAAE;AACnC,cAAc,MAAM,EAAE,IAAI,CAAC,EAAE;AAC7B,cAAc,QAAQ,EAAE,QAAQ,IAAI,EAAE;AACtC,cAAc,QAAQ,EAAE,QAAQ,IAAI,EAAE;AACtC,cAAc,cAAc;AAC5B,cAAc,aAAa;AAC3B,cAAc,gBAAgB;AAC9B,cAAc,SAAS;AACvB,cAAc,SAAS;AACvB,cAAc,kBAAkB;AAChC,cAAc,kBAAkB;AAChC,cAAc,QAAQ;AACtB,cAAc,gBAAgB;AAC9B,cAAc,qBAAqB;AACnC,cAAc,gBAAgB;AAC9B,cAAc,kBAAkB;AAChC,cAAc,cAAc;AAC5B,cAAc,QAAQ,EAAE;AACxB;AACA,aAAa;AACb,WAAW,CAAC;AACZ,UAAU,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;AAC5B,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,qBAAqB,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;AAC9F;AACA,UAAU,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC;AAC/D,SAAS,CAAC,OAAO,SAAS,EAAE;AAC5B,UAAU,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,SAAS,CAAC;AACxD;AACA;AACA,KAAK,CAAC,OAAO,UAAU,EAAE;AACzB,MAAM,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,UAAU,CAAC;AAC/C;AACA,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC;AAC9B,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC;AAC1D,IAAI,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,eAAe;AACjF,IAAI,MAAM,UAAU,GAAG,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,EAAE;AAChE,IAAI,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC;AACjF,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,KAAK,EAAE,iCAAiC;AAChD,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;AACK,MAAC,GAAG,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK;AACnC,EAAE,MAAM,WAAW,GAAG,gBAAgB,CAAC,OAAO,CAAC;AAC/C,EAAE,MAAM,IAAI,GAAG,MAAM,WAAW;AAChC,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;AAC/D,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE;AACZ,UAAU,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;AAC7B,UAAU;AACV,YAAY,OAAO,EAAE;AACrB,cAAc,IAAI,EAAE;AACpB,gBAAgB,OAAO,EAAE;AACzB,kBAAkB,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE;AACzC;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,OAAO,EAAE;AACjB,UAAU,OAAO,EAAE;AACnB,YAAY,IAAI,EAAE,IAAI;AACtB,YAAY,SAAS,EAAE;AACvB,cAAc,KAAK,EAAE;AACrB,gBAAgB,IAAI,EAAE;AACtB;AACA;AACA;AACA;AACA,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,SAAS,EAAE;AACnB;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,cAAc,CAAC;AAC/B,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC;AAC3D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,iCAAiC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9E;AACA;;;;"}