// Cloudflare Worker to proxy R2 static assets with proper CORS and caching
// Deploy this to a custom domain like static.yourdomain.com

export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);

    // Handle CORS preflight requests
    if (request.method === "OPTIONS") {
      return new Response(null, {
        status: 200,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "GET, HEAD, OPTIONS",
          "Access-Control-Allow-Headers": "*",
          "Access-Control-Max-Age": "86400",
        },
      });
    }

    // Only handle GET and HEAD requests
    if (!["GET", "HEAD"].includes(request.method)) {
      return new Response("Method not allowed", { status: 405 });
    }

    // Extract the file path (remove leading slash)
    const filePath = url.pathname.substring(1);

    // If no file path, return 404
    if (!filePath) {
      return new Response("Not found", { status: 404 });
    }

    try {
      // Determine which bucket based on path prefix
      let bucketBinding;
      let objectKey;

      if (filePath.startsWith("logos/")) {
        // Company logos: /logos/company-name-logo-optimized.webp
        bucketBinding = env.COMPANY_LOGOS_BUCKET;
        objectKey = filePath.replace("logos/", "");
      } else if (filePath.startsWith("resumes/")) {
        // Resumes: /resumes/profile-123-resume-456.pdf
        bucketBinding = env.RESUMES_BUCKET;
        objectKey = filePath.replace("resumes/", "");
      } else if (filePath.startsWith("user/")) {
        // User files: /user/profile-123-document.pdf
        bucketBinding = env.USER_BUCKET;
        objectKey = filePath.replace("user/", "");
      } else {
        // Default to company logos bucket for backward compatibility
        bucketBinding = env.COMPANY_LOGOS_BUCKET;
        objectKey = filePath;
      }

      // Get object from R2
      const object = await bucketBinding.get(objectKey);

      if (!object) {
        return new Response("File not found", { status: 404 });
      }

      // Determine content type
      const contentType =
        object.httpMetadata?.contentType || getContentType(objectKey);

      // Create response headers with proper CORS and caching
      const headers = new Headers();
      headers.set("Content-Type", contentType);
      headers.set("Access-Control-Allow-Origin", "*");
      headers.set("Access-Control-Allow-Methods", "GET, HEAD");
      headers.set("Access-Control-Allow-Headers", "*");
      headers.set("Access-Control-Max-Age", "86400"); // 24 hours

      // Cache static assets for 1 year (they have unique names)
      headers.set("Cache-Control", "public, max-age=31536000, immutable");
      headers.set("ETag", object.httpEtag);

      // Add security headers
      headers.set("X-Content-Type-Options", "nosniff");
      headers.set("Referrer-Policy", "strict-origin-when-cross-origin");

      // Handle conditional requests
      const ifNoneMatch = request.headers.get("If-None-Match");
      if (ifNoneMatch && ifNoneMatch === object.httpEtag) {
        return new Response(null, { status: 304, headers });
      }

      // Return the file
      return new Response(object.body, {
        status: 200,
        headers,
      });
    } catch (error) {
      console.error("Error serving static asset:", error);
      return new Response("Internal server error", { status: 500 });
    }
  },
};

// Helper function to determine content type from file extension
function getContentType(filename) {
  const ext = filename.split(".").pop()?.toLowerCase();

  const mimeTypes = {
    webp: "image/webp",
    png: "image/png",
    jpg: "image/jpeg",
    jpeg: "image/jpeg",
    gif: "image/gif",
    svg: "image/svg+xml",
    pdf: "application/pdf",
    doc: "application/msword",
    docx: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    txt: "text/plain",
    json: "application/json",
    css: "text/css",
    js: "application/javascript",
    html: "text/html",
  };

  return mimeTypes[ext] || "application/octet-stream";
}
