{"version": 3, "file": "_server.ts-BLm9eUkV.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/billing/create-portal-session/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../../../chunks/auth.js\";\nconst POST = async ({ cookies, url, request }) => {\n  const token = cookies.get(\"auth_token\");\n  const isProd = process.env.NODE_ENV === \"production\";\n  if (!token) return new Response(\"Unauthorized\", { status: 401 });\n  const userData = await verifySessionToken(token);\n  if (!userData?.id) return new Response(\"Unauthorized\", { status: 401 });\n  const stripeSecret = isProd ? process.env.STRIPE_SECRET_KEY_LIVE || \"sk_live_placeholder\" : process.env.STRIPE_SECRET_KEY_TEST || \"sk_test_placeholder\";\n  const Stripe = (await import(\"stripe\")).default;\n  const stripe = new Stripe(stripeSecret, {\n    apiVersion: \"2025-04-30.basil\"\n  });\n  let user = await prisma.user.findUnique({ where: { id: userData.id } });\n  if (!user) return new Response(\"User not found\", { status: 404 });\n  if (!user.stripeCustomerId) {\n    return new Response(\"No subscription found\", { status: 404 });\n  }\n  const returnUrl = `${url.protocol}//${url.host}/dashboard/settings/billing`;\n  try {\n    const keyPrefix = stripeSecret.substring(0, 4);\n    console.log(\n      `Using Stripe API key with prefix: ${keyPrefix}... (${isProd ? \"LIVE\" : \"TEST\"} mode)`\n    );\n    console.log(`Creating portal session for customer: ${user.stripeCustomerId}`);\n    const session = await stripe.billingPortal.sessions.create({\n      customer: user.stripeCustomerId,\n      return_url: returnUrl\n    });\n    return json({ url: session.url });\n  } catch (error) {\n    console.error(\"Error creating portal session:\", error);\n    const errorMessage = error.message || \"Failed to create portal session\";\n    const errorType = error.type || \"unknown_error\";\n    if (error.raw) {\n      console.error(\"Stripe error details:\", error.raw);\n    }\n    return json(\n      {\n        error: errorMessage,\n        details: errorType,\n        code: error.code || \"unknown_code\"\n      },\n      { status: 500 }\n    );\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;AAGK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK;AAClD,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;AACtD,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE,EAAE,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AAClD,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE,EAAE,MAAM,YAAY,GAAG,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,qBAAqB,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,qBAAqB;AACzJ,EAAE,MAAM,MAAM,GAAG,CAAC,MAAM,OAAO,+BAAQ,CAAC,EAAE,OAAO;AACjD,EAAE,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,YAAY,EAAE;AAC1C,IAAI,UAAU,EAAE;AAChB,GAAG,CAAC;AACJ,EAAE,IAAI,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE,EAAE,CAAC;AACzE,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,QAAQ,CAAC,gBAAgB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnE,EAAE,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;AAC9B,IAAI,OAAO,IAAI,QAAQ,CAAC,uBAAuB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE;AACA,EAAE,MAAM,SAAS,GAAG,CAAC,EAAE,GAAG,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,2BAA2B,CAAC;AAC7E,EAAE,IAAI;AACN,IAAI,MAAM,SAAS,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;AAClD,IAAI,OAAO,CAAC,GAAG;AACf,MAAM,CAAC,kCAAkC,EAAE,SAAS,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC,MAAM;AAC3F,KAAK;AACL,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,sCAAsC,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;AACjF,IAAI,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC;AAC/D,MAAM,QAAQ,EAAE,IAAI,CAAC,gBAAgB;AACrC,MAAM,UAAU,EAAE;AAClB,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,EAAE,GAAG,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC;AACrC,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC;AAC1D,IAAI,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,IAAI,iCAAiC;AAC3E,IAAI,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,IAAI,eAAe;AACnD,IAAI,IAAI,KAAK,CAAC,GAAG,EAAE;AACnB,MAAM,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,GAAG,CAAC;AACvD;AACA,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,KAAK,EAAE,YAAY;AAC3B,QAAQ,OAAO,EAAE,SAAS;AAC1B,QAAQ,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI;AAC5B,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;;;;"}