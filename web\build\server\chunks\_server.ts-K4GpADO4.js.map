{"version": 3, "file": "_server.ts-K4GpADO4.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/auth/signup/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport * as bcrypt from \"bcryptjs\";\nimport { randomBytes } from \"crypto\";\nimport { R as RedisConnection } from \"../../../../../chunks/redis.js\";\nimport { createNotification, NotificationPriority, NotificationType } from \"../../../../../chunks/notifications.js\";\nconst BASE_URL = process.env.PUBLIC_BASE_URL || \"http://localhost:5173\";\nconst TEST_EMAIL = \"<EMAIL>\";\nconst EMAIL_QUEUE_KEY = \"email:queue\";\nconst POST = async ({ request, cookies }) => {\n  const { email, password, confirmPassword, termsAccepted, referralCode } = await request.json();\n  if (!email || !password || !confirmPassword || !termsAccepted) {\n    return json({ message: \"All fields are required.\" }, { status: 400 });\n  }\n  if (password !== confirmPassword) {\n    return json({ message: \"Passwords do not match.\" }, { status: 400 });\n  }\n  const existingUser = await prisma.user.findUnique({\n    where: { email }\n  });\n  if (existingUser) {\n    return json({ message: \"Email already in use.\" }, { status: 400 });\n  }\n  const passwordHash = await bcrypt.hash(password, 10);\n  let referrerId = null;\n  if (referralCode?.trim()) {\n    const referrer = await prisma.user.findUnique({\n      where: { referralCode: referralCode.trim().toUpperCase() }\n    });\n    if (!referrer) {\n      return json({ message: \"Invalid referral code.\" }, { status: 400 });\n    }\n    referrerId = referrer.id;\n  }\n  const verificationToken = randomBytes(32).toString(\"hex\");\n  const verificationExpires = new Date(Date.now() + 1e3 * 60 * 60 * 24);\n  const newUser = await prisma.user.create({\n    data: {\n      email,\n      passwordHash,\n      // Save the hashed password\n      emailVerified: false,\n      verificationToken,\n      verificationExpires,\n      referredById: referrerId,\n      // Also store verification data in preferences JSON for backward compatibility\n      preferences: {\n        emailVerified: false,\n        verificationToken,\n        verificationExpires: verificationExpires.toISOString()\n      }\n    }\n  });\n  if (referrerId && referralCode) {\n    await prisma.referral.create({\n      data: {\n        referrerId,\n        referredId: newUser.id,\n        referralCode: referralCode.toUpperCase(),\n        status: \"pending\"\n      }\n    });\n    try {\n      await createNotification({\n        userId: referrerId,\n        title: \"🎉 New Referral!\",\n        message: `Someone just signed up using your referral code ${referralCode.toUpperCase()}! They'll need to verify their email to complete the referral.`,\n        type: NotificationType.REFERRAL,\n        priority: NotificationPriority.MEDIUM,\n        url: \"/dashboard/settings/referrals\",\n        data: {\n          referralCode: referralCode.toUpperCase(),\n          referredEmail: email,\n          referredId: newUser.id,\n          status: \"pending\"\n        }\n      });\n      console.log(`Sent referral notification to user ${referrerId} for new signup: ${email}`);\n    } catch (notificationError) {\n      console.error(\"Failed to send referral notification:\", notificationError);\n    }\n  }\n  console.log(`User created with verification token: ${verificationToken}`);\n  console.log(`Verification URL: ${BASE_URL}/auth/verify?token=${verificationToken}`);\n  const verificationUrl = `${BASE_URL}/auth/verify?token=${verificationToken}`;\n  console.log(`Verification URL for ${email}: ${verificationUrl}`);\n  try {\n    console.log(\"RedisConnection:\", RedisConnection ? \"Available\" : \"Not available\");\n    if (RedisConnection) {\n      console.log(\"RedisConnection status:\", RedisConnection.status);\n    }\n    if (!RedisConnection) {\n      console.error(\"Redis client not available\");\n    } else {\n      const id = `email_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;\n      const emailJob = {\n        id,\n        template: \"verification\",\n        to: [email],\n        data: {\n          firstName: email.split(\"@\")[0],\n          verificationUrl,\n          expiresInMinutes: 60 * 24\n          // 24 hours\n        },\n        options: {\n          category: \"transactional\",\n          priority: 1,\n          // Highest priority\n          retries: 3,\n          tags: [{ name: \"action\", value: \"verification\" }]\n        },\n        createdAt: (/* @__PURE__ */ new Date()).toISOString()\n      };\n      console.log(\"Adding email job to Redis queue:\", EMAIL_QUEUE_KEY);\n      await RedisConnection.zadd(\n        EMAIL_QUEUE_KEY,\n        1,\n        // Highest priority\n        JSON.stringify(emailJob)\n      );\n      console.log(`Verification email queued in Redis: ${id} (to ${email})`);\n      const SEND_TEST_COPIES = process.env.SEND_TEST_EMAIL_COPIES === \"true\";\n      if (SEND_TEST_COPIES && email.toLowerCase() !== TEST_EMAIL.toLowerCase()) {\n        const testEmailId = `email_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;\n        const testEmailJob = {\n          id: testEmailId,\n          template: \"verification\",\n          to: [TEST_EMAIL],\n          data: {\n            firstName: \"Test Copy\",\n            verificationUrl,\n            expiresInMinutes: 60 * 24\n            // 24 hours\n          },\n          options: {\n            category: \"transactional\",\n            priority: 1,\n            // Highest priority\n            retries: 3,\n            tags: [\n              { name: \"action\", value: \"verification\" },\n              { name: \"test\", value: \"true\" }\n            ]\n          },\n          createdAt: (/* @__PURE__ */ new Date()).toISOString()\n        };\n        await RedisConnection.zadd(\n          EMAIL_QUEUE_KEY,\n          1,\n          // Highest priority\n          JSON.stringify(testEmailJob)\n        );\n        console.log(`Test verification email queued in Redis: ${testEmailId} (to ${TEST_EMAIL})`);\n      }\n      await RedisConnection.publish(\"email:process\", \"process_now\");\n      console.log(\"Published process_now message to email:process channel\");\n    }\n  } catch (error) {\n    console.error(\"Failed to queue verification email:\", error);\n  }\n  return json(\n    {\n      message: \"Account created successfully! Please check your email to verify your account.\",\n      verified: false,\n      // Include the verification token for testing purposes\n      verificationToken,\n      verificationUrl: `${BASE_URL}/auth/verify?token=${verificationToken}`\n    },\n    { status: 201 }\n  );\n};\nexport {\n  POST\n};\n"], "names": ["bcrypt"], "mappings": ";;;;;;;;;AAMA,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,uBAAuB;AACvE,MAAM,UAAU,GAAG,wCAAwC;AAC3D,MAAM,eAAe,GAAG,aAAa;AAChC,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AAC7C,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,eAAe,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AAChG,EAAE,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,IAAI,CAAC,eAAe,IAAI,CAAC,aAAa,EAAE;AACjE,IAAI,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,0BAA0B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE;AACA,EAAE,IAAI,QAAQ,KAAK,eAAe,EAAE;AACpC,IAAI,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,yBAAyB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA,EAAE,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AACpD,IAAI,KAAK,EAAE,EAAE,KAAK;AAClB,GAAG,CAAC;AACJ,EAAE,IAAI,YAAY,EAAE;AACpB,IAAI,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,uBAAuB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtE;AACA,EAAE,MAAM,YAAY,GAAG,MAAMA,eAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;AACtD,EAAE,IAAI,UAAU,GAAG,IAAI;AACvB,EAAE,IAAI,YAAY,EAAE,IAAI,EAAE,EAAE;AAC5B,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAClD,MAAM,KAAK,EAAE,EAAE,YAAY,EAAE,YAAY,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE;AAC9D,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,wBAAwB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE;AACA,IAAI,UAAU,GAAG,QAAQ,CAAC,EAAE;AAC5B;AACA,EAAE,MAAM,iBAAiB,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC;AAC3D,EAAE,MAAM,mBAAmB,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACvE,EAAE,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AAC3C,IAAI,IAAI,EAAE;AACV,MAAM,KAAK;AACX,MAAM,YAAY;AAClB;AACA,MAAM,aAAa,EAAE,KAAK;AAC1B,MAAM,iBAAiB;AACvB,MAAM,mBAAmB;AACzB,MAAM,YAAY,EAAE,UAAU;AAC9B;AACA,MAAM,WAAW,EAAE;AACnB,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,iBAAiB;AACzB,QAAQ,mBAAmB,EAAE,mBAAmB,CAAC,WAAW;AAC5D;AACA;AACA,GAAG,CAAC;AACJ,EAAE,IAAI,UAAU,IAAI,YAAY,EAAE;AAClC,IAAI,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;AACjC,MAAM,IAAI,EAAE;AACZ,QAAQ,UAAU;AAClB,QAAQ,UAAU,EAAE,OAAO,CAAC,EAAE;AAC9B,QAAQ,YAAY,EAAE,YAAY,CAAC,WAAW,EAAE;AAChD,QAAQ,MAAM,EAAE;AAChB;AACA,KAAK,CAAC;AACN,IAAI,IAAI;AACR,MAAM,MAAM,kBAAkB,CAAC;AAC/B,QAAQ,MAAM,EAAE,UAAU;AAC1B,QAAQ,KAAK,EAAE,kBAAkB;AACjC,QAAQ,OAAO,EAAE,CAAC,gDAAgD,EAAE,YAAY,CAAC,WAAW,EAAE,CAAC,8DAA8D,CAAC;AAC9J,QAAQ,IAAI,EAAE,gBAAgB,CAAC,QAAQ;AACvC,QAAQ,QAAQ,EAAE,oBAAoB,CAAC,MAAM;AAC7C,QAAQ,GAAG,EAAE,+BAA+B;AAC5C,QAAQ,IAAI,EAAE;AACd,UAAU,YAAY,EAAE,YAAY,CAAC,WAAW,EAAE;AAClD,UAAU,aAAa,EAAE,KAAK;AAC9B,UAAU,UAAU,EAAE,OAAO,CAAC,EAAE;AAChC,UAAU,MAAM,EAAE;AAClB;AACA,OAAO,CAAC;AACR,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,mCAAmC,EAAE,UAAU,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC,CAAC;AAC9F,KAAK,CAAC,OAAO,iBAAiB,EAAE;AAChC,MAAM,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,iBAAiB,CAAC;AAC/E;AACA;AACA,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,sCAAsC,EAAE,iBAAiB,CAAC,CAAC,CAAC;AAC3E,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,QAAQ,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,CAAC,CAAC;AACrF,EAAE,MAAM,eAAe,GAAG,CAAC,EAAE,QAAQ,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,CAAC;AAC9E,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,KAAK,CAAC,EAAE,EAAE,eAAe,CAAC,CAAC,CAAC;AAClE,EAAE,IAAI;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,eAAe,GAAG,WAAW,GAAG,eAAe,CAAC;AACpF,IAAI,IAAI,eAAe,EAAE;AACzB,MAAM,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,eAAe,CAAC,MAAM,CAAC;AACpE;AACA,IAAI,IAAI,CAAC,eAAe,EAAE;AAC1B,MAAM,OAAO,CAAC,KAAK,CAAC,4BAA4B,CAAC;AACjD,KAAK,MAAM;AACX,MAAM,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AACrF,MAAM,MAAM,QAAQ,GAAG;AACvB,QAAQ,EAAE;AACV,QAAQ,QAAQ,EAAE,cAAc;AAChC,QAAQ,EAAE,EAAE,CAAC,KAAK,CAAC;AACnB,QAAQ,IAAI,EAAE;AACd,UAAU,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACxC,UAAU,eAAe;AACzB,UAAU,gBAAgB,EAAE,EAAE,GAAG;AACjC;AACA,SAAS;AACT,QAAQ,OAAO,EAAE;AACjB,UAAU,QAAQ,EAAE,eAAe;AACnC,UAAU,QAAQ,EAAE,CAAC;AACrB;AACA,UAAU,OAAO,EAAE,CAAC;AACpB,UAAU,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,cAAc,EAAE;AAC1D,SAAS;AACT,QAAQ,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC3D,OAAO;AACP,MAAM,OAAO,CAAC,GAAG,CAAC,kCAAkC,EAAE,eAAe,CAAC;AACtE,MAAM,MAAM,eAAe,CAAC,IAAI;AAChC,QAAQ,eAAe;AACvB,QAAQ,CAAC;AACT;AACA,QAAQ,IAAI,CAAC,SAAS,CAAC,QAAQ;AAC/B,OAAO;AACP,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,oCAAoC,EAAE,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;AAC5E,MAAM,MAAM,gBAAgB,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,KAAK,MAAM;AAC5E,MAAM,IAAI,gBAAgB,IAAI,KAAK,CAAC,WAAW,EAAE,KAAK,UAAU,CAAC,WAAW,EAAE,EAAE;AAChF,QAAQ,MAAM,WAAW,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AAChG,QAAQ,MAAM,YAAY,GAAG;AAC7B,UAAU,EAAE,EAAE,WAAW;AACzB,UAAU,QAAQ,EAAE,cAAc;AAClC,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC;AAC1B,UAAU,IAAI,EAAE;AAChB,YAAY,SAAS,EAAE,WAAW;AAClC,YAAY,eAAe;AAC3B,YAAY,gBAAgB,EAAE,EAAE,GAAG;AACnC;AACA,WAAW;AACX,UAAU,OAAO,EAAE;AACnB,YAAY,QAAQ,EAAE,eAAe;AACrC,YAAY,QAAQ,EAAE,CAAC;AACvB;AACA,YAAY,OAAO,EAAE,CAAC;AACtB,YAAY,IAAI,EAAE;AAClB,cAAc,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,cAAc,EAAE;AACvD,cAAc,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM;AAC3C;AACA,WAAW;AACX,UAAU,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC7D,SAAS;AACT,QAAQ,MAAM,eAAe,CAAC,IAAI;AAClC,UAAU,eAAe;AACzB,UAAU,CAAC;AACX;AACA,UAAU,IAAI,CAAC,SAAS,CAAC,YAAY;AACrC,SAAS;AACT,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,yCAAyC,EAAE,WAAW,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;AACjG;AACA,MAAM,MAAM,eAAe,CAAC,OAAO,CAAC,eAAe,EAAE,aAAa,CAAC;AACnE,MAAM,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC;AAC3E;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC;AAC/D;AACA,EAAE,OAAO,IAAI;AACb,IAAI;AACJ,MAAM,OAAO,EAAE,+EAA+E;AAC9F,MAAM,QAAQ,EAAE,KAAK;AACrB;AACA,MAAM,iBAAiB;AACvB,MAAM,eAAe,EAAE,CAAC,EAAE,QAAQ,CAAC,mBAAmB,EAAE,iBAAiB,CAAC;AAC1E,KAAK;AACL,IAAI,EAAE,MAAM,EAAE,GAAG;AACjB,GAAG;AACH;;;;"}