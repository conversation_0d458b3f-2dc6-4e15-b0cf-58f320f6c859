{"version": 3, "file": "_server.ts-DDpd7SIj.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/resume/_id_/_server.ts.js"], "sourcesContent": ["import { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { j as json } from \"../../../../../chunks/index.js\";\nconst GET = async ({ params, locals }) => {\n  try {\n    console.log(\"GET resume with ID:\", params.id);\n    const resume = await prisma.resume.findUnique({\n      where: { id: params.id },\n      include: {\n        document: {\n          include: {\n            profile: true\n          }\n        },\n        jobSearches: true,\n        optimization: true\n      }\n    });\n    console.log(\"Resume found:\", resume ? \"Yes\" : \"No\");\n    if (!resume) {\n      return json({ error: \"Resume not found\" }, { status: 404 });\n    }\n    return json(resume);\n  } catch (error) {\n    console.error(\"Error fetching resume:\", error);\n    return json({ error: \"Failed to fetch resume\", details: String(error) }, { status: 500 });\n  }\n};\nconst PATCH = async ({ params, request, locals }) => {\n  try {\n    console.log(\"PATCH resume with ID:\", params.id);\n    const data = await request.json();\n    console.log(\"Update data:\", data);\n    const existingResume = await prisma.resume.findUnique({\n      where: { id: params.id },\n      include: {\n        document: true\n      }\n    });\n    if (!existingResume) {\n      return json({ error: \"Resume not found\" }, { status: 404 });\n    }\n    const resumeUpdateData = {\n      updatedAt: /* @__PURE__ */ new Date()\n    };\n    if (data.label) {\n      resumeUpdateData.label = data.label;\n    }\n    if (data.isParsed !== void 0) {\n      resumeUpdateData.isParsed = data.isParsed;\n    }\n    if (data.parsedAt !== void 0) {\n      resumeUpdateData.parsedAt = data.parsedAt;\n    }\n    if (data.parsedData !== void 0) {\n      resumeUpdateData.parsedData = data.parsedData;\n    }\n    if (data.rawText !== void 0) {\n      resumeUpdateData.rawText = data.rawText;\n    }\n    const resume = await prisma.resume.update({\n      where: { id: params.id },\n      data: resumeUpdateData\n    });\n    if (data.profileId) {\n      const profile = await prisma.profile.findUnique({\n        where: { id: data.profileId }\n      });\n      if (!profile) {\n        return json({ error: \"Profile not found\" }, { status: 404 });\n      }\n      await prisma.document.update({\n        where: { id: existingResume.document.id },\n        data: {\n          profileId: data.profileId\n        }\n      });\n      console.log(\n        `Document ${existingResume.document.id} associated with profile ${data.profileId}`\n      );\n    }\n    console.log(\"Resume updated:\", resume ? \"Yes\" : \"No\");\n    return json(resume);\n  } catch (error) {\n    console.error(\"Error updating resume:\", error);\n    return json({ error: \"Failed to update resume\", details: String(error) }, { status: 500 });\n  }\n};\nconst DELETE = async ({ params, locals }) => {\n  try {\n    console.log(\"DELETE resume with ID:\", params.id);\n    const resume = await prisma.resume.findUnique({\n      where: { id: params.id },\n      include: {\n        document: {\n          include: {\n            profile: true\n          }\n        }\n      }\n    });\n    console.log(\"Resume found:\", resume ? \"Yes\" : \"No\");\n    if (!resume) {\n      return json({ error: \"Resume not found\" }, { status: 404 });\n    }\n    await prisma.resume.delete({ where: { id: params.id } });\n    console.log(\"Resume deleted successfully\");\n    return json({ success: true });\n  } catch (error) {\n    console.error(\"Error deleting resume:\", error);\n    return json({ error: \"Failed to delete resume\", details: String(error) }, { status: 500 });\n  }\n};\nexport {\n  DELETE,\n  GET,\n  PATCH\n};\n"], "names": [], "mappings": ";;;;AAEK,MAAC,GAAG,GAAG,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK;AAC1C,EAAE,IAAI;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,MAAM,CAAC,EAAE,CAAC;AACjD,IAAI,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;AAClD,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE;AAC9B,MAAM,OAAO,EAAE;AACf,QAAQ,QAAQ,EAAE;AAClB,UAAU,OAAO,EAAE;AACnB,YAAY,OAAO,EAAE;AACrB;AACA,SAAS;AACT,QAAQ,WAAW,EAAE,IAAI;AACzB,QAAQ,YAAY,EAAE;AACtB;AACA,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,MAAM,GAAG,KAAK,GAAG,IAAI,CAAC;AACvD,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,kBAAkB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE;AACA,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC;AACvB,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC;AAClD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,wBAAwB,EAAE,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7F;AACA;AACK,MAAC,KAAK,GAAG,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AACrD,EAAE,IAAI;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,MAAM,CAAC,EAAE,CAAC;AACnD,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC;AACrC,IAAI,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;AAC1D,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE;AAC9B,MAAM,OAAO,EAAE;AACf,QAAQ,QAAQ,EAAE;AAClB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,cAAc,EAAE;AACzB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,kBAAkB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE;AACA,IAAI,MAAM,gBAAgB,GAAG;AAC7B,MAAM,SAAS,kBAAkB,IAAI,IAAI;AACzC,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;AACpB,MAAM,gBAAgB,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK;AACzC;AACA,IAAI,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,CAAC,EAAE;AAClC,MAAM,gBAAgB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ;AAC/C;AACA,IAAI,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,CAAC,EAAE;AAClC,MAAM,gBAAgB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ;AAC/C;AACA,IAAI,IAAI,IAAI,CAAC,UAAU,KAAK,KAAK,CAAC,EAAE;AACpC,MAAM,gBAAgB,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU;AACnD;AACA,IAAI,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,CAAC,EAAE;AACjC,MAAM,gBAAgB,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO;AAC7C;AACA,IAAI,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;AAC9C,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE;AAC9B,MAAM,IAAI,EAAE;AACZ,KAAK,CAAC;AACN,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;AACxB,MAAM,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;AACtD,QAAQ,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,SAAS;AACnC,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,OAAO,EAAE;AACpB,QAAQ,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpE;AACA,MAAM,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;AACnC,QAAQ,KAAK,EAAE,EAAE,EAAE,EAAE,cAAc,CAAC,QAAQ,CAAC,EAAE,EAAE;AACjD,QAAQ,IAAI,EAAE;AACd,UAAU,SAAS,EAAE,IAAI,CAAC;AAC1B;AACA,OAAO,CAAC;AACR,MAAM,OAAO,CAAC,GAAG;AACjB,QAAQ,CAAC,SAAS,EAAE,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC,yBAAyB,EAAE,IAAI,CAAC,SAAS,CAAC;AACzF,OAAO;AACP;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,MAAM,GAAG,KAAK,GAAG,IAAI,CAAC;AACzD,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC;AACvB,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC;AAClD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9F;AACA;AACK,MAAC,MAAM,GAAG,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK;AAC7C,EAAE,IAAI;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE,MAAM,CAAC,EAAE,CAAC;AACpD,IAAI,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;AAClD,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE;AAC9B,MAAM,OAAO,EAAE;AACf,QAAQ,QAAQ,EAAE;AAClB,UAAU,OAAO,EAAE;AACnB,YAAY,OAAO,EAAE;AACrB;AACA;AACA;AACA,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,MAAM,GAAG,KAAK,GAAG,IAAI,CAAC;AACvD,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,kBAAkB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE;AACA,IAAI,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC;AAC5D,IAAI,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC;AAC9C,IAAI,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;AAClC,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC;AAClD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9F;AACA;;;;"}