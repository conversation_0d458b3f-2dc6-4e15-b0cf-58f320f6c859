{"version": 3, "file": "_server.ts-zFt-jXvS.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/document/_id_/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nconst GET = async ({ params, locals }) => {\n  const user = locals.user;\n  if (!user) return new Response(\"Unauthorized\", { status: 401 });\n  const { id } = params;\n  if (!id) {\n    return json({ error: \"Document ID is required\" }, { status: 400 });\n  }\n  try {\n    const document = await prisma.document.findUnique({\n      where: { id }\n    });\n    if (!document) {\n      return json({ error: \"Document not found\" }, { status: 404 });\n    }\n    if (document.userId !== user.id) {\n      return json({ error: \"Unauthorized access to document\" }, { status: 403 });\n    }\n    return json(document);\n  } catch (error) {\n    console.error(\"Error fetching document:\", error);\n    return json(\n      {\n        error: \"Failed to fetch document\",\n        details: error instanceof Error ? error.message : String(error)\n      },\n      { status: 500 }\n    );\n  }\n};\nconst PATCH = async ({ params, request, locals }) => {\n  const user = locals.user;\n  if (!user) return new Response(\"Unauthorized\", { status: 401 });\n  const { id } = params;\n  if (!id) {\n    return json({ error: \"Document ID is required\" }, { status: 400 });\n  }\n  try {\n    const document = await prisma.document.findUnique({\n      where: { id }\n    });\n    if (!document) {\n      return json({ error: \"Document not found\" }, { status: 404 });\n    }\n    if (document.userId !== user.id) {\n      return json({ error: \"Unauthorized access to document\" }, { status: 403 });\n    }\n    const data = await request.json();\n    const allowedFields = [\"label\", \"profileId\", \"isDefault\"];\n    const updateData = {};\n    for (const field of allowedFields) {\n      if (field in data) {\n        updateData[field] = data[field];\n      }\n    }\n    if (\"profileId\" in data) {\n      const profile = await prisma.profile.findUnique({\n        where: {\n          id: data.profileId,\n          userId: user.id\n        }\n      });\n      if (!profile) {\n        return json({ error: \"Profile not found or unauthorized\" }, { status: 404 });\n      }\n    }\n    const updatedDocument = await prisma.document.update({\n      where: { id },\n      data: updateData\n    });\n    return json({\n      success: true,\n      document: updatedDocument\n    });\n  } catch (error) {\n    console.error(\"Error updating document:\", error);\n    return json(\n      {\n        error: \"Failed to update document\",\n        details: error instanceof Error ? error.message : String(error)\n      },\n      { status: 500 }\n    );\n  }\n};\nconst DELETE = async ({ params, locals }) => {\n  const user = locals.user;\n  if (!user) return new Response(\"Unauthorized\", { status: 401 });\n  const { id } = params;\n  if (!id) {\n    return json({ error: \"Document ID is required\" }, { status: 400 });\n  }\n  try {\n    const document = await prisma.document.findUnique({\n      where: { id }\n    });\n    if (!document) {\n      return json({ error: \"Document not found\" }, { status: 404 });\n    }\n    if (document.userId !== user.id) {\n      return json({ error: \"Unauthorized access to document\" }, { status: 403 });\n    }\n    await prisma.document.delete({\n      where: { id }\n    });\n    return json({\n      success: true,\n      message: \"Document deleted successfully\"\n    });\n  } catch (error) {\n    console.error(\"Error deleting document:\", error);\n    return json(\n      {\n        error: \"Failed to delete document\",\n        details: error instanceof Error ? error.message : String(error)\n      },\n      { status: 500 }\n    );\n  }\n};\nexport {\n  DELETE,\n  GET,\n  PATCH\n};\n"], "names": [], "mappings": ";;;;AAEK,MAAC,GAAG,GAAG,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK;AAC1C,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;AACvB,EAAE,IAAI,CAAC,EAAE,EAAE;AACX,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtE;AACA,EAAE,IAAI;AACN,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;AACtD,MAAM,KAAK,EAAE,EAAE,EAAE;AACjB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oBAAoB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnE;AACA,IAAI,IAAI,QAAQ,CAAC,MAAM,KAAK,IAAI,CAAC,EAAE,EAAE;AACrC,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,iCAAiC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAChF;AACA,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC;AACzB,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC;AACpD,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,KAAK,EAAE,0BAA0B;AACzC,QAAQ,OAAO,EAAE,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC,KAAK;AACtE,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;AACK,MAAC,KAAK,GAAG,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AACrD,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;AACvB,EAAE,IAAI,CAAC,EAAE,EAAE;AACX,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtE;AACA,EAAE,IAAI;AACN,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;AACtD,MAAM,KAAK,EAAE,EAAE,EAAE;AACjB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oBAAoB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnE;AACA,IAAI,IAAI,QAAQ,CAAC,MAAM,KAAK,IAAI,CAAC,EAAE,EAAE;AACrC,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,iCAAiC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAChF;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,MAAM,aAAa,GAAG,CAAC,OAAO,EAAE,WAAW,EAAE,WAAW,CAAC;AAC7D,IAAI,MAAM,UAAU,GAAG,EAAE;AACzB,IAAI,KAAK,MAAM,KAAK,IAAI,aAAa,EAAE;AACvC,MAAM,IAAI,KAAK,IAAI,IAAI,EAAE;AACzB,QAAQ,UAAU,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;AACvC;AACA;AACA,IAAI,IAAI,WAAW,IAAI,IAAI,EAAE;AAC7B,MAAM,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;AACtD,QAAQ,KAAK,EAAE;AACf,UAAU,EAAE,EAAE,IAAI,CAAC,SAAS;AAC5B,UAAU,MAAM,EAAE,IAAI,CAAC;AACvB;AACA,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,OAAO,EAAE;AACpB,QAAQ,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mCAAmC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpF;AACA;AACA,IAAI,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;AACzD,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE;AACnB,MAAM,IAAI,EAAE;AACZ,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,QAAQ,EAAE;AAChB,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC;AACpD,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,KAAK,EAAE,2BAA2B;AAC1C,QAAQ,OAAO,EAAE,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC,KAAK;AACtE,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;AACK,MAAC,MAAM,GAAG,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK;AAC7C,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;AACvB,EAAE,IAAI,CAAC,EAAE,EAAE;AACX,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtE;AACA,EAAE,IAAI;AACN,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;AACtD,MAAM,KAAK,EAAE,EAAE,EAAE;AACjB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oBAAoB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnE;AACA,IAAI,IAAI,QAAQ,CAAC,MAAM,KAAK,IAAI,CAAC,EAAE,EAAE;AACrC,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,iCAAiC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAChF;AACA,IAAI,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;AACjC,MAAM,KAAK,EAAE,EAAE,EAAE;AACjB,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,OAAO,EAAE;AACf,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC;AACpD,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,KAAK,EAAE,2BAA2B;AAC1C,QAAQ,OAAO,EAAE,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC,KAAK;AACtE,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;;;;"}