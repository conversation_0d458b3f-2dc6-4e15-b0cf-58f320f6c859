{"version": 3, "file": "_server.ts-D_geXbqr.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/ai/job-match/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { OpenAI } from \"openai\";\nfunction getOpenAIClient() {\n  if (!process.env.OPENAI_API_KEY) {\n    throw new Error(\"OpenAI API key is not configured\");\n  }\n  return new OpenAI({\n    apiKey: process.env.OPENAI_API_KEY\n  });\n}\nconst POST = async ({ request, locals }) => {\n  if (!locals.user) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  try {\n    const { profileId, jobId } = await request.json();\n    if (!profileId || !jobId) {\n      return json({ error: \"Profile ID and Job ID are required\" }, { status: 400 });\n    }\n    const profile = await prisma.profile.findUnique({\n      where: {\n        id: profileId,\n        userId: locals.user.id\n      },\n      include: {\n        education: true,\n        experience: true,\n        skills: true,\n        certifications: true\n      }\n    });\n    if (!profile) {\n      return json({ error: \"Profile not found\" }, { status: 404 });\n    }\n    const job = await prisma.jobListing.findUnique({\n      where: {\n        id: jobId\n      },\n      select: {\n        title: true,\n        company: true,\n        description: true,\n        requirements: true,\n        skills: true,\n        location: true,\n        salary: true,\n        remote: true\n      }\n    });\n    if (!job) {\n      return json({ error: \"Job not found\" }, { status: 404 });\n    }\n    const analysis = await generateJobMatchAnalysis(profile, job);\n    const savedAnalysis = await prisma.jobMatchAnalysis.create({\n      data: {\n        userId: locals.user.id,\n        profileId,\n        jobId,\n        overallMatchScore: analysis.overallMatchScore,\n        skillsMatchScore: analysis.skillsMatchScore,\n        experienceMatchScore: analysis.experienceMatchScore,\n        educationMatchScore: analysis.educationMatchScore,\n        keywordMatchScore: analysis.keywordMatchScore,\n        matchedSkills: analysis.matchedSkills,\n        missingSkills: analysis.missingSkills,\n        strengthAreas: analysis.strengthAreas,\n        improvementAreas: analysis.improvementAreas,\n        recommendations: analysis.recommendations,\n        jobTitle: job.title,\n        company: job.company\n      }\n    });\n    await prisma.featureUsage.upsert({\n      where: {\n        userId_featureId_limitId: {\n          userId: locals.user.id,\n          featureId: \"ai_job_matching\",\n          limitId: \"job_match_analyses_monthly\"\n        }\n      },\n      update: {\n        usage: {\n          increment: 1\n        }\n      },\n      create: {\n        userId: locals.user.id,\n        featureId: \"ai_job_matching\",\n        limitId: \"job_match_analyses_monthly\",\n        usage: 1\n      }\n    });\n    return json({ analysis: savedAnalysis });\n  } catch (error) {\n    console.error(\"Error generating job match analysis:\", error);\n    return json({ error: \"Failed to generate job match analysis\" }, { status: 500 });\n  }\n};\nconst GET = async ({ url, locals }) => {\n  if (!locals.user) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  try {\n    const profileId = url.searchParams.get(\"profileId\");\n    const jobId = url.searchParams.get(\"jobId\");\n    if (!profileId || !jobId) {\n      return json({ error: \"Profile ID and Job ID are required\" }, { status: 400 });\n    }\n    const analysis = await prisma.jobMatchAnalysis.findFirst({\n      where: {\n        userId: locals.user.id,\n        profileId,\n        jobId\n      },\n      orderBy: {\n        createdAt: \"desc\"\n      }\n    });\n    if (!analysis) {\n      return json({ error: \"No job match analysis found\" }, { status: 404 });\n    }\n    return json({ analysis });\n  } catch (error) {\n    console.error(\"Error fetching job match analysis:\", error);\n    return json({ error: \"Failed to fetch job match analysis\" }, { status: 500 });\n  }\n};\nasync function generateJobMatchAnalysis(profile, job) {\n  try {\n    const prompt = `Analyze how well this candidate's profile matches the job:\n\nProfile:\n${JSON.stringify(profile)}\n\nJob:\n${JSON.stringify(job)}\n\nProvide a detailed analysis with the following scores (0-1, where 1 is perfect match):\n1. Overall Match Score\n2. Skills Match Score\n3. Experience Match Score\n4. Education Match Score\n5. Keyword Match Score\n\nAlso provide:\n- Matched skills\n- Missing skills\n- Strength areas\n- Areas for improvement\n- Recommendations for the candidate\n\nReturn the analysis as a JSON object with these fields.\n`;\n    const openai = getOpenAIClient();\n    const response = await openai.chat.completions.create({\n      model: \"gpt-4\",\n      messages: [\n        { role: \"system\", content: \"You are an expert job match analyzer.\" },\n        { role: \"user\", content: prompt }\n      ],\n      temperature: 0.7,\n      max_tokens: 2e3,\n      response_format: { type: \"json_object\" }\n    });\n    const content = response.choices[0]?.message?.content || \"\";\n    const analysisResult = JSON.parse(content);\n    return {\n      overallMatchScore: analysisResult.overallMatchScore || 0,\n      skillsMatchScore: analysisResult.skillsMatchScore || 0,\n      experienceMatchScore: analysisResult.experienceMatchScore || 0,\n      educationMatchScore: analysisResult.educationMatchScore || 0,\n      keywordMatchScore: analysisResult.keywordMatchScore || 0,\n      matchedSkills: analysisResult.matchedSkills || [],\n      missingSkills: analysisResult.missingSkills || [],\n      strengthAreas: analysisResult.strengthAreas || [],\n      improvementAreas: analysisResult.improvementAreas || [],\n      recommendations: analysisResult.recommendations || []\n    };\n  } catch (error) {\n    console.error(\"Error in job match analysis generation:\", error);\n    return {\n      overallMatchScore: 0.7,\n      skillsMatchScore: 0.65,\n      experienceMatchScore: 0.75,\n      educationMatchScore: 0.8,\n      keywordMatchScore: 0.6,\n      matchedSkills: [\"communication\", \"teamwork\"],\n      missingSkills: [\"specific technical skills\"],\n      strengthAreas: [\"education\", \"general experience\"],\n      improvementAreas: [\"technical skills\", \"industry-specific experience\"],\n      recommendations: [\n        \"Consider highlighting relevant experience more prominently\",\n        \"Add more specific technical skills to your profile\"\n      ]\n    };\n  }\n}\nexport {\n  GET,\n  POST\n};\n"], "names": [], "mappings": ";;;;;AAGA,SAAS,eAAe,GAAG;AAC3B,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE;AACnC,IAAI,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC;AACvD;AACA,EAAE,OAAO,IAAI,MAAM,CAAC;AACpB,IAAI,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC;AACxB,GAAG,CAAC;AACJ;AACK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AAC5C,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;AACpB,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrD,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,KAAK,EAAE;AAC9B,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oCAAoC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnF;AACA,IAAI,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;AACpD,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE,SAAS;AACrB,QAAQ,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC;AAC5B,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,SAAS,EAAE,IAAI;AACvB,QAAQ,UAAU,EAAE,IAAI;AACxB,QAAQ,MAAM,EAAE,IAAI;AACpB,QAAQ,cAAc,EAAE;AACxB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,OAAO,EAAE;AAClB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE;AACA,IAAI,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC;AACnD,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE;AACZ,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,KAAK,EAAE,IAAI;AACnB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,WAAW,EAAE,IAAI;AACzB,QAAQ,YAAY,EAAE,IAAI;AAC1B,QAAQ,MAAM,EAAE,IAAI;AACpB,QAAQ,QAAQ,EAAE,IAAI;AACtB,QAAQ,MAAM,EAAE,IAAI;AACpB,QAAQ,MAAM,EAAE;AAChB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,GAAG,EAAE;AACd,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9D;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,wBAAwB,CAAC,OAAO,EAAE,GAAG,CAAC;AACjE,IAAI,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;AAC/D,MAAM,IAAI,EAAE;AACZ,QAAQ,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;AAC9B,QAAQ,SAAS;AACjB,QAAQ,KAAK;AACb,QAAQ,iBAAiB,EAAE,QAAQ,CAAC,iBAAiB;AACrD,QAAQ,gBAAgB,EAAE,QAAQ,CAAC,gBAAgB;AACnD,QAAQ,oBAAoB,EAAE,QAAQ,CAAC,oBAAoB;AAC3D,QAAQ,mBAAmB,EAAE,QAAQ,CAAC,mBAAmB;AACzD,QAAQ,iBAAiB,EAAE,QAAQ,CAAC,iBAAiB;AACrD,QAAQ,aAAa,EAAE,QAAQ,CAAC,aAAa;AAC7C,QAAQ,aAAa,EAAE,QAAQ,CAAC,aAAa;AAC7C,QAAQ,aAAa,EAAE,QAAQ,CAAC,aAAa;AAC7C,QAAQ,gBAAgB,EAAE,QAAQ,CAAC,gBAAgB;AACnD,QAAQ,eAAe,EAAE,QAAQ,CAAC,eAAe;AACjD,QAAQ,QAAQ,EAAE,GAAG,CAAC,KAAK;AAC3B,QAAQ,OAAO,EAAE,GAAG,CAAC;AACrB;AACA,KAAK,CAAC;AACN,IAAI,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AACrC,MAAM,KAAK,EAAE;AACb,QAAQ,wBAAwB,EAAE;AAClC,UAAU,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;AAChC,UAAU,SAAS,EAAE,iBAAiB;AACtC,UAAU,OAAO,EAAE;AACnB;AACA,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,KAAK,EAAE;AACf,UAAU,SAAS,EAAE;AACrB;AACA,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;AAC9B,QAAQ,SAAS,EAAE,iBAAiB;AACpC,QAAQ,OAAO,EAAE,4BAA4B;AAC7C,QAAQ,KAAK,EAAE;AACf;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,EAAE,QAAQ,EAAE,aAAa,EAAE,CAAC;AAC5C,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC;AAChE,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uCAAuC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpF;AACA;AACK,MAAC,GAAG,GAAG,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK;AACvC,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;AACpB,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,SAAS,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC;AACvD,IAAI,MAAM,KAAK,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC;AAC/C,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,KAAK,EAAE;AAC9B,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oCAAoC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnF;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC;AAC7D,MAAM,KAAK,EAAE;AACb,QAAQ,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;AAC9B,QAAQ,SAAS;AACjB,QAAQ;AACR,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,SAAS,EAAE;AACnB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,6BAA6B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC5E;AACA,IAAI,OAAO,IAAI,CAAC,EAAE,QAAQ,EAAE,CAAC;AAC7B,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC;AAC9D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oCAAoC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjF;AACA;AACA,eAAe,wBAAwB,CAAC,OAAO,EAAE,GAAG,EAAE;AACtD,EAAE,IAAI;AACN,IAAI,MAAM,MAAM,GAAG,CAAC;;AAEpB;AACA,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;;AAEzB;AACA,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;;AAErB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,CAAC;AACD,IAAI,MAAM,MAAM,GAAG,eAAe,EAAE;AACpC,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;AAC1D,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,QAAQ,EAAE;AAChB,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,uCAAuC,EAAE;AAC5E,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM;AACvC,OAAO;AACP,MAAM,WAAW,EAAE,GAAG;AACtB,MAAM,UAAU,EAAE,GAAG;AACrB,MAAM,eAAe,EAAE,EAAE,IAAI,EAAE,aAAa;AAC5C,KAAK,CAAC;AACN,IAAI,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,IAAI,EAAE;AAC/D,IAAI,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;AAC9C,IAAI,OAAO;AACX,MAAM,iBAAiB,EAAE,cAAc,CAAC,iBAAiB,IAAI,CAAC;AAC9D,MAAM,gBAAgB,EAAE,cAAc,CAAC,gBAAgB,IAAI,CAAC;AAC5D,MAAM,oBAAoB,EAAE,cAAc,CAAC,oBAAoB,IAAI,CAAC;AACpE,MAAM,mBAAmB,EAAE,cAAc,CAAC,mBAAmB,IAAI,CAAC;AAClE,MAAM,iBAAiB,EAAE,cAAc,CAAC,iBAAiB,IAAI,CAAC;AAC9D,MAAM,aAAa,EAAE,cAAc,CAAC,aAAa,IAAI,EAAE;AACvD,MAAM,aAAa,EAAE,cAAc,CAAC,aAAa,IAAI,EAAE;AACvD,MAAM,aAAa,EAAE,cAAc,CAAC,aAAa,IAAI,EAAE;AACvD,MAAM,gBAAgB,EAAE,cAAc,CAAC,gBAAgB,IAAI,EAAE;AAC7D,MAAM,eAAe,EAAE,cAAc,CAAC,eAAe,IAAI;AACzD,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC;AACnE,IAAI,OAAO;AACX,MAAM,iBAAiB,EAAE,GAAG;AAC5B,MAAM,gBAAgB,EAAE,IAAI;AAC5B,MAAM,oBAAoB,EAAE,IAAI;AAChC,MAAM,mBAAmB,EAAE,GAAG;AAC9B,MAAM,iBAAiB,EAAE,GAAG;AAC5B,MAAM,aAAa,EAAE,CAAC,eAAe,EAAE,UAAU,CAAC;AAClD,MAAM,aAAa,EAAE,CAAC,2BAA2B,CAAC;AAClD,MAAM,aAAa,EAAE,CAAC,WAAW,EAAE,oBAAoB,CAAC;AACxD,MAAM,gBAAgB,EAAE,CAAC,kBAAkB,EAAE,8BAA8B,CAAC;AAC5E,MAAM,eAAe,EAAE;AACvB,QAAQ,4DAA4D;AACpE,QAAQ;AACR;AACA,KAAK;AACL;AACA;;;;"}