# Low Memory Configuration for 512MB Systems
# Copy this to .env to apply these settings

# Circuit Breaker - Aggressive memory management
CIRCUIT_BREAKER_MEMORY_THRESHOLD=50
CIRCUIT_BREAKER_CPU_THRESHOLD=50
CIRCUIT_BREAKER_DEGRADED_MEMORY_THRESHOLD=30
CIRCUIT_BREAKER_DEGRADED_CPU_THRESHOLD=30

# Job Enrichment - Minimal resource usage
ENRICH_JOBS_BATCH_SIZE=5
ENRICH_JOBS_CONCURRENCY=1
ENRICH_JOBS_MAX_MEMORY=400
ENRICH_JOBS_WARNING_MEMORY=300
ENRICH_JOBS_MAX_PER_RUN=20

# Job Details Scraper - Minimal resource usage
JOB_DETAILS_BATCH_SIZE=1
JOB_DETAILS_MAX_CONCURRENT=1
JOB_DETAILS_MAX_JOBS=20

# General Scraper Settings - Conservative
SCRAPER_MAX_WORKERS=1
SCRAPER_BATCH_SIZE=2
SCRAPER_CONCURRENCY=1
SCRAPER_MAX_JOBS=50

# Memory Management
MEMORY_LIMIT_MB=400
NODE_OPTIONS="--max-old-space-size=400"

# Disable resource-intensive features
DISABLE_HTML_PREVIEWS=true
DISABLE_SCREENSHOTS=true
DISABLE_FILE_STORAGE=true

# Environment
NODE_ENV=production
