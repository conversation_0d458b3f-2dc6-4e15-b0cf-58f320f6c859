{"version": 3, "file": "_server.ts-CyW2704f.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/email/worker/metrics/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { l as logger } from \"../../../../../../chunks/logger.js\";\nimport { g as getPrismaClient } from \"../../../../../../chunks/prisma.js\";\nimport { R as RedisConnection } from \"../../../../../../chunks/redis.js\";\nconst prisma = getPrismaClient();\nasync function GET() {\n  try {\n    const metrics = {\n      processedLast24h: 0,\n      failedLast24h: 0,\n      averageProcessingTime: 0,\n      oldestJobInQueue: null,\n      totalProcessed: 0,\n      totalFailed: 0\n    };\n    try {\n      if (prisma && typeof prisma.emailMetrics?.findFirst === \"function\") {\n        const dbMetrics = await prisma.emailMetrics.findFirst({\n          orderBy: { timestamp: \"desc\" }\n        });\n        if (dbMetrics) {\n          metrics.processedLast24h = dbMetrics.processedLast24h || 0;\n          metrics.failedLast24h = dbMetrics.failedLast24h || 0;\n          metrics.averageProcessingTime = dbMetrics.averageProcessingTime || 0;\n          metrics.totalProcessed = dbMetrics.totalProcessed || 0;\n          metrics.totalFailed = dbMetrics.totalFailed || 0;\n        }\n      }\n    } catch (dbError) {\n      logger.warn(\"Error fetching email metrics from database:\", dbError);\n    }\n    if (RedisConnection) {\n      try {\n        const jobs = await RedisConnection.zrange(\"email:queue\", 0, 0, \"WITHSCORES\");\n        if (jobs && jobs.length >= 2) {\n          const oldestTimestamp = parseInt(jobs[1]);\n          if (!isNaN(oldestTimestamp)) {\n            metrics.oldestJobInQueue = new Date(oldestTimestamp).toISOString();\n          }\n        }\n      } catch (redisError) {\n        logger.warn(\"Error fetching oldest job from Redis:\", redisError);\n      }\n    }\n    return json(metrics);\n  } catch (error) {\n    logger.error(\"Error fetching worker metrics:\", error);\n    return json({\n      error: \"Failed to fetch worker metrics\",\n      processedLast24h: 0,\n      failedLast24h: 0,\n      averageProcessingTime: 0,\n      oldestJobInQueue: null,\n      totalProcessed: 0,\n      totalFailed: 0\n    }, { status: 500 });\n  }\n}\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;;;;AAIA,MAAM,MAAM,GAAG,eAAe,EAAE;AAChC,eAAe,GAAG,GAAG;AACrB,EAAE,IAAI;AACN,IAAI,MAAM,OAAO,GAAG;AACpB,MAAM,gBAAgB,EAAE,CAAC;AACzB,MAAM,aAAa,EAAE,CAAC;AACtB,MAAM,qBAAqB,EAAE,CAAC;AAC9B,MAAM,gBAAgB,EAAE,IAAI;AAC5B,MAAM,cAAc,EAAE,CAAC;AACvB,MAAM,WAAW,EAAE;AACnB,KAAK;AACL,IAAI,IAAI;AACR,MAAM,IAAI,MAAM,IAAI,OAAO,MAAM,CAAC,YAAY,EAAE,SAAS,KAAK,UAAU,EAAE;AAC1E,QAAQ,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC;AAC9D,UAAU,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM;AACtC,SAAS,CAAC;AACV,QAAQ,IAAI,SAAS,EAAE;AACvB,UAAU,OAAO,CAAC,gBAAgB,GAAG,SAAS,CAAC,gBAAgB,IAAI,CAAC;AACpE,UAAU,OAAO,CAAC,aAAa,GAAG,SAAS,CAAC,aAAa,IAAI,CAAC;AAC9D,UAAU,OAAO,CAAC,qBAAqB,GAAG,SAAS,CAAC,qBAAqB,IAAI,CAAC;AAC9E,UAAU,OAAO,CAAC,cAAc,GAAG,SAAS,CAAC,cAAc,IAAI,CAAC;AAChE,UAAU,OAAO,CAAC,WAAW,GAAG,SAAS,CAAC,WAAW,IAAI,CAAC;AAC1D;AACA;AACA,KAAK,CAAC,OAAO,OAAO,EAAE;AACtB,MAAM,MAAM,CAAC,IAAI,CAAC,6CAA6C,EAAE,OAAO,CAAC;AACzE;AACA,IAAI,IAAI,eAAe,EAAE;AACzB,MAAM,IAAI;AACV,QAAQ,MAAM,IAAI,GAAG,MAAM,eAAe,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,EAAE,CAAC,EAAE,YAAY,CAAC;AACpF,QAAQ,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE;AACtC,UAAU,MAAM,eAAe,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACnD,UAAU,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE;AACvC,YAAY,OAAO,CAAC,gBAAgB,GAAG,IAAI,IAAI,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE;AAC9E;AACA;AACA,OAAO,CAAC,OAAO,UAAU,EAAE;AAC3B,QAAQ,MAAM,CAAC,IAAI,CAAC,uCAAuC,EAAE,UAAU,CAAC;AACxE;AACA;AACA,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC;AACxB,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC;AACzD,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,KAAK,EAAE,gCAAgC;AAC7C,MAAM,gBAAgB,EAAE,CAAC;AACzB,MAAM,aAAa,EAAE,CAAC;AACtB,MAAM,qBAAqB,EAAE,CAAC;AAC9B,MAAM,gBAAgB,EAAE,IAAI;AAC5B,MAAM,cAAc,EAAE,CAAC;AACvB,MAAM,WAAW,EAAE;AACnB,KAAK,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACvB;AACA;;;;"}