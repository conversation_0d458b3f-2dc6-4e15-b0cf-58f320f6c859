{"version": 3, "file": "_server.ts-0edZ54cv.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/locations/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../chunks/prisma.js\";\nconst GET = async ({ url }) => {\n  try {\n    if (!prisma) {\n      console.log(\"Prisma client not initialized during build\");\n      return json([]);\n    }\n    const country = url.searchParams.get(\"country\") || \"US\";\n    const search = url.searchParams.get(\"search\") || \"\";\n    const limit = parseInt(url.searchParams.get(\"limit\") || \"100\");\n    const cities = await prisma.city.findMany({\n      where: {\n        state: {\n          country: {\n            isoCode: country\n          }\n        },\n        ...search ? {\n          OR: [\n            { name: { contains: search, mode: \"insensitive\" } },\n            {\n              state: {\n                OR: [\n                  { name: { contains: search, mode: \"insensitive\" } },\n                  { code: { contains: search, mode: \"insensitive\" } }\n                ]\n              }\n            }\n          ]\n        } : {}\n      },\n      include: {\n        state: true\n      },\n      orderBy: {\n        name: \"asc\"\n      },\n      take: limit\n    });\n    const formattedCities = cities.map((city) => ({\n      id: city.id,\n      name: city.name,\n      state: {\n        id: city.state.id,\n        name: city.state.name,\n        code: city.state.code || \"\"\n      },\n      country\n    }));\n    return json(formattedCities);\n  } catch (error) {\n    console.error(\"Error fetching locations:\", error);\n    return json({ error: \"Failed to fetch locations\" }, { status: 500 });\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;AAEK,MAAC,GAAG,GAAG,OAAO,EAAE,GAAG,EAAE,KAAK;AAC/B,EAAE,IAAI;AACN,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC;AAC/D,MAAM,OAAO,IAAI,CAAC,EAAE,CAAC;AACrB;AACA,IAAI,MAAM,OAAO,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,IAAI;AAC3D,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE;AACvD,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC;AAClE,IAAI,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC9C,MAAM,KAAK,EAAE;AACb,QAAQ,KAAK,EAAE;AACf,UAAU,OAAO,EAAE;AACnB,YAAY,OAAO,EAAE;AACrB;AACA,SAAS;AACT,QAAQ,GAAG,MAAM,GAAG;AACpB,UAAU,EAAE,EAAE;AACd,YAAY,EAAE,IAAI,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,aAAa,EAAE,EAAE;AAC/D,YAAY;AACZ,cAAc,KAAK,EAAE;AACrB,gBAAgB,EAAE,EAAE;AACpB,kBAAkB,EAAE,IAAI,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,aAAa,EAAE,EAAE;AACrE,kBAAkB,EAAE,IAAI,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,aAAa,EAAE;AACnE;AACA;AACA;AACA;AACA,SAAS,GAAG;AACZ,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,KAAK,EAAE;AACf,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,IAAI,EAAE;AACd,OAAO;AACP,MAAM,IAAI,EAAE;AACZ,KAAK,CAAC;AACN,IAAI,MAAM,eAAe,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,MAAM;AAClD,MAAM,EAAE,EAAE,IAAI,CAAC,EAAE;AACjB,MAAM,IAAI,EAAE,IAAI,CAAC,IAAI;AACrB,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE;AACzB,QAAQ,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI;AAC7B,QAAQ,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI;AACjC,OAAO;AACP,MAAM;AACN,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,IAAI,CAAC,eAAe,CAAC;AAChC,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC;AACrD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,2BAA2B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA;;;;"}