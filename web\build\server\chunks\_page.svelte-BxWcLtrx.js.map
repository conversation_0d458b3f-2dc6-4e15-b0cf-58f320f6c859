{"version": 3, "file": "_page.svelte-BxWcLtrx.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/resources/_page.svelte.js"], "sourcesContent": ["import { U as ensure_array_like, V as escape_html, R as attr, N as bind_props, y as pop, w as push } from \"../../../chunks/index3.js\";\nimport { S as SEO } from \"../../../chunks/SEO.js\";\nimport { C as Card } from \"../../../chunks/card.js\";\nimport { C as Card_content } from \"../../../chunks/card-content.js\";\nimport { C as Card_header } from \"../../../chunks/card-header.js\";\nimport { B as Button } from \"../../../chunks/button.js\";\nimport { A as Arrow_right } from \"../../../chunks/arrow-right.js\";\nimport { F as File_text } from \"../../../chunks/file-text.js\";\nimport { S as Search } from \"../../../chunks/search.js\";\nimport { C as Calculator } from \"../../../chunks/calculator.js\";\nimport { B as Briefcase } from \"../../../chunks/briefcase.js\";\nfunction _page($$payload, $$props) {\n  push();\n  let filteredResources;\n  let data = $$props[\"data\"];\n  const categories = [\n    \"All\",\n    \"Templates\",\n    \"Tools\",\n    \"Guides\",\n    \"Reports\"\n  ];\n  const resources = data.resources || [];\n  const featuredResources = data.featuredResources || resources.filter((resource) => resource.featured);\n  let selectedCategory = \"All\";\n  function getIconComponent(iconName) {\n    if (typeof iconName !== \"string\") return iconName;\n    switch (iconName) {\n      case \"FileText\":\n        return File_text;\n      case \"Briefcase\":\n        return Briefcase;\n      case \"Calculator\":\n        return Calculator;\n      case \"Search\":\n        return Search;\n      default:\n        return File_text;\n    }\n  }\n  function getResourceUrl(resource) {\n    if (resource.slug && resource.slug.current) {\n      return `/resources/${resource.slug.current}`;\n    }\n    return resource.link || \"#\";\n  }\n  filteredResources = resources.filter((resource) => {\n    resource.resourceType || resource.type;\n    const matchesCategory = selectedCategory === \"All\";\n    return matchesCategory;\n  });\n  const each_array = ensure_array_like(categories);\n  SEO($$payload, {\n    title: \"Career Resources | Hirli\",\n    description: \"Free tools, templates, and guides to help you succeed in your job search and career development.\",\n    keywords: \"resume templates, cover letter templates, ATS optimization, interview preparation, salary calculator, career planning, job market research\"\n  });\n  $$payload.out += `<!----> <div class=\"container mx-auto px-4 py-16\"><div><div class=\"mb-12 text-center\"><h1 class=\"mb-4 text-4xl font-bold\">Career Resources</h1> <p class=\"mx-auto max-w-2xl text-lg text-gray-600\">Free tools, templates, and guides to help you succeed in your job search and career\n        development.</p></div> <div class=\"mb-12\"><div class=\"flex flex-wrap gap-2\"><!--[-->`;\n  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n    let category = each_array[$$index];\n    Button($$payload, {\n      variant: selectedCategory === category ? \"default\" : \"outline\",\n      size: \"sm\",\n      children: ($$payload2) => {\n        $$payload2.out += `<!---->${escape_html(category)}`;\n      },\n      $$slots: { default: true }\n    });\n  }\n  $$payload.out += `<!--]--></div></div> `;\n  if (featuredResources.length > 0) {\n    $$payload.out += \"<!--[-->\";\n    const each_array_1 = ensure_array_like(featuredResources);\n    $$payload.out += `<div class=\"mb-16\"><h2 class=\"mb-6 text-2xl font-semibold\">Featured Resources</h2> <div class=\"grid gap-8 md:grid-cols-2 lg:grid-cols-3\"><!--[-->`;\n    for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {\n      let resource = each_array_1[$$index_1];\n      Card($$payload, {\n        class: \"flex h-full flex-col overflow-hidden border shadow-md\",\n        children: ($$payload2) => {\n          Card_header($$payload2, {\n            class: \"bg-primary/5 flex items-center gap-3 p-6\",\n            children: ($$payload3) => {\n              $$payload3.out += `<div class=\"bg-primary/10 text-primary flex h-10 w-10 items-center justify-center rounded-full\"><!---->`;\n              getIconComponent(resource.icon)?.($$payload3, { class: \"h-5 w-5\" });\n              $$payload3.out += `<!----></div> <h3 class=\"text-xl font-semibold\">${escape_html(resource.title)}</h3>`;\n            },\n            $$slots: { default: true }\n          });\n          $$payload2.out += `<!----> `;\n          Card_content($$payload2, {\n            class: \"flex flex-1 flex-col p-6\",\n            children: ($$payload3) => {\n              $$payload3.out += `<p class=\"mb-4 flex-1 text-gray-600\">${escape_html(resource.description)}</p> <div class=\"mt-auto flex items-center justify-between\"><span class=\"text-sm capitalize text-gray-500\">${escape_html(resource.resourceType || resource.type || \"Resource\")}</span> <a${attr(\"href\", getResourceUrl(resource))} class=\"text-primary inline-flex items-center text-sm font-medium hover:underline\">Access Resource `;\n              Arrow_right($$payload3, { class: \"ml-1 h-3 w-3\" });\n              $$payload3.out += `<!----></a></div>`;\n            },\n            $$slots: { default: true }\n          });\n          $$payload2.out += `<!---->`;\n        },\n        $$slots: { default: true }\n      });\n    }\n    $$payload.out += `<!--]--></div></div>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--> <div><h2 class=\"mb-6 text-2xl font-semibold\">${escape_html(\"All Resources\")}</h2> `;\n  if (filteredResources.length > 0) {\n    $$payload.out += \"<!--[-->\";\n    const each_array_2 = ensure_array_like(filteredResources);\n    $$payload.out += `<div class=\"grid gap-8 md:grid-cols-2 lg:grid-cols-3\"><!--[-->`;\n    for (let $$index_2 = 0, $$length = each_array_2.length; $$index_2 < $$length; $$index_2++) {\n      let resource = each_array_2[$$index_2];\n      Card($$payload, {\n        class: \"flex h-full flex-col overflow-hidden border shadow-md\",\n        children: ($$payload2) => {\n          Card_header($$payload2, {\n            class: \"bg-primary/5 flex items-center gap-3 p-6\",\n            children: ($$payload3) => {\n              $$payload3.out += `<div class=\"bg-primary/10 text-primary flex h-10 w-10 items-center justify-center rounded-full\"><!---->`;\n              getIconComponent(resource.icon)?.($$payload3, { class: \"h-5 w-5\" });\n              $$payload3.out += `<!----></div> <h3 class=\"text-xl font-semibold\">${escape_html(resource.title)}</h3>`;\n            },\n            $$slots: { default: true }\n          });\n          $$payload2.out += `<!----> `;\n          Card_content($$payload2, {\n            class: \"flex flex-1 flex-col p-6\",\n            children: ($$payload3) => {\n              $$payload3.out += `<p class=\"mb-4 flex-1 text-gray-600\">${escape_html(resource.description)}</p> <div class=\"mt-auto flex items-center justify-between\"><span class=\"text-sm capitalize text-gray-500\">${escape_html(resource.resourceType || resource.type || \"Resource\")}</span> <a${attr(\"href\", getResourceUrl(resource))} class=\"text-primary inline-flex items-center text-sm font-medium hover:underline\">Access Resource `;\n              Arrow_right($$payload3, { class: \"ml-1 h-3 w-3\" });\n              $$payload3.out += `<!----></a></div>`;\n            },\n            $$slots: { default: true }\n          });\n          $$payload2.out += `<!---->`;\n        },\n        $$slots: { default: true }\n      });\n    }\n    $$payload.out += `<!--]--></div>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<div class=\"py-8 text-center\"><p class=\"text-gray-500\">No resources found for this category.</p></div>`;\n  }\n  $$payload.out += `<!--]--></div></div></div>`;\n  bind_props($$props, { data });\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAWA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,iBAAiB;AACvB,EAAE,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC;AAC5B,EAAE,MAAM,UAAU,GAAG;AACrB,IAAI,KAAK;AACT,IAAI,WAAW;AACf,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI;AACJ,GAAG;AACH,EAAE,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,EAAE;AACxC,EAAE,MAAM,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,QAAQ,CAAC;AACvG,EAAE,IAAI,gBAAgB,GAAG,KAAK;AAC9B,EAAE,SAAS,gBAAgB,CAAC,QAAQ,EAAE;AACtC,IAAI,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,OAAO,QAAQ;AACrD,IAAI,QAAQ,QAAQ;AACpB,MAAM,KAAK,UAAU;AACrB,QAAQ,OAAO,SAAS;AACxB,MAAM,KAAK,WAAW;AACtB,QAAQ,OAAO,SAAS;AACxB,MAAM,KAAK,YAAY;AACvB,QAAQ,OAAO,UAAU;AACzB,MAAM,KAAK,QAAQ;AACnB,QAAQ,OAAO,MAAM;AACrB,MAAM;AACN,QAAQ,OAAO,SAAS;AACxB;AACA;AACA,EAAE,SAAS,cAAc,CAAC,QAAQ,EAAE;AACpC,IAAI,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE;AAChD,MAAM,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAClD;AACA,IAAI,OAAO,QAAQ,CAAC,IAAI,IAAI,GAAG;AAC/B;AACA,EAAE,iBAAiB,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,QAAQ,KAAK;AACrD,IAAI,QAAQ,CAAC,YAAY,IAAI,QAAQ,CAAC,IAAI;AAC1C,IAAI,MAAM,eAAe,GAAG,gBAAgB,KAAK,KAAK;AACtD,IAAI,OAAO,eAAe;AAC1B,GAAG,CAAC;AACJ,EAAE,MAAM,UAAU,GAAG,iBAAiB,CAAC,UAAU,CAAC;AAClD,EAAE,GAAG,CAAC,SAAS,EAAE;AACjB,IAAI,KAAK,EAAE,0BAA0B;AACrC,IAAI,WAAW,EAAE,kGAAkG;AACnH,IAAI,QAAQ,EAAE;AACd,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC;AACpB,4FAA4F,CAAC;AAC7F,EAAE,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACrF,IAAI,IAAI,QAAQ,GAAG,UAAU,CAAC,OAAO,CAAC;AACtC,IAAI,MAAM,CAAC,SAAS,EAAE;AACtB,MAAM,OAAO,EAAE,gBAAgB,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAS;AACpE,MAAM,IAAI,EAAE,IAAI;AAChB,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC;AAC3D,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B,KAAK,CAAC;AACN;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC;AAC1C,EAAE,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;AACpC,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,MAAM,YAAY,GAAG,iBAAiB,CAAC,iBAAiB,CAAC;AAC7D,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,iJAAiJ,CAAC;AACxK,IAAI,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,QAAQ,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,QAAQ,EAAE,SAAS,EAAE,EAAE;AAC/F,MAAM,IAAI,QAAQ,GAAG,YAAY,CAAC,SAAS,CAAC;AAC5C,MAAM,IAAI,CAAC,SAAS,EAAE;AACtB,QAAQ,KAAK,EAAE,uDAAuD;AACtE,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,WAAW,CAAC,UAAU,EAAE;AAClC,YAAY,KAAK,EAAE,0CAA0C;AAC7D,YAAY,QAAQ,EAAE,CAAC,UAAU,KAAK;AACtC,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,uGAAuG,CAAC;AACzI,cAAc,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,UAAU,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AACjF,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,gDAAgD,EAAE,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;AACrH,aAAa;AACb,YAAY,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACpC,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACtC,UAAU,YAAY,CAAC,UAAU,EAAE;AACnC,YAAY,KAAK,EAAE,0BAA0B;AAC7C,YAAY,QAAQ,EAAE,CAAC,UAAU,KAAK;AACtC,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,qCAAqC,EAAE,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,2GAA2G,EAAE,WAAW,CAAC,QAAQ,CAAC,YAAY,IAAI,QAAQ,CAAC,IAAI,IAAI,UAAU,CAAC,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,mGAAmG,CAAC;AAChb,cAAc,WAAW,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AAChE,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC;AACnD,aAAa;AACb,YAAY,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACpC,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACrC,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC,OAAO,CAAC;AACR;AACA,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC;AAC3C,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,sDAAsD,EAAE,WAAW,CAAC,eAAe,CAAC,CAAC,MAAM,CAAC;AAChH,EAAE,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;AACpC,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,MAAM,YAAY,GAAG,iBAAiB,CAAC,iBAAiB,CAAC;AAC7D,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,8DAA8D,CAAC;AACrF,IAAI,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,QAAQ,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,QAAQ,EAAE,SAAS,EAAE,EAAE;AAC/F,MAAM,IAAI,QAAQ,GAAG,YAAY,CAAC,SAAS,CAAC;AAC5C,MAAM,IAAI,CAAC,SAAS,EAAE;AACtB,QAAQ,KAAK,EAAE,uDAAuD;AACtE,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,WAAW,CAAC,UAAU,EAAE;AAClC,YAAY,KAAK,EAAE,0CAA0C;AAC7D,YAAY,QAAQ,EAAE,CAAC,UAAU,KAAK;AACtC,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,uGAAuG,CAAC;AACzI,cAAc,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,UAAU,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AACjF,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,gDAAgD,EAAE,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;AACrH,aAAa;AACb,YAAY,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACpC,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACtC,UAAU,YAAY,CAAC,UAAU,EAAE;AACnC,YAAY,KAAK,EAAE,0BAA0B;AAC7C,YAAY,QAAQ,EAAE,CAAC,UAAU,KAAK;AACtC,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,qCAAqC,EAAE,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,2GAA2G,EAAE,WAAW,CAAC,QAAQ,CAAC,YAAY,IAAI,QAAQ,CAAC,IAAI,IAAI,UAAU,CAAC,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,mGAAmG,CAAC;AAChb,cAAc,WAAW,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AAChE,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC;AACnD,aAAa;AACb,YAAY,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACpC,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACrC,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC,OAAO,CAAC;AACR;AACA,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACrC,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,sGAAsG,CAAC;AAC7H;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,0BAA0B,CAAC;AAC/C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,CAAC;AAC/B,EAAE,GAAG,EAAE;AACP;;;;"}