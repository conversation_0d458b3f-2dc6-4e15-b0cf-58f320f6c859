{"version": 3, "file": "_server.ts-DtLYWU-O.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/profiles/_id_/publish/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../../chunks/prisma.js\";\nasync function POST({ params, request, locals }) {\n  try {\n    const session = locals.session;\n    if (!session?.user) {\n      return json({ success: false, error: \"Unauthorized\" }, { status: 401 });\n    }\n    const user = session.user;\n    const { id } = params;\n    if (!id) {\n      return json({ success: false, error: \"Profile ID is required\" }, { status: 400 });\n    }\n    const profile = await prisma.profile.findUnique({\n      where: {\n        id,\n        userId: user.id\n      }\n    });\n    if (!profile) {\n      return json({ success: false, error: \"Profile not found\" }, { status: 404 });\n    }\n    let updatedProfile = profile;\n    try {\n      updatedProfile = await prisma.profile.update({\n        where: {\n          id\n        },\n        data: {\n          published: true,\n          publishedAt: /* @__PURE__ */ new Date()\n        }\n      });\n    } catch (error) {\n      console.error(\"Error updating profile published status:\", error);\n    }\n    return json({ success: true, profile: updatedProfile });\n  } catch (error) {\n    console.error(\"Error publishing profile:\", error);\n    return json({ success: false, error: \"Failed to publish profile\" }, { status: 500 });\n  }\n}\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;AAEA,eAAe,IAAI,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE;AACjD,EAAE,IAAI;AACN,IAAI,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO;AAClC,IAAI,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE;AACxB,MAAM,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7E;AACA,IAAI,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI;AAC7B,IAAI,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;AACzB,IAAI,IAAI,CAAC,EAAE,EAAE;AACb,MAAM,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,wBAAwB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACvF;AACA,IAAI,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;AACpD,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE;AACV,QAAQ,MAAM,EAAE,IAAI,CAAC;AACrB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,OAAO,EAAE;AAClB,MAAM,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,mBAAmB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClF;AACA,IAAI,IAAI,cAAc,GAAG,OAAO;AAChC,IAAI,IAAI;AACR,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;AACnD,QAAQ,KAAK,EAAE;AACf,UAAU;AACV,SAAS;AACT,QAAQ,IAAI,EAAE;AACd,UAAU,SAAS,EAAE,IAAI;AACzB,UAAU,WAAW,kBAAkB,IAAI,IAAI;AAC/C;AACA,OAAO,CAAC;AACR,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC;AACtE;AACA,IAAI,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,cAAc,EAAE,CAAC;AAC3D,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC;AACrD,IAAI,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,2BAA2B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxF;AACA;;;;"}