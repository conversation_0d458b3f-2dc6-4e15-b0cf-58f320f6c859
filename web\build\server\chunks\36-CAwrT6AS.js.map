{"version": 3, "file": "36-CAwrT6AS.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/jobs/_page.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/36.js"], "sourcesContent": ["import \"../../../../chunks/auth.js\";\nimport { r as redirect } from \"../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../chunks/prisma.js\";\nasync function load({ locals, url }) {\n  const user = locals.user;\n  if (!user) throw redirect(302, \"/auth/sign-in\");\n  const searches = await prisma.jobSearch.findMany({\n    where: {\n      userId: user.id\n    },\n    orderBy: { createdAt: \"desc\" }\n  });\n  const title = url.searchParams.get(\"title\") || \"\";\n  const locations = url.searchParams.get(\"locations\")?.split(\",\").filter(Boolean) || [];\n  const location = url.searchParams.get(\"location\") || \"\";\n  const locationType = url.searchParams.get(\"locationType\")?.split(\",\").filter(Boolean) || [];\n  const experience = url.searchParams.get(\"experience\")?.split(\",\").filter(Boolean) || [];\n  let salary = url.searchParams.get(\"salary\") || \"\";\n  const state = url.searchParams.get(\"state\") || \"\";\n  const country = url.searchParams.get(\"country\") || \"US\";\n  const datePosted = url.searchParams.get(\"datePosted\") || \"\";\n  const easyApply = url.searchParams.get(\"easyApply\") === \"true\";\n  const companies = url.searchParams.get(\"companies\")?.split(\",\").filter(Boolean) || [];\n  const validSalaryValues = [\n    \"0-50000\",\n    \"50000-75000\",\n    \"75000-100000\",\n    \"100000-150000\",\n    \"150000+\",\n    \"\"\n  ];\n  if (!validSalaryValues.includes(salary)) {\n    salary = \"\";\n  }\n  return {\n    user,\n    searches,\n    searchParams: {\n      title,\n      locations,\n      location,\n      locationType,\n      experience,\n      salary,\n      state,\n      country,\n      datePosted,\n      easyApply,\n      companies\n    }\n  };\n}\nexport {\n  load\n};\n", "import * as server from '../entries/pages/dashboard/jobs/_page.server.ts.js';\n\nexport const index = 36;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/dashboard/jobs/_page.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/dashboard/jobs/+page.server.ts\";\nexport const imports = [\"_app/immutable/nodes/36.6ildDWrI.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/C6g8ubaU.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/B-Xjo-Yt.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/Btcx8l8F.js\",\"_app/immutable/chunks/FN1sk3P2.js\",\"_app/immutable/chunks/nZgk9enP.js\",\"_app/immutable/chunks/DjPYYl4Z.js\",\"_app/immutable/chunks/Cf6rS4LV.js\",\"_app/immutable/chunks/DV_57wcZ.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/u21ee2wt.js\",\"_app/immutable/chunks/C3w0v0gR.js\",\"_app/immutable/chunks/BvdI7LR8.js\",\"_app/immutable/chunks/DMTMHyMa.js\",\"_app/immutable/chunks/CzsE_FAw.js\",\"_app/immutable/chunks/5V1tIHTN.js\",\"_app/immutable/chunks/ncUU1dSD.js\",\"_app/immutable/chunks/CGK0g3x_.js\",\"_app/immutable/chunks/BfX7a-t9.js\",\"_app/immutable/chunks/BosuxZz1.js\",\"_app/immutable/chunks/CnMg5bH0.js\",\"_app/immutable/chunks/DX6rZLP_.js\",\"_app/immutable/chunks/D2egQzE8.js\",\"_app/immutable/chunks/Ntteq2n_.js\",\"_app/immutable/chunks/DrQfh6BY.js\",\"_app/immutable/chunks/DxW95yuQ.js\",\"_app/immutable/chunks/w80wGXGd.js\",\"_app/immutable/chunks/D-o7ybA5.js\",\"_app/immutable/chunks/XESq6qWN.js\",\"_app/immutable/chunks/OOsIR5sE.js\",\"_app/immutable/chunks/BaVT73bJ.js\",\"_app/immutable/chunks/DT9WCdWY.js\",\"_app/immutable/chunks/Bpi49Nrf.js\",\"_app/immutable/chunks/Cb-3cdbh.js\",\"_app/immutable/chunks/CIOgxH3l.js\",\"_app/immutable/chunks/DuoUhxYL.js\",\"_app/immutable/chunks/BJIrNhIJ.js\",\"_app/immutable/chunks/Bd3zs5C6.js\",\"_app/immutable/chunks/BjCTmJLi.js\",\"_app/immutable/chunks/Ci8yIwIB.js\",\"_app/immutable/chunks/3WmhYGjL.js\",\"_app/immutable/chunks/B1K98fMG.js\",\"_app/immutable/chunks/DM07Bv7T.js\",\"_app/immutable/chunks/B6TiSgAN.js\",\"_app/immutable/chunks/Dmwghw4a.js\",\"_app/immutable/chunks/BniYvUIG.js\",\"_app/immutable/chunks/DW5gea7N.js\",\"_app/immutable/chunks/B5tu6DNS.js\",\"_app/immutable/chunks/BwkAotBa.js\",\"_app/immutable/chunks/BBa424ah.js\",\"_app/immutable/chunks/D4f2twK-.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/BNEH2jqx.js\",\"_app/immutable/chunks/CfcZq63z.js\",\"_app/immutable/chunks/yW0TxTga.js\",\"_app/immutable/chunks/9r-6KH_O.js\",\"_app/immutable/chunks/CTn0v-X8.js\",\"_app/immutable/chunks/DMoa_yM9.js\",\"_app/immutable/chunks/tdzGgazS.js\",\"_app/immutable/chunks/CnpHcmx3.js\",\"_app/immutable/chunks/BKLOCbjP.js\",\"_app/immutable/chunks/D9yI7a4E.js\",\"_app/immutable/chunks/eW6QhNR3.js\",\"_app/immutable/chunks/B2lQHLf_.js\",\"_app/immutable/chunks/CVVv9lPb.js\",\"_app/immutable/chunks/KVutzy_p.js\",\"_app/immutable/chunks/CKh8VGVX.js\",\"_app/immutable/chunks/VYoCKyli.js\",\"_app/immutable/chunks/DaBofrVv.js\",\"_app/immutable/chunks/CIPPbbaT.js\",\"_app/immutable/chunks/CwgkX8t9.js\",\"_app/immutable/chunks/6BxQgNmX.js\",\"_app/immutable/chunks/CDnvByek.js\",\"_app/immutable/chunks/-SpbofVw.js\",\"_app/immutable/chunks/DYwWIJ9y.js\",\"_app/immutable/chunks/BAawoUIy.js\",\"_app/immutable/chunks/BM9SsHQg.js\",\"_app/immutable/chunks/FAbXdqfL.js\",\"_app/immutable/chunks/C2MdR6K0.js\",\"_app/immutable/chunks/hQ6uUXJy.js\",\"_app/immutable/chunks/BhzFx1Wy.js\"];\nexport const stylesheets = [\"_app/immutable/assets/Toaster.DKF17Rty.css\",\"_app/immutable/assets/index.CV-KWLNP.css\",\"_app/immutable/assets/scroll-area.bHHIbcsu.css\",\"_app/immutable/assets/36.m2vV48tT.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;AAGA,eAAe,IAAI,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE;AACrC,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE,MAAM,QAAQ,CAAC,GAAG,EAAE,eAAe,CAAC;AACjD,EAAE,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;AACnD,IAAI,KAAK,EAAE;AACX,MAAM,MAAM,EAAE,IAAI,CAAC;AACnB,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM;AAChC,GAAG,CAAC;AACJ,EAAE,MAAM,KAAK,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;AACnD,EAAE,MAAM,SAAS,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE;AACvF,EAAE,MAAM,QAAQ,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE;AACzD,EAAE,MAAM,YAAY,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE;AAC7F,EAAE,MAAM,UAAU,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE;AACzF,EAAE,IAAI,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE;AACnD,EAAE,MAAM,KAAK,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;AACnD,EAAE,MAAM,OAAO,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,IAAI;AACzD,EAAE,MAAM,UAAU,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE;AAC7D,EAAE,MAAM,SAAS,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,KAAK,MAAM;AAChE,EAAE,MAAM,SAAS,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE;AACvF,EAAE,MAAM,iBAAiB,GAAG;AAC5B,IAAI,SAAS;AACb,IAAI,aAAa;AACjB,IAAI,cAAc;AAClB,IAAI,eAAe;AACnB,IAAI,SAAS;AACb,IAAI;AACJ,GAAG;AACH,EAAE,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;AAC3C,IAAI,MAAM,GAAG,EAAE;AACf;AACA,EAAE,OAAO;AACT,IAAI,IAAI;AACR,IAAI,QAAQ;AACZ,IAAI,YAAY,EAAE;AAClB,MAAM,KAAK;AACX,MAAM,SAAS;AACf,MAAM,QAAQ;AACd,MAAM,YAAY;AAClB,MAAM,UAAU;AAChB,MAAM,MAAM;AACZ,MAAM,KAAK;AACX,MAAM,OAAO;AACb,MAAM,UAAU;AAChB,MAAM,SAAS;AACf,MAAM;AACN;AACA,GAAG;AACH;;;;;;;ACjDY,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAAiD,CAAC,EAAE;AAE/G,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACriG,MAAC,WAAW,GAAG,CAAC,4CAA4C,CAAC,0CAA0C,CAAC,gDAAgD,CAAC,uCAAuC;AAChM,MAAC,KAAK,GAAG;;;;"}