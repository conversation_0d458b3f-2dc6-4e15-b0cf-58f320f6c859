{"version": 3, "file": "_server.ts-B1niV8hL.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/email/queue-status/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { l as logger } from \"../../../../../chunks/logger.js\";\nimport { g as getRedisClient } from \"../../../../../chunks/redis.js\";\nasync function GET() {\n  try {\n    logger.info(\"Fetching email queue status\");\n    const redis = await getRedisClient();\n    if (!redis) {\n      return json({ error: \"Redis client not available\" }, { status: 500 });\n    }\n    const queueLength = await redis.llen(\"email:queue\");\n    const processingCount = await redis.scard(\"email:processing\");\n    const completedCount = await redis.scard(\"email:completed\");\n    const failedCount = await redis.scard(\"email:failed\");\n    const recentJobs = await redis.lrange(\"email:queue\", 0, 9);\n    const parsedJobs = recentJobs.map((job) => {\n      try {\n        return JSON.parse(job);\n      } catch (error) {\n        return { error: \"Failed to parse job\" };\n      }\n    });\n    return json({\n      status: \"success\",\n      queue: {\n        waiting: queueLength,\n        processing: processingCount,\n        completed: completedCount,\n        failed: failedCount\n      },\n      recentJobs: parsedJobs\n    });\n  } catch (error) {\n    logger.error(\"Error fetching email queue status:\", error);\n    return json({ error: \"Failed to fetch email queue status\" }, { status: 500 });\n  }\n}\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;;AAGA,eAAe,GAAG,GAAG;AACrB,EAAE,IAAI;AACN,IAAI,MAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC;AAC9C,IAAI,MAAM,KAAK,GAAG,MAAM,cAAc,EAAE;AACxC,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,4BAA4B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3E;AACA,IAAI,MAAM,WAAW,GAAG,MAAM,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC;AACvD,IAAI,MAAM,eAAe,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC,kBAAkB,CAAC;AACjE,IAAI,MAAM,cAAc,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC,iBAAiB,CAAC;AAC/D,IAAI,MAAM,WAAW,GAAG,MAAM,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC;AACzD,IAAI,MAAM,UAAU,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,EAAE,CAAC,CAAC;AAC9D,IAAI,MAAM,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;AAC/C,MAAM,IAAI;AACV,QAAQ,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;AAC9B,OAAO,CAAC,OAAO,KAAK,EAAE;AACtB,QAAQ,OAAO,EAAE,KAAK,EAAE,qBAAqB,EAAE;AAC/C;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,KAAK,EAAE;AACb,QAAQ,OAAO,EAAE,WAAW;AAC5B,QAAQ,UAAU,EAAE,eAAe;AACnC,QAAQ,SAAS,EAAE,cAAc;AACjC,QAAQ,MAAM,EAAE;AAChB,OAAO;AACP,MAAM,UAAU,EAAE;AAClB,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC;AAC7D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oCAAoC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjF;AACA;;;;"}