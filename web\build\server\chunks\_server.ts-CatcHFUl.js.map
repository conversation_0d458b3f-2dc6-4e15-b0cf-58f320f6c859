{"version": 3, "file": "_server.ts-CatcHFUl.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/documents/_server.ts.js"], "sourcesContent": ["import { p as prisma } from \"../../../../chunks/prisma.js\";\nconst GET = async ({ url, locals }) => {\n  const user = locals.user;\n  if (!user) return new Response(\"Unauthorized\", { status: 401 });\n  const type = url.searchParams.get(\"type\");\n  console.log(\"Document type requested:\", type);\n  try {\n    console.log(\"Fetching documents for user:\", user.id, \"with type:\", type);\n    const documents = await prisma.document.findMany({\n      where: {\n        userId: user.id,\n        ...type ? { type } : {}\n      },\n      orderBy: {\n        createdAt: \"desc\"\n      },\n      include: {\n        resume: {\n          select: {\n            id: true,\n            isParsed: true,\n            parsedAt: true\n          }\n        }\n      }\n    });\n    console.log(`Found ${documents.length} documents`);\n    return new Response(\n      JSON.stringify({\n        documents\n      }),\n      {\n        headers: { \"Content-Type\": \"application/json\" }\n      }\n    );\n  } catch (error) {\n    console.error(\"Error fetching documents:\", error);\n    return new Response(\n      JSON.stringify({\n        error: error instanceof Error ? error.message : \"Unknown error\",\n        stack: error instanceof Error ? error.stack : void 0\n      }),\n      {\n        status: 500,\n        headers: { \"Content-Type\": \"application/json\" }\n      }\n    );\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;AACK,MAAC,GAAG,GAAG,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK;AACvC,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE,EAAE,MAAM,IAAI,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC;AAC3C,EAAE,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAC;AAC/C,EAAE,IAAI;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC,EAAE,EAAE,YAAY,EAAE,IAAI,CAAC;AAC5E,IAAI,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;AACrD,MAAM,KAAK,EAAE;AACb,QAAQ,MAAM,EAAE,IAAI,CAAC,EAAE;AACvB,QAAQ,GAAG,IAAI,GAAG,EAAE,IAAI,EAAE,GAAG;AAC7B,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,SAAS,EAAE;AACnB,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,MAAM,EAAE;AAChB,UAAU,MAAM,EAAE;AAClB,YAAY,EAAE,EAAE,IAAI;AACpB,YAAY,QAAQ,EAAE,IAAI;AAC1B,YAAY,QAAQ,EAAE;AACtB;AACA;AACA;AACA,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;AACtD,IAAI,OAAO,IAAI,QAAQ;AACvB,MAAM,IAAI,CAAC,SAAS,CAAC;AACrB,QAAQ;AACR,OAAO,CAAC;AACR,MAAM;AACN,QAAQ,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB;AACrD;AACA,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC;AACrD,IAAI,OAAO,IAAI,QAAQ;AACvB,MAAM,IAAI,CAAC,SAAS,CAAC;AACrB,QAAQ,KAAK,EAAE,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,eAAe;AACvE,QAAQ,KAAK,EAAE,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG;AACtD,OAAO,CAAC;AACR,MAAM;AACN,QAAQ,MAAM,EAAE,GAAG;AACnB,QAAQ,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB;AACrD;AACA,KAAK;AACL;AACA;;;;"}