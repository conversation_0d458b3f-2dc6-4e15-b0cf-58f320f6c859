{"version": 3, "file": "_server.ts-DhnzsR3Y.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/ai/ats/analyze/_resumeId_/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../../../../../chunks/auth.js\";\nconst GET = async ({ params, cookies }) => {\n  try {\n    const token = cookies.get(\"auth_token\");\n    if (!token) {\n      return json({ error: \"Unauthorized\" }, { status: 401 });\n    }\n    const tokenData = verifySessionToken(token);\n    if (!tokenData || !tokenData.id) {\n      return json({ error: \"Invalid token\" }, { status: 401 });\n    }\n    const userId = tokenData.id;\n    const resumeId = params.resumeId;\n    if (!resumeId) {\n      return json({ error: \"Missing resume ID\" }, { status: 400 });\n    }\n    const resume = await prisma.resume.findFirst({\n      where: {\n        id: resumeId,\n        document: {\n          userId\n        }\n      }\n    });\n    if (!resume) {\n      return json({ error: \"Resume not found or access denied\" }, { status: 404 });\n    }\n    const user = await prisma.user.findUnique({\n      where: { id: userId },\n      include: {\n        subscriptions: {\n          where: { status: \"active\" },\n          include: {\n            plan: {\n              include: {\n                features: {\n                  where: { featureId: \"resume_analysis\" }\n                }\n              }\n            }\n          }\n        }\n      }\n    });\n    const hasAccess = user?.subscriptions.some(\n      (sub) => sub.plan.features.some((feature) => feature.featureId === \"resume_analysis\")\n    );\n    if (!hasAccess && process.env.NODE_ENV === \"production\") {\n      return json({ error: \"Feature not available in your plan\" }, { status: 403 });\n    }\n    let analysis = await prisma.atsAnalysis.findUnique({\n      where: { resumeId }\n    });\n    if (!analysis) {\n      const mockAnalysis = generateMockATSAnalysis();\n      analysis = await prisma.atsAnalysis.create({\n        data: {\n          id: `ats-${Date.now()}`,\n          resumeId,\n          overallScore: mockAnalysis.overallScore,\n          keywordScore: mockAnalysis.keywordScore,\n          formatScore: mockAnalysis.formatScore,\n          contentScore: mockAnalysis.contentScore,\n          readabilityScore: mockAnalysis.readabilityScore,\n          detectedIssues: mockAnalysis.detectedIssues,\n          suggestedKeywords: mockAnalysis.suggestedKeywords,\n          analysisDetails: mockAnalysis.analysisDetails,\n          createdAt: /* @__PURE__ */ new Date(),\n          updatedAt: /* @__PURE__ */ new Date()\n        }\n      });\n    }\n    return json({ success: true, analysis });\n  } catch (error) {\n    console.error(\"Error getting ATS analysis:\", error);\n    return json({ error: \"Internal server error\" }, { status: 500 });\n  }\n};\nfunction generateMockATSAnalysis() {\n  return {\n    overallScore: 75,\n    keywordScore: 80,\n    formatScore: 70,\n    contentScore: 85,\n    readabilityScore: 65,\n    detectedIssues: [\n      { section: \"format\", message: \"Resume exceeds one page\", severity: \"medium\" },\n      { section: \"content\", message: \"Summary section is too generic\", severity: \"medium\" },\n      { section: \"readability\", message: \"Some sentences are too long\", severity: \"low\" }\n    ],\n    suggestedKeywords: [\n      \"JavaScript\",\n      \"React\",\n      \"Node.js\",\n      \"TypeScript\",\n      \"REST API\",\n      \"GraphQL\",\n      \"CI/CD\",\n      \"Agile\",\n      \"AWS\",\n      \"Docker\"\n    ],\n    analysisDetails: {\n      format: {\n        pageCount: 2,\n        sectionCount: 5,\n        hasProperHeadings: true,\n        hasBulletPoints: true\n      },\n      content: {\n        hasQuantifiableResults: true,\n        hasActionVerbs: true,\n        hasTooManyBuzzwords: false\n      },\n      readability: {\n        averageSentenceLength: 15,\n        complexWordPercentage: 12,\n        passiveVoiceCount: 3\n      }\n    }\n  };\n}\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;;;;;;AAGK,MAAC,GAAG,GAAG,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK;AAC3C,EAAE,IAAI;AACN,IAAI,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AAC3C,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7D;AACA,IAAI,MAAM,SAAS,GAAG,kBAAkB,CAAC,KAAK,CAAC;AAC/C,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE;AACrC,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9D;AACA,IAAI,MAAM,MAAM,GAAG,SAAS,CAAC,EAAE;AAC/B,IAAI,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ;AACpC,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE;AACA,IAAI,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC;AACjD,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE,QAAQ;AACpB,QAAQ,QAAQ,EAAE;AAClB,UAAU;AACV;AACA;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mCAAmC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClF;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAC9C,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;AAC3B,MAAM,OAAO,EAAE;AACf,QAAQ,aAAa,EAAE;AACvB,UAAU,KAAK,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE;AACrC,UAAU,OAAO,EAAE;AACnB,YAAY,IAAI,EAAE;AAClB,cAAc,OAAO,EAAE;AACvB,gBAAgB,QAAQ,EAAE;AAC1B,kBAAkB,KAAK,EAAE,EAAE,SAAS,EAAE,iBAAiB;AACvD;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,CAAC;AACN,IAAI,MAAM,SAAS,GAAG,IAAI,EAAE,aAAa,CAAC,IAAI;AAC9C,MAAM,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,SAAS,KAAK,iBAAiB;AAC1F,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE;AAC7D,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oCAAoC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnF;AACA,IAAI,IAAI,QAAQ,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;AACvD,MAAM,KAAK,EAAE,EAAE,QAAQ;AACvB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,MAAM,YAAY,GAAG,uBAAuB,EAAE;AACpD,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;AACjD,QAAQ,IAAI,EAAE;AACd,UAAU,EAAE,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;AACjC,UAAU,QAAQ;AAClB,UAAU,YAAY,EAAE,YAAY,CAAC,YAAY;AACjD,UAAU,YAAY,EAAE,YAAY,CAAC,YAAY;AACjD,UAAU,WAAW,EAAE,YAAY,CAAC,WAAW;AAC/C,UAAU,YAAY,EAAE,YAAY,CAAC,YAAY;AACjD,UAAU,gBAAgB,EAAE,YAAY,CAAC,gBAAgB;AACzD,UAAU,cAAc,EAAE,YAAY,CAAC,cAAc;AACrD,UAAU,iBAAiB,EAAE,YAAY,CAAC,iBAAiB;AAC3D,UAAU,eAAe,EAAE,YAAY,CAAC,eAAe;AACvD,UAAU,SAAS,kBAAkB,IAAI,IAAI,EAAE;AAC/C,UAAU,SAAS,kBAAkB,IAAI,IAAI;AAC7C;AACA,OAAO,CAAC;AACR;AACA,IAAI,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;AAC5C,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC;AACvD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpE;AACA;AACA,SAAS,uBAAuB,GAAG;AACnC,EAAE,OAAO;AACT,IAAI,YAAY,EAAE,EAAE;AACpB,IAAI,YAAY,EAAE,EAAE;AACpB,IAAI,WAAW,EAAE,EAAE;AACnB,IAAI,YAAY,EAAE,EAAE;AACpB,IAAI,gBAAgB,EAAE,EAAE;AACxB,IAAI,cAAc,EAAE;AACpB,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,yBAAyB,EAAE,QAAQ,EAAE,QAAQ,EAAE;AACnF,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,gCAAgC,EAAE,QAAQ,EAAE,QAAQ,EAAE;AAC3F,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,OAAO,EAAE,6BAA6B,EAAE,QAAQ,EAAE,KAAK;AACvF,KAAK;AACL,IAAI,iBAAiB,EAAE;AACvB,MAAM,YAAY;AAClB,MAAM,OAAO;AACb,MAAM,SAAS;AACf,MAAM,YAAY;AAClB,MAAM,UAAU;AAChB,MAAM,SAAS;AACf,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,KAAK;AACX,MAAM;AACN,KAAK;AACL,IAAI,eAAe,EAAE;AACrB,MAAM,MAAM,EAAE;AACd,QAAQ,SAAS,EAAE,CAAC;AACpB,QAAQ,YAAY,EAAE,CAAC;AACvB,QAAQ,iBAAiB,EAAE,IAAI;AAC/B,QAAQ,eAAe,EAAE;AACzB,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,sBAAsB,EAAE,IAAI;AACpC,QAAQ,cAAc,EAAE,IAAI;AAC5B,QAAQ,mBAAmB,EAAE;AAC7B,OAAO;AACP,MAAM,WAAW,EAAE;AACnB,QAAQ,qBAAqB,EAAE,EAAE;AACjC,QAAQ,qBAAqB,EAAE,EAAE;AACjC,QAAQ,iBAAiB,EAAE;AAC3B;AACA;AACA,GAAG;AACH;;;;"}