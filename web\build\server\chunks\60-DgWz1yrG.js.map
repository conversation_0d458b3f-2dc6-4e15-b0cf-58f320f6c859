{"version": 3, "file": "60-DgWz1yrG.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/settings/email/_page.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/60.js"], "sourcesContent": ["import { r as redirect } from \"../../../../../chunks/index.js\";\nconst load = async () => {\n  throw redirect(302, \"/dashboard/settings/admin/email\");\n};\nexport {\n  load\n};\n", "import * as server from '../entries/pages/dashboard/settings/email/_page.server.ts.js';\n\nexport const index = 60;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/dashboard/settings/email/_page.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/dashboard/settings/email/+page.server.ts\";\nexport const imports = [\"_app/immutable/nodes/60.o8OjGAq7.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/nZgk9enP.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/FN1sk3P2.js\"];\nexport const stylesheets = [];\nexport const fonts = [];\n"], "names": [], "mappings": ";;AACA,MAAM,IAAI,GAAG,YAAY;AACzB,EAAE,MAAM,QAAQ,CAAC,GAAG,EAAE,iCAAiC,CAAC;AACxD,CAAC;;;;;;;ACDW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAA2D,CAAC,EAAE;AAEzH,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACzQ,MAAC,WAAW,GAAG;AACf,MAAC,KAAK,GAAG;;;;"}