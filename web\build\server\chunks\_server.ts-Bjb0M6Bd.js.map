{"version": 3, "file": "_server.ts-Bjb0M6Bd.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/admin/check-admin/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../../../chunks/auth.js\";\nconst GET = async ({ cookies }) => {\n  const token = cookies.get(\"auth_token\");\n  if (!token) {\n    return json({\n      isAuthenticated: false,\n      isAdmin: false,\n      error: \"Not authenticated\"\n    });\n  }\n  try {\n    const userData = await verifySessionToken(token);\n    if (!userData?.id) {\n      return json({\n        isAuthenticated: false,\n        isAdmin: false,\n        error: \"Invalid token\"\n      });\n    }\n    const user = await prisma.user.findUnique({\n      where: { id: userData.id },\n      select: {\n        id: true,\n        email: true,\n        role: true,\n        isAdmin: true\n      }\n    });\n    if (!user) {\n      return json({\n        isAuthenticated: true,\n        isAdmin: false,\n        error: \"User not found\"\n      });\n    }\n    return json({\n      isAuthenticated: true,\n      isAdmin: user.isAdmin === true || user.role === \"admin\",\n      user: {\n        id: user.id,\n        email: user.email,\n        role: user.role,\n        isAdmin: user.isAdmin\n      }\n    });\n  } catch (error) {\n    console.error(\"Error checking admin status:\", error);\n    return json(\n      {\n        isAuthenticated: false,\n        isAdmin: false,\n        error: \"Error checking admin status\"\n      },\n      { status: 500 }\n    );\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;;;;;;AAGK,MAAC,GAAG,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK;AACnC,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE;AACd,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,eAAe,EAAE,KAAK;AAC5B,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,KAAK,EAAE;AACb,KAAK,CAAC;AACN;AACA,EAAE,IAAI;AACN,IAAI,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AACpD,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE;AACvB,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,eAAe,EAAE,KAAK;AAC9B,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,KAAK,EAAE;AACf,OAAO,CAAC;AACR;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAC9C,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE;AAChC,MAAM,MAAM,EAAE;AACd,QAAQ,EAAE,EAAE,IAAI;AAChB,QAAQ,KAAK,EAAE,IAAI;AACnB,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,OAAO,EAAE;AACjB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,eAAe,EAAE,IAAI;AAC7B,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,KAAK,EAAE;AACf,OAAO,CAAC;AACR;AACA,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,eAAe,EAAE,IAAI;AAC3B,MAAM,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO;AAC7D,MAAM,IAAI,EAAE;AACZ,QAAQ,EAAE,EAAE,IAAI,CAAC,EAAE;AACnB,QAAQ,KAAK,EAAE,IAAI,CAAC,KAAK;AACzB,QAAQ,IAAI,EAAE,IAAI,CAAC,IAAI;AACvB,QAAQ,OAAO,EAAE,IAAI,CAAC;AACtB;AACA,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC;AACxD,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,eAAe,EAAE,KAAK;AAC9B,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,KAAK,EAAE;AACf,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;;;;"}