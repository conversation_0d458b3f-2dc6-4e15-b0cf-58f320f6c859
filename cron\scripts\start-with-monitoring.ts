#!/usr/bin/env tsx
// cron/scripts/start-with-monitoring.ts - Enhanced startup script with monitoring

import { spawn } from "child_process";
import { logger } from "../utils/logger";
import os from "os";

// Configuration
const MEMORY_CHECK_INTERVAL = 60000; // 1 minute
const RESTART_DELAY = 5000; // 5 seconds
const MAX_RESTARTS = 5;
const RESTART_WINDOW = 300000; // 5 minutes

let restartCount = 0;
let lastRestartTime = 0;

function getSystemInfo() {
  const memoryUsage = process.memoryUsage();
  const totalMemory = os.totalmem();
  const freeMemory = os.freemem();
  const usedMemory = totalMemory - freeMemory;
  
  return {
    memory: {
      rss: Math.round(memoryUsage.rss / 1024 / 1024),
      heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
      heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
      external: Math.round(memoryUsage.external / 1024 / 1024),
    },
    system: {
      totalMemory: Math.round(totalMemory / 1024 / 1024),
      freeMemory: Math.round(freeMemory / 1024 / 1024),
      usedMemory: Math.round(usedMemory / 1024 / 1024),
      memoryUsagePercent: Math.round((usedMemory / totalMemory) * 100),
      loadAverage: os.loadavg(),
      uptime: Math.round(os.uptime()),
    }
  };
}

function shouldRestart(): boolean {
  const now = Date.now();
  
  // Reset restart count if enough time has passed
  if (now - lastRestartTime > RESTART_WINDOW) {
    restartCount = 0;
  }
  
  if (restartCount >= MAX_RESTARTS) {
    logger.error(`❌ Maximum restart attempts (${MAX_RESTARTS}) reached within ${RESTART_WINDOW / 1000} seconds`);
    return false;
  }
  
  return true;
}

function startScheduler(): Promise<void> {
  return new Promise((resolve, reject) => {
    logger.info("🚀 Starting scheduler service with monitoring...");
    
    const systemInfo = getSystemInfo();
    logger.info(`📊 System info at startup: ${JSON.stringify(systemInfo, null, 2)}`);
    
    // Start the main scheduler process
    const child = spawn("tsx", ["index.ts"], {
      stdio: "inherit",
      cwd: process.cwd(),
      env: {
        ...process.env,
        NODE_OPTIONS: "--max-old-space-size=1024", // Limit memory to 1GB
      }
    });
    
    // Monitor memory usage
    const memoryMonitor = setInterval(() => {
      const info = getSystemInfo();
      
      // Log memory usage every 5 minutes
      if (Date.now() % (5 * 60 * 1000) < MEMORY_CHECK_INTERVAL) {
        logger.info(`📊 Memory usage: ${info.memory.rss}MB RSS, ${info.system.memoryUsagePercent}% system memory used`);
      }
      
      // Check for memory issues
      if (info.system.memoryUsagePercent > 90) {
        logger.warn(`⚠️ High memory usage detected: ${info.system.memoryUsagePercent}%`);
      }
      
      if (info.memory.rss > 800) { // 800MB
        logger.warn(`⚠️ High process memory usage: ${info.memory.rss}MB`);
      }
    }, MEMORY_CHECK_INTERVAL);
    
    child.on("exit", (code, signal) => {
      clearInterval(memoryMonitor);
      
      logger.info(`📊 Process exited with code ${code}, signal ${signal}`);
      
      if (code === 0) {
        logger.info("✅ Scheduler service exited normally");
        resolve();
        return;
      }
      
      if (signal === "SIGTERM" || signal === "SIGKILL") {
        logger.warn(`⚠️ Process was terminated by signal ${signal}`);
        
        if (shouldRestart()) {
          restartCount++;
          lastRestartTime = Date.now();
          
          logger.info(`🔄 Attempting restart ${restartCount}/${MAX_RESTARTS} in ${RESTART_DELAY / 1000} seconds...`);
          
          setTimeout(() => {
            startScheduler().then(resolve).catch(reject);
          }, RESTART_DELAY);
        } else {
          reject(new Error(`Process terminated and restart limit reached`));
        }
      } else {
        logger.error(`❌ Process exited with error code ${code}`);
        reject(new Error(`Process exited with code ${code}`));
      }
    });
    
    child.on("error", (error) => {
      clearInterval(memoryMonitor);
      logger.error("❌ Failed to start scheduler process:", error);
      reject(error);
    });
    
    // Handle our own signals
    process.on("SIGINT", () => {
      logger.info("🛑 Received SIGINT, stopping monitoring and child process...");
      clearInterval(memoryMonitor);
      child.kill("SIGINT");
    });
    
    process.on("SIGTERM", () => {
      logger.info("🛑 Received SIGTERM, stopping monitoring and child process...");
      clearInterval(memoryMonitor);
      child.kill("SIGTERM");
    });
  });
}

// Start the monitoring wrapper
async function main() {
  try {
    await startScheduler();
    logger.info("✅ Scheduler monitoring completed successfully");
    process.exit(0);
  } catch (error) {
    logger.error("❌ Scheduler monitoring failed:", error);
    process.exit(1);
  }
}

// Only run if this script is executed directly
if (require.main === module) {
  main();
}
