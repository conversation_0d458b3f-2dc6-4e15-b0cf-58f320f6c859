import { f as fail } from './index-Ddp2AB5f.js';
import { s as superValidate, z as zod } from './zod-DfpldWlD.js';
import { o as objectType, s as stringType } from './types-D78SXuvm.js';
import './constants-BaiUsPxc.js';
import './_commonjsHelpers-BFTU3MAI.js';

const contactFormSchema = objectType({
  name: stringType().min(2, { message: "Name must be at least 2 characters" }),
  email: stringType().email({ message: "Please enter a valid email address" }),
  department: stringType({ required_error: "Please select a department" }),
  subject: stringType().min(5, { message: "Subject must be at least 5 characters" }),
  message: stringType().min(10, { message: "Message must be at least 10 characters" })
});
const load = async () => {
  const form = await superValidate(zod(contactFormSchema));
  return { form };
};
const actions = {
  default: async ({ request }) => {
    const form = await superValidate(request, zod(contactFormSchema));
    if (!form.valid) {
      return fail(400, { form });
    }
    console.log("Form submission:", form.data);
    const departmentMap = {
      general: "General Inquiries",
      support: "Technical Support",
      sales: "Sales",
      partnerships: "Partnerships",
      careers: "Careers",
      press: "Press & Media",
      legal: "Legal"
    };
    const departmentName = departmentMap[form.data.department] || form.data.department;
    console.log(`Department: ${departmentName}`);
    await new Promise((resolve) => setTimeout(resolve, 1e3));
    return { form };
  }
};

var _page_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  actions: actions,
  load: load
});

const index = 25;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-DjHkRTFT.js')).default;
const server_id = "src/routes/contact/+page.server.ts";
const imports = ["_app/immutable/nodes/25.hWBl6GlH.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/C3w0v0gR.js","_app/immutable/chunks/BvdI7LR8.js","_app/immutable/chunks/DDUgF6Ik.js","_app/immutable/chunks/B-Xjo-Yt.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/DjPYYl4Z.js","_app/immutable/chunks/C6g8ubaU.js","_app/immutable/chunks/B1K98fMG.js","_app/immutable/chunks/ncUU1dSD.js","_app/immutable/chunks/5V1tIHTN.js","_app/immutable/chunks/DM07Bv7T.js","_app/immutable/chunks/DMTMHyMa.js","_app/immutable/chunks/CzsE_FAw.js","_app/immutable/chunks/VNuMAkuB.js","_app/immutable/chunks/BvvicRXk.js","_app/immutable/chunks/BfX7a-t9.js","_app/immutable/chunks/BosuxZz1.js","_app/immutable/chunks/CnMg5bH0.js","_app/immutable/chunks/e3H2jrNf.js","_app/immutable/chunks/BSkrKq6e.js","_app/immutable/chunks/FN1sk3P2.js","_app/immutable/chunks/NEMeLqAU.js","_app/immutable/chunks/QtAhPN2H.js","_app/immutable/chunks/BBa424ah.js","_app/immutable/chunks/D4f2twK-.js","_app/immutable/chunks/w80wGXGd.js","_app/immutable/chunks/yPulTJ2h.js","_app/immutable/chunks/CsOU4yHs.js","_app/immutable/chunks/BQS6hE8b.js","_app/immutable/chunks/D871oxnv.js","_app/immutable/chunks/Cs0qIT7f.js"];
const stylesheets = ["_app/immutable/assets/Toaster.DKF17Rty.css"];
const fonts = [];

export { component, fonts, imports, index, _page_server_ts as server, server_id, stylesheets };
//# sourceMappingURL=25-CMPlQgJB.js.map
