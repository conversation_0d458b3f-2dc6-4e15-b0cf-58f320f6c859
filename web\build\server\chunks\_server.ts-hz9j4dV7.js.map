{"version": 3, "file": "_server.ts-hz9j4dV7.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/email/status/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { R as RedisConnection } from \"../../../../../chunks/redis.js\";\nimport { l as logger } from \"../../../../../chunks/logger.js\";\nconst statusChecks = /* @__PURE__ */ new Map();\nconst MAX_CHECKS_PER_MINUTE = 10;\nconst RATE_LIMIT_WINDOW = 60 * 1e3;\nasync function GET({ url, request, locals }) {\n  try {\n    const jobId = url.searchParams.get(\"id\");\n    if (!jobId) {\n      return json({ error: \"Job ID is required\" }, { status: 400 });\n    }\n    const clientIp = request.headers.get(\"x-forwarded-for\") || \"unknown\";\n    const rateKey = `${clientIp}:${jobId}`;\n    const now = Date.now();\n    if (!statusChecks.has(rateKey)) {\n      statusChecks.set(rateKey, { count: 1, lastReset: now });\n    } else {\n      const check = statusChecks.get(rateKey);\n      if (now - check.lastReset > RATE_LIMIT_WINDOW) {\n        check.count = 1;\n        check.lastReset = now;\n      } else if (check.count >= MAX_CHECKS_PER_MINUTE) {\n        return json(\n          {\n            error: \"Rate limit exceeded\",\n            message: \"Too many status checks. Please wait before trying again.\",\n            retryAfter: Math.ceil((check.lastReset + RATE_LIMIT_WINDOW - now) / 1e3)\n          },\n          {\n            status: 429,\n            headers: {\n              \"Retry-After\": Math.ceil(\n                (check.lastReset + RATE_LIMIT_WINDOW - now) / 1e3\n              ).toString()\n            }\n          }\n        );\n      } else {\n        check.count++;\n      }\n    }\n    if (!RedisConnection) {\n      return json(\n        {\n          status: \"unknown\",\n          message: \"Redis connection not available\",\n          jobId\n        },\n        { status: 500 }\n      );\n    }\n    const result = await RedisConnection.hget(\"email:results\", jobId);\n    const processing = await RedisConnection.hget(\"email:processing\", jobId);\n    const queueItems = await RedisConnection.zrange(\"email:queue\", 0, -1, \"WITHSCORES\");\n    let inQueue = false;\n    let queuePosition = -1;\n    let priority = 0;\n    for (let i = 0; i < queueItems.length; i += 2) {\n      const item = queueItems[i];\n      try {\n        const parsedItem = JSON.parse(item);\n        if (parsedItem.id === jobId) {\n          inQueue = true;\n          queuePosition = i / 2;\n          priority = parseInt(queueItems[i + 1]);\n          break;\n        }\n      } catch (e) {\n      }\n    }\n    if (result) {\n      const parsedResult = JSON.parse(result);\n      return json({\n        status: parsedResult.success ? \"completed\" : \"failed\",\n        result: parsedResult,\n        jobId\n      });\n    } else if (processing) {\n      const parsedProcessing = JSON.parse(processing);\n      return json({\n        status: \"processing\",\n        processing: parsedProcessing,\n        jobId,\n        startedAt: parsedProcessing.processingStartedAt\n      });\n    } else if (inQueue) {\n      return json({\n        status: \"queued\",\n        jobId,\n        position: queuePosition,\n        priority\n      });\n    } else {\n      return json({\n        status: \"not_found\",\n        message: \"Job not found in queue, processing, or results\",\n        jobId\n      });\n    }\n  } catch (error) {\n    logger.error(\"Error checking email status:\", error);\n    return json({ error: \"Failed to check email status\" }, { status: 500 });\n  }\n}\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;;AAGA,MAAM,YAAY,mBAAmB,IAAI,GAAG,EAAE;AAC9C,MAAM,qBAAqB,GAAG,EAAE;AAChC,MAAM,iBAAiB,GAAG,EAAE,GAAG,GAAG;AAClC,eAAe,GAAG,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE;AAC7C,EAAE,IAAI;AACN,IAAI,MAAM,KAAK,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC;AAC5C,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oBAAoB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnE;AACA,IAAI,MAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,SAAS;AACxE,IAAI,MAAM,OAAO,GAAG,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;AAC1C,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE;AAC1B,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;AACpC,MAAM,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC;AAC7D,KAAK,MAAM;AACX,MAAM,MAAM,KAAK,GAAG,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC;AAC7C,MAAM,IAAI,GAAG,GAAG,KAAK,CAAC,SAAS,GAAG,iBAAiB,EAAE;AACrD,QAAQ,KAAK,CAAC,KAAK,GAAG,CAAC;AACvB,QAAQ,KAAK,CAAC,SAAS,GAAG,GAAG;AAC7B,OAAO,MAAM,IAAI,KAAK,CAAC,KAAK,IAAI,qBAAqB,EAAE;AACvD,QAAQ,OAAO,IAAI;AACnB,UAAU;AACV,YAAY,KAAK,EAAE,qBAAqB;AACxC,YAAY,OAAO,EAAE,0DAA0D;AAC/E,YAAY,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,SAAS,GAAG,iBAAiB,GAAG,GAAG,IAAI,GAAG;AACnF,WAAW;AACX,UAAU;AACV,YAAY,MAAM,EAAE,GAAG;AACvB,YAAY,OAAO,EAAE;AACrB,cAAc,aAAa,EAAE,IAAI,CAAC,IAAI;AACtC,gBAAgB,CAAC,KAAK,CAAC,SAAS,GAAG,iBAAiB,GAAG,GAAG,IAAI;AAC9D,eAAe,CAAC,QAAQ;AACxB;AACA;AACA,SAAS;AACT,OAAO,MAAM;AACb,QAAQ,KAAK,CAAC,KAAK,EAAE;AACrB;AACA;AACA,IAAI,IAAI,CAAC,eAAe,EAAE;AAC1B,MAAM,OAAO,IAAI;AACjB,QAAQ;AACR,UAAU,MAAM,EAAE,SAAS;AAC3B,UAAU,OAAO,EAAE,gCAAgC;AACnD,UAAU;AACV,SAAS;AACT,QAAQ,EAAE,MAAM,EAAE,GAAG;AACrB,OAAO;AACP;AACA,IAAI,MAAM,MAAM,GAAG,MAAM,eAAe,CAAC,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC;AACrE,IAAI,MAAM,UAAU,GAAG,MAAM,eAAe,CAAC,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC;AAC5E,IAAI,MAAM,UAAU,GAAG,MAAM,eAAe,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,YAAY,CAAC;AACvF,IAAI,IAAI,OAAO,GAAG,KAAK;AACvB,IAAI,IAAI,aAAa,GAAG,CAAC,CAAC;AAC1B,IAAI,IAAI,QAAQ,GAAG,CAAC;AACpB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;AACnD,MAAM,MAAM,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC;AAChC,MAAM,IAAI;AACV,QAAQ,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;AAC3C,QAAQ,IAAI,UAAU,CAAC,EAAE,KAAK,KAAK,EAAE;AACrC,UAAU,OAAO,GAAG,IAAI;AACxB,UAAU,aAAa,GAAG,CAAC,GAAG,CAAC;AAC/B,UAAU,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAChD,UAAU;AACV;AACA,OAAO,CAAC,OAAO,CAAC,EAAE;AAClB;AACA;AACA,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;AAC7C,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,MAAM,EAAE,YAAY,CAAC,OAAO,GAAG,WAAW,GAAG,QAAQ;AAC7D,QAAQ,MAAM,EAAE,YAAY;AAC5B,QAAQ;AACR,OAAO,CAAC;AACR,KAAK,MAAM,IAAI,UAAU,EAAE;AAC3B,MAAM,MAAM,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;AACrD,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,MAAM,EAAE,YAAY;AAC5B,QAAQ,UAAU,EAAE,gBAAgB;AACpC,QAAQ,KAAK;AACb,QAAQ,SAAS,EAAE,gBAAgB,CAAC;AACpC,OAAO,CAAC;AACR,KAAK,MAAM,IAAI,OAAO,EAAE;AACxB,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,MAAM,EAAE,QAAQ;AACxB,QAAQ,KAAK;AACb,QAAQ,QAAQ,EAAE,aAAa;AAC/B,QAAQ;AACR,OAAO,CAAC;AACR,KAAK,MAAM;AACX,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,MAAM,EAAE,WAAW;AAC3B,QAAQ,OAAO,EAAE,gDAAgD;AACjE,QAAQ;AACR,OAAO,CAAC;AACR;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC;AACvD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,8BAA8B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3E;AACA;;;;"}