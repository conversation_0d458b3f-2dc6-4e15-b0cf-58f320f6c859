{"version": 3, "file": "_server.ts-C6NcCdLF.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/automation/runs/_id_/stop/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../../../chunks/prisma.js\";\nimport { g as getUserFromToken } from \"../../../../../../../chunks/auth.js\";\nimport { createClient } from \"redis\";\nconst POST = async ({ params, cookies }) => {\n  const user = getUserFromToken(cookies);\n  const { id } = params;\n  if (!user) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  try {\n    const automationRun = await prisma.automationRun.findFirst({\n      where: {\n        id,\n        OR: [\n          { userId: user.id },\n          {\n            profile: {\n              team: {\n                members: {\n                  some: { userId: user.id }\n                }\n              }\n            }\n          }\n        ]\n      }\n    });\n    if (!automationRun) {\n      return json({ error: \"Automation run not found\" }, { status: 404 });\n    }\n    if ([\"stopped\", \"completed\", \"failed\"].includes(automationRun.status)) {\n      return json({ message: \"Automation run already stopped\" });\n    }\n    try {\n      try {\n        const redisUrl = process.env.NODE_ENV === \"production\" ? \"rediss://red-cvmu1me3jp1c738ve7ig:<EMAIL>:6379\" : \"redis://localhost:6379\";\n        const redis = createClient({\n          url: redisUrl\n        });\n        await redis.connect();\n        await redis.publish(\n          \"automation:stop\",\n          JSON.stringify({\n            runId: automationRun.id,\n            userId: user.id\n          })\n        );\n        await redis.disconnect();\n        console.log(\"Sent stop request to Redis\");\n      } catch (redisError) {\n        console.error(\"Redis error, falling back to mock worker:\", redisError);\n        try {\n          const response = await fetch(\"http://localhost:3001/stop\", {\n            method: \"POST\",\n            headers: {\n              \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n              runId: automationRun.id,\n              userId: user.id\n            })\n          });\n          if (!response.ok) {\n            throw new Error(`Mock worker returned ${response.status}: ${response.statusText}`);\n          }\n          console.log(\"Sent stop request to mock worker\");\n        } catch (mockError) {\n          console.error(\"Mock worker error:\", mockError);\n        }\n      }\n    } catch (error) {\n      console.error(\"Error sending stop request:\", error);\n    }\n    const updatedRun = await prisma.automationRun.update({\n      where: { id },\n      data: {\n        status: \"stopped\",\n        stoppedAt: /* @__PURE__ */ new Date()\n      }\n    });\n    try {\n      const { createNotification } = await import(\"../../../../../../../chunks/notifications.js\");\n      await createNotification({\n        userId: user.id,\n        title: \"Automation Run Stopped\",\n        message: `Your automation run was stopped manually.`,\n        url: `/dashboard/automation/${id}`,\n        type: \"warning\",\n        data: {\n          automationRunId: id,\n          status: \"stopped\"\n        }\n      });\n      console.log(`[automation] Sent stop notification to user ${user.id}`);\n    } catch (notificationError) {\n      console.error(\"Error sending stop notification:\", notificationError);\n    }\n    return json(updatedRun);\n  } catch (error) {\n    console.error(\"Error stopping automation run:\", error);\n    return json({ error: \"Failed to stop automation run\" }, { status: 500 });\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAIK,MAAC,IAAI,GAAG,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK;AAC5C,EAAE,MAAM,IAAI,GAAG,gBAAgB,CAAC,OAAO,CAAC;AACxC,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;AACvB,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC;AAC/D,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE;AACV,QAAQ,EAAE,EAAE;AACZ,UAAU,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;AAC7B,UAAU;AACV,YAAY,OAAO,EAAE;AACrB,cAAc,IAAI,EAAE;AACpB,gBAAgB,OAAO,EAAE;AACzB,kBAAkB,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE;AACzC;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,aAAa,EAAE;AACxB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,0BAA0B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE;AACA,IAAI,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE;AAC3E,MAAM,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,gCAAgC,EAAE,CAAC;AAChE;AACA,IAAI,IAAI;AACR,MAAM,IAAI;AACV,QAAQ,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,GAAG,oGAAoG,GAAG,wBAAwB;AAChM,QAAQ,MAAM,KAAK,GAAG,YAAY,CAAC;AACnC,UAAU,GAAG,EAAE;AACf,SAAS,CAAC;AACV,QAAQ,MAAM,KAAK,CAAC,OAAO,EAAE;AAC7B,QAAQ,MAAM,KAAK,CAAC,OAAO;AAC3B,UAAU,iBAAiB;AAC3B,UAAU,IAAI,CAAC,SAAS,CAAC;AACzB,YAAY,KAAK,EAAE,aAAa,CAAC,EAAE;AACnC,YAAY,MAAM,EAAE,IAAI,CAAC;AACzB,WAAW;AACX,SAAS;AACT,QAAQ,MAAM,KAAK,CAAC,UAAU,EAAE;AAChC,QAAQ,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC;AACjD,OAAO,CAAC,OAAO,UAAU,EAAE;AAC3B,QAAQ,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,UAAU,CAAC;AAC9E,QAAQ,IAAI;AACZ,UAAU,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,4BAA4B,EAAE;AACrE,YAAY,MAAM,EAAE,MAAM;AAC1B,YAAY,OAAO,EAAE;AACrB,cAAc,cAAc,EAAE;AAC9B,aAAa;AACb,YAAY,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;AACjC,cAAc,KAAK,EAAE,aAAa,CAAC,EAAE;AACrC,cAAc,MAAM,EAAE,IAAI,CAAC;AAC3B,aAAa;AACb,WAAW,CAAC;AACZ,UAAU,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;AAC5B,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,qBAAqB,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;AAC9F;AACA,UAAU,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC;AACzD,SAAS,CAAC,OAAO,SAAS,EAAE;AAC5B,UAAU,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,SAAS,CAAC;AACxD;AACA;AACA,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC;AACzD;AACA,IAAI,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;AACzD,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE;AACnB,MAAM,IAAI,EAAE;AACZ,QAAQ,MAAM,EAAE,SAAS;AACzB,QAAQ,SAAS,kBAAkB,IAAI,IAAI;AAC3C;AACA,KAAK,CAAC;AACN,IAAI,IAAI;AACR,MAAM,MAAM,EAAE,kBAAkB,EAAE,GAAG,MAAM,OAAO,6BAA8C,CAAC;AACjG,MAAM,MAAM,kBAAkB,CAAC;AAC/B,QAAQ,MAAM,EAAE,IAAI,CAAC,EAAE;AACvB,QAAQ,KAAK,EAAE,wBAAwB;AACvC,QAAQ,OAAO,EAAE,CAAC,yCAAyC,CAAC;AAC5D,QAAQ,GAAG,EAAE,CAAC,sBAAsB,EAAE,EAAE,CAAC,CAAC;AAC1C,QAAQ,IAAI,EAAE,SAAS;AACvB,QAAQ,IAAI,EAAE;AACd,UAAU,eAAe,EAAE,EAAE;AAC7B,UAAU,MAAM,EAAE;AAClB;AACA,OAAO,CAAC;AACR,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,4CAA4C,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3E,KAAK,CAAC,OAAO,iBAAiB,EAAE;AAChC,MAAM,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,iBAAiB,CAAC;AAC1E;AACA,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC;AAC3B,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC;AAC1D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,+BAA+B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC5E;AACA;;;;"}