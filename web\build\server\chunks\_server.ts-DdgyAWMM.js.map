{"version": 3, "file": "_server.ts-DdgyAWMM.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/billing/data/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../../../chunks/auth.js\";\nimport { b as getPlanById, i as initializePlansInDatabase, g as getPlansFromDatabase } from \"../../../../../chunks/plan-sync.js\";\nasync function getBillingData(userId) {\n  const userData = await prisma.user.findUnique({\n    where: { id: userId },\n    include: {\n      subscriptions: {\n        orderBy: { createdAt: \"desc\" },\n        take: 1\n      }\n    }\n  });\n  if (!userData) {\n    throw new Error(\"User not found\");\n  }\n  let currentPlan = null;\n  try {\n    currentPlan = await getPlanById(userData.role || \"free\");\n    if (!currentPlan) {\n      console.warn(`Plan not found for role: ${userData.role}. Initializing plans in database.`);\n      await initializePlansInDatabase();\n      currentPlan = await getPlanById(userData.role || \"free\");\n      if (!currentPlan) {\n        console.error(`Plan still not found for role: ${userData.role} after initialization.`);\n        throw new Error(`Plan not found for role: ${userData.role}`);\n      }\n    }\n  } catch (error) {\n    console.error(\"Error getting plan from database:\", error);\n    throw error;\n  }\n  let paymentMethods = [];\n  let invoices = [];\n  let upcomingInvoice = null;\n  let subscriptionData = null;\n  if (userData.stripeCustomerId) {\n    const isProd = process.env.NODE_ENV === \"production\";\n    const stripeSecret = isProd ? process.env.STRIPE_SECRET_KEY_LIVE || \"sk_live_placeholder\" : process.env.STRIPE_SECRET_KEY_TEST || \"sk_test_placeholder\";\n    const Stripe = (await import(\"stripe\")).default;\n    const stripe = new Stripe(stripeSecret, {\n      apiVersion: \"2025-04-30.basil\"\n    });\n    try {\n      const paymentMethodsResponse = await stripe.paymentMethods.list({\n        customer: userData.stripeCustomerId,\n        type: \"card\"\n      });\n      paymentMethods = paymentMethodsResponse.data;\n      const customer = await stripe.customers.retrieve(userData.stripeCustomerId);\n      let defaultPaymentMethod = null;\n      if (customer && !(\"deleted\" in customer) && customer.invoice_settings?.default_payment_method) {\n        defaultPaymentMethod = customer.invoice_settings.default_payment_method;\n      }\n      paymentMethods = paymentMethods.map((method) => ({\n        ...method,\n        isDefault: method.id === defaultPaymentMethod\n      }));\n      const invoicesResponse = await stripe.invoices.list({\n        customer: userData.stripeCustomerId,\n        limit: 10\n      });\n      invoices = invoicesResponse.data;\n      try {\n        console.log(\"Skipping upcoming invoice retrieval due to API version changes\");\n        upcomingInvoice = null;\n      } catch (err) {\n        console.log(\"No upcoming invoice found:\", err);\n      }\n      const subscriptionsResponse = await stripe.subscriptions.list({\n        customer: userData.stripeCustomerId,\n        limit: 5,\n        expand: [\"data.default_payment_method\", \"data.items.data.price\"]\n      });\n      const subscription = subscriptionsResponse.data.find(\n        (sub) => sub.status === \"active\" || sub.status === \"trialing\"\n      );\n      if (subscription) {\n        const paymentMethodDetails = subscription.default_payment_method;\n        const subscriptionItem = subscription.items.data[0];\n        const price = subscriptionItem?.price;\n        const productId = price?.product;\n        const interval = price?.recurring?.interval || \"month\";\n        const intervalCount = price?.recurring?.interval_count || 1;\n        const billingCycle = interval === \"year\" || intervalCount > 1 ? \"annual\" : \"monthly\";\n        let productName = null;\n        let productDescription = null;\n        if (productId) {\n          try {\n            const product = await stripe.products.retrieve(productId);\n            productName = product.name;\n            productDescription = product.description;\n          } catch (error) {\n            console.error(\"Error fetching product details:\", error);\n          }\n        }\n        const sub = subscription;\n        console.log(\"Stripe subscription period data:\", {\n          start_snake: sub.current_period_start,\n          start_camel: sub.currentPeriodStart,\n          end_snake: sub.current_period_end,\n          end_camel: sub.currentPeriodEnd,\n          raw_subscription: sub\n        });\n        let startTimestamp = sub.currentPeriodStart || sub.current_period_start;\n        let endTimestamp = sub.currentPeriodEnd || sub.current_period_end;\n        if ((!startTimestamp || !endTimestamp) && subscription.items?.data?.length > 0) {\n          const firstItem = subscription.items.data[0];\n          if (firstItem.current_period_start && !startTimestamp) {\n            startTimestamp = firstItem.current_period_start;\n          }\n          if (firstItem.current_period_end && !endTimestamp) {\n            endTimestamp = firstItem.current_period_end;\n          }\n        }\n        const startDate = startTimestamp ? new Date(startTimestamp * 1e3) : null;\n        const endDate = endTimestamp ? new Date(endTimestamp * 1e3) : null;\n        const isCancelled = subscription.status === \"canceled\" || subscription.status === \"cancelled\";\n        let cancelledDate = null;\n        if (isCancelled && sub.canceled_at) {\n          cancelledDate = new Date(sub.canceled_at * 1e3);\n        }\n        subscriptionData = {\n          id: subscription.id,\n          status: subscription.status,\n          currentPeriodStart: startDate,\n          currentPeriodEnd: endDate,\n          // Also include the snake_case versions for compatibility\n          current_period_start: startDate,\n          current_period_end: endDate,\n          cancelAtPeriodEnd: sub.cancelAtPeriodEnd || sub.cancel_at_period_end,\n          // Include cancelled date if available\n          canceled_at: cancelledDate,\n          canceledAt: cancelledDate,\n          pause_collection: sub.pauseCollection || sub.pause_collection,\n          metadata: subscription.metadata || {},\n          paymentMethod: paymentMethodDetails,\n          items: subscription.items.data,\n          price: {\n            id: price?.id,\n            unitAmount: price?.unit_amount,\n            currency: price?.currency,\n            interval,\n            intervalCount,\n            billingCycle\n          },\n          product: {\n            id: productId,\n            name: productName,\n            description: productDescription\n          }\n        };\n        if (subscriptionData.product.name && (!currentPlan || currentPlan.id === \"free\")) {\n          try {\n            const allPlans = await getPlansFromDatabase();\n            const matchingPlan = allPlans.find(\n              (p) => p.name.toLowerCase() === subscriptionData.product.name.toLowerCase()\n            );\n            if (matchingPlan) {\n              currentPlan = matchingPlan;\n            }\n          } catch (error) {\n            console.error(\"Error finding matching plan:\", error);\n          }\n        }\n      }\n    } catch (error) {\n      console.error(\"Error fetching Stripe data:\", error);\n    }\n  }\n  return {\n    currentPlan,\n    subscription: subscriptionData,\n    paymentMethods,\n    invoices,\n    upcomingInvoice\n  };\n}\nconst GET = async ({ cookies }) => {\n  const token = cookies.get(\"auth_token\");\n  if (!token) {\n    return new Response(\"Unauthorized\", { status: 401 });\n  }\n  const userData = await verifySessionToken(token);\n  if (!userData?.id) {\n    return new Response(\"Unauthorized\", { status: 401 });\n  }\n  try {\n    const billingData = await getBillingData(userData.id);\n    return json(billingData);\n  } catch (error) {\n    console.error(\"Error fetching billing data:\", error);\n    return json({ error: \"Failed to fetch billing data\" }, { status: 500 });\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAIA,eAAe,cAAc,CAAC,MAAM,EAAE;AACtC,EAAE,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAChD,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;AACzB,IAAI,OAAO,EAAE;AACb,MAAM,aAAa,EAAE;AACrB,QAAQ,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;AACtC,QAAQ,IAAI,EAAE;AACd;AACA;AACA,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,QAAQ,EAAE;AACjB,IAAI,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC;AACrC;AACA,EAAE,IAAI,WAAW,GAAG,IAAI;AACxB,EAAE,IAAI;AACN,IAAI,WAAW,GAAG,MAAM,WAAW,CAAC,QAAQ,CAAC,IAAI,IAAI,MAAM,CAAC;AAC5D,IAAI,IAAI,CAAC,WAAW,EAAE;AACtB,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC,yBAAyB,EAAE,QAAQ,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;AAChG,MAAM,MAAM,yBAAyB,EAAE;AACvC,MAAM,WAAW,GAAG,MAAM,WAAW,CAAC,QAAQ,CAAC,IAAI,IAAI,MAAM,CAAC;AAC9D,MAAM,IAAI,CAAC,WAAW,EAAE;AACxB,QAAQ,OAAO,CAAC,KAAK,CAAC,CAAC,+BAA+B,EAAE,QAAQ,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;AAC9F,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,yBAAyB,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;AACpE;AACA;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC;AAC7D,IAAI,MAAM,KAAK;AACf;AACA,EAAE,IAAI,cAAc,GAAG,EAAE;AACzB,EAAE,IAAI,QAAQ,GAAG,EAAE;AACnB,EAAE,IAAI,eAAe,GAAG,IAAI;AAC5B,EAAE,IAAI,gBAAgB,GAAG,IAAI;AAC7B,EAAE,IAAI,QAAQ,CAAC,gBAAgB,EAAE;AACjC,IAAI,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;AACxD,IAAI,MAAM,YAAY,GAAG,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,qBAAqB,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,qBAAqB;AAC3J,IAAI,MAAM,MAAM,GAAG,CAAC,MAAM,OAAO,+BAAQ,CAAC,EAAE,OAAO;AACnD,IAAI,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,YAAY,EAAE;AAC5C,MAAM,UAAU,EAAE;AAClB,KAAK,CAAC;AACN,IAAI,IAAI;AACR,MAAM,MAAM,sBAAsB,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC;AACtE,QAAQ,QAAQ,EAAE,QAAQ,CAAC,gBAAgB;AAC3C,QAAQ,IAAI,EAAE;AACd,OAAO,CAAC;AACR,MAAM,cAAc,GAAG,sBAAsB,CAAC,IAAI;AAClD,MAAM,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,gBAAgB,CAAC;AACjF,MAAM,IAAI,oBAAoB,GAAG,IAAI;AACrC,MAAM,IAAI,QAAQ,IAAI,EAAE,SAAS,IAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,gBAAgB,EAAE,sBAAsB,EAAE;AACrG,QAAQ,oBAAoB,GAAG,QAAQ,CAAC,gBAAgB,CAAC,sBAAsB;AAC/E;AACA,MAAM,cAAc,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,MAAM;AACvD,QAAQ,GAAG,MAAM;AACjB,QAAQ,SAAS,EAAE,MAAM,CAAC,EAAE,KAAK;AACjC,OAAO,CAAC,CAAC;AACT,MAAM,MAAM,gBAAgB,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;AAC1D,QAAQ,QAAQ,EAAE,QAAQ,CAAC,gBAAgB;AAC3C,QAAQ,KAAK,EAAE;AACf,OAAO,CAAC;AACR,MAAM,QAAQ,GAAG,gBAAgB,CAAC,IAAI;AACtC,MAAM,IAAI;AACV,QAAQ,OAAO,CAAC,GAAG,CAAC,gEAAgE,CAAC;AACrF,QAAQ,eAAe,GAAG,IAAI;AAC9B,OAAO,CAAC,OAAO,GAAG,EAAE;AACpB,QAAQ,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE,GAAG,CAAC;AACtD;AACA,MAAM,MAAM,qBAAqB,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC;AACpE,QAAQ,QAAQ,EAAE,QAAQ,CAAC,gBAAgB;AAC3C,QAAQ,KAAK,EAAE,CAAC;AAChB,QAAQ,MAAM,EAAE,CAAC,6BAA6B,EAAE,uBAAuB;AACvE,OAAO,CAAC;AACR,MAAM,MAAM,YAAY,GAAG,qBAAqB,CAAC,IAAI,CAAC,IAAI;AAC1D,QAAQ,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,QAAQ,IAAI,GAAG,CAAC,MAAM,KAAK;AAC3D,OAAO;AACP,MAAM,IAAI,YAAY,EAAE;AACxB,QAAQ,MAAM,oBAAoB,GAAG,YAAY,CAAC,sBAAsB;AACxE,QAAQ,MAAM,gBAAgB,GAAG,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;AAC3D,QAAQ,MAAM,KAAK,GAAG,gBAAgB,EAAE,KAAK;AAC7C,QAAQ,MAAM,SAAS,GAAG,KAAK,EAAE,OAAO;AACxC,QAAQ,MAAM,QAAQ,GAAG,KAAK,EAAE,SAAS,EAAE,QAAQ,IAAI,OAAO;AAC9D,QAAQ,MAAM,aAAa,GAAG,KAAK,EAAE,SAAS,EAAE,cAAc,IAAI,CAAC;AACnE,QAAQ,MAAM,YAAY,GAAG,QAAQ,KAAK,MAAM,IAAI,aAAa,GAAG,CAAC,GAAG,QAAQ,GAAG,SAAS;AAC5F,QAAQ,IAAI,WAAW,GAAG,IAAI;AAC9B,QAAQ,IAAI,kBAAkB,GAAG,IAAI;AACrC,QAAQ,IAAI,SAAS,EAAE;AACvB,UAAU,IAAI;AACd,YAAY,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC;AACrE,YAAY,WAAW,GAAG,OAAO,CAAC,IAAI;AACtC,YAAY,kBAAkB,GAAG,OAAO,CAAC,WAAW;AACpD,WAAW,CAAC,OAAO,KAAK,EAAE;AAC1B,YAAY,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC;AACnE;AACA;AACA,QAAQ,MAAM,GAAG,GAAG,YAAY;AAChC,QAAQ,OAAO,CAAC,GAAG,CAAC,kCAAkC,EAAE;AACxD,UAAU,WAAW,EAAE,GAAG,CAAC,oBAAoB;AAC/C,UAAU,WAAW,EAAE,GAAG,CAAC,kBAAkB;AAC7C,UAAU,SAAS,EAAE,GAAG,CAAC,kBAAkB;AAC3C,UAAU,SAAS,EAAE,GAAG,CAAC,gBAAgB;AACzC,UAAU,gBAAgB,EAAE;AAC5B,SAAS,CAAC;AACV,QAAQ,IAAI,cAAc,GAAG,GAAG,CAAC,kBAAkB,IAAI,GAAG,CAAC,oBAAoB;AAC/E,QAAQ,IAAI,YAAY,GAAG,GAAG,CAAC,gBAAgB,IAAI,GAAG,CAAC,kBAAkB;AACzE,QAAQ,IAAI,CAAC,CAAC,cAAc,IAAI,CAAC,YAAY,KAAK,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,MAAM,GAAG,CAAC,EAAE;AACxF,UAAU,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;AACtD,UAAU,IAAI,SAAS,CAAC,oBAAoB,IAAI,CAAC,cAAc,EAAE;AACjE,YAAY,cAAc,GAAG,SAAS,CAAC,oBAAoB;AAC3D;AACA,UAAU,IAAI,SAAS,CAAC,kBAAkB,IAAI,CAAC,YAAY,EAAE;AAC7D,YAAY,YAAY,GAAG,SAAS,CAAC,kBAAkB;AACvD;AACA;AACA,QAAQ,MAAM,SAAS,GAAG,cAAc,GAAG,IAAI,IAAI,CAAC,cAAc,GAAG,GAAG,CAAC,GAAG,IAAI;AAChF,QAAQ,MAAM,OAAO,GAAG,YAAY,GAAG,IAAI,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC,GAAG,IAAI;AAC1E,QAAQ,MAAM,WAAW,GAAG,YAAY,CAAC,MAAM,KAAK,UAAU,IAAI,YAAY,CAAC,MAAM,KAAK,WAAW;AACrG,QAAQ,IAAI,aAAa,GAAG,IAAI;AAChC,QAAQ,IAAI,WAAW,IAAI,GAAG,CAAC,WAAW,EAAE;AAC5C,UAAU,aAAa,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,GAAG,CAAC;AACzD;AACA,QAAQ,gBAAgB,GAAG;AAC3B,UAAU,EAAE,EAAE,YAAY,CAAC,EAAE;AAC7B,UAAU,MAAM,EAAE,YAAY,CAAC,MAAM;AACrC,UAAU,kBAAkB,EAAE,SAAS;AACvC,UAAU,gBAAgB,EAAE,OAAO;AACnC;AACA,UAAU,oBAAoB,EAAE,SAAS;AACzC,UAAU,kBAAkB,EAAE,OAAO;AACrC,UAAU,iBAAiB,EAAE,GAAG,CAAC,iBAAiB,IAAI,GAAG,CAAC,oBAAoB;AAC9E;AACA,UAAU,WAAW,EAAE,aAAa;AACpC,UAAU,UAAU,EAAE,aAAa;AACnC,UAAU,gBAAgB,EAAE,GAAG,CAAC,eAAe,IAAI,GAAG,CAAC,gBAAgB;AACvE,UAAU,QAAQ,EAAE,YAAY,CAAC,QAAQ,IAAI,EAAE;AAC/C,UAAU,aAAa,EAAE,oBAAoB;AAC7C,UAAU,KAAK,EAAE,YAAY,CAAC,KAAK,CAAC,IAAI;AACxC,UAAU,KAAK,EAAE;AACjB,YAAY,EAAE,EAAE,KAAK,EAAE,EAAE;AACzB,YAAY,UAAU,EAAE,KAAK,EAAE,WAAW;AAC1C,YAAY,QAAQ,EAAE,KAAK,EAAE,QAAQ;AACrC,YAAY,QAAQ;AACpB,YAAY,aAAa;AACzB,YAAY;AACZ,WAAW;AACX,UAAU,OAAO,EAAE;AACnB,YAAY,EAAE,EAAE,SAAS;AACzB,YAAY,IAAI,EAAE,WAAW;AAC7B,YAAY,WAAW,EAAE;AACzB;AACA,SAAS;AACT,QAAQ,IAAI,gBAAgB,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,WAAW,IAAI,WAAW,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE;AAC1F,UAAU,IAAI;AACd,YAAY,MAAM,QAAQ,GAAG,MAAM,oBAAoB,EAAE;AACzD,YAAY,MAAM,YAAY,GAAG,QAAQ,CAAC,IAAI;AAC9C,cAAc,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW;AACvF,aAAa;AACb,YAAY,IAAI,YAAY,EAAE;AAC9B,cAAc,WAAW,GAAG,YAAY;AACxC;AACA,WAAW,CAAC,OAAO,KAAK,EAAE;AAC1B,YAAY,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC;AAChE;AACA;AACA;AACA,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC;AACzD;AACA;AACA,EAAE,OAAO;AACT,IAAI,WAAW;AACf,IAAI,YAAY,EAAE,gBAAgB;AAClC,IAAI,cAAc;AAClB,IAAI,QAAQ;AACZ,IAAI;AACJ,GAAG;AACH;AACK,MAAC,GAAG,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK;AACnC,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE;AACd,IAAI,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxD;AACA,EAAE,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AAClD,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE;AACrB,IAAI,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxD;AACA,EAAE,IAAI;AACN,IAAI,MAAM,WAAW,GAAG,MAAM,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC;AACzD,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC;AAC5B,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC;AACxD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,8BAA8B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3E;AACA;;;;"}