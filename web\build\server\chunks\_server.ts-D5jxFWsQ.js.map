{"version": 3, "file": "_server.ts-D5jxFWsQ.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/admin/features/seed-analysis/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../../chunks/prisma.js\";\nimport { A as ANALYSIS_FEATURES } from \"../../../../../../chunks/analysis-features.js\";\nimport { v as verifySessionToken } from \"../../../../../../chunks/auth.js\";\nconst POST = async ({ cookies }) => {\n  const token = cookies.get(\"auth_token\");\n  if (!token) return new Response(\"Unauthorized\", { status: 401 });\n  const userData = await verifySessionToken(token);\n  if (!userData?.id) return new Response(\"Unauthorized\", { status: 401 });\n  const user = await prisma.user.findUnique({\n    where: { id: userData.id },\n    select: { isAdmin: true, role: true }\n  });\n  if (!user || !user.isAdmin && user.role !== \"admin\") {\n    return new Response(\"Unauthorized - Admin access required\", { status: 403 });\n  }\n  try {\n    const results = {\n      features: 0,\n      updated: 0,\n      limits: 0,\n      updatedLimits: 0,\n      errors: 0\n    };\n    console.log(`Starting to seed ${ANALYSIS_FEATURES.length} analysis features...`);\n    await prisma.$transaction(async (tx) => {\n      for (const feature of ANALYSIS_FEATURES) {\n        try {\n          console.log(`Processing feature: ${feature.id} - ${feature.name}`);\n          const existingFeature = await tx.feature.findUnique({\n            where: { id: feature.id }\n          });\n          const featureData = {\n            name: feature.name,\n            description: feature.description ?? \"\",\n            category: feature.category ?? \"analytics\",\n            icon: feature.icon ?? null,\n            beta: feature.beta ?? false,\n            updatedAt: /* @__PURE__ */ new Date()\n          };\n          if (!existingFeature) {\n            await tx.feature.create({\n              data: {\n                id: feature.id,\n                ...featureData\n              }\n            });\n            console.log(`Created new feature: ${feature.name}`);\n            results.features++;\n          } else {\n            await tx.feature.update({\n              where: { id: feature.id },\n              data: featureData\n            });\n            console.log(`Updated existing feature: ${feature.name}`);\n            results.updated++;\n          }\n          if (feature.limits && Array.isArray(feature.limits) && feature.limits.length > 0) {\n            console.log(`Processing ${feature.limits.length} limits for feature ${feature.id}`);\n            for (const limit of feature.limits) {\n              if (!limit?.id) {\n                console.warn(`Skipping invalid limit for feature ${feature.id}`);\n                continue;\n              }\n              const existingLimit = await tx.featureLimit.findUnique({\n                where: { id: limit.id }\n              });\n              const limitData = {\n                name: limit.name,\n                description: limit.description ?? \"\",\n                defaultValue: (limit.defaultValue ?? 10).toString(),\n                type: limit.type,\n                unit: limit.unit ?? null,\n                resetDay: limit.resetDay ?? null\n              };\n              if (!existingLimit) {\n                await tx.featureLimit.create({\n                  data: {\n                    id: limit.id,\n                    featureId: feature.id,\n                    ...limitData\n                  }\n                });\n                console.log(`Created new limit: ${limit.name} for feature ${feature.name}`);\n                results.limits++;\n              } else {\n                await tx.featureLimit.update({\n                  where: { id: limit.id },\n                  data: limitData\n                });\n                console.log(`Updated existing limit: ${limit.name} for feature ${feature.name}`);\n                results.updatedLimits++;\n              }\n            }\n          } else {\n            console.log(`No limits defined for feature ${feature.id}`);\n          }\n        } catch (error) {\n          console.error(`Error processing feature ${feature.name}:`, error);\n          if (error instanceof Error) {\n            console.error(\"Error details:\", error.message);\n            console.error(\"Stack trace:\", error.stack);\n          }\n          results.errors++;\n        }\n      }\n    });\n    console.log(\"Analysis features seeding completed successfully\");\n    return json({\n      success: true,\n      message: `Seeded ${results.features} new analysis features, updated ${results.updated} existing features, added ${results.limits} limits, updated ${results.updatedLimits} limits`,\n      results\n    });\n  } catch (error) {\n    console.error(\"Error seeding analysis features:\", error);\n    if (error instanceof Error) {\n      console.error(\"Error details:\", error.message);\n      console.error(\"Stack trace:\", error.stack);\n    }\n    return json(\n      {\n        success: false,\n        error: \"Failed to seed analysis features\",\n        message: error instanceof Error ? error.message : String(error)\n      },\n      { status: 500 }\n    );\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAIK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK;AACpC,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE,EAAE,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AAClD,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE,EAAE,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAC5C,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE;AAC9B,IAAI,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AACvC,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;AACvD,IAAI,OAAO,IAAI,QAAQ,CAAC,sCAAsC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAChF;AACA,EAAE,IAAI;AACN,IAAI,MAAM,OAAO,GAAG;AACpB,MAAM,QAAQ,EAAE,CAAC;AACjB,MAAM,OAAO,EAAE,CAAC;AAChB,MAAM,MAAM,EAAE,CAAC;AACf,MAAM,aAAa,EAAE,CAAC;AACtB,MAAM,MAAM,EAAE;AACd,KAAK;AACL,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;AACpF,IAAI,MAAM,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK;AAC5C,MAAM,KAAK,MAAM,OAAO,IAAI,iBAAiB,EAAE;AAC/C,QAAQ,IAAI;AACZ,UAAU,OAAO,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;AAC5E,UAAU,MAAM,eAAe,GAAG,MAAM,EAAE,CAAC,OAAO,CAAC,UAAU,CAAC;AAC9D,YAAY,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE;AACnC,WAAW,CAAC;AACZ,UAAU,MAAM,WAAW,GAAG;AAC9B,YAAY,IAAI,EAAE,OAAO,CAAC,IAAI;AAC9B,YAAY,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,EAAE;AAClD,YAAY,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,WAAW;AACrD,YAAY,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,IAAI;AACtC,YAAY,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,KAAK;AACvC,YAAY,SAAS,kBAAkB,IAAI,IAAI;AAC/C,WAAW;AACX,UAAU,IAAI,CAAC,eAAe,EAAE;AAChC,YAAY,MAAM,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC;AACpC,cAAc,IAAI,EAAE;AACpB,gBAAgB,EAAE,EAAE,OAAO,CAAC,EAAE;AAC9B,gBAAgB,GAAG;AACnB;AACA,aAAa,CAAC;AACd,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;AAC/D,YAAY,OAAO,CAAC,QAAQ,EAAE;AAC9B,WAAW,MAAM;AACjB,YAAY,MAAM,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC;AACpC,cAAc,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE;AACvC,cAAc,IAAI,EAAE;AACpB,aAAa,CAAC;AACd,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,0BAA0B,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;AACpE,YAAY,OAAO,CAAC,OAAO,EAAE;AAC7B;AACA,UAAU,IAAI,OAAO,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;AAC5F,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,oBAAoB,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;AAC/F,YAAY,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,MAAM,EAAE;AAChD,cAAc,IAAI,CAAC,KAAK,EAAE,EAAE,EAAE;AAC9B,gBAAgB,OAAO,CAAC,IAAI,CAAC,CAAC,mCAAmC,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;AAChF,gBAAgB;AAChB;AACA,cAAc,MAAM,aAAa,GAAG,MAAM,EAAE,CAAC,YAAY,CAAC,UAAU,CAAC;AACrE,gBAAgB,KAAK,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,EAAE;AACrC,eAAe,CAAC;AAChB,cAAc,MAAM,SAAS,GAAG;AAChC,gBAAgB,IAAI,EAAE,KAAK,CAAC,IAAI;AAChC,gBAAgB,WAAW,EAAE,KAAK,CAAC,WAAW,IAAI,EAAE;AACpD,gBAAgB,YAAY,EAAE,CAAC,KAAK,CAAC,YAAY,IAAI,EAAE,EAAE,QAAQ,EAAE;AACnE,gBAAgB,IAAI,EAAE,KAAK,CAAC,IAAI;AAChC,gBAAgB,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,IAAI;AACxC,gBAAgB,QAAQ,EAAE,KAAK,CAAC,QAAQ,IAAI;AAC5C,eAAe;AACf,cAAc,IAAI,CAAC,aAAa,EAAE;AAClC,gBAAgB,MAAM,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC;AAC7C,kBAAkB,IAAI,EAAE;AACxB,oBAAoB,EAAE,EAAE,KAAK,CAAC,EAAE;AAChC,oBAAoB,SAAS,EAAE,OAAO,CAAC,EAAE;AACzC,oBAAoB,GAAG;AACvB;AACA,iBAAiB,CAAC;AAClB,gBAAgB,OAAO,CAAC,GAAG,CAAC,CAAC,mBAAmB,EAAE,KAAK,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;AAC3F,gBAAgB,OAAO,CAAC,MAAM,EAAE;AAChC,eAAe,MAAM;AACrB,gBAAgB,MAAM,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC;AAC7C,kBAAkB,KAAK,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,EAAE,EAAE;AACzC,kBAAkB,IAAI,EAAE;AACxB,iBAAiB,CAAC;AAClB,gBAAgB,OAAO,CAAC,GAAG,CAAC,CAAC,wBAAwB,EAAE,KAAK,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;AAChG,gBAAgB,OAAO,CAAC,aAAa,EAAE;AACvC;AACA;AACA,WAAW,MAAM;AACjB,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,8BAA8B,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;AACtE;AACA,SAAS,CAAC,OAAO,KAAK,EAAE;AACxB,UAAU,OAAO,CAAC,KAAK,CAAC,CAAC,yBAAyB,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;AAC3E,UAAU,IAAI,KAAK,YAAY,KAAK,EAAE;AACtC,YAAY,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,OAAO,CAAC;AAC1D,YAAY,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,KAAK,CAAC;AACtD;AACA,UAAU,OAAO,CAAC,MAAM,EAAE;AAC1B;AACA;AACA,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC;AACnE,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,QAAQ,CAAC,gCAAgC,EAAE,OAAO,CAAC,OAAO,CAAC,0BAA0B,EAAE,OAAO,CAAC,MAAM,CAAC,iBAAiB,EAAE,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC;AACxL,MAAM;AACN,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC;AAC5D,IAAI,IAAI,KAAK,YAAY,KAAK,EAAE;AAChC,MAAM,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,OAAO,CAAC;AACpD,MAAM,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,KAAK,CAAC;AAChD;AACA,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,KAAK,EAAE,kCAAkC;AACjD,QAAQ,OAAO,EAAE,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC,KAAK;AACtE,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;;;;"}