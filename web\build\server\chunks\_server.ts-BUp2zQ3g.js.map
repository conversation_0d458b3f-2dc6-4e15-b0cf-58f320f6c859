{"version": 3, "file": "_server.ts-BUp2zQ3g.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/help/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../chunks/prisma.js\";\nconst GET = async ({ url }) => {\n  try {\n    const categorySlug = url.searchParams.get(\"category\") || void 0;\n    const tagSlug = url.searchParams.get(\"tag\") || void 0;\n    const limit = parseInt(url.searchParams.get(\"limit\") || \"20\");\n    const page = parseInt(url.searchParams.get(\"page\") || \"1\");\n    const skip = (page - 1) * limit;\n    const featured = url.searchParams.get(\"featured\") === \"true\";\n    const filters = {\n      published: true\n    };\n    if (categorySlug) {\n      filters.category = {\n        slug: categorySlug\n      };\n    }\n    if (tagSlug) {\n      filters.tags = {\n        some: {\n          slug: tagSlug\n        }\n      };\n    }\n    if (featured) {\n      const featuredArticles = await prisma.helpArticle.findMany({\n        where: {\n          published: true\n        },\n        orderBy: {\n          viewCount: \"desc\"\n        },\n        take: limit,\n        include: {\n          category: true,\n          tags: true\n        }\n      });\n      return json(featuredArticles);\n    }\n    const [articles, totalCount] = await Promise.all([\n      prisma.helpArticle.findMany({\n        where: filters,\n        orderBy: {\n          updatedAt: \"desc\"\n        },\n        skip,\n        take: limit,\n        include: {\n          category: true,\n          tags: true\n        }\n      }),\n      prisma.helpArticle.count({\n        where: filters\n      })\n    ]);\n    return json({\n      articles,\n      pagination: {\n        total: totalCount,\n        page,\n        limit,\n        pages: Math.ceil(totalCount / limit)\n      }\n    });\n  } catch (error) {\n    console.error(\"Error fetching help articles:\", error);\n    return json({ error: \"Failed to fetch help articles\" }, { status: 500 });\n  }\n};\nconst POST = async ({ request, locals }) => {\n  const user = locals.user;\n  if (!user || user.role !== \"ADMIN\") {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  try {\n    const { title, slug, content, excerpt, categoryId, tagIds, published } = await request.json();\n    const article = await prisma.helpArticle.create({\n      data: {\n        title,\n        slug,\n        content,\n        excerpt,\n        published: published || false,\n        categoryId,\n        tags: {\n          connect: tagIds?.map((id) => ({ id })) || []\n        }\n      },\n      include: {\n        category: true,\n        tags: true\n      }\n    });\n    return json(article);\n  } catch (error) {\n    console.error(\"Error creating help article:\", error);\n    return json({ error: \"Failed to create help article\" }, { status: 500 });\n  }\n};\nexport {\n  GET,\n  POST\n};\n"], "names": [], "mappings": ";;;;AAEK,MAAC,GAAG,GAAG,OAAO,EAAE,GAAG,EAAE,KAAK;AAC/B,EAAE,IAAI;AACN,IAAI,MAAM,YAAY,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC;AACnE,IAAI,MAAM,OAAO,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC;AACzD,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC;AACjE,IAAI,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC;AAC9D,IAAI,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK;AACnC,IAAI,MAAM,QAAQ,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,MAAM;AAChE,IAAI,MAAM,OAAO,GAAG;AACpB,MAAM,SAAS,EAAE;AACjB,KAAK;AACL,IAAI,IAAI,YAAY,EAAE;AACtB,MAAM,OAAO,CAAC,QAAQ,GAAG;AACzB,QAAQ,IAAI,EAAE;AACd,OAAO;AACP;AACA,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,OAAO,CAAC,IAAI,GAAG;AACrB,QAAQ,IAAI,EAAE;AACd,UAAU,IAAI,EAAE;AAChB;AACA,OAAO;AACP;AACA,IAAI,IAAI,QAAQ,EAAE;AAClB,MAAM,MAAM,gBAAgB,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;AACjE,QAAQ,KAAK,EAAE;AACf,UAAU,SAAS,EAAE;AACrB,SAAS;AACT,QAAQ,OAAO,EAAE;AACjB,UAAU,SAAS,EAAE;AACrB,SAAS;AACT,QAAQ,IAAI,EAAE,KAAK;AACnB,QAAQ,OAAO,EAAE;AACjB,UAAU,QAAQ,EAAE,IAAI;AACxB,UAAU,IAAI,EAAE;AAChB;AACA,OAAO,CAAC;AACR,MAAM,OAAO,IAAI,CAAC,gBAAgB,CAAC;AACnC;AACA,IAAI,MAAM,CAAC,QAAQ,EAAE,UAAU,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;AACrD,MAAM,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;AAClC,QAAQ,KAAK,EAAE,OAAO;AACtB,QAAQ,OAAO,EAAE;AACjB,UAAU,SAAS,EAAE;AACrB,SAAS;AACT,QAAQ,IAAI;AACZ,QAAQ,IAAI,EAAE,KAAK;AACnB,QAAQ,OAAO,EAAE;AACjB,UAAU,QAAQ,EAAE,IAAI;AACxB,UAAU,IAAI,EAAE;AAChB;AACA,OAAO,CAAC;AACR,MAAM,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC;AAC/B,QAAQ,KAAK,EAAE;AACf,OAAO;AACP,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,QAAQ;AACd,MAAM,UAAU,EAAE;AAClB,QAAQ,KAAK,EAAE,UAAU;AACzB,QAAQ,IAAI;AACZ,QAAQ,KAAK;AACb,QAAQ,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,KAAK;AAC3C;AACA,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC;AACzD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,+BAA+B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC5E;AACA;AACK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AAC5C,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;AACtC,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACjG,IAAI,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;AACpD,MAAM,IAAI,EAAE;AACZ,QAAQ,KAAK;AACb,QAAQ,IAAI;AACZ,QAAQ,OAAO;AACf,QAAQ,OAAO;AACf,QAAQ,SAAS,EAAE,SAAS,IAAI,KAAK;AACrC,QAAQ,UAAU;AAClB,QAAQ,IAAI,EAAE;AACd,UAAU,OAAO,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC,IAAI;AACpD;AACA,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,QAAQ,EAAE,IAAI;AACtB,QAAQ,IAAI,EAAE;AACd;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC;AACxB,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC;AACxD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,+BAA+B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC5E;AACA;;;;"}