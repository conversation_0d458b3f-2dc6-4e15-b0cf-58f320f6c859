{"version": 3, "file": "_server.ts-7qBrzJRX.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/auth/verify/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport \"../../../../../chunks/auth.js\";\nimport { g as getRedisClient } from \"../../../../../chunks/redis.js\";\nimport { createNotification, NotificationPriority, NotificationType } from \"../../../../../chunks/notifications.js\";\nconst BASE_URL = process.env.PUBLIC_BASE_URL || \"http://localhost:5173\";\nconst TEST_EMAIL = \"<EMAIL>\";\nconst EMAIL_QUEUE_KEY = \"email:queue\";\nconst GET = async ({ url, cookies }) => {\n  const token = url.searchParams.get(\"token\");\n  if (!token) {\n    return json({ error: \"Verification token is required\" }, { status: 400 });\n  }\n  let user = await prisma.user.findFirst({\n    where: {\n      verificationToken: token,\n      verificationExpires: {\n        gt: /* @__PURE__ */ new Date()\n      }\n    },\n    select: {\n      id: true,\n      email: true,\n      name: true,\n      emailVerified: true,\n      verificationToken: true,\n      verificationExpires: true,\n      preferences: true,\n      referredById: true\n    }\n  });\n  if (!user) {\n    const users = await prisma.user.findMany({\n      select: {\n        id: true,\n        email: true,\n        name: true,\n        emailVerified: true,\n        verificationToken: true,\n        verificationExpires: true,\n        preferences: true,\n        referredById: true\n      }\n    });\n    user = users.find((u) => {\n      try {\n        const prefs = typeof u.preferences === \"string\" ? JSON.parse(u.preferences) : u.preferences;\n        return prefs.verificationToken === token && new Date(prefs.verificationExpires) > /* @__PURE__ */ new Date();\n      } catch (e) {\n        console.error(\"Error parsing preferences:\", e);\n        return false;\n      }\n    });\n  }\n  if (!user) {\n    return json({ error: \"Invalid or expired verification token\" }, { status: 400 });\n  }\n  try {\n    let prefs = {};\n    if (typeof user.preferences === \"string\") {\n      prefs = JSON.parse(user.preferences) || {};\n    } else if (user.preferences && typeof user.preferences === \"object\") {\n      prefs = { ...user.preferences };\n    }\n    prefs.emailVerified = true;\n    delete prefs.verificationToken;\n    delete prefs.verificationExpires;\n    await prisma.user.update({\n      where: { id: user.id },\n      data: {\n        emailVerified: true,\n        verificationToken: null,\n        verificationExpires: null,\n        preferences: prefs\n      }\n    });\n  } catch (error) {\n    console.error(\"Failed to update user verification status:\", error);\n    return json({ error: \"Failed to verify email\" }, { status: 500 });\n  }\n  if (user.referredById) {\n    try {\n      const updatedReferrals = await prisma.referral.updateMany({\n        where: {\n          referredId: user.id,\n          referrerId: user.referredById,\n          status: \"pending\"\n        },\n        data: {\n          status: \"completed\",\n          completedAt: /* @__PURE__ */ new Date()\n        }\n      });\n      if (updatedReferrals.count > 0) {\n        await prisma.user.update({\n          where: { id: user.referredById },\n          data: {\n            referralCount: {\n              increment: 1\n            }\n          }\n        });\n        try {\n          await createNotification({\n            userId: user.referredById,\n            title: \"✅ Referral Completed!\",\n            message: `Your referral has been completed! ${user.email} has verified their email and is now an active user.`,\n            type: NotificationType.REFERRAL,\n            priority: NotificationPriority.HIGH,\n            url: \"/dashboard/settings/referrals\",\n            data: {\n              referredEmail: user.email,\n              referredId: user.id,\n              status: \"completed\",\n              completedAt: (/* @__PURE__ */ new Date()).toISOString()\n            }\n          });\n          console.log(\n            `Sent referral completion notification to user ${user.referredById} for ${user.email}`\n          );\n        } catch (notificationError) {\n          console.error(\"Failed to send referral completion notification:\", notificationError);\n        }\n        console.log(`Referral completed for user ${user.id} referred by ${user.referredById}`);\n      } else {\n        console.log(`No pending referral found for user ${user.id}`);\n      }\n    } catch (error) {\n      console.error(\"Error completing referral:\", error);\n    }\n  }\n  console.log(`User ${user.email} has been verified successfully`);\n  try {\n    const redis = await getRedisClient();\n    if (!redis) {\n      console.error(\"Redis client not available\");\n    } else {\n      const id = `email_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;\n      const loginUrl = `${BASE_URL}/auth/sign-in`;\n      const dashboardUrl = `${BASE_URL}/dashboard`;\n      const emailJob = {\n        id,\n        template: \"welcome\",\n        to: [user.email],\n        data: {\n          firstName: user.name || user.email.split(\"@\")[0],\n          loginUrl,\n          dashboardUrl\n        },\n        options: {\n          category: \"transactional\",\n          priority: 2,\n          // High priority\n          retries: 3,\n          tags: [{ name: \"action\", value: \"welcome\" }]\n        },\n        createdAt: (/* @__PURE__ */ new Date()).toISOString()\n      };\n      await redis.zadd(\n        EMAIL_QUEUE_KEY,\n        2,\n        // High priority\n        JSON.stringify(emailJob)\n      );\n      console.log(`Welcome email queued in Redis: ${id} (to ${user.email})`);\n      if (user.email.toLowerCase() !== TEST_EMAIL.toLowerCase()) {\n        const testEmailId = `email_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;\n        const testEmailJob = {\n          id: testEmailId,\n          template: \"welcome\",\n          to: [TEST_EMAIL],\n          data: {\n            firstName: \"Christopher (Test Copy)\",\n            loginUrl,\n            dashboardUrl\n          },\n          options: {\n            category: \"transactional\",\n            priority: 2,\n            // High priority\n            retries: 3,\n            tags: [\n              { name: \"action\", value: \"welcome\" },\n              { name: \"test\", value: \"true\" }\n            ]\n          },\n          createdAt: (/* @__PURE__ */ new Date()).toISOString()\n        };\n        await redis.zadd(\n          EMAIL_QUEUE_KEY,\n          2,\n          // High priority\n          JSON.stringify(testEmailJob)\n        );\n        console.log(`Test welcome email queued in Redis: ${testEmailId} (to ${TEST_EMAIL})`);\n      }\n      await redis.publish(\"email:process\", \"process_now\");\n    }\n  } catch (error) {\n    console.error(\"Failed to queue welcome email:\", error);\n  }\n  return new Response(null, {\n    status: 302,\n    headers: {\n      Location: `${BASE_URL}/auth/sign-in?verified=true&email=${encodeURIComponent(user.email)}`\n    }\n  });\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;;;;AAKA,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,uBAAuB;AACvE,MAAM,UAAU,GAAG,wCAAwC;AAC3D,MAAM,eAAe,GAAG,aAAa;AAChC,MAAC,GAAG,GAAG,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK;AACxC,EAAE,MAAM,KAAK,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC;AAC7C,EAAE,IAAI,CAAC,KAAK,EAAE;AACd,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,gCAAgC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7E;AACA,EAAE,IAAI,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;AACzC,IAAI,KAAK,EAAE;AACX,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,mBAAmB,EAAE;AAC3B,QAAQ,EAAE,kBAAkB,IAAI,IAAI;AACpC;AACA,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,EAAE,EAAE,IAAI;AACd,MAAM,KAAK,EAAE,IAAI;AACjB,MAAM,IAAI,EAAE,IAAI;AAChB,MAAM,aAAa,EAAE,IAAI;AACzB,MAAM,iBAAiB,EAAE,IAAI;AAC7B,MAAM,mBAAmB,EAAE,IAAI;AAC/B,MAAM,WAAW,EAAE,IAAI;AACvB,MAAM,YAAY,EAAE;AACpB;AACA,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC7C,MAAM,MAAM,EAAE;AACd,QAAQ,EAAE,EAAE,IAAI;AAChB,QAAQ,KAAK,EAAE,IAAI;AACnB,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,aAAa,EAAE,IAAI;AAC3B,QAAQ,iBAAiB,EAAE,IAAI;AAC/B,QAAQ,mBAAmB,EAAE,IAAI;AACjC,QAAQ,WAAW,EAAE,IAAI;AACzB,QAAQ,YAAY,EAAE;AACtB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK;AAC7B,MAAM,IAAI;AACV,QAAQ,MAAM,KAAK,GAAG,OAAO,CAAC,CAAC,WAAW,KAAK,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,WAAW;AACnG,QAAQ,OAAO,KAAK,CAAC,iBAAiB,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,mBAAmB,IAAI,IAAI,EAAE;AACpH,OAAO,CAAC,OAAO,CAAC,EAAE;AAClB,QAAQ,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,CAAC,CAAC;AACtD,QAAQ,OAAO,KAAK;AACpB;AACA,KAAK,CAAC;AACN;AACA,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uCAAuC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpF;AACA,EAAE,IAAI;AACN,IAAI,IAAI,KAAK,GAAG,EAAE;AAClB,IAAI,IAAI,OAAO,IAAI,CAAC,WAAW,KAAK,QAAQ,EAAE;AAC9C,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE;AAChD,KAAK,MAAM,IAAI,IAAI,CAAC,WAAW,IAAI,OAAO,IAAI,CAAC,WAAW,KAAK,QAAQ,EAAE;AACzE,MAAM,KAAK,GAAG,EAAE,GAAG,IAAI,CAAC,WAAW,EAAE;AACrC;AACA,IAAI,KAAK,CAAC,aAAa,GAAG,IAAI;AAC9B,IAAI,OAAO,KAAK,CAAC,iBAAiB;AAClC,IAAI,OAAO,KAAK,CAAC,mBAAmB;AACpC,IAAI,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AAC7B,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;AAC5B,MAAM,IAAI,EAAE;AACZ,QAAQ,aAAa,EAAE,IAAI;AAC3B,QAAQ,iBAAiB,EAAE,IAAI;AAC/B,QAAQ,mBAAmB,EAAE,IAAI;AACjC,QAAQ,WAAW,EAAE;AACrB;AACA,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC;AACtE,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,wBAAwB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACrE;AACA,EAAE,IAAI,IAAI,CAAC,YAAY,EAAE;AACzB,IAAI,IAAI;AACR,MAAM,MAAM,gBAAgB,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;AAChE,QAAQ,KAAK,EAAE;AACf,UAAU,UAAU,EAAE,IAAI,CAAC,EAAE;AAC7B,UAAU,UAAU,EAAE,IAAI,CAAC,YAAY;AACvC,UAAU,MAAM,EAAE;AAClB,SAAS;AACT,QAAQ,IAAI,EAAE;AACd,UAAU,MAAM,EAAE,WAAW;AAC7B,UAAU,WAAW,kBAAkB,IAAI,IAAI;AAC/C;AACA,OAAO,CAAC;AACR,MAAM,IAAI,gBAAgB,CAAC,KAAK,GAAG,CAAC,EAAE;AACtC,QAAQ,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AACjC,UAAU,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,YAAY,EAAE;AAC1C,UAAU,IAAI,EAAE;AAChB,YAAY,aAAa,EAAE;AAC3B,cAAc,SAAS,EAAE;AACzB;AACA;AACA,SAAS,CAAC;AACV,QAAQ,IAAI;AACZ,UAAU,MAAM,kBAAkB,CAAC;AACnC,YAAY,MAAM,EAAE,IAAI,CAAC,YAAY;AACrC,YAAY,KAAK,EAAE,uBAAuB;AAC1C,YAAY,OAAO,EAAE,CAAC,kCAAkC,EAAE,IAAI,CAAC,KAAK,CAAC,oDAAoD,CAAC;AAC1H,YAAY,IAAI,EAAE,gBAAgB,CAAC,QAAQ;AAC3C,YAAY,QAAQ,EAAE,oBAAoB,CAAC,IAAI;AAC/C,YAAY,GAAG,EAAE,+BAA+B;AAChD,YAAY,IAAI,EAAE;AAClB,cAAc,aAAa,EAAE,IAAI,CAAC,KAAK;AACvC,cAAc,UAAU,EAAE,IAAI,CAAC,EAAE;AACjC,cAAc,MAAM,EAAE,WAAW;AACjC,cAAc,WAAW,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AACnE;AACA,WAAW,CAAC;AACZ,UAAU,OAAO,CAAC,GAAG;AACrB,YAAY,CAAC,8CAA8C,EAAE,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC;AACjG,WAAW;AACX,SAAS,CAAC,OAAO,iBAAiB,EAAE;AACpC,UAAU,OAAO,CAAC,KAAK,CAAC,kDAAkD,EAAE,iBAAiB,CAAC;AAC9F;AACA,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,4BAA4B,EAAE,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;AAC9F,OAAO,MAAM;AACb,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,mCAAmC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AACpE;AACA,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC;AACxD;AACA;AACA,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;AAClE,EAAE,IAAI;AACN,IAAI,MAAM,KAAK,GAAG,MAAM,cAAc,EAAE;AACxC,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,OAAO,CAAC,KAAK,CAAC,4BAA4B,CAAC;AACjD,KAAK,MAAM;AACX,MAAM,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AACrF,MAAM,MAAM,QAAQ,GAAG,CAAC,EAAE,QAAQ,CAAC,aAAa,CAAC;AACjD,MAAM,MAAM,YAAY,GAAG,CAAC,EAAE,QAAQ,CAAC,UAAU,CAAC;AAClD,MAAM,MAAM,QAAQ,GAAG;AACvB,QAAQ,EAAE;AACV,QAAQ,QAAQ,EAAE,SAAS;AAC3B,QAAQ,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;AACxB,QAAQ,IAAI,EAAE;AACd,UAAU,SAAS,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC1D,UAAU,QAAQ;AAClB,UAAU;AACV,SAAS;AACT,QAAQ,OAAO,EAAE;AACjB,UAAU,QAAQ,EAAE,eAAe;AACnC,UAAU,QAAQ,EAAE,CAAC;AACrB;AACA,UAAU,OAAO,EAAE,CAAC;AACpB,UAAU,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE;AACrD,SAAS;AACT,QAAQ,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC3D,OAAO;AACP,MAAM,MAAM,KAAK,CAAC,IAAI;AACtB,QAAQ,eAAe;AACvB,QAAQ,CAAC;AACT;AACA,QAAQ,IAAI,CAAC,SAAS,CAAC,QAAQ;AAC/B,OAAO;AACP,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,+BAA+B,EAAE,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC5E,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,UAAU,CAAC,WAAW,EAAE,EAAE;AACjE,QAAQ,MAAM,WAAW,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AAChG,QAAQ,MAAM,YAAY,GAAG;AAC7B,UAAU,EAAE,EAAE,WAAW;AACzB,UAAU,QAAQ,EAAE,SAAS;AAC7B,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC;AAC1B,UAAU,IAAI,EAAE;AAChB,YAAY,SAAS,EAAE,yBAAyB;AAChD,YAAY,QAAQ;AACpB,YAAY;AACZ,WAAW;AACX,UAAU,OAAO,EAAE;AACnB,YAAY,QAAQ,EAAE,eAAe;AACrC,YAAY,QAAQ,EAAE,CAAC;AACvB;AACA,YAAY,OAAO,EAAE,CAAC;AACtB,YAAY,IAAI,EAAE;AAClB,cAAc,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE;AAClD,cAAc,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM;AAC3C;AACA,WAAW;AACX,UAAU,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC7D,SAAS;AACT,QAAQ,MAAM,KAAK,CAAC,IAAI;AACxB,UAAU,eAAe;AACzB,UAAU,CAAC;AACX;AACA,UAAU,IAAI,CAAC,SAAS,CAAC,YAAY;AACrC,SAAS;AACT,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,oCAAoC,EAAE,WAAW,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;AAC5F;AACA,MAAM,MAAM,KAAK,CAAC,OAAO,CAAC,eAAe,EAAE,aAAa,CAAC;AACzD;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC;AAC1D;AACA,EAAE,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE;AAC5B,IAAI,MAAM,EAAE,GAAG;AACf,IAAI,OAAO,EAAE;AACb,MAAM,QAAQ,EAAE,CAAC,EAAE,QAAQ,CAAC,kCAAkC,EAAE,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC/F;AACA,GAAG,CAAC;AACJ;;;;"}