{"version": 3, "file": "_server.ts-D-UxiXT_.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/user/status/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { l as logger } from \"../../../../../chunks/logger.js\";\nconst GET = async () => {\n  try {\n    const status = {\n      operational: true,\n      loginSuccessRate: 99.5,\n      // percentage\n      averageResponseTime: 0.3,\n      // seconds\n      activeUsers: 850\n    };\n    return json({\n      ...status,\n      timestamp: (/* @__PURE__ */ new Date()).toISOString()\n    });\n  } catch (error) {\n    logger.error(\"Error checking account services status:\", error);\n    return json(\n      {\n        error: \"Failed to check account services status\",\n        timestamp: (/* @__PURE__ */ new Date()).toISOString()\n      },\n      { status: 500 }\n    );\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;AAEK,MAAC,GAAG,GAAG,YAAY;AACxB,EAAE,IAAI;AACN,IAAI,MAAM,MAAM,GAAG;AACnB,MAAM,WAAW,EAAE,IAAI;AACvB,MAAM,gBAAgB,EAAE,IAAI;AAC5B;AACA,MAAM,mBAAmB,EAAE,GAAG;AAC9B;AACA,MAAM,WAAW,EAAE;AACnB,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,GAAG,MAAM;AACf,MAAM,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AACzD,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC;AAClE,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,KAAK,EAAE,yCAAyC;AACxD,QAAQ,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC3D,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;;;;"}