{"version": 3, "file": "_server.ts-0MXR2gQQ.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/admin/plans/load-from-stripe/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../../../../chunks/auth.js\";\nimport { s as stripe } from \"../../../../../../chunks/stripe.js\";\nimport { F as FeatureAccessLevel } from \"../../../../../../chunks/features.js\";\nasync function loadPlansFromStripe() {\n  try {\n    console.log(\"Loading plans from Stripe...\");\n    const products = await stripe.products.list({\n      active: true,\n      expand: [\"data.default_price\"]\n    });\n    console.log(`Found ${products.data.length} active products in Stripe`);\n    const planProducts = products.data.filter((product) => product.metadata.plan_id);\n    console.log(`Found ${planProducts.length} products with plan_id metadata`);\n    let count = 0;\n    for (const product of planProducts) {\n      const planId = product.metadata.plan_id;\n      const section = product.metadata.section || \"pro\";\n      const prices = await stripe.prices.list({\n        product: product.id,\n        active: true\n      });\n      const monthlyPrice = prices.data.find(\n        (price) => price.recurring?.interval === \"month\" && price.recurring?.interval_count === 1\n      );\n      const yearlyPrice = prices.data.find(\n        (price) => price.recurring?.interval === \"year\" && price.recurring?.interval_count === 1\n      );\n      if (!monthlyPrice && !yearlyPrice) {\n        console.log(`Skipping product ${product.id} (${product.name}) - no valid prices found`);\n        continue;\n      }\n      const existingPlan = await prisma.plan.findUnique({\n        where: { id: planId }\n      });\n      const planData = {\n        name: product.name,\n        description: product.description || \"\",\n        section,\n        monthlyPrice: monthlyPrice?.unit_amount || 0,\n        annualPrice: yearlyPrice?.unit_amount || 0,\n        stripePriceMonthlyId: monthlyPrice?.id || null,\n        stripePriceYearlyId: yearlyPrice?.id || null,\n        popular: product.metadata.popular === \"true\"\n      };\n      if (existingPlan) {\n        await prisma.plan.update({\n          where: { id: planId },\n          data: planData\n        });\n        console.log(`Updated plan ${planId} (${product.name}) in database`);\n      } else {\n        await prisma.plan.create({\n          data: {\n            id: planId,\n            ...planData\n          }\n        });\n        await addDefaultFeaturesToPlan(planId);\n        console.log(`Created plan ${planId} (${product.name}) in database`);\n      }\n      count++;\n    }\n    console.log(`Successfully loaded ${count} plans from Stripe`);\n    return count;\n  } catch (error) {\n    console.error(\"Error loading plans from Stripe:\", error);\n    throw error;\n  }\n}\nasync function addDefaultFeaturesToPlan(planId) {\n  const features = await prisma.feature.findMany();\n  const coreFeatures = [\n    { featureId: \"dashboard\", accessLevel: FeatureAccessLevel.Included },\n    { featureId: \"profile\", accessLevel: FeatureAccessLevel.Included },\n    { featureId: \"resume_scanner\", accessLevel: FeatureAccessLevel.Limited },\n    { featureId: \"job_search\", accessLevel: FeatureAccessLevel.Included }\n  ];\n  for (const feature of coreFeatures) {\n    const dbFeature = features.find((f) => f.id === feature.featureId);\n    if (dbFeature) {\n      const planFeature = await prisma.planFeature.create({\n        data: {\n          planId,\n          featureId: feature.featureId,\n          accessLevel: feature.accessLevel\n        }\n      });\n      if (feature.accessLevel === FeatureAccessLevel.Limited) {\n        const limits = await prisma.featureLimit.findMany({\n          where: { featureId: feature.featureId }\n        });\n        for (const limit of limits) {\n          await prisma.planFeatureLimit.create({\n            data: {\n              planFeatureId: planFeature.id,\n              limitId: limit.id,\n              value: limit.defaultValue\n            }\n          });\n        }\n      }\n    }\n  }\n}\nconst POST = async ({ cookies }) => {\n  const token = cookies.get(\"auth_token\");\n  if (!token) return new Response(\"Unauthorized\", { status: 401 });\n  const userData = await verifySessionToken(token);\n  if (!userData?.id) return new Response(\"Unauthorized\", { status: 401 });\n  const user = await prisma.user.findUnique({\n    where: { id: userData.id },\n    select: { isAdmin: true, role: true }\n  });\n  if (!user || !user.isAdmin && user.role !== \"admin\") {\n    return new Response(\"Unauthorized\", { status: 401 });\n  }\n  try {\n    const count = await loadPlansFromStripe();\n    return json({\n      success: true,\n      count,\n      message: `Successfully loaded ${count} plans from Stripe`\n    });\n  } catch (error) {\n    console.error(\"Error loading plans from Stripe:\", error);\n    return json(\n      {\n        success: false,\n        error: error.message\n      },\n      { status: 500 }\n    );\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAKA,eAAe,mBAAmB,GAAG;AACrC,EAAE,IAAI;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC;AAC/C,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;AAChD,MAAM,MAAM,EAAE,IAAI;AAClB,MAAM,MAAM,EAAE,CAAC,oBAAoB;AACnC,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,0BAA0B,CAAC,CAAC;AAC1E,IAAI,MAAM,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC;AACpF,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,YAAY,CAAC,MAAM,CAAC,+BAA+B,CAAC,CAAC;AAC9E,IAAI,IAAI,KAAK,GAAG,CAAC;AACjB,IAAI,KAAK,MAAM,OAAO,IAAI,YAAY,EAAE;AACxC,MAAM,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,OAAO;AAC7C,MAAM,MAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,OAAO,IAAI,KAAK;AACvD,MAAM,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;AAC9C,QAAQ,OAAO,EAAE,OAAO,CAAC,EAAE;AAC3B,QAAQ,MAAM,EAAE;AAChB,OAAO,CAAC;AACR,MAAM,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI;AAC3C,QAAQ,CAAC,KAAK,KAAK,KAAK,CAAC,SAAS,EAAE,QAAQ,KAAK,OAAO,IAAI,KAAK,CAAC,SAAS,EAAE,cAAc,KAAK;AAChG,OAAO;AACP,MAAM,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI;AAC1C,QAAQ,CAAC,KAAK,KAAK,KAAK,CAAC,SAAS,EAAE,QAAQ,KAAK,MAAM,IAAI,KAAK,CAAC,SAAS,EAAE,cAAc,KAAK;AAC/F,OAAO;AACP,MAAM,IAAI,CAAC,YAAY,IAAI,CAAC,WAAW,EAAE;AACzC,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,OAAO,CAAC,EAAE,CAAC,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;AAC/F,QAAQ;AACR;AACA,MAAM,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AACxD,QAAQ,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM;AAC3B,OAAO,CAAC;AACR,MAAM,MAAM,QAAQ,GAAG;AACvB,QAAQ,IAAI,EAAE,OAAO,CAAC,IAAI;AAC1B,QAAQ,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,EAAE;AAC9C,QAAQ,OAAO;AACf,QAAQ,YAAY,EAAE,YAAY,EAAE,WAAW,IAAI,CAAC;AACpD,QAAQ,WAAW,EAAE,WAAW,EAAE,WAAW,IAAI,CAAC;AAClD,QAAQ,oBAAoB,EAAE,YAAY,EAAE,EAAE,IAAI,IAAI;AACtD,QAAQ,mBAAmB,EAAE,WAAW,EAAE,EAAE,IAAI,IAAI;AACpD,QAAQ,OAAO,EAAE,OAAO,CAAC,QAAQ,CAAC,OAAO,KAAK;AAC9C,OAAO;AACP,MAAM,IAAI,YAAY,EAAE;AACxB,QAAQ,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AACjC,UAAU,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;AAC/B,UAAU,IAAI,EAAE;AAChB,SAAS,CAAC;AACV,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAC3E,OAAO,MAAM;AACb,QAAQ,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AACjC,UAAU,IAAI,EAAE;AAChB,YAAY,EAAE,EAAE,MAAM;AACtB,YAAY,GAAG;AACf;AACA,SAAS,CAAC;AACV,QAAQ,MAAM,wBAAwB,CAAC,MAAM,CAAC;AAC9C,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAC3E;AACA,MAAM,KAAK,EAAE;AACb;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,KAAK,CAAC,kBAAkB,CAAC,CAAC;AACjE,IAAI,OAAO,KAAK;AAChB,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC;AAC5D,IAAI,MAAM,KAAK;AACf;AACA;AACA,eAAe,wBAAwB,CAAC,MAAM,EAAE;AAChD,EAAE,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE;AAClD,EAAE,MAAM,YAAY,GAAG;AACvB,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,kBAAkB,CAAC,QAAQ,EAAE;AACxE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,kBAAkB,CAAC,QAAQ,EAAE;AACtE,IAAI,EAAE,SAAS,EAAE,gBAAgB,EAAE,WAAW,EAAE,kBAAkB,CAAC,OAAO,EAAE;AAC5E,IAAI,EAAE,SAAS,EAAE,YAAY,EAAE,WAAW,EAAE,kBAAkB,CAAC,QAAQ;AACvE,GAAG;AACH,EAAE,KAAK,MAAM,OAAO,IAAI,YAAY,EAAE;AACtC,IAAI,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,SAAS,CAAC;AACtE,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;AAC1D,QAAQ,IAAI,EAAE;AACd,UAAU,MAAM;AAChB,UAAU,SAAS,EAAE,OAAO,CAAC,SAAS;AACtC,UAAU,WAAW,EAAE,OAAO,CAAC;AAC/B;AACA,OAAO,CAAC;AACR,MAAM,IAAI,OAAO,CAAC,WAAW,KAAK,kBAAkB,CAAC,OAAO,EAAE;AAC9D,QAAQ,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;AAC1D,UAAU,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,SAAS;AAC/C,SAAS,CAAC;AACV,QAAQ,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;AACpC,UAAU,MAAM,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;AAC/C,YAAY,IAAI,EAAE;AAClB,cAAc,aAAa,EAAE,WAAW,CAAC,EAAE;AAC3C,cAAc,OAAO,EAAE,KAAK,CAAC,EAAE;AAC/B,cAAc,KAAK,EAAE,KAAK,CAAC;AAC3B;AACA,WAAW,CAAC;AACZ;AACA;AACA;AACA;AACA;AACK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK;AACpC,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE,EAAE,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AAClD,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE,EAAE,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAC5C,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE;AAC9B,IAAI,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AACvC,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;AACvD,IAAI,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxD;AACA,EAAE,IAAI;AACN,IAAI,MAAM,KAAK,GAAG,MAAM,mBAAmB,EAAE;AAC7C,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,KAAK;AACX,MAAM,OAAO,EAAE,CAAC,oBAAoB,EAAE,KAAK,CAAC,kBAAkB;AAC9D,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC;AAC5D,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,KAAK,EAAE,KAAK,CAAC;AACrB,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;;;;"}