#!/usr/bin/env node

/**
 * Test script for proxy quota detection
 * This script tests various error patterns to ensure proper detection and notification
 */

// Import the proxy quota detector (adjust path as needed)
let proxyDetector;
try {
  proxyDetector = require("../cron/utils/proxyQuotaDetector");
} catch (error) {
  console.error("❌ Failed to load proxyQuotaDetector:", error.message);
  console.log(
    "💡 Make sure you are running this from the project root directory"
  );
  process.exit(1);
}

const {
  isProxyQuotaExhausted,
  isProxyConnectivityIssue,
  analyzeProxyError,
  handleProxyError,
} = proxyDetector;

console.log("🧪 Testing Proxy Quota Detection System\n");

// Test cases for quota exhaustion
const quotaErrors = [
  "Traffic limit exceeded for your account",
  "Monthly quota has been reached",
  "Bandwidth limit exceeded",
  "Account suspended due to usage limits",
  "Insufficient traffic remaining",
  "Data allowance exceeded",
  "HTTP 402 Payment Required",
  "Subscription expired",
  "Plan limit exceeded",
];

// Test cases for connectivity issues
const connectivityErrors = [
  "net::ERR_EMPTY_RESPONSE",
  "net::ERR_CONNECTION_REFUSED",
  "net::ERR_TIMED_OUT",
  "ECONNREFUSED",
  "ENOTFOUND",
  "Connection timeout",
  "Request timeout",
];

// Test cases for unknown errors
const unknownErrors = [
  "Invalid credentials",
  "Server error",
  "Unknown error occurred",
  "Internal server error",
];

console.log("📋 Testing Quota Exhaustion Detection:");
console.log("=====================================");

quotaErrors.forEach((error, index) => {
  const isQuota = isProxyQuotaExhausted(error);
  const analysis = analyzeProxyError(error);
  console.log(`${index + 1}. "${error}"`);
  console.log(`   ✅ Quota detected: ${isQuota}`);
  console.log(
    `   📊 Analysis: ${analysis.type} (notify: ${analysis.shouldNotify})`
  );
  console.log(`   💬 Message: ${analysis.message}\n`);
});

console.log("🔌 Testing Connectivity Issues Detection:");
console.log("=========================================");

connectivityErrors.forEach((error, index) => {
  const isConnectivity = isProxyConnectivityIssue(error);
  const analysis = analyzeProxyError(error);
  console.log(`${index + 1}. "${error}"`);
  console.log(`   ✅ Connectivity detected: ${isConnectivity}`);
  console.log(
    `   📊 Analysis: ${analysis.type} (notify: ${analysis.shouldNotify})`
  );
  console.log(`   💬 Message: ${analysis.message}\n`);
});

console.log("❓ Testing Unknown Errors:");
console.log("==========================");

unknownErrors.forEach((error, index) => {
  const isQuota = isProxyQuotaExhausted(error);
  const isConnectivity = isProxyConnectivityIssue(error);
  const analysis = analyzeProxyError(error);
  console.log(`${index + 1}. "${error}"`);
  console.log(`   ❌ Quota detected: ${isQuota}`);
  console.log(`   ❌ Connectivity detected: ${isConnectivity}`);
  console.log(
    `   📊 Analysis: ${analysis.type} (notify: ${analysis.shouldNotify})`
  );
  console.log(`   💬 Message: ${analysis.message}\n`);
});

console.log("🎯 Summary:");
console.log("===========");
console.log(
  `✅ Quota errors correctly identified: ${
    quotaErrors.filter((e) => isProxyQuotaExhausted(e)).length
  }/${quotaErrors.length}`
);
console.log(
  `✅ Connectivity errors correctly identified: ${
    connectivityErrors.filter((e) => isProxyConnectivityIssue(e)).length
  }/${connectivityErrors.length}`
);
console.log(
  `✅ Unknown errors correctly ignored: ${
    unknownErrors.filter(
      (e) => !isProxyQuotaExhausted(e) && !isProxyConnectivityIssue(e)
    ).length
  }/${unknownErrors.length}`
);

console.log("\n🚀 Test completed! The proxy quota detection system is ready.");
console.log(
  "💡 To test email notifications, run with SEND_TEST_EMAIL=true environment variable."
);

// Optional: Send test email if requested
if (process.env.SEND_TEST_EMAIL === "true") {
  console.log("\n📧 Sending test email notification...");

  handleProxyError("Traffic limit exceeded for your account", {
    test: true,
    timestamp: new Date().toISOString(),
    context: "test_script",
  })
    .then(() => {
      console.log("✅ Test email notification sent successfully!");
    })
    .catch((error) => {
      console.error("❌ Failed to send test email:", error);
    });
}
