{"version": 3, "file": "_page.svelte-C8yI6ED4.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/employers/_page.svelte.js"], "sourcesContent": ["import { U as ensure_array_like, V as escape_html } from \"../../../chunks/index3.js\";\nimport { B as Button } from \"../../../chunks/button.js\";\nimport { I as Input } from \"../../../chunks/input.js\";\nimport { T as Textarea } from \"../../../chunks/textarea.js\";\nimport { S as SEO } from \"../../../chunks/SEO.js\";\nimport { A as Arrow_right } from \"../../../chunks/arrow-right.js\";\nimport { S as Search } from \"../../../chunks/search.js\";\nimport { C as Chart_column, U as User_check } from \"../../../chunks/user-check.js\";\nimport { C as Clock } from \"../../../chunks/clock.js\";\nimport { S as Shield } from \"../../../chunks/shield.js\";\nimport { B as Building } from \"../../../chunks/building.js\";\nimport { T as Target } from \"../../../chunks/target.js\";\nimport { A as Award } from \"../../../chunks/award.js\";\nfunction _page($$payload) {\n  const features = {\n    talentAcquisition: {\n      title: \"Streamline Your Hiring Process\",\n      description: \"Find the perfect candidates faster with our AI-powered talent acquisition platform.\",\n      secondary: [\n        {\n          icon: Search,\n          title: \"Smart Candidate Matching\",\n          description: \"Our AI analyzes thousands of profiles to find candidates that truly match your requirements.\"\n        },\n        {\n          icon: Chart_column,\n          title: \"Hiring Analytics\",\n          description: \"Gain valuable insights into your recruitment process with comprehensive analytics.\"\n        },\n        {\n          icon: Clock,\n          title: \"Reduce Time-to-Hire\",\n          description: \"Cut your hiring timeline by up to 50% with our streamlined processes and automation.\"\n        },\n        {\n          icon: Shield,\n          title: \"Quality Assurance\",\n          description: \"Our pre-screening tools ensure you only review candidates who meet your standards.\"\n        }\n      ]\n    },\n    employerBranding: {\n      title: \"Enhance Your Employer Brand\",\n      description: \"Showcase your company culture and attract top talent with our employer branding solutions.\",\n      secondary: [\n        {\n          icon: Building,\n          title: \"Company Profile Optimization\",\n          description: \"Create a compelling company profile that highlights your unique culture and benefits.\"\n        },\n        {\n          icon: User_check,\n          title: \"Candidate Experience\",\n          description: \"Provide a seamless application experience that reflects positively on your brand.\"\n        },\n        {\n          icon: Target,\n          title: \"Targeted Outreach\",\n          description: \"Reach passive candidates who align with your company values and mission.\"\n        },\n        {\n          icon: Award,\n          title: \"Reputation Management\",\n          description: \"Monitor and improve your employer reputation across multiple platforms.\"\n        }\n      ]\n    }\n  };\n  const testimonials = [\n    {\n      quote: \"Hirli has transformed our recruitment process. We've reduced our time-to-hire by 40% and improved the quality of our candidates significantly.\",\n      author: \"Sarah Johnson\",\n      position: \"Head of Talent Acquisition\",\n      company: \"TechGrowth Inc.\"\n    },\n    {\n      quote: \"The analytics provided by Hirli have given us unprecedented insights into our hiring process, allowing us to make data-driven decisions.\",\n      author: \"Michael Chen\",\n      position: \"Director of HR\",\n      company: \"Innovate Solutions\"\n    },\n    {\n      quote: \"We've seen a 35% increase in qualified applicants since implementing Hirli's employer branding solutions.\",\n      author: \"Jessica Williams\",\n      position: \"Recruitment Manager\",\n      company: \"Global Systems\"\n    }\n  ];\n  const each_array = ensure_array_like(features.talentAcquisition.secondary);\n  const each_array_1 = ensure_array_like(features.employerBranding.secondary);\n  const each_array_2 = ensure_array_like(testimonials);\n  SEO($$payload, {\n    title: \"Hirli for Employers - Streamline Your Hiring Process\",\n    description: \"Find the perfect candidates faster with our AI-powered talent acquisition platform. Reduce time-to-hire and improve candidate quality.\",\n    keywords: \"talent acquisition, hiring, recruitment, employer branding, HR technology\",\n    url: \"https://hirli.com/employers\",\n    image: \"/assets/og-image-employers.jpg\"\n  });\n  $$payload.out += `<!----> <section class=\"border border-l border-r border-t py-16 md:py-40\"><div class=\"grid grid-cols-10 items-center gap-12\"><div class=\"col-span-4 col-start-2\"><div class=\"leading-tighter mb-8 w-[90%] text-4xl font-light md:text-5xl lg:text-[80px]\">Find <span class=\"gradient-text\">Top Talent</span> Faster &amp; Smarter</div> <p class=\"mb-12 text-gray-600 md:text-2xl\">Our AI-powered platform helps you identify, attract, and hire the best candidates while\n        saving time and resources.</p> <div class=\"flex flex-col space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0\">`;\n  Button($$payload, {\n    class: \"rounded-none border border-transparent bg-neutral-200 p-8 text-lg font-medium text-white transition-colors hover:bg-blue-600\",\n    children: ($$payload2) => {\n      $$payload2.out += `<!---->Schedule a Demo`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----> `;\n  Button($$payload, {\n    class: \"group flex items-center rounded-none border border-gray-300 p-8 text-lg font-medium transition-colors hover:bg-gray-50\",\n    children: ($$payload2) => {\n      $$payload2.out += `<!---->Learn More `;\n      Arrow_right($$payload2, { class: \"ml-2 h-4 w-4\" });\n      $$payload2.out += `<!---->`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----></div> <div class=\"mt-8 flex items-center text-sm text-gray-500\"><div class=\"mr-3 flex -space-x-2\"><img src=\"https://randomuser.me/api/portraits/women/28.jpg\" alt=\"Employer\" class=\"h-8 w-8 rounded-full border-2 border-white\"/> <img src=\"https://randomuser.me/api/portraits/men/45.jpg\" alt=\"Employer\" class=\"h-8 w-8 rounded-full border-2 border-white\"/> <img src=\"https://randomuser.me/api/portraits/women/32.jpg\" alt=\"Employer\" class=\"h-8 w-8 rounded-full border-2 border-white\"/></div> <span>Trusted by <span class=\"font-semibold\">500+</span> companies worldwide</span></div></div> <div class=\"relative col-span-2 col-start-9\"><div class=\"h-[500px] w-full rounded-lg bg-gradient-to-br from-blue-100 to-blue-200\"></div></div></div></section> <section class=\"border border-b border-l border-r py-16\"><div class=\"container mx-auto px-4\"><div class=\"grid grid-cols-1 gap-8 md:grid-cols-3\"><div class=\"text-center\"><div class=\"text-5xl font-bold text-blue-600\">40%</div> <p class=\"mt-2 text-xl text-gray-600\">Reduction in Time-to-Hire</p></div> <div class=\"text-center\"><div class=\"text-5xl font-bold text-blue-600\">3x</div> <p class=\"mt-2 text-xl text-gray-600\">More Qualified Candidates</p></div> <div class=\"text-center\"><div class=\"text-5xl font-bold text-blue-600\">65%</div> <p class=\"mt-2 text-xl text-gray-600\">Cost Savings on Recruitment</p></div></div></div></section> <section class=\"border border-b border-l border-r py-16\"><div class=\"container mx-auto px-4\"><h2 class=\"mb-16 text-center text-4xl font-light\">Key Features for Employers</h2> <div class=\"grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3\"><div class=\"rounded-lg border border-gray-200 bg-white p-8 shadow-md transition-shadow hover:shadow-lg\"><div class=\"mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100 text-blue-600\">`;\n  Search($$payload, { class: \"h-8 w-8\" });\n  $$payload.out += `<!----></div> <h3 class=\"mb-4 text-2xl font-medium\">AI-Powered Matching</h3> <p class=\"text-gray-600\">Our advanced algorithms analyze job requirements and candidate profiles to find the\n          perfect match, reducing screening time by up to 75%.</p></div> <div class=\"rounded-lg border border-gray-200 bg-white p-8 shadow-md transition-shadow hover:shadow-lg\"><div class=\"mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100 text-blue-600\">`;\n  Chart_column($$payload, { class: \"h-8 w-8\" });\n  $$payload.out += `<!----></div> <h3 class=\"mb-4 text-2xl font-medium\">Recruitment Analytics</h3> <p class=\"text-gray-600\">Gain valuable insights into your hiring process with comprehensive analytics dashboards\n          that help you optimize your recruitment strategy.</p></div> <div class=\"rounded-lg border border-gray-200 bg-white p-8 shadow-md transition-shadow hover:shadow-lg\"><div class=\"mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100 text-blue-600\">`;\n  Clock($$payload, { class: \"h-8 w-8\" });\n  $$payload.out += `<!----></div> <h3 class=\"mb-4 text-2xl font-medium\">Automated Screening</h3> <p class=\"text-gray-600\">Automate initial candidate screening with customizable assessment tools that evaluate\n          skills, experience, and cultural fit.</p></div> <div class=\"rounded-lg border border-gray-200 bg-white p-8 shadow-md transition-shadow hover:shadow-lg\"><div class=\"mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100 text-blue-600\">`;\n  Shield($$payload, { class: \"h-8 w-8\" });\n  $$payload.out += `<!----></div> <h3 class=\"mb-4 text-2xl font-medium\">Enterprise Security</h3> <p class=\"text-gray-600\">Protect sensitive candidate data with enterprise-grade security measures, including\n          encryption, access controls, and compliance with regulations.</p></div> <div class=\"rounded-lg border border-gray-200 bg-white p-8 shadow-md transition-shadow hover:shadow-lg\"><div class=\"mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100 text-blue-600\">`;\n  Building($$payload, { class: \"h-8 w-8\" });\n  $$payload.out += `<!----></div> <h3 class=\"mb-4 text-2xl font-medium\">Employer Branding</h3> <p class=\"text-gray-600\">Showcase your company culture and values with customizable employer profiles that attract\n          top talent to your organization.</p></div> <div class=\"rounded-lg border border-gray-200 bg-white p-8 shadow-md transition-shadow hover:shadow-lg\"><div class=\"mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100 text-blue-600\">`;\n  User_check($$payload, { class: \"h-8 w-8\" });\n  $$payload.out += `<!----></div> <h3 class=\"mb-4 text-2xl font-medium\">Seamless Integration</h3> <p class=\"text-gray-600\">Integrate with your existing HR tech stack, including ATS, HRIS, and calendar systems for\n          a streamlined recruitment workflow.</p></div></div> <div class=\"mt-16 text-center\">`;\n  Button($$payload, {\n    class: \"rounded-none border border-transparent bg-neutral-200 p-6 text-lg font-medium text-white transition-colors hover:bg-blue-600\",\n    children: ($$payload2) => {\n      $$payload2.out += `<!---->Explore All Features`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----></div></div></section> <section id=\"talent-acquisition\" class=\"border border-b border-l border-r border-neutral-200\"><div class=\"flex flex-col\"><div class=\"md:grid-cols-16 flex flex-col border border-t-neutral-500 [--column-count:8] md:grid md:grid-rows-8 md:[--column-count:16]\"><div class=\"p-15 text-primary col-span-8 row-span-8 row-start-1 flex aspect-auto flex-col justify-between md:aspect-square md:items-center md:justify-center\"><div class=\"gap-50 flex max-w-[280px] flex-1 flex-col justify-between md:max-w-[400px] md:flex-[unset]\"><div class=\"flex flex-col gap-20\"><h3 class=\"font-light! max-w-3xs text-6xl\">${escape_html(features.talentAcquisition.title)}</h3> <p class=\"typography font-montreal text-xl\">${escape_html(features.talentAcquisition.description)}</p> <a href=\"#contact\" class=\"flex w-48 flex-row items-center justify-between rounded-md bg-blue-500 px-6 py-3 text-white transition-colors hover:bg-blue-600\">Get Started `;\n  Arrow_right($$payload, { class: \"ml-2 h-4 w-4\" });\n  $$payload.out += `<!----></a></div></div></div> <div class=\"bg-grid border-left-neutral col-span-8 col-start-9 row-span-8 row-start-1 border border-b border-r border-t\"></div></div> <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4\"><!--[-->`;\n  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n    let feature = each_array[$$index];\n    $$payload.out += `<div class=\"p-22 rounded-none border border-gray-100 bg-white shadow-md transition-shadow duration-300 hover:shadow-lg\"><div class=\"mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500/10\"><!---->`;\n    feature.icon?.($$payload, { class: \"h-6 w-6 text-blue-500\" });\n    $$payload.out += `<!----></div> <h3 class=\"font-normal! mb-4 text-3xl\">${escape_html(feature.title)}</h3> <p class=\"text-md text-gray-600\">${escape_html(feature.description)}</p></div>`;\n  }\n  $$payload.out += `<!--]--></div></div></section> <section id=\"employer-branding\" class=\"border border-b border-l border-r border-neutral-200\"><div class=\"flex flex-col\"><div class=\"md:grid-cols-16 flex flex-col border border-t-neutral-500 [--column-count:8] md:grid md:grid-rows-8 md:[--column-count:16]\"><div class=\"bg-grid bg-grid-blue-200 dark:bg-grid-blue-600 col-span-8 col-start-1 row-span-8 row-start-1 border border-b border-l border-neutral-200\"></div> <div class=\"p-15 text-text-primary col-span-8 col-start-9 row-span-8 row-start-1 flex aspect-auto flex-col justify-between md:aspect-square md:items-center md:justify-center\"><div class=\"gap-50 flex max-w-[280px] flex-1 flex-col justify-between md:max-w-[400px] md:flex-[unset]\"><div class=\"flex flex-col gap-20\"><h3 class=\"font-light! max-w-3xs text-6xl\">${escape_html(features.employerBranding.title)}</h3> <p class=\"typography font-montreal text-xl\">${escape_html(features.employerBranding.description)}</p> <a href=\"#contact\" class=\"flex w-48 flex-row items-center justify-between rounded-md bg-blue-500 px-6 py-3 text-white transition-colors hover:bg-blue-600\">Learn More `;\n  Arrow_right($$payload, { class: \"ml-2 h-4 w-4\" });\n  $$payload.out += `<!----></a></div></div></div></div> <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4\"><!--[-->`;\n  for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {\n    let feature = each_array_1[$$index_1];\n    $$payload.out += `<div class=\"p-22 rounded-none border border-gray-100 bg-white shadow-md transition-shadow duration-300 hover:shadow-lg\"><div class=\"mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500/10\"><!---->`;\n    feature.icon?.($$payload, { class: \"h-6 w-6 text-blue-500\" });\n    $$payload.out += `<!----></div> <h3 class=\"font-normal! mb-4 text-3xl\">${escape_html(feature.title)}</h3> <p class=\"text-md text-gray-600\">${escape_html(feature.description)}</p></div>`;\n  }\n  $$payload.out += `<!--]--></div></div></section> <section class=\"border border-b border-l border-r py-16\"><div class=\"container mx-auto px-4\"><h2 class=\"mb-12 text-center text-4xl font-light\">What Our Clients Say</h2> <div class=\"grid grid-cols-1 gap-8 md:grid-cols-3\"><!--[-->`;\n  for (let $$index_2 = 0, $$length = each_array_2.length; $$index_2 < $$length; $$index_2++) {\n    let testimonial = each_array_2[$$index_2];\n    $$payload.out += `<div class=\"rounded-lg border border-gray-200 bg-white p-8 shadow-md\"><p class=\"mb-4 text-gray-600\">\"${escape_html(testimonial.quote)}\"</p> <div class=\"flex items-center\"><div class=\"mr-4 h-12 w-12 rounded-full bg-gray-300\"></div> <div><p class=\"font-semibold\">${escape_html(testimonial.author)}</p> <p class=\"text-sm text-gray-600\">${escape_html(testimonial.position)}, ${escape_html(testimonial.company)}</p></div></div></div>`;\n  }\n  $$payload.out += `<!--]--></div></div></section> <section id=\"contact\" class=\"border border-b border-l border-r py-16\"><div class=\"container mx-auto px-4\"><div class=\"mx-auto max-w-4xl\"><h2 class=\"mb-6 text-center text-4xl font-light\">Ready to Transform Your Hiring Process?</h2> <p class=\"mx-auto mb-12 max-w-2xl text-center text-xl text-gray-600\">Fill out the form below to schedule a demo with our team and see how Hirli can help you find\n        and hire the best talent faster.</p> <div class=\"grid grid-cols-1 gap-8 md:grid-cols-2\"><div class=\"rounded-lg border border-gray-200 bg-white p-8 shadow-md\"><form class=\"space-y-6\"><div class=\"space-y-2\"><label for=\"name\" class=\"text-sm font-medium\">Full Name</label> `;\n  Input($$payload, {\n    id: \"name\",\n    type: \"text\",\n    placeholder: \"John Doe\",\n    class: \"w-full\"\n  });\n  $$payload.out += `<!----></div> <div class=\"space-y-2\"><label for=\"company\" class=\"text-sm font-medium\">Company</label> `;\n  Input($$payload, {\n    id: \"company\",\n    type: \"text\",\n    placeholder: \"Acme Inc.\",\n    class: \"w-full\"\n  });\n  $$payload.out += `<!----></div> <div class=\"space-y-2\"><label for=\"email\" class=\"text-sm font-medium\">Work Email</label> `;\n  Input($$payload, {\n    id: \"email\",\n    type: \"email\",\n    placeholder: \"<EMAIL>\",\n    class: \"w-full\"\n  });\n  $$payload.out += `<!----></div> <div class=\"space-y-2\"><label for=\"phone\" class=\"text-sm font-medium\">Phone Number</label> `;\n  Input($$payload, {\n    id: \"phone\",\n    type: \"tel\",\n    placeholder: \"(*************\",\n    class: \"w-full\"\n  });\n  $$payload.out += `<!----></div> <div class=\"space-y-2\"><label for=\"message\" class=\"text-sm font-medium\">How can we help?</label> `;\n  Textarea($$payload, {\n    id: \"message\",\n    placeholder: \"Tell us about your hiring needs...\",\n    class: \"min-h-[120px] w-full\"\n  });\n  $$payload.out += `<!----></div> `;\n  Button($$payload, {\n    class: \"w-full rounded-none border border-transparent bg-neutral-200 p-6 text-lg font-medium text-white transition-colors hover:bg-blue-600\",\n    children: ($$payload2) => {\n      $$payload2.out += `<!---->Schedule a Demo`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----></form></div> <div class=\"flex flex-col justify-between space-y-8\"><div><h3 class=\"mb-4 text-2xl font-light\">Enterprise Solutions</h3> <p class=\"mb-6 text-gray-600\">Our enterprise solutions are tailored to meet the unique needs of large organizations.\n              Get in touch with our team to learn more about how we can help you streamline your\n              hiring process.</p> <div class=\"space-y-4\"><div class=\"flex items-start\"><div class=\"mr-3 mt-1 flex h-6 w-6 items-center justify-center rounded-full bg-blue-100 text-blue-600\"><svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"h-4 w-4\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75\"></path></svg></div> <div><h4 class=\"font-medium\">Email Us</h4> <p class=\"text-gray-600\"><EMAIL></p></div></div></div></div> <div class=\"rounded-lg bg-gray-50 p-6\"><p class=\"italic text-gray-600\">\"Hirli's enterprise solution has transformed our hiring process. We've reduced our\n              time-to-hire by 40% and improved the quality of our candidates significantly.\"</p> <div class=\"mt-4 flex items-center\"><div class=\"mr-3 h-10 w-10 rounded-full bg-gray-300\"></div> <div><p class=\"font-medium\">Sarah Johnson</p> <p class=\"text-sm text-gray-600\">Head of Talent Acquisition, TechGrowth Inc.</p></div></div></div></div></div></div></div></section> <section id=\"faq\" class=\"border border-b border-l border-r py-16\"><div class=\"container mx-auto px-4\"><div class=\"mx-auto max-w-4xl\"><h2 class=\"mb-12 text-center text-4xl font-light\">Frequently Asked Questions</h2> <div class=\"grid grid-cols-1 gap-8 md:grid-cols-2\"><div class=\"space-y-6\"><div class=\"rounded-lg border border-gray-200 bg-white p-6 shadow-sm\"><h3 class=\"mb-3 text-xl font-medium\">How does Hirli help with talent acquisition?</h3> <p class=\"text-gray-600\">Hirli uses AI to analyze job requirements and candidate profiles, matching the right\n              talent to your open positions. Our platform automates screening, scheduling, and\n              initial interviews, saving your team valuable time.</p></div> <div class=\"rounded-lg border border-gray-200 bg-white p-6 shadow-sm\"><h3 class=\"mb-3 text-xl font-medium\">What size companies can benefit from Hirli?</h3> <p class=\"text-gray-600\">Hirli is designed to scale with your needs. We serve companies of all sizes, from\n              startups to enterprise organizations. Our platform can be customized to match your\n              specific hiring workflows and volume.</p></div> <div class=\"rounded-lg border border-gray-200 bg-white p-6 shadow-sm\"><h3 class=\"mb-3 text-xl font-medium\">How secure is candidate data on your platform?</h3> <p class=\"text-gray-600\">We take security seriously. All candidate data is encrypted and stored securely. We\n              are GDPR and CCPA compliant, and we implement enterprise-grade security measures to\n              protect your information.</p></div></div> <div class=\"space-y-6\"><div class=\"rounded-lg border border-gray-200 bg-white p-6 shadow-sm\"><h3 class=\"mb-3 text-xl font-medium\">Can Hirli integrate with our existing ATS?</h3> <p class=\"text-gray-600\">Yes, Hirli integrates seamlessly with most popular Applicant Tracking Systems. Our API\n              allows for custom integrations with your existing HR tech stack, ensuring a smooth\n              workflow.</p></div> <div class=\"rounded-lg border border-gray-200 bg-white p-6 shadow-sm\"><h3 class=\"mb-3 text-xl font-medium\">How much time can we save using Hirli?</h3> <p class=\"text-gray-600\">Our clients typically report a 40-60% reduction in time-to-hire. By automating\n              repetitive tasks and streamlining the candidate screening process, your team can focus\n              on high-value activities.</p></div> <div class=\"rounded-lg border border-gray-200 bg-white p-6 shadow-sm\"><h3 class=\"mb-3 text-xl font-medium\">What kind of support do you offer?</h3> <p class=\"text-gray-600\">We provide comprehensive support including implementation assistance, training, and\n              ongoing technical support. Enterprise clients receive dedicated account managers and\n              priority support.</p></div></div></div> <div class=\"mt-12 text-center\"><p class=\"mb-6 text-gray-600\">Still have questions about how Hirli can help your organization?</p> `;\n  Button($$payload, {\n    class: \"rounded-none border border-transparent bg-neutral-200 p-6 text-lg font-medium text-white transition-colors hover:bg-blue-600\",\n    children: ($$payload2) => {\n      $$payload2.out += `<!---->Contact Our Team`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----></div></div></div></section> <section class=\"bg-gray-50 py-16\"><div class=\"container mx-auto px-4\"><div class=\"grid grid-cols-1 items-center gap-16 md:grid-cols-2\"><div><h3 class=\"mb-6 text-2xl font-light\">Secure candidate data with enterprise-grade protection</h3> <p class=\"mb-8 text-lg text-gray-600\">Protect sensitive candidate information with our robust security measures, including\n          encryption, access controls, and compliance with data protection regulations.</p> `;\n  Button($$payload, {\n    variant: \"link\",\n    class: \"flex items-center gap-2 p-0 text-lg text-black\",\n    children: ($$payload2) => {\n      $$payload2.out += `<!---->Learn more about our security features `;\n      Arrow_right($$payload2, { class: \"h-4 w-4\" });\n      $$payload2.out += `<!---->`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----></div> <div><img src=\"/images/employers-security.jpg\" alt=\"Security Features\" class=\"h-auto w-full shadow-lg\"/></div></div></div></section> <section class=\"py-24\"><div class=\"container mx-auto px-4\"><div class=\"mx-auto max-w-4xl\"><h2 class=\"mb-16 text-center text-3xl font-light\">Explore how companies use Hirli</h2> <div class=\"relative aspect-video bg-gray-100 shadow-lg\"><div class=\"absolute inset-0 flex items-center justify-center\"><img src=\"/images/video-thumbnail.jpg\" alt=\"Video Thumbnail\" class=\"h-full w-full object-cover\"/> <div class=\"absolute inset-0 flex items-center justify-center bg-black bg-opacity-30\"><button class=\"flex h-20 w-20 items-center justify-center rounded-full bg-white\" aria-label=\"Play video\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"ml-1 text-black\"><polygon points=\"5 3 19 12 5 21 5 3\"></polygon></svg></button></div></div></div> <div class=\"mt-16 bg-gray-50 p-12\"><blockquote class=\"mb-8 text-2xl font-light italic\">\"Hirli has completely transformed our hiring process. We've reduced our time-to-hire by\n          60% and improved the quality of our candidates significantly.\"</blockquote> <div class=\"flex items-center\"><img src=\"https://randomuser.me/api/portraits/women/42.jpg\" alt=\"Sarah Johnson\" class=\"mr-4 h-12 w-12 rounded-full\"/> <div><p class=\"font-medium\">Sarah Johnson</p> <p class=\"text-gray-600\">Head of Talent Acquisition, TechCorp</p></div></div></div></div></div></section> <footer class=\"border border-b border-l border-r bg-gray-50 py-12\"><div class=\"container mx-auto px-4\"><div class=\"grid grid-cols-1 gap-8 md:grid-cols-4\"><div><h3 class=\"mb-4 text-lg font-semibold\">Hirli for Employers</h3> <p class=\"text-gray-600\">The AI-powered platform that helps you find and hire the best talent faster.</p></div> <div><h3 class=\"mb-4 text-lg font-semibold\">Features</h3> <ul class=\"space-y-2 text-gray-600\"><li><a href=\"#talent-acquisition\" class=\"hover:text-blue-500\">Talent Acquisition</a></li> <li><a href=\"#employer-branding\" class=\"hover:text-blue-500\">Employer Branding</a></li> <li><a href=\"#candidate-experience\" class=\"hover:text-blue-500\">Candidate Experience</a></li></ul></div> <div><h3 class=\"mb-4 text-lg font-semibold\">Resources</h3> <ul class=\"space-y-2 text-gray-600\"><li><a href=\"/blog\" class=\"hover:text-blue-500\">Blog</a></li> <li><a href=\"/case-studies\" class=\"hover:text-blue-500\">Case Studies</a></li> <li><a href=\"/webinars\" class=\"hover:text-blue-500\">Webinars</a></li></ul></div> <div><h3 class=\"mb-4 text-lg font-semibold\">Contact</h3> <ul class=\"space-y-2 text-gray-600\"><li>Email: <EMAIL></li> <li>Address: 123 Main St, City, Country</li></ul></div></div> <div class=\"mt-8 border-t border-gray-200 pt-8 text-center text-gray-600\"><p>© 2023 Hirli. All rights reserved.</p></div></div></footer>`;\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAaA,SAAS,KAAK,CAAC,SAAS,EAAE;AAC1B,EAAE,MAAM,QAAQ,GAAG;AACnB,IAAI,iBAAiB,EAAE;AACvB,MAAM,KAAK,EAAE,gCAAgC;AAC7C,MAAM,WAAW,EAAE,qFAAqF;AACxG,MAAM,SAAS,EAAE;AACjB,QAAQ;AACR,UAAU,IAAI,EAAE,MAAM;AACtB,UAAU,KAAK,EAAE,0BAA0B;AAC3C,UAAU,WAAW,EAAE;AACvB,SAAS;AACT,QAAQ;AACR,UAAU,IAAI,EAAE,YAAY;AAC5B,UAAU,KAAK,EAAE,kBAAkB;AACnC,UAAU,WAAW,EAAE;AACvB,SAAS;AACT,QAAQ;AACR,UAAU,IAAI,EAAE,KAAK;AACrB,UAAU,KAAK,EAAE,qBAAqB;AACtC,UAAU,WAAW,EAAE;AACvB,SAAS;AACT,QAAQ;AACR,UAAU,IAAI,EAAE,MAAM;AACtB,UAAU,KAAK,EAAE,mBAAmB;AACpC,UAAU,WAAW,EAAE;AACvB;AACA;AACA,KAAK;AACL,IAAI,gBAAgB,EAAE;AACtB,MAAM,KAAK,EAAE,6BAA6B;AAC1C,MAAM,WAAW,EAAE,4FAA4F;AAC/G,MAAM,SAAS,EAAE;AACjB,QAAQ;AACR,UAAU,IAAI,EAAE,QAAQ;AACxB,UAAU,KAAK,EAAE,8BAA8B;AAC/C,UAAU,WAAW,EAAE;AACvB,SAAS;AACT,QAAQ;AACR,UAAU,IAAI,EAAE,UAAU;AAC1B,UAAU,KAAK,EAAE,sBAAsB;AACvC,UAAU,WAAW,EAAE;AACvB,SAAS;AACT,QAAQ;AACR,UAAU,IAAI,EAAE,MAAM;AACtB,UAAU,KAAK,EAAE,mBAAmB;AACpC,UAAU,WAAW,EAAE;AACvB,SAAS;AACT,QAAQ;AACR,UAAU,IAAI,EAAE,KAAK;AACrB,UAAU,KAAK,EAAE,uBAAuB;AACxC,UAAU,WAAW,EAAE;AACvB;AACA;AACA;AACA,GAAG;AACH,EAAE,MAAM,YAAY,GAAG;AACvB,IAAI;AACJ,MAAM,KAAK,EAAE,gJAAgJ;AAC7J,MAAM,MAAM,EAAE,eAAe;AAC7B,MAAM,QAAQ,EAAE,4BAA4B;AAC5C,MAAM,OAAO,EAAE;AACf,KAAK;AACL,IAAI;AACJ,MAAM,KAAK,EAAE,0IAA0I;AACvJ,MAAM,MAAM,EAAE,cAAc;AAC5B,MAAM,QAAQ,EAAE,gBAAgB;AAChC,MAAM,OAAO,EAAE;AACf,KAAK;AACL,IAAI;AACJ,MAAM,KAAK,EAAE,2GAA2G;AACxH,MAAM,MAAM,EAAE,kBAAkB;AAChC,MAAM,QAAQ,EAAE,qBAAqB;AACrC,MAAM,OAAO,EAAE;AACf;AACA,GAAG;AACH,EAAE,MAAM,UAAU,GAAG,iBAAiB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,SAAS,CAAC;AAC5E,EAAE,MAAM,YAAY,GAAG,iBAAiB,CAAC,QAAQ,CAAC,gBAAgB,CAAC,SAAS,CAAC;AAC7E,EAAE,MAAM,YAAY,GAAG,iBAAiB,CAAC,YAAY,CAAC;AACtD,EAAE,GAAG,CAAC,SAAS,EAAE;AACjB,IAAI,KAAK,EAAE,sDAAsD;AACjE,IAAI,WAAW,EAAE,wIAAwI;AACzJ,IAAI,QAAQ,EAAE,2EAA2E;AACzF,IAAI,GAAG,EAAE,6BAA6B;AACtC,IAAI,KAAK,EAAE;AACX,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC;AACpB,kHAAkH,CAAC;AACnH,EAAE,MAAM,CAAC,SAAS,EAAE;AACpB,IAAI,KAAK,EAAE,8HAA8H;AACzI,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC;AAChD,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,MAAM,CAAC,SAAS,EAAE;AACpB,IAAI,KAAK,EAAE,wHAAwH;AACnI,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;AAC5C,MAAM,WAAW,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AACxD,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACjC,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,kyDAAkyD,CAAC;AACvzD,EAAE,MAAM,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AACzC,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC;AACpB,qRAAqR,CAAC;AACtR,EAAE,YAAY,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AAC/C,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC;AACpB,kRAAkR,CAAC;AACnR,EAAE,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AACxC,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC;AACpB,sQAAsQ,CAAC;AACvQ,EAAE,MAAM,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AACzC,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC;AACpB,8RAA8R,CAAC;AAC/R,EAAE,QAAQ,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AAC3C,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC;AACpB,iQAAiQ,CAAC;AAClQ,EAAE,UAAU,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AAC7C,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC;AACpB,6FAA6F,CAAC;AAC9F,EAAE,MAAM,CAAC,SAAS,EAAE;AACpB,IAAI,KAAK,EAAE,8HAA8H;AACzI,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,2BAA2B,CAAC;AACrD,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,knBAAknB,EAAE,WAAW,CAAC,QAAQ,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,kDAAkD,EAAE,WAAW,CAAC,QAAQ,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC,4KAA4K,CAAC;AAC38B,EAAE,WAAW,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AACnD,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,wOAAwO,CAAC;AAC7P,EAAE,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACrF,IAAI,IAAI,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;AACrC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,sNAAsN,CAAC;AAC7O,IAAI,OAAO,CAAC,IAAI,GAAG,SAAS,EAAE,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC;AACjE,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,qDAAqD,EAAE,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,uCAAuC,EAAE,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,UAAU,CAAC;AAC7L;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,gyBAAgyB,EAAE,WAAW,CAAC,QAAQ,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,kDAAkD,EAAE,WAAW,CAAC,QAAQ,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,2KAA2K,CAAC;AACtnC,EAAE,WAAW,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AACnD,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,wGAAwG,CAAC;AAC7H,EAAE,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,QAAQ,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,QAAQ,EAAE,SAAS,EAAE,EAAE;AAC7F,IAAI,IAAI,OAAO,GAAG,YAAY,CAAC,SAAS,CAAC;AACzC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,sNAAsN,CAAC;AAC7O,IAAI,OAAO,CAAC,IAAI,GAAG,SAAS,EAAE,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC;AACjE,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,qDAAqD,EAAE,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,uCAAuC,EAAE,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,UAAU,CAAC;AAC7L;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,mQAAmQ,CAAC;AACxR,EAAE,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,QAAQ,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,QAAQ,EAAE,SAAS,EAAE,EAAE;AAC7F,IAAI,IAAI,WAAW,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,qGAAqG,EAAE,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,+HAA+H,EAAE,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,sCAAsC,EAAE,WAAW,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,sBAAsB,CAAC;AACnc;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC;AACpB,qRAAqR,CAAC;AACtR,EAAE,KAAK,CAAC,SAAS,EAAE;AACnB,IAAI,EAAE,EAAE,MAAM;AACd,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,WAAW,EAAE,UAAU;AAC3B,IAAI,KAAK,EAAE;AACX,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,sGAAsG,CAAC;AAC3H,EAAE,KAAK,CAAC,SAAS,EAAE;AACnB,IAAI,EAAE,EAAE,SAAS;AACjB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,WAAW,EAAE,WAAW;AAC5B,IAAI,KAAK,EAAE;AACX,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,uGAAuG,CAAC;AAC5H,EAAE,KAAK,CAAC,SAAS,EAAE;AACnB,IAAI,EAAE,EAAE,OAAO;AACf,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,WAAW,EAAE,kBAAkB;AACnC,IAAI,KAAK,EAAE;AACX,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,yGAAyG,CAAC;AAC9H,EAAE,KAAK,CAAC,SAAS,EAAE;AACnB,IAAI,EAAE,EAAE,OAAO;AACf,IAAI,IAAI,EAAE,KAAK;AACf,IAAI,WAAW,EAAE,gBAAgB;AACjC,IAAI,KAAK,EAAE;AACX,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,+GAA+G,CAAC;AACpI,EAAE,QAAQ,CAAC,SAAS,EAAE;AACtB,IAAI,EAAE,EAAE,SAAS;AACjB,IAAI,WAAW,EAAE,oCAAoC;AACrD,IAAI,KAAK,EAAE;AACX,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACnC,EAAE,MAAM,CAAC,SAAS,EAAE;AACpB,IAAI,KAAK,EAAE,qIAAqI;AAChJ,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC;AAChD,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wLAAwL,CAAC;AACzL,EAAE,MAAM,CAAC,SAAS,EAAE;AACpB,IAAI,KAAK,EAAE,8HAA8H;AACzI,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC;AACjD,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC;AACpB,4FAA4F,CAAC;AAC7F,EAAE,MAAM,CAAC,SAAS,EAAE;AACpB,IAAI,OAAO,EAAE,MAAM;AACnB,IAAI,KAAK,EAAE,gDAAgD;AAC3D,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,8CAA8C,CAAC;AACxE,MAAM,WAAW,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AACnD,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACjC,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC;AACpB,ysDAAysD,CAAC;AAC1sD;;;;"}