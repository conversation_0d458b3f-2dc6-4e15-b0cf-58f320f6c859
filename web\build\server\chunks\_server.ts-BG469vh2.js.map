{"version": 3, "file": "_server.ts-BG469vh2.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/resume/default/_server.ts.js"], "sourcesContent": ["import { p as prisma } from \"../../../../../chunks/prisma.js\";\nconst POST = async ({ request, locals }) => {\n  const { resumeId } = await request.json();\n  const user = locals.user;\n  if (!user) return new Response(\"Unauthorized\", { status: 401 });\n  const resume = await prisma.resume.findUnique({\n    where: { id: resumeId },\n    include: { profile: true }\n  });\n  if (!resume || resume.profile.userId !== user.id && resume.profile.teamId === null) {\n    return new Response(\"Forbidden\", { status: 403 });\n  }\n  await prisma.profile.update({\n    where: { id: resume.profileId },\n    data: { defaultResumeId: resume.id }\n  });\n  return new Response(\"Set as default\", { status: 200 });\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;AACK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AAC5C,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AAC3C,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE,EAAE,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;AAChD,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE;AAC3B,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC,EAAE,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,KAAK,IAAI,EAAE;AACtF,IAAI,OAAO,IAAI,QAAQ,CAAC,WAAW,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACrD;AACA,EAAE,MAAM,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;AAC9B,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,SAAS,EAAE;AACnC,IAAI,IAAI,EAAE,EAAE,eAAe,EAAE,MAAM,CAAC,EAAE;AACtC,GAAG,CAAC;AACJ,EAAE,OAAO,IAAI,QAAQ,CAAC,gBAAgB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxD;;;;"}