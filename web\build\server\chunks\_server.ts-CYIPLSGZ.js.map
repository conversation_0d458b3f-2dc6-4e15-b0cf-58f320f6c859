{"version": 3, "file": "_server.ts-CYIPLSGZ.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/resume/rename/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { e as ensureUniqueDocumentName } from \"../../../../../chunks/documentNameUniqueness.js\";\nimport { d as determineDocumentSource } from \"../../../../../chunks/documentSource.js\";\nconst POST = async ({ request, locals }) => {\n  const { resumeId, label } = await request.json();\n  const user = locals.user;\n  if (!user) return new Response(\"Unauthorized\", { status: 401 });\n  const resume = await prisma.resume.findUnique({\n    where: { id: resumeId },\n    include: { document: true }\n  });\n  if (!resume) {\n    return new Response(\"Resume not found\", { status: 404 });\n  }\n  if (resume.document.userId !== user.id) {\n    return new Response(\"Forbidden\", { status: 403 });\n  }\n  const finalLabel = await ensureUniqueDocumentName(\n    label,\n    user.id,\n    resume.document.type,\n    resume.document.id\n  );\n  const updatedDocument = await prisma.document.update({\n    where: { id: resume.document.id },\n    data: { label: finalLabel }\n  });\n  const source = determineDocumentSource(updatedDocument);\n  return json({\n    success: true,\n    label: finalLabel,\n    source\n    // Include the source in the response\n  });\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;AAIK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AAC5C,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AAClD,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE,EAAE,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;AAChD,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE;AAC3B,IAAI,OAAO,EAAE,EAAE,QAAQ,EAAE,IAAI;AAC7B,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,MAAM,EAAE;AACf,IAAI,OAAO,IAAI,QAAQ,CAAC,kBAAkB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC5D;AACA,EAAE,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,KAAK,IAAI,CAAC,EAAE,EAAE;AAC1C,IAAI,OAAO,IAAI,QAAQ,CAAC,WAAW,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACrD;AACA,EAAE,MAAM,UAAU,GAAG,MAAM,wBAAwB;AACnD,IAAI,KAAK;AACT,IAAI,IAAI,CAAC,EAAE;AACX,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI;AACxB,IAAI,MAAM,CAAC,QAAQ,CAAC;AACpB,GAAG;AACH,EAAE,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;AACvD,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,QAAQ,CAAC,EAAE,EAAE;AACrC,IAAI,IAAI,EAAE,EAAE,KAAK,EAAE,UAAU;AAC7B,GAAG,CAAC;AACJ,EAAE,MAAM,MAAM,GAAG,uBAAuB,CAAC,eAAe,CAAC;AACzD,EAAE,OAAO,IAAI,CAAC;AACd,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,KAAK,EAAE,UAAU;AACrB,IAAI;AACJ;AACA,GAAG,CAAC;AACJ;;;;"}