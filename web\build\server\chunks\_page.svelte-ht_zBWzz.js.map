{"version": 3, "file": "_page.svelte-ht_zBWzz.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/press/releases/_slug_/_page.svelte.js"], "sourcesContent": ["import { V as escape_html, R as attr, U as ensure_array_like, N as bind_props, y as pop, w as push } from \"../../../../../chunks/index3.js\";\nimport { S as SEO } from \"../../../../../chunks/SEO.js\";\nimport \"../../../../../chunks/button.js\";\nimport { u as urlFor } from \"../../../../../chunks/client2.js\";\nimport { P as PortableText } from \"../../../../../chunks/PortableText.js\";\nimport { A as Arrow_left } from \"../../../../../chunks/arrow-left.js\";\nimport { C as Calendar } from \"../../../../../chunks/calendar.js\";\nimport { M as Map_pin } from \"../../../../../chunks/map-pin.js\";\nimport { D as Download } from \"../../../../../chunks/download.js\";\nimport { E as External_link } from \"../../../../../chunks/external-link.js\";\nfunction _page($$payload, $$props) {\n  push();\n  let data = $$props[\"data\"];\n  const { pressRelease, relatedPressReleases } = data;\n  function formatDate(dateStr) {\n    const date = new Date(dateStr);\n    return date.toLocaleDateString(\"en-US\", {\n      year: \"numeric\",\n      month: \"long\",\n      day: \"numeric\"\n    });\n  }\n  SEO($$payload, {\n    title: pressRelease.title,\n    description: pressRelease.excerpt || \"\",\n    keywords: pressRelease.categories?.map((c) => c.title).join(\", \") || \"\"\n  });\n  $$payload.out += `<!----> <div class=\"container mx-auto px-4 py-12\"><div class=\"mb-8\"><a href=\"/press/releases\" class=\"text-primary inline-flex items-center text-sm hover:underline\">`;\n  Arrow_left($$payload, { class: \"mr-1 h-4 w-4\" });\n  $$payload.out += `<!----> Back to Press Releases</a></div> <div class=\"grid gap-8 lg:grid-cols-3\"><div class=\"lg:col-span-2\"><article><header class=\"mb-8\"><div class=\"mb-4 flex items-center gap-2\">`;\n  Calendar($$payload, { class: \"h-5 w-5 text-gray-500\" });\n  $$payload.out += `<!----> <p class=\"text-muted-foreground text-sm\">${escape_html(formatDate(pressRelease.publishedAt))}</p> `;\n  if (pressRelease.location) {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<span class=\"mx-1 text-gray-400\">•</span> `;\n    Map_pin($$payload, { class: \"h-5 w-5 text-gray-500\" });\n    $$payload.out += `<!----> <p class=\"text-muted-foreground text-sm\">${escape_html(pressRelease.location)}</p>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--></div> <h1 class=\"mb-4 text-3xl font-bold\">${escape_html(pressRelease.title)}</h1> `;\n  if (pressRelease.subtitle) {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<p class=\"mb-4 text-xl text-gray-700\">${escape_html(pressRelease.subtitle)}</p>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--></header> `;\n  if (pressRelease.mainImage) {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<div class=\"mb-8 overflow-hidden rounded-lg\"><img${attr(\"src\", urlFor(pressRelease.mainImage, { width: 800 }))}${attr(\"alt\", pressRelease.mainImage.alt || pressRelease.title)} class=\"w-full\"/></div>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--> `;\n  if (pressRelease.body) {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<div class=\"prose prose-lg max-w-none\">`;\n    PortableText($$payload, { value: pressRelease.body });\n    $$payload.out += `<!----></div>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--> `;\n  if (pressRelease.pdfAttachment) {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<div class=\"mt-8\"><a${attr(\"href\", pressRelease.pdfAttachment.asset.url)} target=\"_blank\" rel=\"noopener noreferrer\" class=\"bg-primary hover:bg-primary/90 inline-flex items-center rounded-md px-4 py-2 text-white\">`;\n    Download($$payload, { class: \"mr-2 h-5 w-5\" });\n    $$payload.out += `<!----> Download PDF</a></div>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--> `;\n  if (pressRelease.boilerplate) {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<div class=\"mt-12 border-t pt-8\"><h2 class=\"mb-4 text-xl font-semibold\">About Hirli</h2> <p class=\"text-gray-700\">${escape_html(pressRelease.boilerplate)}</p></div>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--> `;\n  if (pressRelease.contactInfo) {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<div class=\"mt-8 border-t pt-8\"><h2 class=\"mb-4 text-xl font-semibold\">Media Contact</h2> <div class=\"text-gray-700\"><p class=\"font-medium\">${escape_html(pressRelease.contactInfo.name)}</p> `;\n    if (pressRelease.contactInfo.email) {\n      $$payload.out += \"<!--[-->\";\n      $$payload.out += `<p><a${attr(\"href\", `mailto:${pressRelease.contactInfo.email}`)} class=\"text-primary hover:underline\">${escape_html(pressRelease.contactInfo.email)}</a></p>`;\n    } else {\n      $$payload.out += \"<!--[!-->\";\n    }\n    $$payload.out += `<!--]--> `;\n    if (pressRelease.contactInfo.phone) {\n      $$payload.out += \"<!--[-->\";\n      $$payload.out += `<p>${escape_html(pressRelease.contactInfo.phone)}</p>`;\n    } else {\n      $$payload.out += \"<!--[!-->\";\n    }\n    $$payload.out += `<!--]--></div></div>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--></article></div> <div>`;\n  if (relatedPressReleases && relatedPressReleases.length > 0) {\n    $$payload.out += \"<!--[-->\";\n    const each_array = ensure_array_like(relatedPressReleases);\n    $$payload.out += `<div class=\"sticky top-8 rounded-lg bg-gray-50 p-6\"><h3 class=\"mb-6 text-xl font-semibold\">Related Press Releases</h3> <div class=\"space-y-6\"><!--[-->`;\n    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n      let release = each_array[$$index];\n      $$payload.out += `<div class=\"border-b pb-4 last:border-0 last:pb-0\"><p class=\"text-muted-foreground mb-2 text-sm\">${escape_html(formatDate(release.publishedAt))}</p> <h4 class=\"mb-2 font-medium\">${escape_html(release.title)}</h4> `;\n      if (release.excerpt) {\n        $$payload.out += \"<!--[-->\";\n        $$payload.out += `<p class=\"mb-2 text-sm text-gray-600\">${escape_html(release.excerpt.substring(0, 100))}...</p>`;\n      } else {\n        $$payload.out += \"<!--[!-->\";\n      }\n      $$payload.out += `<!--]--> <a${attr(\"href\", `/press/releases/${release.slug.current}`)} class=\"text-primary inline-flex items-center text-sm font-medium hover:underline\">Read More `;\n      External_link($$payload, { class: \"ml-1 h-3 w-3\" });\n      $$payload.out += `<!----></a></div>`;\n    }\n    $$payload.out += `<!--]--></div></div>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--></div></div></div>`;\n  bind_props($$props, { data });\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAUA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC;AAC5B,EAAE,MAAM,EAAE,YAAY,EAAE,oBAAoB,EAAE,GAAG,IAAI;AACrD,EAAE,SAAS,UAAU,CAAC,OAAO,EAAE;AAC/B,IAAI,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC;AAClC,IAAI,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE;AAC5C,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,KAAK,EAAE,MAAM;AACnB,MAAM,GAAG,EAAE;AACX,KAAK,CAAC;AACN;AACA,EAAE,GAAG,CAAC,SAAS,EAAE;AACjB,IAAI,KAAK,EAAE,YAAY,CAAC,KAAK;AAC7B,IAAI,WAAW,EAAE,YAAY,CAAC,OAAO,IAAI,EAAE;AAC3C,IAAI,QAAQ,EAAE,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;AACzE,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,oKAAoK,CAAC;AACzL,EAAE,UAAU,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AAClD,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,mLAAmL,CAAC;AACxM,EAAE,QAAQ,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC;AACzD,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,iDAAiD,EAAE,WAAW,CAAC,UAAU,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC;AAC/H,EAAE,IAAI,YAAY,CAAC,QAAQ,EAAE;AAC7B,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,0CAA0C,CAAC;AACjE,IAAI,OAAO,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC;AAC1D,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,iDAAiD,EAAE,WAAW,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC;AACjH,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,mDAAmD,EAAE,WAAW,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AAChH,EAAE,IAAI,YAAY,CAAC,QAAQ,EAAE;AAC7B,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,sCAAsC,EAAE,WAAW,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC;AACtG,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;AACvC,EAAE,IAAI,YAAY,CAAC,SAAS,EAAE;AAC9B,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,iDAAiD,EAAE,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,YAAY,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC,SAAS,CAAC,GAAG,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC,uBAAuB,CAAC;AAC7N,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AAC9B,EAAE,IAAI,YAAY,CAAC,IAAI,EAAE;AACzB,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,uCAAuC,CAAC;AAC9D,IAAI,YAAY,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,YAAY,CAAC,IAAI,EAAE,CAAC;AACzD,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACpC,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AAC9B,EAAE,IAAI,YAAY,CAAC,aAAa,EAAE;AAClC,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,2IAA2I,CAAC;AAC3O,IAAI,QAAQ,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AAClD,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,8BAA8B,CAAC;AACrD,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AAC9B,EAAE,IAAI,YAAY,CAAC,WAAW,EAAE;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,kHAAkH,EAAE,WAAW,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,UAAU,CAAC;AAC3L,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AAC9B,EAAE,IAAI,YAAY,CAAC,WAAW,EAAE;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,4IAA4I,EAAE,WAAW,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC;AACrN,IAAI,IAAI,YAAY,CAAC,WAAW,CAAC,KAAK,EAAE;AACxC,MAAM,SAAS,CAAC,GAAG,IAAI,UAAU;AACjC,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,sCAAsC,EAAE,WAAW,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC;AACrL,KAAK,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,IAAI,WAAW;AAClC;AACA,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AAChC,IAAI,IAAI,YAAY,CAAC,WAAW,CAAC,KAAK,EAAE;AACxC,MAAM,SAAS,CAAC,GAAG,IAAI,UAAU;AACjC,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,WAAW,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AAC9E,KAAK,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,IAAI,WAAW;AAClC;AACA,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC;AAC3C,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,8BAA8B,CAAC;AACnD,EAAE,IAAI,oBAAoB,IAAI,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE;AAC/D,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,MAAM,UAAU,GAAG,iBAAiB,CAAC,oBAAoB,CAAC;AAC9D,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,sJAAsJ,CAAC;AAC7K,IAAI,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACvF,MAAM,IAAI,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;AACvC,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,iGAAiG,EAAE,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,kCAAkC,EAAE,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AAC9O,MAAM,IAAI,OAAO,CAAC,OAAO,EAAE;AAC3B,QAAQ,SAAS,CAAC,GAAG,IAAI,UAAU;AACnC,QAAQ,SAAS,CAAC,GAAG,IAAI,CAAC,sCAAsC,EAAE,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC;AACzH,OAAO,MAAM;AACb,QAAQ,SAAS,CAAC,GAAG,IAAI,WAAW;AACpC;AACA,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,gBAAgB,EAAE,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,6FAA6F,CAAC;AAC3L,MAAM,aAAa,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AACzD,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC;AAC1C;AACA,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC;AAC3C,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,0BAA0B,CAAC;AAC/C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,CAAC;AAC/B,EAAE,GAAG,EAAE;AACP;;;;"}