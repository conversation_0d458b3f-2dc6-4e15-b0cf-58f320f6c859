{"version": 3, "file": "_server.ts-C_uOfiGa.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/resume/duplicate/_server.ts.js"], "sourcesContent": ["import { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { j as json } from \"../../../../../chunks/index.js\";\nconst POST = async ({ request, locals }) => {\n  try {\n    const user = locals.user;\n    console.log(\"User from locals:\", user);\n    const userId = user?.id || \"98489dfa-3bde-47e7-81d0-31866951ddfe\";\n    const { sourceResumeId } = await request.json();\n    if (!sourceResumeId) {\n      return json({ error: \"Source resume ID is required\" }, { status: 400 });\n    }\n    console.log(\"Duplicating resume with ID:\", sourceResumeId);\n    const sourceResume = await prisma.resume.findUnique({\n      where: { id: sourceResumeId },\n      include: {\n        document: true\n      }\n    });\n    if (!sourceResume) {\n      return json({ error: \"Source resume not found\" }, { status: 404 });\n    }\n    const newDocument = await prisma.document.create({\n      data: {\n        label: `Copy of ${sourceResume.document.label}`,\n        fileUrl: sourceResume.document.fileUrl,\n        // Use the same file URL\n        filePath: sourceResume.document.filePath,\n        // Use the same file path\n        fileName: sourceResume.document.fileName,\n        type: \"resume\",\n        contentType: sourceResume.document.contentType,\n        storageType: sourceResume.document.storageType,\n        isDefault: false,\n        source: \"created\",\n        // Mark as created since it's created through the app\n        // Connect to the user instead of just providing the userId\n        user: {\n          connect: { id: userId }\n        },\n        // Only connect to profile if profileId is provided\n        ...sourceResume.document.profileId ? { profile: { connect: { id: sourceResume.document.profileId } } } : {},\n        // Only connect to team if teamId is provided\n        ...sourceResume.document.teamId ? { team: { connect: { id: sourceResume.document.teamId } } } : {}\n      }\n    });\n    const newResume = await prisma.resume.create({\n      data: {\n        documentId: newDocument.id,\n        isParsed: sourceResume.isParsed,\n        parsedData: sourceResume.parsedData || {},\n        rawText: sourceResume.rawText || \"\"\n      }\n    });\n    console.log(\"Resume duplicated successfully:\", newResume.id);\n    return json({\n      success: true,\n      id: newResume.id,\n      documentId: newDocument.id,\n      message: \"Resume duplicated successfully\"\n    });\n  } catch (error) {\n    console.error(\"Error duplicating resume:\", error);\n    return json({ error: \"Failed to duplicate resume\", details: String(error) }, { status: 500 });\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;AAEK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AAC5C,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC5B,IAAI,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC;AAC1C,IAAI,MAAM,MAAM,GAAG,IAAI,EAAE,EAAE,IAAI,sCAAsC;AACrE,IAAI,MAAM,EAAE,cAAc,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACnD,IAAI,IAAI,CAAC,cAAc,EAAE;AACzB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,8BAA8B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7E;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,cAAc,CAAC;AAC9D,IAAI,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;AACxD,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,cAAc,EAAE;AACnC,MAAM,OAAO,EAAE;AACf,QAAQ,QAAQ,EAAE;AAClB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,YAAY,EAAE;AACvB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA,IAAI,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;AACrD,MAAM,IAAI,EAAE;AACZ,QAAQ,KAAK,EAAE,CAAC,QAAQ,EAAE,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACvD,QAAQ,OAAO,EAAE,YAAY,CAAC,QAAQ,CAAC,OAAO;AAC9C;AACA,QAAQ,QAAQ,EAAE,YAAY,CAAC,QAAQ,CAAC,QAAQ;AAChD;AACA,QAAQ,QAAQ,EAAE,YAAY,CAAC,QAAQ,CAAC,QAAQ;AAChD,QAAQ,IAAI,EAAE,QAAQ;AACtB,QAAQ,WAAW,EAAE,YAAY,CAAC,QAAQ,CAAC,WAAW;AACtD,QAAQ,WAAW,EAAE,YAAY,CAAC,QAAQ,CAAC,WAAW;AACtD,QAAQ,SAAS,EAAE,KAAK;AACxB,QAAQ,MAAM,EAAE,SAAS;AACzB;AACA;AACA,QAAQ,IAAI,EAAE;AACd,UAAU,OAAO,EAAE,EAAE,EAAE,EAAE,MAAM;AAC/B,SAAS;AACT;AACA,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,YAAY,CAAC,QAAQ,CAAC,SAAS,EAAE,EAAE,EAAE,GAAG,EAAE;AACnH;AACA,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC,MAAM,GAAG,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,YAAY,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,EAAE,GAAG;AACxG;AACA,KAAK,CAAC;AACN,IAAI,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;AACjD,MAAM,IAAI,EAAE;AACZ,QAAQ,UAAU,EAAE,WAAW,CAAC,EAAE;AAClC,QAAQ,QAAQ,EAAE,YAAY,CAAC,QAAQ;AACvC,QAAQ,UAAU,EAAE,YAAY,CAAC,UAAU,IAAI,EAAE;AACjD,QAAQ,OAAO,EAAE,YAAY,CAAC,OAAO,IAAI;AACzC;AACA,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,iCAAiC,EAAE,SAAS,CAAC,EAAE,CAAC;AAChE,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,EAAE,EAAE,SAAS,CAAC,EAAE;AACtB,MAAM,UAAU,EAAE,WAAW,CAAC,EAAE;AAChC,MAAM,OAAO,EAAE;AACf,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC;AACrD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,4BAA4B,EAAE,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjG;AACA;;;;"}