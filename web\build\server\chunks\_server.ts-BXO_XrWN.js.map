{"version": 3, "file": "_server.ts-BXO_XrWN.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/automation/runs/_id_/settings/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../../../chunks/prisma.js\";\nimport { g as getUserFromToken } from \"../../../../../../../chunks/auth.js\";\nconst PUT = async ({ params, request, cookies }) => {\n  const user = getUserFromToken(cookies);\n  const { id } = params;\n  if (!user) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  try {\n    const automationRun = await prisma.automationRun.findFirst({\n      where: {\n        id,\n        OR: [\n          { userId: user.id },\n          {\n            profile: {\n              team: {\n                members: {\n                  some: { userId: user.id }\n                }\n              }\n            }\n          }\n        ]\n      }\n    });\n    if (!automationRun) {\n      return json({ error: \"Automation run not found\" }, { status: 404 });\n    }\n    const { autoApplyEnabled, maxJobsToApply, minMatchScore, selectedJobIds } = await request.json();\n    const updatedRun = await prisma.automationRun.update({\n      where: { id },\n      data: {\n        autoApplyEnabled: autoApplyEnabled !== void 0 ? autoApplyEnabled : automationRun.autoApplyEnabled,\n        maxJobsToApply: maxJobsToApply !== void 0 ? maxJobsToApply : automationRun.maxJobsToApply,\n        minMatchScore: minMatchScore !== void 0 ? minMatchScore : automationRun.minMatchScore,\n        matchedJobIds: selectedJobIds !== void 0 ? selectedJobIds : automationRun.matchedJobIds,\n        updatedAt: /* @__PURE__ */ new Date()\n      },\n      include: {\n        profile: {\n          include: {\n            data: true,\n            resumes: {\n              include: {\n                document: true\n              }\n            }\n          }\n        },\n        jobs: {\n          orderBy: {\n            createdAt: \"desc\"\n          }\n        }\n      }\n    });\n    return json(updatedRun);\n  } catch (error) {\n    console.error(\"Error updating automation run settings:\", error);\n    return json({ error: \"Failed to update automation run settings\" }, { status: 500 });\n  }\n};\nexport {\n  PUT\n};\n"], "names": [], "mappings": ";;;;;;;;;AAGK,MAAC,GAAG,GAAG,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AACpD,EAAE,MAAM,IAAI,GAAG,gBAAgB,CAAC,OAAO,CAAC;AACxC,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;AACvB,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC;AAC/D,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE;AACV,QAAQ,EAAE,EAAE;AACZ,UAAU,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;AAC7B,UAAU;AACV,YAAY,OAAO,EAAE;AACrB,cAAc,IAAI,EAAE;AACpB,gBAAgB,OAAO,EAAE;AACzB,kBAAkB,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE;AACzC;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,aAAa,EAAE;AACxB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,0BAA0B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE;AACA,IAAI,MAAM,EAAE,gBAAgB,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACpG,IAAI,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;AACzD,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE;AACnB,MAAM,IAAI,EAAE;AACZ,QAAQ,gBAAgB,EAAE,gBAAgB,KAAK,KAAK,CAAC,GAAG,gBAAgB,GAAG,aAAa,CAAC,gBAAgB;AACzG,QAAQ,cAAc,EAAE,cAAc,KAAK,KAAK,CAAC,GAAG,cAAc,GAAG,aAAa,CAAC,cAAc;AACjG,QAAQ,aAAa,EAAE,aAAa,KAAK,KAAK,CAAC,GAAG,aAAa,GAAG,aAAa,CAAC,aAAa;AAC7F,QAAQ,aAAa,EAAE,cAAc,KAAK,KAAK,CAAC,GAAG,cAAc,GAAG,aAAa,CAAC,aAAa;AAC/F,QAAQ,SAAS,kBAAkB,IAAI,IAAI;AAC3C,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,OAAO,EAAE;AACjB,UAAU,OAAO,EAAE;AACnB,YAAY,IAAI,EAAE,IAAI;AACtB,YAAY,OAAO,EAAE;AACrB,cAAc,OAAO,EAAE;AACvB,gBAAgB,QAAQ,EAAE;AAC1B;AACA;AACA;AACA,SAAS;AACT,QAAQ,IAAI,EAAE;AACd,UAAU,OAAO,EAAE;AACnB,YAAY,SAAS,EAAE;AACvB;AACA;AACA;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC;AAC3B,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC;AACnE,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,0CAA0C,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACvF;AACA;;;;"}