{"version": 3, "file": "_server.ts-DyRZbP4C.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/jobs/search/status/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { l as logger } from \"../../../../../../chunks/logger.js\";\nconst GET = async () => {\n  try {\n    const status = {\n      operational: true,\n      averageSearchTime: 0.9,\n      // seconds\n      dailySearches: 350,\n      jobsIndexed: 125e3\n    };\n    return json({\n      ...status,\n      timestamp: (/* @__PURE__ */ new Date()).toISOString()\n    });\n  } catch (error) {\n    logger.error(\"Error checking job search status:\", error);\n    return json(\n      {\n        error: \"Failed to check job search status\",\n        timestamp: (/* @__PURE__ */ new Date()).toISOString()\n      },\n      { status: 500 }\n    );\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;AAEK,MAAC,GAAG,GAAG,YAAY;AACxB,EAAE,IAAI;AACN,IAAI,MAAM,MAAM,GAAG;AACnB,MAAM,WAAW,EAAE,IAAI;AACvB,MAAM,iBAAiB,EAAE,GAAG;AAC5B;AACA,MAAM,aAAa,EAAE,GAAG;AACxB,MAAM,WAAW,EAAE;AACnB,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,GAAG,MAAM;AACf,MAAM,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AACzD,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC;AAC5D,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,KAAK,EAAE,mCAAmC;AAClD,QAAQ,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC3D,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;;;;"}