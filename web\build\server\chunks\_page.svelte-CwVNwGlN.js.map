{"version": 3, "file": "_page.svelte-CwVNwGlN.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/press/images/_page.svelte.js"], "sourcesContent": ["import { U as ensure_array_like, R as attr, V as escape_html, N as bind_props } from \"../../../../chunks/index3.js\";\nimport { S as SEO } from \"../../../../chunks/SEO.js\";\nimport \"../../../../chunks/button.js\";\nimport { P as PortableText } from \"../../../../chunks/PortableText.js\";\nimport { D as Download } from \"../../../../chunks/download.js\";\nfunction _page($$payload, $$props) {\n  let data = $$props[\"data\"];\n  const { pressImagesPage } = data;\n  const defaultImages = [\n    {\n      title: \"Hirli Logo\",\n      description: \"Official Hirli logo in full color\",\n      image: \"/assets/images/press/logo.png\",\n      downloadUrl: \"/assets/images/press/logo.png\"\n    },\n    {\n      title: \"Hirli <PERSON>go (Dark)\",\n      description: \"Official Hirli logo for dark backgrounds\",\n      image: \"/assets/images/press/logo-dark.png\",\n      downloadUrl: \"/assets/images/press/logo-dark.png\"\n    },\n    {\n      title: \"Hirli App Screenshot\",\n      description: \"Screenshot of the Hirli application dashboard\",\n      image: \"/assets/images/press/app-screenshot.png\",\n      downloadUrl: \"/assets/images/press/app-screenshot.png\"\n    },\n    {\n      title: \"Founder Photo\",\n      description: \"Official photo of Hirli founder\",\n      image: \"/assets/images/press/founder.jpg\",\n      downloadUrl: \"/assets/images/press/founder.jpg\"\n    }\n  ];\n  const images = pressImagesPage?.images?.length > 0 ? pressImagesPage.images : defaultImages;\n  const each_array = ensure_array_like(images);\n  SEO($$payload, {\n    title: pressImagesPage?.seo?.metaTitle || \"Press Images | Hirli\",\n    description: pressImagesPage?.seo?.metaDescription || \"Official Hirli brand assets, logos, and images for media use.\",\n    keywords: pressImagesPage?.seo?.keywords?.join(\", \") || \"Hirli logo, brand assets, press images, media kit\"\n  });\n  $$payload.out += `<!----> <div><h2 class=\"mb-8 text-3xl font-semibold\">Press Images</h2> `;\n  if (pressImagesPage?.content) {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<div class=\"mb-8\">`;\n    PortableText($$payload, { value: pressImagesPage.content });\n    $$payload.out += `<!----></div>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<p class=\"text-muted-foreground mb-6\">Download official Hirli logos, product screenshots, and other brand assets for media use. All\n      images are available in high resolution and can be used in accordance with our brand\n      guidelines.</p>`;\n  }\n  $$payload.out += `<!--]--> <div class=\"mt-8 grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3\"><!--[-->`;\n  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n    let image = each_array[$$index];\n    $$payload.out += `<div class=\"border-border bg-card text-card-foreground overflow-hidden rounded-lg border shadow-sm\"><div class=\"bg-muted relative aspect-video overflow-hidden\"><img${attr(\"src\", image.image)}${attr(\"alt\", image.title)} class=\"h-full w-full object-cover\"/></div> <div class=\"p-4\"><h3 class=\"mb-2 text-lg font-medium\">${escape_html(image.title)}</h3> <p class=\"text-muted-foreground mb-4 text-sm\">${escape_html(image.description)}</p> <a${attr(\"href\", image.downloadUrl)} download=\"\" class=\"text-primary inline-flex items-center gap-2 text-sm font-medium hover:underline\">`;\n    Download($$payload, { class: \"h-4 w-4\" });\n    $$payload.out += `<!----> <span>Download</span></a></div></div>`;\n  }\n  $$payload.out += `<!--]--></div> <div class=\"mt-12 rounded-lg border bg-gray-50 p-6\"><h3 class=\"mb-4 text-xl font-medium\">Usage Guidelines</h3> <ul class=\"text-muted-foreground list-inside list-disc space-y-2\"><li>Do not alter, distort, or modify the logos in any way</li> <li>Maintain adequate spacing around logos</li> <li>Do not use Hirli logos or images to imply partnership or endorsement without permission</li> <li>For questions about usage, please contact <a href=\"mailto:<EMAIL>\" class=\"text-primary hover:underline\"><EMAIL></a></li></ul></div></div>`;\n  bind_props($$props, { data });\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAKA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC;AAC5B,EAAE,MAAM,EAAE,eAAe,EAAE,GAAG,IAAI;AAClC,EAAE,MAAM,aAAa,GAAG;AACxB,IAAI;AACJ,MAAM,KAAK,EAAE,YAAY;AACzB,MAAM,WAAW,EAAE,mCAAmC;AACtD,MAAM,KAAK,EAAE,+BAA+B;AAC5C,MAAM,WAAW,EAAE;AACnB,KAAK;AACL,IAAI;AACJ,MAAM,KAAK,EAAE,mBAAmB;AAChC,MAAM,WAAW,EAAE,0CAA0C;AAC7D,MAAM,KAAK,EAAE,oCAAoC;AACjD,MAAM,WAAW,EAAE;AACnB,KAAK;AACL,IAAI;AACJ,MAAM,KAAK,EAAE,sBAAsB;AACnC,MAAM,WAAW,EAAE,+CAA+C;AAClE,MAAM,KAAK,EAAE,yCAAyC;AACtD,MAAM,WAAW,EAAE;AACnB,KAAK;AACL,IAAI;AACJ,MAAM,KAAK,EAAE,eAAe;AAC5B,MAAM,WAAW,EAAE,iCAAiC;AACpD,MAAM,KAAK,EAAE,kCAAkC;AAC/C,MAAM,WAAW,EAAE;AACnB;AACA,GAAG;AACH,EAAE,MAAM,MAAM,GAAG,eAAe,EAAE,MAAM,EAAE,MAAM,GAAG,CAAC,GAAG,eAAe,CAAC,MAAM,GAAG,aAAa;AAC7F,EAAE,MAAM,UAAU,GAAG,iBAAiB,CAAC,MAAM,CAAC;AAC9C,EAAE,GAAG,CAAC,SAAS,EAAE;AACjB,IAAI,KAAK,EAAE,eAAe,EAAE,GAAG,EAAE,SAAS,IAAI,sBAAsB;AACpE,IAAI,WAAW,EAAE,eAAe,EAAE,GAAG,EAAE,eAAe,IAAI,+DAA+D;AACzH,IAAI,QAAQ,EAAE,eAAe,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI;AAC5D,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,uEAAuE,CAAC;AAC5F,EAAE,IAAI,eAAe,EAAE,OAAO,EAAE;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;AACzC,IAAI,YAAY,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,eAAe,CAAC,OAAO,EAAE,CAAC;AAC/D,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACpC,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC;AACtB;AACA,qBAAqB,CAAC;AACtB;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,wFAAwF,CAAC;AAC7G,EAAE,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACrF,IAAI,IAAI,KAAK,GAAG,UAAU,CAAC,OAAO,CAAC;AACnC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,oKAAoK,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,kGAAkG,EAAE,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,oDAAoD,EAAE,WAAW,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,WAAW,CAAC,CAAC,qGAAqG,CAAC;AACjlB,IAAI,QAAQ,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AAC7C,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,6CAA6C,CAAC;AACpE;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,6iBAA6iB,CAAC;AAClkB,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,CAAC;AAC/B;;;;"}