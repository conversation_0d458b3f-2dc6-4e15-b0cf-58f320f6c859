{"version": 3, "file": "_server.ts-_pXMVOtK.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/studio/_path_/_server.ts.js"], "sourcesContent": ["import fs from \"fs\";\nimport path from \"path\";\nimport { fileURLToPath } from \"url\";\nconst __dirname = path.dirname(fileURLToPath(import.meta.url));\nconst studioPath = path.resolve(__dirname, \"../../../static/studio\");\nasync function GET({ params, url }) {\n  let filePath = params.path || \"index.html\";\n  filePath = filePath.replace(/\\.\\.\\//g, \"\");\n  const fullPath = path.join(studioPath, filePath);\n  try {\n    if (!fs.existsSync(fullPath)) {\n      return new Response(fs.readFileSync(path.join(studioPath, \"index.html\")), {\n        headers: {\n          \"Content-Type\": \"text/html\"\n        }\n      });\n    }\n    const ext = path.extname(fullPath).toLowerCase();\n    let contentType = \"text/plain\";\n    switch (ext) {\n      case \".html\":\n        contentType = \"text/html\";\n        break;\n      case \".js\":\n        contentType = \"application/javascript\";\n        break;\n      case \".css\":\n        contentType = \"text/css\";\n        break;\n      case \".json\":\n        contentType = \"application/json\";\n        break;\n      case \".png\":\n        contentType = \"image/png\";\n        break;\n      case \".jpg\":\n      case \".jpeg\":\n        contentType = \"image/jpeg\";\n        break;\n      case \".svg\":\n        contentType = \"image/svg+xml\";\n        break;\n      case \".ico\":\n        contentType = \"image/x-icon\";\n        break;\n    }\n    return new Response(fs.readFileSync(fullPath), {\n      headers: {\n        \"Content-Type\": contentType\n      }\n    });\n  } catch (error) {\n    console.error(\"Error serving Sanity Studio file:\", error);\n    return new Response(\"Not Found\", { status: 404 });\n  }\n}\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;AAGA,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC9D,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,wBAAwB,CAAC;AACpE,eAAe,GAAG,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE;AACpC,EAAE,IAAI,QAAQ,GAAG,MAAM,CAAC,IAAI,IAAI,YAAY;AAC5C,EAAE,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;AAC5C,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC;AAClD,EAAE,IAAI;AACN,IAAI,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;AAClC,MAAM,OAAO,IAAI,QAAQ,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,EAAE;AAChF,QAAQ,OAAO,EAAE;AACjB,UAAU,cAAc,EAAE;AAC1B;AACA,OAAO,CAAC;AACR;AACA,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE;AACpD,IAAI,IAAI,WAAW,GAAG,YAAY;AAClC,IAAI,QAAQ,GAAG;AACf,MAAM,KAAK,OAAO;AAClB,QAAQ,WAAW,GAAG,WAAW;AACjC,QAAQ;AACR,MAAM,KAAK,KAAK;AAChB,QAAQ,WAAW,GAAG,wBAAwB;AAC9C,QAAQ;AACR,MAAM,KAAK,MAAM;AACjB,QAAQ,WAAW,GAAG,UAAU;AAChC,QAAQ;AACR,MAAM,KAAK,OAAO;AAClB,QAAQ,WAAW,GAAG,kBAAkB;AACxC,QAAQ;AACR,MAAM,KAAK,MAAM;AACjB,QAAQ,WAAW,GAAG,WAAW;AACjC,QAAQ;AACR,MAAM,KAAK,MAAM;AACjB,MAAM,KAAK,OAAO;AAClB,QAAQ,WAAW,GAAG,YAAY;AAClC,QAAQ;AACR,MAAM,KAAK,MAAM;AACjB,QAAQ,WAAW,GAAG,eAAe;AACrC,QAAQ;AACR,MAAM,KAAK,MAAM;AACjB,QAAQ,WAAW,GAAG,cAAc;AACpC,QAAQ;AACR;AACA,IAAI,OAAO,IAAI,QAAQ,CAAC,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE;AACnD,MAAM,OAAO,EAAE;AACf,QAAQ,cAAc,EAAE;AACxB;AACA,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC;AAC7D,IAAI,OAAO,IAAI,QAAQ,CAAC,WAAW,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACrD;AACA;;;;"}