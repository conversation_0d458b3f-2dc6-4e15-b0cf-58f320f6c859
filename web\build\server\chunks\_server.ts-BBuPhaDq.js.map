{"version": 3, "file": "_server.ts-BBuPhaDq.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/admin/features/sync/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../../../../chunks/auth.js\";\nconst POST = async ({ cookies, request }) => {\n  const token = cookies.get(\"auth_token\");\n  if (!token) {\n    return new Response(\"Unauthorized\", { status: 401 });\n  }\n  const userData = await verifySessionToken(token);\n  if (!userData?.id) {\n    return new Response(\"Unauthorized\", { status: 401 });\n  }\n  const user = await prisma.user.findUnique({\n    where: { id: userData.id },\n    select: { isAdmin: true, role: true }\n  });\n  if (!user || !user.isAdmin && user.role !== \"admin\") {\n    return new Response(\"Unauthorized - Admin access required\", { status: 401 });\n  }\n  try {\n    const features = await prisma.feature.findMany();\n    const plans = await prisma.plan.findMany({\n      include: {\n        features: true\n      }\n    });\n    const changes = {\n      total: 0,\n      byPlan: {}\n    };\n    for (const plan of plans) {\n      changes.byPlan[plan.id] = 0;\n      const existingFeatureIds = plan.features.map((pf) => pf.featureId);\n      const missingFeatures = features.filter(\n        (feature) => !existingFeatureIds.includes(feature.id)\n      );\n      if (missingFeatures.length > 0) {\n        console.log(`Adding ${missingFeatures.length} missing features to plan ${plan.name}`);\n        let defaultAccessLevel = \"included\";\n        if (plan.id === \"free\" || plan.name.toLowerCase() === \"free\") {\n          defaultAccessLevel = \"not_included\";\n          const includedCategories = [\"core\"];\n          const limitedCategories = [\"resume\", \"job_search\", \"applications\"];\n          const includedFeatures = [\"application_tracker\", \"application_submit\"];\n          const limitedFeatures = [\"cover_letter_generator\", \"application_tracking\", \"resume_scanner\", \"resume_builder\"];\n          const featureAssignments = missingFeatures.map((feature) => {\n            let accessLevel = defaultAccessLevel;\n            if (includedFeatures.includes(feature.id)) {\n              accessLevel = \"included\";\n            } else if (limitedFeatures.includes(feature.id)) {\n              accessLevel = \"limited\";\n            } else if (includedCategories.includes(feature.category)) {\n              accessLevel = \"included\";\n            } else if (limitedCategories.includes(feature.category)) {\n              accessLevel = \"limited\";\n            }\n            return {\n              planId: plan.id,\n              featureId: feature.id,\n              accessLevel\n            };\n          });\n          await prisma.planFeature.createMany({\n            data: featureAssignments\n          });\n          changes.total += featureAssignments.length;\n          changes.byPlan[plan.id] = featureAssignments.length;\n        } else {\n          const featureAssignments = missingFeatures.map((feature) => ({\n            planId: plan.id,\n            featureId: feature.id,\n            accessLevel: defaultAccessLevel\n          }));\n          await prisma.planFeature.createMany({\n            data: featureAssignments\n          });\n          changes.total += featureAssignments.length;\n          changes.byPlan[plan.id] = featureAssignments.length;\n        }\n      }\n    }\n    return json({\n      success: true,\n      message: `Synced features across all plans. Added ${changes.total} missing feature assignments.`,\n      changes\n    });\n  } catch (error) {\n    console.error(\"Error syncing features:\", error);\n    return json({\n      success: false,\n      error: \"Failed to sync features\",\n      details: error.message\n    }, { status: 500 });\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;AAGK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AAC7C,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE;AACd,IAAI,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxD;AACA,EAAE,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AAClD,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE;AACrB,IAAI,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxD;AACA,EAAE,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAC5C,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE;AAC9B,IAAI,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AACvC,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;AACvD,IAAI,OAAO,IAAI,QAAQ,CAAC,sCAAsC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAChF;AACA,EAAE,IAAI;AACN,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE;AACpD,IAAI,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC7C,MAAM,OAAO,EAAE;AACf,QAAQ,QAAQ,EAAE;AAClB;AACA,KAAK,CAAC;AACN,IAAI,MAAM,OAAO,GAAG;AACpB,MAAM,KAAK,EAAE,CAAC;AACd,MAAM,MAAM,EAAE;AACd,KAAK;AACL,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;AAC9B,MAAM,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC;AACjC,MAAM,MAAM,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,SAAS,CAAC;AACxE,MAAM,MAAM,eAAe,GAAG,QAAQ,CAAC,MAAM;AAC7C,QAAQ,CAAC,OAAO,KAAK,CAAC,kBAAkB,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;AAC5D,OAAO;AACP,MAAM,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;AACtC,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,eAAe,CAAC,MAAM,CAAC,0BAA0B,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC7F,QAAQ,IAAI,kBAAkB,GAAG,UAAU;AAC3C,QAAQ,IAAI,IAAI,CAAC,EAAE,KAAK,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,MAAM,EAAE;AACtE,UAAU,kBAAkB,GAAG,cAAc;AAC7C,UAAU,MAAM,kBAAkB,GAAG,CAAC,MAAM,CAAC;AAC7C,UAAU,MAAM,iBAAiB,GAAG,CAAC,QAAQ,EAAE,YAAY,EAAE,cAAc,CAAC;AAC5E,UAAU,MAAM,gBAAgB,GAAG,CAAC,qBAAqB,EAAE,oBAAoB,CAAC;AAChF,UAAU,MAAM,eAAe,GAAG,CAAC,wBAAwB,EAAE,sBAAsB,EAAE,gBAAgB,EAAE,gBAAgB,CAAC;AACxH,UAAU,MAAM,kBAAkB,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK;AACtE,YAAY,IAAI,WAAW,GAAG,kBAAkB;AAChD,YAAY,IAAI,gBAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;AACvD,cAAc,WAAW,GAAG,UAAU;AACtC,aAAa,MAAM,IAAI,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;AAC7D,cAAc,WAAW,GAAG,SAAS;AACrC,aAAa,MAAM,IAAI,kBAAkB,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AACtE,cAAc,WAAW,GAAG,UAAU;AACtC,aAAa,MAAM,IAAI,iBAAiB,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AACrE,cAAc,WAAW,GAAG,SAAS;AACrC;AACA,YAAY,OAAO;AACnB,cAAc,MAAM,EAAE,IAAI,CAAC,EAAE;AAC7B,cAAc,SAAS,EAAE,OAAO,CAAC,EAAE;AACnC,cAAc;AACd,aAAa;AACb,WAAW,CAAC;AACZ,UAAU,MAAM,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;AAC9C,YAAY,IAAI,EAAE;AAClB,WAAW,CAAC;AACZ,UAAU,OAAO,CAAC,KAAK,IAAI,kBAAkB,CAAC,MAAM;AACpD,UAAU,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,kBAAkB,CAAC,MAAM;AAC7D,SAAS,MAAM;AACf,UAAU,MAAM,kBAAkB,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,OAAO,MAAM;AACvE,YAAY,MAAM,EAAE,IAAI,CAAC,EAAE;AAC3B,YAAY,SAAS,EAAE,OAAO,CAAC,EAAE;AACjC,YAAY,WAAW,EAAE;AACzB,WAAW,CAAC,CAAC;AACb,UAAU,MAAM,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;AAC9C,YAAY,IAAI,EAAE;AAClB,WAAW,CAAC;AACZ,UAAU,OAAO,CAAC,KAAK,IAAI,kBAAkB,CAAC,MAAM;AACpD,UAAU,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,kBAAkB,CAAC,MAAM;AAC7D;AACA;AACA;AACA,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,OAAO,EAAE,CAAC,wCAAwC,EAAE,OAAO,CAAC,KAAK,CAAC,6BAA6B,CAAC;AACtG,MAAM;AACN,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC;AACnD,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,KAAK,EAAE,yBAAyB;AACtC,MAAM,OAAO,EAAE,KAAK,CAAC;AACrB,KAAK,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACvB;AACA;;;;"}