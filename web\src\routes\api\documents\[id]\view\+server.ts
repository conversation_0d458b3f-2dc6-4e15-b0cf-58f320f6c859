import { error, json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { prisma } from '$lib/server/prisma';

export const GET: RequestHandler = async ({ params, locals }) => {
  // Check if user is authenticated
  if (!locals.user) {
    throw error(401, 'Unauthorized');
  }

  try {
    // Get the document ID from the params
    const { id } = params;

    if (!id) {
      throw error(400, 'Document ID is required');
    }

    // Fetch the document from the database
    const document = await prisma.document.findUnique({
      where: { id },
    });

    // Check if document exists and belongs to the user
    if (!document || document.userId !== locals.user.id) {
      throw error(404, 'Document not found');
    }

    // Return the document URL directly from database
    // All files are now stored in R2 with public URLs
    let directUrl = document.fileUrl || '/placeholder.pdf';
    let isExternal = false;

    // Check if it's an R2 URL (absolute URL)
    if (directUrl.startsWith('http://') || directUrl.startsWith('https://')) {
      isExternal = true;
    } else if (directUrl !== '/placeholder.pdf') {
      // Legacy local file - format for static serving
      if (!directUrl.startsWith('/')) {
        directUrl = '/' + directUrl;
      }
      if (!directUrl.includes('/uploads/')) {
        // Add uploads prefix for legacy files
        const fileExtension = directUrl.split('.').pop()?.toLowerCase();
        let folder = 'documents';
        if (document.type === 'resume') folder = 'resumes';
        else if (document.type === 'cover_letter') folder = 'cover-letters';
        else if (document.type === 'reference') folder = 'references';

        directUrl = `/uploads/${folder}${directUrl}`;
      }
    }

    // Return the document with the direct URL
    return json({
      success: true,
      document: {
        ...document,
        directUrl,
        isExternal,
        fileExists: true, // Assume files exist since they're in database
      },
    });
  } catch (err) {
    console.error('Error serving document:', err);
    throw error(500, 'Failed to serve document');
  }
};
