{"version": 3, "file": "_server.ts-DXeb0JTL.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/job-alerts/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../chunks/index.js\";\nimport { v as verifySessionToken, g as getUser<PERSON>romToken } from \"../../../../chunks/auth.js\";\nimport { p as prisma } from \"../../../../chunks/prisma.js\";\nconst POST = async ({ request, cookies }) => {\n  let user = null;\n  const authToken = cookies.get(\"auth_token\");\n  const token = cookies.get(\"token\");\n  if (authToken) {\n    user = await verifySessionToken(authToken);\n  } else if (token) {\n    user = await getUserFromToken(token);\n  }\n  if (!user) {\n    return json({ error: \"Authentication required\" }, { status: 401 });\n  }\n  try {\n    const { name, searchParams, frequency, enabled } = await request.json();\n    if (!name || !searchParams) {\n      return json({ error: \"Name and search parameters are required\" }, { status: 400 });\n    }\n    const jobAlert = await prisma.jobAlert.create({\n      data: {\n        userId: user.id,\n        name,\n        searchParams,\n        frequency: frequency || \"daily\",\n        enabled: enabled !== void 0 ? enabled : true\n      }\n    });\n    return json({\n      success: true,\n      jobAlert: {\n        id: jobAlert.id,\n        name: jobAlert.name,\n        frequency: jobAlert.frequency,\n        enabled: jobAlert.enabled,\n        createdAt: jobAlert.createdAt\n      }\n    });\n  } catch (error) {\n    console.error(\"Error creating job alert:\", error);\n    return json({ error: \"Failed to create job alert\" }, { status: 500 });\n  }\n};\nconst GET = async ({ cookies }) => {\n  let user = null;\n  const authToken = cookies.get(\"auth_token\");\n  const token = cookies.get(\"token\");\n  if (authToken) {\n    user = await verifySessionToken(authToken);\n  } else if (token) {\n    user = await getUserFromToken(token);\n  }\n  if (!user) {\n    return json({ error: \"Authentication required\" }, { status: 401 });\n  }\n  try {\n    const jobAlerts = await prisma.jobAlert.findMany({\n      where: {\n        userId: user.id\n      },\n      orderBy: {\n        createdAt: \"desc\"\n      }\n    });\n    return json({\n      success: true,\n      jobAlerts\n    });\n  } catch (error) {\n    console.error(\"Error fetching job alerts:\", error);\n    return json({ error: \"Failed to fetch job alerts\" }, { status: 500 });\n  }\n};\nconst PUT = async ({ request, cookies }) => {\n  let user = null;\n  const authToken = cookies.get(\"auth_token\");\n  const token = cookies.get(\"token\");\n  if (authToken) {\n    user = await verifySessionToken(authToken);\n  } else if (token) {\n    user = await getUserFromToken(token);\n  }\n  if (!user) {\n    return json({ error: \"Authentication required\" }, { status: 401 });\n  }\n  try {\n    const { id, name, searchParams, frequency, enabled } = await request.json();\n    if (!id) {\n      return json({ error: \"Job alert ID is required\" }, { status: 400 });\n    }\n    const existingAlert = await prisma.jobAlert.findUnique({\n      where: { id }\n    });\n    if (!existingAlert) {\n      return json({ error: \"Job alert not found\" }, { status: 404 });\n    }\n    if (existingAlert.userId !== user.id) {\n      return json({ error: \"Unauthorized\" }, { status: 403 });\n    }\n    const updatedAlert = await prisma.jobAlert.update({\n      where: { id },\n      data: {\n        name: name !== void 0 ? name : existingAlert.name,\n        searchParams: searchParams !== void 0 ? searchParams : existingAlert.searchParams,\n        frequency: frequency !== void 0 ? frequency : existingAlert.frequency,\n        enabled: enabled !== void 0 ? enabled : existingAlert.enabled\n      }\n    });\n    return json({\n      success: true,\n      jobAlert: {\n        id: updatedAlert.id,\n        name: updatedAlert.name,\n        frequency: updatedAlert.frequency,\n        enabled: updatedAlert.enabled,\n        updatedAt: updatedAlert.updatedAt\n      }\n    });\n  } catch (error) {\n    console.error(\"Error updating job alert:\", error);\n    return json({ error: \"Failed to update job alert\" }, { status: 500 });\n  }\n};\nconst DELETE = async ({ request, cookies }) => {\n  let user = null;\n  const authToken = cookies.get(\"auth_token\");\n  const token = cookies.get(\"token\");\n  if (authToken) {\n    user = await verifySessionToken(authToken);\n  } else if (token) {\n    user = await getUserFromToken(token);\n  }\n  if (!user) {\n    return json({ error: \"Authentication required\" }, { status: 401 });\n  }\n  try {\n    const { id } = await request.json();\n    if (!id) {\n      return json({ error: \"Job alert ID is required\" }, { status: 400 });\n    }\n    const existingAlert = await prisma.jobAlert.findUnique({\n      where: { id }\n    });\n    if (!existingAlert) {\n      return json({ error: \"Job alert not found\" }, { status: 404 });\n    }\n    if (existingAlert.userId !== user.id) {\n      return json({ error: \"Unauthorized\" }, { status: 403 });\n    }\n    await prisma.jobAlert.delete({\n      where: { id }\n    });\n    return json({\n      success: true,\n      message: \"Job alert deleted successfully\"\n    });\n  } catch (error) {\n    console.error(\"Error deleting job alert:\", error);\n    return json({ error: \"Failed to delete job alert\" }, { status: 500 });\n  }\n};\nexport {\n  DELETE,\n  GET,\n  POST,\n  PUT\n};\n"], "names": [], "mappings": ";;;;;;;;;AAGK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AAC7C,EAAE,IAAI,IAAI,GAAG,IAAI;AACjB,EAAE,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AAC7C,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC;AACpC,EAAE,IAAI,SAAS,EAAE;AACjB,IAAI,IAAI,GAAG,MAAM,kBAAkB,CAAC,SAAS,CAAC;AAC9C,GAAG,MAAM,IAAI,KAAK,EAAE;AACpB,IAAI,IAAI,GAAG,MAAM,gBAAgB,CAAC,KAAK,CAAC;AACxC;AACA,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtE;AACA,EAAE,IAAI;AACN,IAAI,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AAC3E,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,YAAY,EAAE;AAChC,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yCAAyC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxF;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;AAClD,MAAM,IAAI,EAAE;AACZ,QAAQ,MAAM,EAAE,IAAI,CAAC,EAAE;AACvB,QAAQ,IAAI;AACZ,QAAQ,YAAY;AACpB,QAAQ,SAAS,EAAE,SAAS,IAAI,OAAO;AACvC,QAAQ,OAAO,EAAE,OAAO,KAAK,KAAK,CAAC,GAAG,OAAO,GAAG;AAChD;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,QAAQ,EAAE;AAChB,QAAQ,EAAE,EAAE,QAAQ,CAAC,EAAE;AACvB,QAAQ,IAAI,EAAE,QAAQ,CAAC,IAAI;AAC3B,QAAQ,SAAS,EAAE,QAAQ,CAAC,SAAS;AACrC,QAAQ,OAAO,EAAE,QAAQ,CAAC,OAAO;AACjC,QAAQ,SAAS,EAAE,QAAQ,CAAC;AAC5B;AACA,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC;AACrD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,4BAA4B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE;AACA;AACK,MAAC,GAAG,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK;AACnC,EAAE,IAAI,IAAI,GAAG,IAAI;AACjB,EAAE,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AAC7C,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC;AACpC,EAAE,IAAI,SAAS,EAAE;AACjB,IAAI,IAAI,GAAG,MAAM,kBAAkB,CAAC,SAAS,CAAC;AAC9C,GAAG,MAAM,IAAI,KAAK,EAAE;AACpB,IAAI,IAAI,GAAG,MAAM,gBAAgB,CAAC,KAAK,CAAC;AACxC;AACA,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtE;AACA,EAAE,IAAI;AACN,IAAI,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;AACrD,MAAM,KAAK,EAAE;AACb,QAAQ,MAAM,EAAE,IAAI,CAAC;AACrB,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,SAAS,EAAE;AACnB;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM;AACN,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC;AACtD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,4BAA4B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE;AACA;AACK,MAAC,GAAG,GAAG,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AAC5C,EAAE,IAAI,IAAI,GAAG,IAAI;AACjB,EAAE,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AAC7C,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC;AACpC,EAAE,IAAI,SAAS,EAAE;AACjB,IAAI,IAAI,GAAG,MAAM,kBAAkB,CAAC,SAAS,CAAC;AAC9C,GAAG,MAAM,IAAI,KAAK,EAAE;AACpB,IAAI,IAAI,GAAG,MAAM,gBAAgB,CAAC,KAAK,CAAC;AACxC;AACA,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtE;AACA,EAAE,IAAI;AACN,IAAI,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AAC/E,IAAI,IAAI,CAAC,EAAE,EAAE;AACb,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,0BAA0B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE;AACA,IAAI,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;AAC3D,MAAM,KAAK,EAAE,EAAE,EAAE;AACjB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,aAAa,EAAE;AACxB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,qBAAqB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpE;AACA,IAAI,IAAI,aAAa,CAAC,MAAM,KAAK,IAAI,CAAC,EAAE,EAAE;AAC1C,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7D;AACA,IAAI,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;AACtD,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE;AACnB,MAAM,IAAI,EAAE;AACZ,QAAQ,IAAI,EAAE,IAAI,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,aAAa,CAAC,IAAI;AACzD,QAAQ,YAAY,EAAE,YAAY,KAAK,KAAK,CAAC,GAAG,YAAY,GAAG,aAAa,CAAC,YAAY;AACzF,QAAQ,SAAS,EAAE,SAAS,KAAK,KAAK,CAAC,GAAG,SAAS,GAAG,aAAa,CAAC,SAAS;AAC7E,QAAQ,OAAO,EAAE,OAAO,KAAK,KAAK,CAAC,GAAG,OAAO,GAAG,aAAa,CAAC;AAC9D;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,QAAQ,EAAE;AAChB,QAAQ,EAAE,EAAE,YAAY,CAAC,EAAE;AAC3B,QAAQ,IAAI,EAAE,YAAY,CAAC,IAAI;AAC/B,QAAQ,SAAS,EAAE,YAAY,CAAC,SAAS;AACzC,QAAQ,OAAO,EAAE,YAAY,CAAC,OAAO;AACrC,QAAQ,SAAS,EAAE,YAAY,CAAC;AAChC;AACA,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC;AACrD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,4BAA4B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE;AACA;AACK,MAAC,MAAM,GAAG,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AAC/C,EAAE,IAAI,IAAI,GAAG,IAAI;AACjB,EAAE,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AAC7C,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC;AACpC,EAAE,IAAI,SAAS,EAAE;AACjB,IAAI,IAAI,GAAG,MAAM,kBAAkB,CAAC,SAAS,CAAC;AAC9C,GAAG,MAAM,IAAI,KAAK,EAAE;AACpB,IAAI,IAAI,GAAG,MAAM,gBAAgB,CAAC,KAAK,CAAC;AACxC;AACA,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtE;AACA,EAAE,IAAI;AACN,IAAI,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACvC,IAAI,IAAI,CAAC,EAAE,EAAE;AACb,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,0BAA0B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE;AACA,IAAI,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;AAC3D,MAAM,KAAK,EAAE,EAAE,EAAE;AACjB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,aAAa,EAAE;AACxB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,qBAAqB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpE;AACA,IAAI,IAAI,aAAa,CAAC,MAAM,KAAK,IAAI,CAAC,EAAE,EAAE;AAC1C,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7D;AACA,IAAI,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;AACjC,MAAM,KAAK,EAAE,EAAE,EAAE;AACjB,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,OAAO,EAAE;AACf,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC;AACrD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,4BAA4B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE;AACA;;;;"}