{"version": 3, "file": "_server.ts-i4wXduPG.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/health/report/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { l as logger } from \"../../../../../chunks/logger.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nconst SERVICE_MAPPING = {\n  // Worker service maps to Automation\n  \"auto-apply-worker\": \"Automation\",\n  worker: \"Automation\",\n  automation: \"Automation\",\n  // AI service maps to System (internal AI processing)\n  \"auto-apply-ai\": \"System\",\n  ai: \"System\",\n  // Web service maps to Website\n  \"auto-apply-web\": \"Website\",\n  web: \"Website\",\n  website: \"Website\",\n  // Direct mappings for consumer-facing services\n  matches: \"Matches\",\n  jobs: \"Jobs\",\n  tracker: \"Tracker\",\n  documents: \"Documents\",\n  system: \"System\",\n  // Resume services map to Documents\n  \"resume-builder\": \"Documents\",\n  \"resume-scanner\": \"Documents\",\n  resume: \"Documents\",\n  // Job services map to Jobs\n  \"job-search\": \"Jobs\",\n  \"job-scraper\": \"Jobs\",\n  scraper: \"Jobs\",\n  // Application services map to Tracker\n  \"application-system\": \"Tracker\",\n  applications: \"Tracker\",\n  // Account services map to System\n  \"account-services\": \"System\",\n  accounts: \"System\",\n  auth: \"System\",\n  // Database and infrastructure map to System\n  database: \"System\",\n  redis: \"System\",\n  api: \"System\"\n};\nconst POST = async ({ request }) => {\n  try {\n    const healthReport = await request.json();\n    if (!healthReport.service || !healthReport.status) {\n      return json(\n        {\n          error: \"Missing required fields: service and status are required\",\n          timestamp: (/* @__PURE__ */ new Date()).toISOString()\n        },\n        { status: 400 }\n      );\n    }\n    const serviceName = healthReport.service.toLowerCase();\n    const mappedServiceName = SERVICE_MAPPING[serviceName] || \"System\";\n    logger.info(\n      `Health report received: ${healthReport.service} -> ${mappedServiceName} (${healthReport.status})`\n    );\n    let serviceStatus = await prisma.serviceStatus.findUnique({\n      where: { name: mappedServiceName }\n    });\n    if (!serviceStatus) {\n      const serviceDescriptions = {\n        Matches: \"Job matching and recommendations\",\n        Jobs: \"Job search and listings\",\n        Tracker: \"Application tracking\",\n        Documents: \"Resume and document management\",\n        Automation: \"Automated job application tools\",\n        System: \"Core system services\",\n        Website: \"Website and user interface\"\n      };\n      serviceStatus = await prisma.serviceStatus.create({\n        data: {\n          name: mappedServiceName,\n          status: healthReport.status,\n          description: serviceDescriptions[mappedServiceName] || \"Service\",\n          lastCheckedAt: /* @__PURE__ */ new Date()\n        }\n      });\n      logger.info(`Created new service status record for ${mappedServiceName}`);\n    } else {\n      const statusChanged = serviceStatus.status !== healthReport.status;\n      await prisma.serviceStatus.update({\n        where: { id: serviceStatus.id },\n        data: {\n          status: healthReport.status,\n          lastCheckedAt: /* @__PURE__ */ new Date()\n        }\n      });\n      if (statusChanged) {\n        await prisma.serviceStatusHistory.create({\n          data: {\n            serviceId: serviceStatus.id,\n            status: healthReport.status\n          }\n        });\n        logger.info(\n          `Status changed for ${mappedServiceName}: ${serviceStatus.status} -> ${healthReport.status}`\n        );\n      }\n    }\n    return json({\n      success: true,\n      message: `Health status updated for ${mappedServiceName}`,\n      mappedService: mappedServiceName,\n      originalService: healthReport.service,\n      status: healthReport.status,\n      timestamp: (/* @__PURE__ */ new Date()).toISOString()\n    });\n  } catch (error) {\n    logger.error(\"Error processing health report:\", error);\n    return json(\n      {\n        success: false,\n        error: \"Failed to process health report\",\n        message: error instanceof Error ? error.message : \"Unknown error\",\n        timestamp: (/* @__PURE__ */ new Date()).toISOString()\n      },\n      { status: 500 }\n    );\n  }\n};\nconst GET = async ({ url }) => {\n  try {\n    const service = url.searchParams.get(\"service\");\n    if (service) {\n      const serviceStatus = await prisma.serviceStatus.findUnique({\n        where: { name: service },\n        include: {\n          statusHistory: {\n            orderBy: { recordedAt: \"desc\" },\n            take: 10\n            // Last 10 status changes\n          }\n        }\n      });\n      if (!serviceStatus) {\n        return json({ error: `Service '${service}' not found` }, { status: 404 });\n      }\n      return json({\n        service: serviceStatus.name,\n        status: serviceStatus.status,\n        description: serviceStatus.description,\n        lastCheckedAt: serviceStatus.lastCheckedAt.toISOString(),\n        history: serviceStatus.statusHistory.map((h) => ({\n          status: h.status,\n          recordedAt: h.recordedAt.toISOString()\n        })),\n        timestamp: (/* @__PURE__ */ new Date()).toISOString()\n      });\n    } else {\n      const services = await prisma.serviceStatus.findMany({\n        orderBy: { name: \"asc\" }\n      });\n      return json({\n        services: services.map((s) => ({\n          name: s.name,\n          status: s.status,\n          description: s.description,\n          lastCheckedAt: s.lastCheckedAt.toISOString()\n        })),\n        totalServices: services.length,\n        timestamp: (/* @__PURE__ */ new Date()).toISOString()\n      });\n    }\n  } catch (error) {\n    logger.error(\"Error retrieving service health:\", error);\n    return json(\n      {\n        error: \"Failed to retrieve service health\",\n        message: error instanceof Error ? error.message : \"Unknown error\",\n        timestamp: (/* @__PURE__ */ new Date()).toISOString()\n      },\n      { status: 500 }\n    );\n  }\n};\nconst OPTIONS = async () => {\n  return new Response(null, {\n    status: 200,\n    headers: {\n      \"Access-Control-Allow-Origin\": \"*\",\n      \"Access-Control-Allow-Methods\": \"GET, POST, OPTIONS\",\n      \"Access-Control-Allow-Headers\": \"Content-Type, Authorization\"\n    }\n  });\n};\nexport {\n  GET,\n  OPTIONS,\n  POST\n};\n"], "names": [], "mappings": ";;;;;AAGA,MAAM,eAAe,GAAG;AACxB;AACA,EAAE,mBAAmB,EAAE,YAAY;AACnC,EAAE,MAAM,EAAE,YAAY;AACtB,EAAE,UAAU,EAAE,YAAY;AAC1B;AACA,EAAE,eAAe,EAAE,QAAQ;AAC3B,EAAE,EAAE,EAAE,QAAQ;AACd;AACA,EAAE,gBAAgB,EAAE,SAAS;AAC7B,EAAE,GAAG,EAAE,SAAS;AAChB,EAAE,OAAO,EAAE,SAAS;AACpB;AACA,EAAE,OAAO,EAAE,SAAS;AACpB,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,OAAO,EAAE,SAAS;AACpB,EAAE,SAAS,EAAE,WAAW;AACxB,EAAE,MAAM,EAAE,QAAQ;AAClB;AACA,EAAE,gBAAgB,EAAE,WAAW;AAC/B,EAAE,gBAAgB,EAAE,WAAW;AAC/B,EAAE,MAAM,EAAE,WAAW;AACrB;AACA,EAAE,YAAY,EAAE,MAAM;AACtB,EAAE,aAAa,EAAE,MAAM;AACvB,EAAE,OAAO,EAAE,MAAM;AACjB;AACA,EAAE,oBAAoB,EAAE,SAAS;AACjC,EAAE,YAAY,EAAE,SAAS;AACzB;AACA,EAAE,kBAAkB,EAAE,QAAQ;AAC9B,EAAE,QAAQ,EAAE,QAAQ;AACpB,EAAE,IAAI,EAAE,QAAQ;AAChB;AACA,EAAE,QAAQ,EAAE,QAAQ;AACpB,EAAE,KAAK,EAAE,QAAQ;AACjB,EAAE,GAAG,EAAE;AACP,CAAC;AACI,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK;AACpC,EAAE,IAAI;AACN,IAAI,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AAC7C,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;AACvD,MAAM,OAAO,IAAI;AACjB,QAAQ;AACR,UAAU,KAAK,EAAE,0DAA0D;AAC3E,UAAU,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC7D,SAAS;AACT,QAAQ,EAAE,MAAM,EAAE,GAAG;AACrB,OAAO;AACP;AACA,IAAI,MAAM,WAAW,GAAG,YAAY,CAAC,OAAO,CAAC,WAAW,EAAE;AAC1D,IAAI,MAAM,iBAAiB,GAAG,eAAe,CAAC,WAAW,CAAC,IAAI,QAAQ;AACtE,IAAI,MAAM,CAAC,IAAI;AACf,MAAM,CAAC,wBAAwB,EAAE,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,iBAAiB,CAAC,EAAE,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC;AACvG,KAAK;AACL,IAAI,IAAI,aAAa,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC;AAC9D,MAAM,KAAK,EAAE,EAAE,IAAI,EAAE,iBAAiB;AACtC,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,aAAa,EAAE;AACxB,MAAM,MAAM,mBAAmB,GAAG;AAClC,QAAQ,OAAO,EAAE,kCAAkC;AACnD,QAAQ,IAAI,EAAE,yBAAyB;AACvC,QAAQ,OAAO,EAAE,sBAAsB;AACvC,QAAQ,SAAS,EAAE,gCAAgC;AACnD,QAAQ,UAAU,EAAE,iCAAiC;AACrD,QAAQ,MAAM,EAAE,sBAAsB;AACtC,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;AACxD,QAAQ,IAAI,EAAE;AACd,UAAU,IAAI,EAAE,iBAAiB;AACjC,UAAU,MAAM,EAAE,YAAY,CAAC,MAAM;AACrC,UAAU,WAAW,EAAE,mBAAmB,CAAC,iBAAiB,CAAC,IAAI,SAAS;AAC1E,UAAU,aAAa,kBAAkB,IAAI,IAAI;AACjD;AACA,OAAO,CAAC;AACR,MAAM,MAAM,CAAC,IAAI,CAAC,CAAC,sCAAsC,EAAE,iBAAiB,CAAC,CAAC,CAAC;AAC/E,KAAK,MAAM;AACX,MAAM,MAAM,aAAa,GAAG,aAAa,CAAC,MAAM,KAAK,YAAY,CAAC,MAAM;AACxE,MAAM,MAAM,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;AACxC,QAAQ,KAAK,EAAE,EAAE,EAAE,EAAE,aAAa,CAAC,EAAE,EAAE;AACvC,QAAQ,IAAI,EAAE;AACd,UAAU,MAAM,EAAE,YAAY,CAAC,MAAM;AACrC,UAAU,aAAa,kBAAkB,IAAI,IAAI;AACjD;AACA,OAAO,CAAC;AACR,MAAM,IAAI,aAAa,EAAE;AACzB,QAAQ,MAAM,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC;AACjD,UAAU,IAAI,EAAE;AAChB,YAAY,SAAS,EAAE,aAAa,CAAC,EAAE;AACvC,YAAY,MAAM,EAAE,YAAY,CAAC;AACjC;AACA,SAAS,CAAC;AACV,QAAQ,MAAM,CAAC,IAAI;AACnB,UAAU,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,EAAE,EAAE,aAAa,CAAC,MAAM,CAAC,IAAI,EAAE,YAAY,CAAC,MAAM,CAAC;AACrG,SAAS;AACT;AACA;AACA,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,OAAO,EAAE,CAAC,0BAA0B,EAAE,iBAAiB,CAAC,CAAC;AAC/D,MAAM,aAAa,EAAE,iBAAiB;AACtC,MAAM,eAAe,EAAE,YAAY,CAAC,OAAO;AAC3C,MAAM,MAAM,EAAE,YAAY,CAAC,MAAM;AACjC,MAAM,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AACzD,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC;AAC1D,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,KAAK,EAAE,iCAAiC;AAChD,QAAQ,OAAO,EAAE,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,eAAe;AACzE,QAAQ,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC3D,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;AACK,MAAC,GAAG,GAAG,OAAO,EAAE,GAAG,EAAE,KAAK;AAC/B,EAAE,IAAI;AACN,IAAI,MAAM,OAAO,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC;AACnD,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC;AAClE,QAAQ,KAAK,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE;AAChC,QAAQ,OAAO,EAAE;AACjB,UAAU,aAAa,EAAE;AACzB,YAAY,OAAO,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;AAC3C,YAAY,IAAI,EAAE;AAClB;AACA;AACA;AACA,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,aAAa,EAAE;AAC1B,QAAQ,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,WAAW,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjF;AACA,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,OAAO,EAAE,aAAa,CAAC,IAAI;AACnC,QAAQ,MAAM,EAAE,aAAa,CAAC,MAAM;AACpC,QAAQ,WAAW,EAAE,aAAa,CAAC,WAAW;AAC9C,QAAQ,aAAa,EAAE,aAAa,CAAC,aAAa,CAAC,WAAW,EAAE;AAChE,QAAQ,OAAO,EAAE,aAAa,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM;AACzD,UAAU,MAAM,EAAE,CAAC,CAAC,MAAM;AAC1B,UAAU,UAAU,EAAE,CAAC,CAAC,UAAU,CAAC,WAAW;AAC9C,SAAS,CAAC,CAAC;AACX,QAAQ,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC3D,OAAO,CAAC;AACR,KAAK,MAAM;AACX,MAAM,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;AAC3D,QAAQ,OAAO,EAAE,EAAE,IAAI,EAAE,KAAK;AAC9B,OAAO,CAAC;AACR,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,QAAQ,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM;AACvC,UAAU,IAAI,EAAE,CAAC,CAAC,IAAI;AACtB,UAAU,MAAM,EAAE,CAAC,CAAC,MAAM;AAC1B,UAAU,WAAW,EAAE,CAAC,CAAC,WAAW;AACpC,UAAU,aAAa,EAAE,CAAC,CAAC,aAAa,CAAC,WAAW;AACpD,SAAS,CAAC,CAAC;AACX,QAAQ,aAAa,EAAE,QAAQ,CAAC,MAAM;AACtC,QAAQ,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC3D,OAAO,CAAC;AACR;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC;AAC3D,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,KAAK,EAAE,mCAAmC;AAClD,QAAQ,OAAO,EAAE,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,eAAe;AACzE,QAAQ,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC3D,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;AACK,MAAC,OAAO,GAAG,YAAY;AAC5B,EAAE,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE;AAC5B,IAAI,MAAM,EAAE,GAAG;AACf,IAAI,OAAO,EAAE;AACb,MAAM,6BAA6B,EAAE,GAAG;AACxC,MAAM,8BAA8B,EAAE,oBAAoB;AAC1D,MAAM,8BAA8B,EAAE;AACtC;AACA,GAAG,CAAC;AACJ;;;;"}