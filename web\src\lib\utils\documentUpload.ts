import { uploadFile, FileType } from '../../../../utils/r2FileUpload.js';
import path from 'path';

// Document type to R2 file type mapping
const DOCUMENT_TYPE_TO_R2_TYPE: Record<string, FileType> = {
  resume: 'resumes',
  cover_letter: 'userDocuments',
  question_response: 'userDocuments',
  letter_of_recommendation: 'userDocuments',
  references: 'userDocuments',
  employment_certification: 'userDocuments',
  document: 'userDocuments',
  default: 'userDocuments',
};

/**
 * Upload a document file to Cloudflare R2 storage
 *
 * @param file The file to upload
 * @param documentType The type of document (resume, cover_letter, etc.)
 * @param userId Optional user ID for file naming
 * @returns Object containing file information
 */
export async function uploadDocument(file: File, documentType: string, userId?: string) {
  console.log('📤 Uploading document to R2:', {
    fileName: file.name,
    fileType: file.type,
    fileSize: file.size,
    documentType,
  });

  // Validate file extension
  const fileExtension = path.extname(file.name).toLowerCase() || '.pdf';
  const allowedExtensions = ['.pdf', '.doc', '.docx'];
  if (!allowedExtensions.includes(fileExtension)) {
    console.error(`File type ${fileExtension} is not supported.`);
    throw new Error(
      `File type ${fileExtension} is not supported. Please upload a PDF or Word document.`
    );
  }

  console.log(`File extension ${fileExtension} is valid.`);

  // Get the appropriate R2 file type
  const r2FileType = DOCUMENT_TYPE_TO_R2_TYPE[documentType] || DOCUMENT_TYPE_TO_R2_TYPE.default;

  // Create a buffer from the file
  const buffer = Buffer.from(await file.arrayBuffer());

  try {
    // Upload to R2 using the new utility
    const uploadResult = await uploadFile(
      buffer,
      file.name,
      file.type || 'application/octet-stream',
      r2FileType,
      userId
    );

    if (!uploadResult.success) {
      throw new Error(uploadResult.error || 'Upload failed');
    }

    console.log('✅ Document uploaded successfully to R2:', {
      fileKey: uploadResult.fileKey,
      publicUrl: uploadResult.publicUrl,
      fileSize: uploadResult.fileSize,
    });

    // Return in the same format as the old function for compatibility
    return {
      originalFileName: file.name,
      filename: path.basename(uploadResult.fileKey!),
      filePath: uploadResult.fileKey!, // R2 file key
      publicPath: uploadResult.publicUrl!, // R2 public URL
      fileSize: uploadResult.fileSize!,
      contentType: uploadResult.contentType!,
    };
  } catch (error) {
    console.error('❌ Failed to upload document to R2:', error);
    throw new Error(
      `Failed to upload file: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}
