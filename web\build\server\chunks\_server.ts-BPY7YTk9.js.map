{"version": 3, "file": "_server.ts-BPY7YTk9.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/profile-picture/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../chunks/prisma.js\";\nconst POST = async ({ request, locals }) => {\n  try {\n    if (!locals.user) {\n      console.error(\"No user in locals\");\n      return json({ error: \"Unauthorized\" }, { status: 401 });\n    }\n    const userData = await prisma.user.findUnique({\n      where: { id: locals.user.id }\n    });\n    if (!userData) {\n      console.error(\"User not found in database\");\n      return json({ error: \"User not found\" }, { status: 404 });\n    }\n    console.log(\"User authenticated for profile picture upload:\", userData.id);\n    const formData = await request.formData();\n    const file = formData.get(\"profilePicture\");\n    if (!file) {\n      return json({ error: \"No file provided\" }, { status: 400 });\n    }\n    if (!file.type.startsWith(\"image/\")) {\n      return json({ error: \"File must be an image\" }, { status: 400 });\n    }\n    if (file.size > 5 * 1024 * 1024) {\n      return json({ error: \"File size must be less than 5MB\" }, { status: 400 });\n    }\n    const arrayBuffer = await file.arrayBuffer();\n    const buffer = Buffer.from(arrayBuffer);\n    const base64 = buffer.toString(\"base64\");\n    const dataUrl = `data:${file.type};base64,${base64}`;\n    await prisma.user.update({\n      where: { id: userData.id },\n      data: {\n        image: dataUrl\n      }\n    });\n    return json({ success: true, imageUrl: dataUrl });\n  } catch (error) {\n    console.error(\"Error uploading profile picture:\", error);\n    return json({ error: \"Failed to upload profile picture\" }, { status: 500 });\n  }\n};\nconst DELETE = async ({ locals }) => {\n  try {\n    if (!locals.user) {\n      console.error(\"No user in locals\");\n      return json({ error: \"Unauthorized\" }, { status: 401 });\n    }\n    const userData = await prisma.user.findUnique({\n      where: { id: locals.user.id }\n    });\n    if (!userData) {\n      console.error(\"User not found in database\");\n      return json({ error: \"User not found\" }, { status: 404 });\n    }\n    console.log(\"User authenticated for profile picture deletion:\", userData.id);\n    await prisma.user.update({\n      where: { id: userData.id },\n      data: {\n        image: null\n      }\n    });\n    return json({ success: true });\n  } catch (error) {\n    console.error(\"Error removing profile picture:\", error);\n    return json({ error: \"Failed to remove profile picture\" }, { status: 500 });\n  }\n};\nexport {\n  DELETE,\n  POST\n};\n"], "names": [], "mappings": ";;;;AAEK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AAC5C,EAAE,IAAI;AACN,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;AACtB,MAAM,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC;AACxC,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7D;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAClD,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;AACjC,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,OAAO,CAAC,KAAK,CAAC,4BAA4B,CAAC;AACjD,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,gBAAgB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC/D;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,gDAAgD,EAAE,QAAQ,CAAC,EAAE,CAAC;AAC9E,IAAI,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,QAAQ,EAAE;AAC7C,IAAI,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,gBAAgB,CAAC;AAC/C,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,kBAAkB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE;AACA,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;AACzC,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtE;AACA,IAAI,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;AACrC,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,iCAAiC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAChF;AACA,IAAI,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE;AAChD,IAAI,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC;AAC3C,IAAI,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;AAC5C,IAAI,MAAM,OAAO,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AACxD,IAAI,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AAC7B,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE;AAChC,MAAM,IAAI,EAAE;AACZ,QAAQ,KAAK,EAAE;AACf;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC;AACrD,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC;AAC5D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,kCAAkC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC/E;AACA;AACK,MAAC,MAAM,GAAG,OAAO,EAAE,MAAM,EAAE,KAAK;AACrC,EAAE,IAAI;AACN,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;AACtB,MAAM,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC;AACxC,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7D;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAClD,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;AACjC,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,OAAO,CAAC,KAAK,CAAC,4BAA4B,CAAC;AACjD,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,gBAAgB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC/D;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,kDAAkD,EAAE,QAAQ,CAAC,EAAE,CAAC;AAChF,IAAI,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AAC7B,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE;AAChC,MAAM,IAAI,EAAE;AACZ,QAAQ,KAAK,EAAE;AACf;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;AAClC,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC;AAC3D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,kCAAkC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC/E;AACA;;;;"}