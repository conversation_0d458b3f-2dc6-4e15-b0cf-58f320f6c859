{"version": 3, "file": "_server.ts-gEAYWwWj.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/profile/_id_/parsing-status/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../../chunks/prisma.js\";\nasync function getProfileParsingState(profileId) {\n  try {\n    const parsingProcess = await prisma.workerProcess.findFirst({\n      where: {\n        id: { startsWith: `profile-parsing-${profileId}-` },\n        status: { in: [\"pending\", \"processing\"] }\n      },\n      orderBy: {\n        createdAt: \"desc\"\n      }\n    });\n    if (!parsingProcess) {\n      return { isParsing: false };\n    }\n    const resumeId = parsingProcess.id.split(`profile-parsing-${profileId}-`)[1];\n    return {\n      isParsing: true,\n      resumeId,\n      status: parsingProcess.status,\n      startedAt: parsingProcess.startedAt,\n      createdAt: parsingProcess.createdAt\n    };\n  } catch (error) {\n    console.error(\"Error checking profile parsing state:\", error);\n    return { isParsing: false, error: String(error) };\n  }\n}\nasync function getProfileParsedState(profileId) {\n  try {\n    const parsedResume = await prisma.parsedResume.findFirst({\n      where: {\n        profileId\n      },\n      orderBy: {\n        parsedAt: \"desc\"\n      }\n    });\n    if (!parsedResume) {\n      return { isParsed: false };\n    }\n    return {\n      isParsed: true,\n      parsedResumeId: parsedResume.id,\n      resumeId: parsedResume.resumeId,\n      parsedAt: parsedResume.parsedAt\n    };\n  } catch (error) {\n    console.error(\"Error checking profile parsed state:\", error);\n    return { isParsed: false, error: String(error) };\n  }\n}\nasync function getProfileParsingStatus(profileId) {\n  try {\n    const parsingState = await getProfileParsingState(profileId);\n    if (parsingState.isParsing) {\n      return {\n        status: \"parsing\",\n        ...parsingState\n      };\n    }\n    const parsedState = await getProfileParsedState(profileId);\n    if (parsedState.isParsed) {\n      return {\n        status: \"parsed\",\n        ...parsedState\n      };\n    }\n    return {\n      status: \"not_parsed\"\n    };\n  } catch (error) {\n    console.error(\"Error getting profile parsing status:\", error);\n    return { status: \"error\", error: String(error) };\n  }\n}\nconst GET = async ({ params, locals }) => {\n  const user = locals.user;\n  if (!user) return new Response(\"Unauthorized\", { status: 401 });\n  const profileId = params.id;\n  const profile = await prisma.profile.findUnique({\n    where: { id: profileId },\n    include: { team: { include: { members: true } } }\n  });\n  if (!profile) {\n    return json({ error: \"Profile not found\" }, { status: 404 });\n  }\n  const isOwner = profile.userId === user.id;\n  const isTeamMember = profile.team?.members.some((m) => m.userId === user.id);\n  if (!isOwner && !isTeamMember) {\n    return json({ error: \"Unauthorized access to profile\" }, { status: 403 });\n  }\n  const parsingStatus = await getProfileParsingStatus(profileId);\n  return json(parsingStatus);\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;AAEA,eAAe,sBAAsB,CAAC,SAAS,EAAE;AACjD,EAAE,IAAI;AACN,IAAI,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC;AAChE,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE,EAAE,UAAU,EAAE,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE;AAC3D,QAAQ,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,SAAS,EAAE,YAAY,CAAC;AAC/C,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,SAAS,EAAE;AACnB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,cAAc,EAAE;AACzB,MAAM,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE;AACjC;AACA,IAAI,MAAM,QAAQ,GAAG,cAAc,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChF,IAAI,OAAO;AACX,MAAM,SAAS,EAAE,IAAI;AACrB,MAAM,QAAQ;AACd,MAAM,MAAM,EAAE,cAAc,CAAC,MAAM;AACnC,MAAM,SAAS,EAAE,cAAc,CAAC,SAAS;AACzC,MAAM,SAAS,EAAE,cAAc,CAAC;AAChC,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC;AACjE,IAAI,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;AACrD;AACA;AACA,eAAe,qBAAqB,CAAC,SAAS,EAAE;AAChD,EAAE,IAAI;AACN,IAAI,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC;AAC7D,MAAM,KAAK,EAAE;AACb,QAAQ;AACR,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,QAAQ,EAAE;AAClB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,YAAY,EAAE;AACvB,MAAM,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE;AAChC;AACA,IAAI,OAAO;AACX,MAAM,QAAQ,EAAE,IAAI;AACpB,MAAM,cAAc,EAAE,YAAY,CAAC,EAAE;AACrC,MAAM,QAAQ,EAAE,YAAY,CAAC,QAAQ;AACrC,MAAM,QAAQ,EAAE,YAAY,CAAC;AAC7B,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC;AAChE,IAAI,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;AACpD;AACA;AACA,eAAe,uBAAuB,CAAC,SAAS,EAAE;AAClD,EAAE,IAAI;AACN,IAAI,MAAM,YAAY,GAAG,MAAM,sBAAsB,CAAC,SAAS,CAAC;AAChE,IAAI,IAAI,YAAY,CAAC,SAAS,EAAE;AAChC,MAAM,OAAO;AACb,QAAQ,MAAM,EAAE,SAAS;AACzB,QAAQ,GAAG;AACX,OAAO;AACP;AACA,IAAI,MAAM,WAAW,GAAG,MAAM,qBAAqB,CAAC,SAAS,CAAC;AAC9D,IAAI,IAAI,WAAW,CAAC,QAAQ,EAAE;AAC9B,MAAM,OAAO;AACb,QAAQ,MAAM,EAAE,QAAQ;AACxB,QAAQ,GAAG;AACX,OAAO;AACP;AACA,IAAI,OAAO;AACX,MAAM,MAAM,EAAE;AACd,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC;AACjE,IAAI,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;AACpD;AACA;AACK,MAAC,GAAG,GAAG,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK;AAC1C,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE,EAAE,MAAM,SAAS,GAAG,MAAM,CAAC,EAAE;AAC7B,EAAE,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;AAClD,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;AAC5B,IAAI,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE;AACnD,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAChE;AACA,EAAE,MAAM,OAAO,GAAG,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC,EAAE;AAC5C,EAAE,MAAM,YAAY,GAAG,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,EAAE,CAAC;AAC9E,EAAE,IAAI,CAAC,OAAO,IAAI,CAAC,YAAY,EAAE;AACjC,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,gCAAgC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7E;AACA,EAAE,MAAM,aAAa,GAAG,MAAM,uBAAuB,CAAC,SAAS,CAAC;AAChE,EAAE,OAAO,IAAI,CAAC,aAAa,CAAC;AAC5B;;;;"}