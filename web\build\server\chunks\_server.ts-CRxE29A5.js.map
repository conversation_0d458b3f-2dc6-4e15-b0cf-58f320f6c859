{"version": 3, "file": "_server.ts-CRxE29A5.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/referrals/analytics/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nconst GET = async ({ locals }) => {\n  const user = locals.user;\n  if (!user?.email) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  try {\n    const userData = await prisma.user.findUnique({\n      where: { email: user.email }\n    });\n    if (!userData) {\n      return json({ error: \"User not found\" }, { status: 404 });\n    }\n    const referralsByCode = await prisma.referral.findMany({\n      where: { referrerId: userData.id },\n      include: {\n        referred: {\n          select: {\n            id: true,\n            name: true,\n            email: true,\n            createdAt: true\n          }\n        }\n      },\n      orderBy: { createdAt: \"desc\" }\n    });\n    const codesWithData = [...new Set(referralsByCode.map((r) => r.referralCode))];\n    const referralCodeHistory = await prisma.referralCodeHistory.findMany({\n      where: {\n        userId: userData.id,\n        referralCode: { in: codesWithData }\n      },\n      orderBy: { createdAt: \"desc\" }\n    });\n    const codeAnalytics = codesWithData.map((code) => {\n      const referralsForCode = referralsByCode.filter(\n        (referral) => referral.referralCode === code\n      );\n      const historyEntry = referralCodeHistory.find((h) => h.referralCode === code);\n      const isCurrentCode = userData.referralCode === code;\n      return {\n        referralCode: code,\n        isActive: isCurrentCode,\n        createdAt: historyEntry?.createdAt || referralsForCode[0]?.createdAt,\n        deactivatedAt: historyEntry?.deactivatedAt,\n        reason: historyEntry?.reason || (isCurrentCode ? \"current\" : \"historical\"),\n        referralCount: referralsForCode.length,\n        completedReferrals: referralsForCode.filter((r) => r.status === \"completed\").length,\n        pendingReferrals: referralsForCode.filter((r) => r.status === \"pending\").length,\n        referrals: referralsForCode\n      };\n    }).sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());\n    const monthlyAnalytics = [];\n    const now = /* @__PURE__ */ new Date();\n    for (let i = 5; i >= 0; i--) {\n      const monthDate = new Date(now.getFullYear(), now.getMonth() - i, 1);\n      const nextMonthDate = new Date(now.getFullYear(), now.getMonth() - i + 1, 1);\n      const monthReferrals = referralsByCode.filter((referral) => {\n        const referralDate = new Date(referral.createdAt);\n        return referralDate >= monthDate && referralDate < nextMonthDate;\n      });\n      monthlyAnalytics.push({\n        month: monthDate.toLocaleDateString(\"en-US\", { month: \"short\" }),\n        year: monthDate.getFullYear(),\n        referrals: monthReferrals.length,\n        completed: monthReferrals.filter((r) => r.status === \"completed\").length,\n        pending: monthReferrals.filter((r) => r.status === \"pending\").length\n      });\n    }\n    let cumulative = 0;\n    const cumulativeData = monthlyAnalytics.map((month) => {\n      cumulative += month.referrals;\n      return {\n        ...month,\n        cumulative\n      };\n    });\n    return json({\n      codeHistory: codeAnalytics,\n      monthlyAnalytics: cumulativeData,\n      totalReferrals: referralsByCode.length,\n      totalCompleted: referralsByCode.filter((r) => r.status === \"completed\").length,\n      totalPending: referralsByCode.filter((r) => r.status === \"pending\").length,\n      conversionRate: referralsByCode.length > 0 ? Math.round(\n        referralsByCode.filter((r) => r.status === \"completed\").length / referralsByCode.length * 100\n      ) : 0\n    });\n  } catch (error) {\n    console.error(\"Error getting referral analytics:\", error);\n    return json({ error: \"Failed to get referral analytics\" }, { status: 500 });\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;AAEK,MAAC,GAAG,GAAG,OAAO,EAAE,MAAM,EAAE,KAAK;AAClC,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE;AACpB,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAClD,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK;AAChC,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,gBAAgB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC/D;AACA,IAAI,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;AAC3D,MAAM,KAAK,EAAE,EAAE,UAAU,EAAE,QAAQ,CAAC,EAAE,EAAE;AACxC,MAAM,OAAO,EAAE;AACf,QAAQ,QAAQ,EAAE;AAClB,UAAU,MAAM,EAAE;AAClB,YAAY,EAAE,EAAE,IAAI;AACpB,YAAY,IAAI,EAAE,IAAI;AACtB,YAAY,KAAK,EAAE,IAAI;AACvB,YAAY,SAAS,EAAE;AACvB;AACA;AACA,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM;AAClC,KAAK,CAAC;AACN,IAAI,MAAM,aAAa,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;AAClF,IAAI,MAAM,mBAAmB,GAAG,MAAM,MAAM,CAAC,mBAAmB,CAAC,QAAQ,CAAC;AAC1E,MAAM,KAAK,EAAE;AACb,QAAQ,MAAM,EAAE,QAAQ,CAAC,EAAE;AAC3B,QAAQ,YAAY,EAAE,EAAE,EAAE,EAAE,aAAa;AACzC,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM;AAClC,KAAK,CAAC;AACN,IAAI,MAAM,aAAa,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK;AACtD,MAAM,MAAM,gBAAgB,GAAG,eAAe,CAAC,MAAM;AACrD,QAAQ,CAAC,QAAQ,KAAK,QAAQ,CAAC,YAAY,KAAK;AAChD,OAAO;AACP,MAAM,MAAM,YAAY,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,YAAY,KAAK,IAAI,CAAC;AACnF,MAAM,MAAM,aAAa,GAAG,QAAQ,CAAC,YAAY,KAAK,IAAI;AAC1D,MAAM,OAAO;AACb,QAAQ,YAAY,EAAE,IAAI;AAC1B,QAAQ,QAAQ,EAAE,aAAa;AAC/B,QAAQ,SAAS,EAAE,YAAY,EAAE,SAAS,IAAI,gBAAgB,CAAC,CAAC,CAAC,EAAE,SAAS;AAC5E,QAAQ,aAAa,EAAE,YAAY,EAAE,aAAa;AAClD,QAAQ,MAAM,EAAE,YAAY,EAAE,MAAM,KAAK,aAAa,GAAG,SAAS,GAAG,YAAY,CAAC;AAClF,QAAQ,aAAa,EAAE,gBAAgB,CAAC,MAAM;AAC9C,QAAQ,kBAAkB,EAAE,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC,MAAM;AAC3F,QAAQ,gBAAgB,EAAE,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,MAAM;AACvF,QAAQ,SAAS,EAAE;AACnB,OAAO;AACP,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC;AACxF,IAAI,MAAM,gBAAgB,GAAG,EAAE;AAC/B,IAAI,MAAM,GAAG,mBAAmB,IAAI,IAAI,EAAE;AAC1C,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AACjC,MAAM,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;AAC1E,MAAM,MAAM,aAAa,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AAClF,MAAM,MAAM,cAAc,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,QAAQ,KAAK;AAClE,QAAQ,MAAM,YAAY,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;AACzD,QAAQ,OAAO,YAAY,IAAI,SAAS,IAAI,YAAY,GAAG,aAAa;AACxE,OAAO,CAAC;AACR,MAAM,gBAAgB,CAAC,IAAI,CAAC;AAC5B,QAAQ,KAAK,EAAE,SAAS,CAAC,kBAAkB,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;AACxE,QAAQ,IAAI,EAAE,SAAS,CAAC,WAAW,EAAE;AACrC,QAAQ,SAAS,EAAE,cAAc,CAAC,MAAM;AACxC,QAAQ,SAAS,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC,MAAM;AAChF,QAAQ,OAAO,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC;AACtE,OAAO,CAAC;AACR;AACA,IAAI,IAAI,UAAU,GAAG,CAAC;AACtB,IAAI,MAAM,cAAc,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK;AAC3D,MAAM,UAAU,IAAI,KAAK,CAAC,SAAS;AACnC,MAAM,OAAO;AACb,QAAQ,GAAG,KAAK;AAChB,QAAQ;AACR,OAAO;AACP,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,WAAW,EAAE,aAAa;AAChC,MAAM,gBAAgB,EAAE,cAAc;AACtC,MAAM,cAAc,EAAE,eAAe,CAAC,MAAM;AAC5C,MAAM,cAAc,EAAE,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC,MAAM;AACpF,MAAM,YAAY,EAAE,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,MAAM;AAChF,MAAM,cAAc,EAAE,eAAe,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK;AAC7D,QAAQ,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC,MAAM,GAAG,eAAe,CAAC,MAAM,GAAG;AAClG,OAAO,GAAG;AACV,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC;AAC7D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,kCAAkC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC/E;AACA;;;;"}