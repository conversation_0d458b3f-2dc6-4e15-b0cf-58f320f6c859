{"version": 3, "file": "_server.ts-DoUYZTNa.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/profiles/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../chunks/prisma.js\";\nimport { a as hasFeatureAccess, g as getFeatureAccessDetails } from \"../../../../chunks/feature-check.js\";\nimport { incrementFeatureUsage } from \"../../../../chunks/feature-usage.js\";\nconst GET = async ({ locals }) => {\n  const user = locals.user;\n  if (!user) return new Response(\"Unauthorized\", { status: 401 });\n  const profiles = await prisma.profile.findMany({\n    where: {\n      OR: [\n        { userId: user.id },\n        {\n          team: {\n            members: {\n              some: { userId: user.id }\n            }\n          }\n        }\n      ]\n    },\n    select: { id: true, name: true }\n  });\n  const resumes = await prisma.resume.findMany({\n    where: {\n      OR: [\n        { profileId: null },\n        // Resumes not associated with any profile\n        { usedByProfile: null }\n        // Resumes not used by any profile\n      ]\n    },\n    select: {\n      id: true,\n      label: true,\n      profileId: true,\n      // Include profileId to check the association\n      usedByProfile: true\n      // Include usedByProfileId to check if it's been used\n    },\n    orderBy: {\n      createdAt: \"desc\"\n    }\n  });\n  return json({ profiles, resumes });\n};\nconst POST = async ({ request, locals }) => {\n  const user = locals.user;\n  if (!user) {\n    return new Response(\"Unauthorized\", { status: 401 });\n  }\n  try {\n    console.log(\"Creating profile for user:\", user.id);\n    const body = await request.json();\n    console.log(\"Request body:\", body);\n    const { name, data, defaultDocumentId } = body;\n    if (!name) {\n      console.log(\"Profile name is required\");\n      return new Response(\"Profile name is required\", { status: 400 });\n    }\n    const hasAccess = await hasFeatureAccess(user.id, \"job_search_profiles\");\n    console.log(\"User access to job_search_profiles:\", hasAccess);\n    const userData = await prisma.user.findUnique({\n      where: { id: user.id },\n      select: { role: true }\n    });\n    console.log(\"User role:\", userData?.role);\n    console.log(\"Allowing user to create a profile regardless of feature check\");\n    const userProfilesCount = await prisma.profile.count({\n      where: { userId: user.id }\n    });\n    let profileLimit = 1;\n    try {\n      const accessDetails = await getFeatureAccessDetails(user.id, \"job_search_profiles\");\n      console.log(\"Feature access details:\", JSON.stringify(accessDetails));\n      if (accessDetails.hasAccess && accessDetails.limits && accessDetails.limits.length > 0) {\n        const foundLimit = accessDetails.limits.find(\n          (l) => l.limitId === \"job_search_profiles\" || l.limitId === \"job_search_profiles_limit\"\n        );\n        if (foundLimit) {\n          if (foundLimit.value === \"unlimited\") {\n            profileLimit = Infinity;\n            console.log(\"User has unlimited profiles from plan\");\n          } else {\n            profileLimit = parseInt(foundLimit.value, 10);\n            console.log(`User has ${profileLimit} profiles limit from plan`);\n          }\n        }\n      }\n    } catch (error) {\n      console.error(\"Error getting feature access details:\", error);\n    }\n    if (profileLimit === 1) {\n      try {\n        const { Roles } = await import(\"../../../../chunks/roles.js\");\n        const roleConfig = Roles[userData.role] || Roles[\"free\"];\n        if (roleConfig.limits.profiles !== void 0) {\n          profileLimit = roleConfig.limits.profiles === null ? Infinity : roleConfig.limits.profiles;\n          console.log(`Using role-based profile limit: ${profileLimit} for role ${userData.role}`);\n        }\n      } catch (error) {\n        console.error(\"Error getting role-based limits:\", error);\n      }\n    }\n    if (userProfilesCount >= profileLimit) {\n      console.log(`User has reached their profile limit: ${userProfilesCount}/${profileLimit}`);\n      return json(\n        {\n          success: false,\n          error: profileLimit === 1 ? `Your current plan allows only ${profileLimit} profile. Please upgrade your plan to create more profiles.` : `You've reached your profile limit of ${profileLimit}. Please upgrade your plan to create more profiles.`,\n          limitReached: true,\n          currentCount: userProfilesCount,\n          limit: profileLimit\n        },\n        { status: 403 }\n      );\n    }\n    const newProfile = await prisma.profile.create({\n      data: {\n        name,\n        userId: user.id,\n        ...defaultDocumentId && { defaultDocumentId }\n      }\n    });\n    if (data) {\n      await prisma.profileData.create({\n        data: {\n          profileId: newProfile.id,\n          data\n        }\n      });\n    }\n    try {\n      await incrementFeatureUsage(user.id, \"job_search_profiles\", \"job_search_profiles\");\n    } catch (usageError) {\n      console.warn(\"Could not increment feature usage:\", usageError.message);\n    }\n    console.log(\"Profile created successfully:\", newProfile.id);\n    return json({\n      success: true,\n      profileId: newProfile.id,\n      profile: {\n        id: newProfile.id,\n        name: newProfile.name,\n        createdAt: newProfile.createdAt,\n        updatedAt: newProfile.updatedAt\n      }\n    });\n  } catch (error) {\n    console.error(\"Error creating profile:\", error);\n    return json(\n      {\n        success: false,\n        error: \"Failed to create profile\",\n        details: error.message || String(error)\n      },\n      { status: 500 }\n    );\n  }\n};\nexport {\n  GET,\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAIK,MAAC,GAAG,GAAG,OAAO,EAAE,MAAM,EAAE,KAAK;AAClC,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE,EAAE,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;AACjD,IAAI,KAAK,EAAE;AACX,MAAM,EAAE,EAAE;AACV,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;AAC3B,QAAQ;AACR,UAAU,IAAI,EAAE;AAChB,YAAY,OAAO,EAAE;AACrB,cAAc,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE;AACrC;AACA;AACA;AACA;AACA,KAAK;AACL,IAAI,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AAClC,GAAG,CAAC;AACJ,EAAE,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;AAC/C,IAAI,KAAK,EAAE;AACX,MAAM,EAAE,EAAE;AACV,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE;AAC3B;AACA,QAAQ,EAAE,aAAa,EAAE,IAAI;AAC7B;AACA;AACA,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,EAAE,EAAE,IAAI;AACd,MAAM,KAAK,EAAE,IAAI;AACjB,MAAM,SAAS,EAAE,IAAI;AACrB;AACA,MAAM,aAAa,EAAE;AACrB;AACA,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,SAAS,EAAE;AACjB;AACA,GAAG,CAAC;AACJ,EAAE,OAAO,IAAI,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC;AACpC;AACK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AAC5C,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxD;AACA,EAAE,IAAI;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAAC,EAAE,CAAC;AACtD,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC;AACtC,IAAI,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,iBAAiB,EAAE,GAAG,IAAI;AAClD,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC;AAC7C,MAAM,OAAO,IAAI,QAAQ,CAAC,0BAA0B,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtE;AACA,IAAI,MAAM,SAAS,GAAG,MAAM,gBAAgB,CAAC,IAAI,CAAC,EAAE,EAAE,qBAAqB,CAAC;AAC5E,IAAI,OAAO,CAAC,GAAG,CAAC,qCAAqC,EAAE,SAAS,CAAC;AACjE,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAClD,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;AAC5B,MAAM,MAAM,EAAE,EAAE,IAAI,EAAE,IAAI;AAC1B,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,QAAQ,EAAE,IAAI,CAAC;AAC7C,IAAI,OAAO,CAAC,GAAG,CAAC,+DAA+D,CAAC;AAChF,IAAI,MAAM,iBAAiB,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;AACzD,MAAM,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE;AAC9B,KAAK,CAAC;AACN,IAAI,IAAI,YAAY,GAAG,CAAC;AACxB,IAAI,IAAI;AACR,MAAM,MAAM,aAAa,GAAG,MAAM,uBAAuB,CAAC,IAAI,CAAC,EAAE,EAAE,qBAAqB,CAAC;AACzF,MAAM,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;AAC3E,MAAM,IAAI,aAAa,CAAC,SAAS,IAAI,aAAa,CAAC,MAAM,IAAI,aAAa,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;AAC9F,QAAQ,MAAM,UAAU,GAAG,aAAa,CAAC,MAAM,CAAC,IAAI;AACpD,UAAU,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,KAAK,qBAAqB,IAAI,CAAC,CAAC,OAAO,KAAK;AACtE,SAAS;AACT,QAAQ,IAAI,UAAU,EAAE;AACxB,UAAU,IAAI,UAAU,CAAC,KAAK,KAAK,WAAW,EAAE;AAChD,YAAY,YAAY,GAAG,QAAQ;AACnC,YAAY,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC;AAChE,WAAW,MAAM;AACjB,YAAY,YAAY,GAAG,QAAQ,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE,CAAC;AACzD,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,YAAY,CAAC,yBAAyB,CAAC,CAAC;AAC5E;AACA;AACA;AACA,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC;AACnE;AACA,IAAI,IAAI,YAAY,KAAK,CAAC,EAAE;AAC5B,MAAM,IAAI;AACV,QAAQ,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,qBAA6B,CAAC;AACrE,QAAQ,MAAM,UAAU,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC;AAChE,QAAQ,IAAI,UAAU,CAAC,MAAM,CAAC,QAAQ,KAAK,KAAK,CAAC,EAAE;AACnD,UAAU,YAAY,GAAG,UAAU,CAAC,MAAM,CAAC,QAAQ,KAAK,IAAI,GAAG,QAAQ,GAAG,UAAU,CAAC,MAAM,CAAC,QAAQ;AACpG,UAAU,OAAO,CAAC,GAAG,CAAC,CAAC,gCAAgC,EAAE,YAAY,CAAC,UAAU,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;AAClG;AACA,OAAO,CAAC,OAAO,KAAK,EAAE;AACtB,QAAQ,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC;AAChE;AACA;AACA,IAAI,IAAI,iBAAiB,IAAI,YAAY,EAAE;AAC3C,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,sCAAsC,EAAE,iBAAiB,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC;AAC/F,MAAM,OAAO,IAAI;AACjB,QAAQ;AACR,UAAU,OAAO,EAAE,KAAK;AACxB,UAAU,KAAK,EAAE,YAAY,KAAK,CAAC,GAAG,CAAC,8BAA8B,EAAE,YAAY,CAAC,2DAA2D,CAAC,GAAG,CAAC,qCAAqC,EAAE,YAAY,CAAC,mDAAmD,CAAC;AAC5P,UAAU,YAAY,EAAE,IAAI;AAC5B,UAAU,YAAY,EAAE,iBAAiB;AACzC,UAAU,KAAK,EAAE;AACjB,SAAS;AACT,QAAQ,EAAE,MAAM,EAAE,GAAG;AACrB,OAAO;AACP;AACA,IAAI,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;AACnD,MAAM,IAAI,EAAE;AACZ,QAAQ,IAAI;AACZ,QAAQ,MAAM,EAAE,IAAI,CAAC,EAAE;AACvB,QAAQ,GAAG,iBAAiB,IAAI,EAAE,iBAAiB;AACnD;AACA,KAAK,CAAC;AACN,IAAI,IAAI,IAAI,EAAE;AACd,MAAM,MAAM,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;AACtC,QAAQ,IAAI,EAAE;AACd,UAAU,SAAS,EAAE,UAAU,CAAC,EAAE;AAClC,UAAU;AACV;AACA,OAAO,CAAC;AACR;AACA,IAAI,IAAI;AACR,MAAM,MAAM,qBAAqB,CAAC,IAAI,CAAC,EAAE,EAAE,qBAAqB,EAAE,qBAAqB,CAAC;AACxF,KAAK,CAAC,OAAO,UAAU,EAAE;AACzB,MAAM,OAAO,CAAC,IAAI,CAAC,oCAAoC,EAAE,UAAU,CAAC,OAAO,CAAC;AAC5E;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,+BAA+B,EAAE,UAAU,CAAC,EAAE,CAAC;AAC/D,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,SAAS,EAAE,UAAU,CAAC,EAAE;AAC9B,MAAM,OAAO,EAAE;AACf,QAAQ,EAAE,EAAE,UAAU,CAAC,EAAE;AACzB,QAAQ,IAAI,EAAE,UAAU,CAAC,IAAI;AAC7B,QAAQ,SAAS,EAAE,UAAU,CAAC,SAAS;AACvC,QAAQ,SAAS,EAAE,UAAU,CAAC;AAC9B;AACA,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC;AACnD,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,KAAK,EAAE,0BAA0B;AACzC,QAAQ,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,MAAM,CAAC,KAAK;AAC9C,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;;;;"}