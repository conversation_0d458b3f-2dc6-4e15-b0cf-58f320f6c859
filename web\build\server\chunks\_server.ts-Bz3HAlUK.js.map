{"version": 3, "file": "_server.ts-Bz3HAlUK.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/robots.txt/_server.ts.js"], "sourcesContent": ["async function GET({ url }) {\n  const host = process.env.NODE_ENV === \"production\" ? \"hirli.com\" : url.host;\n  const protocol = process.env.NODE_ENV === \"production\" ? \"https\" : url.protocol.slice(0, -1);\n  const domain = `${protocol}://${host}`;\n  if (process.env.NODE_ENV !== \"production\") {\n    return new Response(\n      `# Non-production environment - disallow all\nUser-agent: *\nDisallow: /\n`,\n      {\n        headers: {\n          \"Content-Type\": \"text/plain\",\n          \"Cache-Control\": \"max-age=0, s-maxage=3600\"\n        }\n      }\n    );\n  }\n  const robotsTxt = `# https://www.robotstxt.org/robotstxt.html\nUser-agent: *\nAllow: /\n\n# Disallow admin and API routes\nDisallow: /admin/\nDisallow: /api/\nDisallow: /dashboard/\nDisallow: /graphql/\nDisallow: /graphiql/\n\n# Disallow authentication routes\nDisallow: /auth/\nDisallow: /login/\nDisallow: /signup/\nDisallow: /reset-password/\nDisallow: /verify-email/\nDisallow: /forgot-password/\n\n# Disallow user-specific routes\nDisallow: /profile/\nDisallow: /settings/\nDisallow: /account/\nDisallow: /user/\nDisallow: /notifications/\n\n# Disallow temporary and test routes\nDisallow: /test/\nDisallow: /demo/\nDisallow: /beta/\nDisallow: /dev/\nDisallow: /staging/\n\n# Disallow duplicate content\nDisallow: /*?*\nDisallow: /*&*\nDisallow: /*.pdf$\nDisallow: /*.doc$\nDisallow: /*.docx$\nDisallow: /*.xls$\nDisallow: /*.xlsx$\nDisallow: /*.ppt$\nDisallow: /*.pptx$\n\n# Block AI and crawler bots\nUser-agent: AI2Bot\nUser-agent: Ai2Bot-Dolma\nUser-agent: aiHitBot\nUser-agent: Amazonbot\nUser-agent: anthropic-ai\nUser-agent: Anthropic-AI\nUser-agent: Applebot\nUser-agent: Applebot-Extended\nUser-agent: Bard\nUser-agent: BardBot\nUser-agent: Baiduspider\nUser-agent: Bingbot\nUser-agent: Brightbot 1.0\nUser-agent: Bytespider\nUser-agent: CCBot\nUser-agent: ChatGPT-User\nUser-agent: Claude-Web\nUser-agent: ClaudeBot\nUser-agent: ClaudeBot-Preview\nUser-agent: cohere-ai\nUser-agent: cohere-training-data-crawler\nUser-agent: Cotoyogi\nUser-agent: Crawlspace\nUser-agent: DataForSeoBot\nUser-agent: Diffbot\nUser-agent: DuckAssistBot\nUser-agent: FacebookBot\nUser-agent: Factset_spyderbot\nUser-agent: FirecrawlAgent\nUser-agent: FriendlyCrawler\nUser-agent: Google-Extended\nUser-agent: GoogleOther\nUser-agent: GoogleOther-Image\nUser-agent: GoogleOther-Video\nUser-agent: GPTBot\nUser-agent: iaskspider/2.0\nUser-agent: ICC-Crawler\nUser-agent: ImagesiftBot\nUser-agent: img2dataset\nUser-agent: imgproxy\nUser-agent: ISSCyberRiskCrawler\nUser-agent: Kangaroo Bot\nUser-agent: llama-cpp\nUser-agent: llama-2\nUser-agent: llama-3\nUser-agent: LLaMABot\nUser-agent: magpie-crawler\nUser-agent: Mistral-AI\nUser-agent: MistralBot\nUser-agent: meta-externalagent\nUser-agent: Meta-ExternalAgent\nUser-agent: meta-externalfetcher\nUser-agent: Meta-ExternalFetcher\nUser-agent: NovaAct\nUser-agent: OAI-SearchBot\nUser-agent: omgili\nUser-agent: omgilibot\nUser-agent: Operator\nUser-agent: PanguBot\nUser-agent: Perplexity-User\nUser-agent: PerplexityBot\nUser-agent: PetalBot\nUser-agent: Poe\nUser-agent: PoeBot\nUser-agent: Qwant\nUser-agent: Scrapy\nUser-agent: SemrushBot-OCOB\nUser-agent: SemrushBot-SWA\nUser-agent: Sidetrade indexer bot\nUser-agent: TikTokSpider\nUser-agent: Timpibot\nUser-agent: VelenPublicWebCrawler\nUser-agent: Webzio-Extended\nUser-agent: YouBot\nUser-agent: YandexBot\nUser-agent: ZoominfoBot\nDisallow: /\n\n# Sitemap\nSitemap: ${domain}/sitemap.xml\n`;\n  return new Response(robotsTxt, {\n    headers: {\n      \"Content-Type\": \"text/plain\",\n      \"Cache-Control\": \"max-age=0, s-maxage=3600\"\n    }\n  });\n}\nexport {\n  GET\n};\n"], "names": [], "mappings": "AAAA,eAAe,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE;AAC5B,EAAE,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,GAAG,WAAW,GAAG,GAAG,CAAC,IAAI;AAC7E,EAAE,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,GAAG,OAAO,GAAG,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;AAC9F,EAAE,MAAM,MAAM,GAAG,CAAC,EAAE,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE;AAC7C,IAAI,OAAO,IAAI,QAAQ;AACvB,MAAM,CAAC;AACP;AACA;AACA,CAAC;AACD,MAAM;AACN,QAAQ,OAAO,EAAE;AACjB,UAAU,cAAc,EAAE,YAAY;AACtC,UAAU,eAAe,EAAE;AAC3B;AACA;AACA,KAAK;AACL;AACA,EAAE,MAAM,SAAS,GAAG,CAAC;AACrB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAAS,EAAE,MAAM,CAAC;AAClB,CAAC;AACD,EAAE,OAAO,IAAI,QAAQ,CAAC,SAAS,EAAE;AACjC,IAAI,OAAO,EAAE;AACb,MAAM,cAAc,EAAE,YAAY;AAClC,MAAM,eAAe,EAAE;AACvB;AACA,GAAG,CAAC;AACJ;;;;"}