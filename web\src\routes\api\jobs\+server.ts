import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async ({ url }) => {
  try {
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const title = url.searchParams.get('title') || '';
    const location = url.searchParams.get('location') || '';
    const locationType = url.searchParams.get('locationType') || '';
    const experience = url.searchParams.get('experience') || '';
    const collection = url.searchParams.get('collection') || '';
    const random = url.searchParams.get('random') === 'true';

    const skip = (page - 1) * limit;

    // Build the query filters
    const filters: any = {
      isActive: true,
    };

    if (title) {
      filters.title = {
        contains: title,
        mode: 'insensitive',
      };
    }

    if (location) {
      filters.location = {
        contains: location,
        mode: 'insensitive',
      };
    }

    if (locationType) {
      const types = locationType.split(',');
      if (types.length > 0) {
        filters.OR = filters.OR || [];

        // Add each location type as an OR condition
        types.forEach((type) => {
          filters.OR.push({
            remoteType: {
              contains: type,
              mode: 'insensitive',
            },
          });
        });
      }
    }

    if (experience) {
      const levels = experience.split(',');
      if (levels.length > 0) {
        filters.OR = filters.OR || [];

        // Add each experience level as an OR condition
        levels.forEach((level) => {
          filters.OR.push({
            experienceLevel: {
              contains: level,
              mode: 'insensitive',
            },
          });
        });
      }
    }

    // If collection is specified, get jobs from that collection
    if (collection) {
      // This would need to be implemented based on how collections are stored
      // For now, we'll just add a placeholder filter
      filters.collection = {
        equals: collection,
      };
    }

    // Handle salary filter
    const salary = url.searchParams.get('salary') || '';
    if (salary) {
      // Parse salary range
      const [min, max] = salary.split('-');

      if (min && max) {
        // If it's a range like '50000-75000'
        filters.OR = filters.OR || [];
        filters.OR.push({
          salary: {
            contains: `$${parseInt(min).toLocaleString()}-$${parseInt(max).toLocaleString()}`,
            mode: 'insensitive',
          },
        });
      } else if (salary.endsWith('+')) {
        // If it's a minimum like '150000+'
        const minValue = parseInt(salary.replace('+', ''));
        filters.OR = filters.OR || [];
        filters.OR.push({
          salary: {
            gte: minValue,
          },
        });
      }
    }

    // Query the database with company information including R2 logos
    const jobs = await prisma.job_listing.findMany({
      where: filters,
      take: limit,
      skip,
      select: {
        id: true,
        platform: true,
        jobId: true,
        title: true,
        company: true,
        location: true,
        url: true,
        isActive: true,
        createdAt: true,
        lastCheckedAt: true,
        employmentType: true,
        remoteType: true,
        experienceLevel: true,
        description: true,
        postedDate: true,
        applyLink: true,
        benefits: true,
        salary: true,
        salaryCurrency: true,
        salaryMax: true,
        salaryMin: true,
        // Include company data with R2 logo URLs (optional - many jobs don't have companyId yet)
        companyRelation: {
          select: {
            id: true,
            name: true,
            logoUrl: true, // This now contains R2 URLs
            domain: true,
            companySize: true,
            companyStage: true,
          },
        },
      },
      orderBy: random
        ? undefined
        : {
            postedDate: 'desc',
          },
    });

    // If random is requested, shuffle the results
    if (random) {
      for (let i = jobs.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [jobs[i], jobs[j]] = [jobs[j], jobs[i]];
      }
    }

    // Get total count for pagination
    const totalCount = await prisma.job_listing.count({
      where: filters,
    });

    return json({
      jobs,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasMore: skip + jobs.length < totalCount,
      },
    });
  } catch (error) {
    console.error('Error fetching jobs:', error);
    return json({ error: 'Failed to fetch jobs' }, { status: 500 });
  }
};
