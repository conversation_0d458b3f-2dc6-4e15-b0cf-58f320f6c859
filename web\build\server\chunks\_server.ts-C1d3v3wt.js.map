{"version": 3, "file": "_server.ts-C1d3v3wt.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/notifications/history/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { g as getUserFromToken } from \"../../../../../chunks/auth.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nconst GET = async ({ cookies, url }) => {\n  const user = await getUserFromToken(cookies);\n  if (!user) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  const userData = await prisma.user.findUnique({\n    where: { id: user.id },\n    select: { isAdmin: true, role: true }\n  });\n  if (!userData || !userData.isAdmin && userData.role !== \"admin\") {\n    return json({ error: \"Unauthorized\" }, { status: 403 });\n  }\n  try {\n    const limit = parseInt(url.searchParams.get(\"limit\") || \"50\");\n    const offset = parseInt(url.searchParams.get(\"offset\") || \"0\");\n    const type = url.searchParams.get(\"type\") || void 0;\n    let notifications = [];\n    let totalCount = 0;\n    try {\n      const whereClause = {};\n      if (type) {\n        whereClause.type = type;\n      }\n      try {\n        notifications = await prisma.notification.findMany({\n          where: whereClause,\n          orderBy: { createdAt: \"desc\" },\n          take: limit,\n          skip: offset,\n          include: {\n            user: {\n              select: {\n                id: true,\n                email: true,\n                name: true\n              }\n            }\n          }\n        });\n        totalCount = await prisma.notification.count({\n          where: whereClause\n        });\n        console.log(`Found ${notifications.length} notifications in database`);\n        if (notifications.length > 0) {\n          console.log(\"First notification:\", JSON.stringify(notifications[0]));\n        }\n      } catch (modelError) {\n        console.warn(\"Error getting notifications:\", modelError);\n        return json({\n          success: true,\n          notifications: [],\n          pagination: {\n            total: 0,\n            limit,\n            offset\n          }\n        });\n      }\n    } catch (error) {\n      console.error(\"Error getting notification history:\", error);\n      return json({ error: \"Failed to get notification history\" }, { status: 500 });\n    }\n    const formattedNotifications = notifications.map((notification) => {\n      let recipients = \"Unknown\";\n      if (notification.global) {\n        recipients = \"All Users\";\n      } else if (notification.userId) {\n        recipients = \"1 User\";\n      }\n      return {\n        id: notification.id,\n        title: notification.title,\n        message: notification.message,\n        url: notification.url,\n        type: notification.type,\n        global: notification.global,\n        sentAt: notification.createdAt,\n        sentBy: notification.user?.email || user.email,\n        // Use the notification user or current user as the sender\n        recipients,\n        metadata: notification.metadata ? JSON.parse(notification.metadata) : null\n      };\n    });\n    return json({\n      success: true,\n      notifications: formattedNotifications,\n      pagination: {\n        total: totalCount,\n        limit,\n        offset\n      }\n    });\n  } catch (error) {\n    console.error(\"Error getting notification history:\", error);\n    return json({ error: \"Failed to get notification history\" }, { status: 500 });\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;;;;;;AAGK,MAAC,GAAG,GAAG,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK;AACxC,EAAE,MAAM,IAAI,GAAG,MAAM,gBAAgB,CAAC,OAAO,CAAC;AAC9C,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAChD,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;AAC1B,IAAI,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AACvC,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,IAAI,KAAK,OAAO,EAAE;AACnE,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC;AACjE,IAAI,MAAM,MAAM,GAAG,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC;AAClE,IAAI,MAAM,IAAI,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC;AACvD,IAAI,IAAI,aAAa,GAAG,EAAE;AAC1B,IAAI,IAAI,UAAU,GAAG,CAAC;AACtB,IAAI,IAAI;AACR,MAAM,MAAM,WAAW,GAAG,EAAE;AAC5B,MAAM,IAAI,IAAI,EAAE;AAChB,QAAQ,WAAW,CAAC,IAAI,GAAG,IAAI;AAC/B;AACA,MAAM,IAAI;AACV,QAAQ,aAAa,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;AAC3D,UAAU,KAAK,EAAE,WAAW;AAC5B,UAAU,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;AACxC,UAAU,IAAI,EAAE,KAAK;AACrB,UAAU,IAAI,EAAE,MAAM;AACtB,UAAU,OAAO,EAAE;AACnB,YAAY,IAAI,EAAE;AAClB,cAAc,MAAM,EAAE;AACtB,gBAAgB,EAAE,EAAE,IAAI;AACxB,gBAAgB,KAAK,EAAE,IAAI;AAC3B,gBAAgB,IAAI,EAAE;AACtB;AACA;AACA;AACA,SAAS,CAAC;AACV,QAAQ,UAAU,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC;AACrD,UAAU,KAAK,EAAE;AACjB,SAAS,CAAC;AACV,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,aAAa,CAAC,MAAM,CAAC,0BAA0B,CAAC,CAAC;AAC9E,QAAQ,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;AACtC,UAAU,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9E;AACA,OAAO,CAAC,OAAO,UAAU,EAAE;AAC3B,QAAQ,OAAO,CAAC,IAAI,CAAC,8BAA8B,EAAE,UAAU,CAAC;AAChE,QAAQ,OAAO,IAAI,CAAC;AACpB,UAAU,OAAO,EAAE,IAAI;AACvB,UAAU,aAAa,EAAE,EAAE;AAC3B,UAAU,UAAU,EAAE;AACtB,YAAY,KAAK,EAAE,CAAC;AACpB,YAAY,KAAK;AACjB,YAAY;AACZ;AACA,SAAS,CAAC;AACV;AACA,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC;AACjE,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oCAAoC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnF;AACA,IAAI,MAAM,sBAAsB,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,YAAY,KAAK;AACvE,MAAM,IAAI,UAAU,GAAG,SAAS;AAChC,MAAM,IAAI,YAAY,CAAC,MAAM,EAAE;AAC/B,QAAQ,UAAU,GAAG,WAAW;AAChC,OAAO,MAAM,IAAI,YAAY,CAAC,MAAM,EAAE;AACtC,QAAQ,UAAU,GAAG,QAAQ;AAC7B;AACA,MAAM,OAAO;AACb,QAAQ,EAAE,EAAE,YAAY,CAAC,EAAE;AAC3B,QAAQ,KAAK,EAAE,YAAY,CAAC,KAAK;AACjC,QAAQ,OAAO,EAAE,YAAY,CAAC,OAAO;AACrC,QAAQ,GAAG,EAAE,YAAY,CAAC,GAAG;AAC7B,QAAQ,IAAI,EAAE,YAAY,CAAC,IAAI;AAC/B,QAAQ,MAAM,EAAE,YAAY,CAAC,MAAM;AACnC,QAAQ,MAAM,EAAE,YAAY,CAAC,SAAS;AACtC,QAAQ,MAAM,EAAE,YAAY,CAAC,IAAI,EAAE,KAAK,IAAI,IAAI,CAAC,KAAK;AACtD;AACA,QAAQ,UAAU;AAClB,QAAQ,QAAQ,EAAE,YAAY,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG;AAC9E,OAAO;AACP,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,aAAa,EAAE,sBAAsB;AAC3C,MAAM,UAAU,EAAE;AAClB,QAAQ,KAAK,EAAE,UAAU;AACzB,QAAQ,KAAK;AACb,QAAQ;AACR;AACA,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC;AAC/D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oCAAoC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjF;AACA;;;;"}