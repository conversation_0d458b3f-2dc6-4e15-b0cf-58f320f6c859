{"version": 3, "file": "_server.ts-Msm2EkAt.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/search/global/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { v as verifySessionToken } from \"../../../../../chunks/auth.js\";\nimport { s as searchUsers, a as searchJobs, b as searchDocuments } from \"../../../../../chunks/prisma-search-service.js\";\nconst POST = async ({ request, cookies }) => {\n  const token = cookies.get(\"auth_token\");\n  const user = token ? await verifySessionToken(token) : null;\n  const { query, limit = 10 } = await request.json();\n  if (!query || query.length < 2) {\n    return json({\n      users: { hits: [] },\n      jobs: { hits: [] },\n      documents: { hits: [] }\n    });\n  }\n  try {\n    const [usersResponse, jobsResponse, documentsResponse] = await Promise.all([\n      searchUsers(query, { limit }),\n      searchJobs(query, { limit }),\n      user ? searchDocuments(query, { limit, userId: user.id }) : { hits: [] }\n    ]);\n    return json({\n      users: usersResponse,\n      jobs: jobsResponse,\n      documents: documentsResponse\n    });\n  } catch (error) {\n    console.error(\"Global search error:\", error);\n    return json({ error: \"Failed to perform search\" }, { status: 500 });\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAGK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AAC7C,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,MAAM,IAAI,GAAG,KAAK,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC,GAAG,IAAI;AAC7D,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,GAAG,EAAE,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACpD,EAAE,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AAClC,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;AACzB,MAAM,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;AACxB,MAAM,SAAS,EAAE,EAAE,IAAI,EAAE,EAAE;AAC3B,KAAK,CAAC;AACN;AACA,EAAE,IAAI;AACN,IAAI,MAAM,CAAC,aAAa,EAAE,YAAY,EAAE,iBAAiB,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;AAC/E,MAAM,WAAW,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,CAAC;AACnC,MAAM,UAAU,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,CAAC;AAClC,MAAM,IAAI,GAAG,eAAe,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;AAC5E,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,KAAK,EAAE,aAAa;AAC1B,MAAM,IAAI,EAAE,YAAY;AACxB,MAAM,SAAS,EAAE;AACjB,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC;AAChD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,0BAA0B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACvE;AACA;;;;"}