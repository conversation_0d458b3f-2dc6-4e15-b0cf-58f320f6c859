{"version": 3, "file": "_server.ts-CINsiiXA.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/applications/_applicationId_/interviews/_interviewId_/questions/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../../../../chunks/prisma.js\";\nasync function GET({ params, locals }) {\n  if (!locals.user) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  const { applicationId, interviewId } = params;\n  try {\n    const application = await prisma.application.findUnique({\n      where: {\n        id: applicationId,\n        userId: locals.user.id\n      }\n    });\n    if (!application) {\n      return json({ error: \"Application not found\" }, { status: 404 });\n    }\n    const interviewStage = await prisma.interviewStage.findUnique({\n      where: {\n        id: interviewId,\n        applicationId\n      }\n    });\n    if (!interviewStage) {\n      return json({ error: \"Interview stage not found\" }, { status: 404 });\n    }\n    const questions = await prisma.interviewQuestion.findMany({\n      where: {\n        interviewStageId: interviewId\n      },\n      orderBy: {\n        createdAt: \"asc\"\n      }\n    });\n    return json({ questions });\n  } catch (error) {\n    console.error(\"Error fetching interview questions:\", error);\n    return json({ error: \"Failed to fetch interview questions\" }, { status: 500 });\n  }\n}\nasync function POST({ request, params, locals }) {\n  if (!locals.user) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  const { applicationId, interviewId } = params;\n  try {\n    const application = await prisma.application.findUnique({\n      where: {\n        id: applicationId,\n        userId: locals.user.id\n      }\n    });\n    if (!application) {\n      return json({ error: \"Application not found\" }, { status: 404 });\n    }\n    const interviewStage = await prisma.interviewStage.findUnique({\n      where: {\n        id: interviewId,\n        applicationId\n      }\n    });\n    if (!interviewStage) {\n      return json({ error: \"Interview stage not found\" }, { status: 404 });\n    }\n    const body = await request.json();\n    const { question, category, difficulty, userResponse, userConfidence, notes } = body;\n    if (!question || !category) {\n      return json({ error: \"Question and category are required\" }, { status: 400 });\n    }\n    const interviewQuestion = await prisma.interviewQuestion.create({\n      data: {\n        interviewStageId: interviewId,\n        question,\n        category,\n        difficulty,\n        userResponse,\n        userConfidence,\n        notes\n      }\n    });\n    return json({ interviewQuestion }, { status: 201 });\n  } catch (error) {\n    console.error(\"Error creating interview question:\", error);\n    return json({ error: \"Failed to create interview question\" }, { status: 500 });\n  }\n}\nexport {\n  GET,\n  POST\n};\n"], "names": [], "mappings": ";;;;AAEA,eAAe,GAAG,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;AACvC,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;AACpB,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,MAAM;AAC/C,EAAE,IAAI;AACN,IAAI,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;AAC5D,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE,aAAa;AACzB,QAAQ,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC;AAC5B;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,WAAW,EAAE;AACtB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtE;AACA,IAAI,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC;AAClE,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE,WAAW;AACvB,QAAQ;AACR;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,cAAc,EAAE;AACzB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,2BAA2B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1E;AACA,IAAI,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC;AAC9D,MAAM,KAAK,EAAE;AACb,QAAQ,gBAAgB,EAAE;AAC1B,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,SAAS,EAAE;AACnB;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,EAAE,SAAS,EAAE,CAAC;AAC9B,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC;AAC/D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,qCAAqC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClF;AACA;AACA,eAAe,IAAI,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;AACjD,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;AACpB,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,MAAM;AAC/C,EAAE,IAAI;AACN,IAAI,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;AAC5D,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE,aAAa;AACzB,QAAQ,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC;AAC5B;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,WAAW,EAAE;AACtB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtE;AACA,IAAI,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC;AAClE,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE,WAAW;AACvB,QAAQ;AACR;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,cAAc,EAAE;AACzB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,2BAA2B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1E;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,cAAc,EAAE,KAAK,EAAE,GAAG,IAAI;AACxF,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,EAAE;AAChC,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oCAAoC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnF;AACA,IAAI,MAAM,iBAAiB,GAAG,MAAM,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC;AACpE,MAAM,IAAI,EAAE;AACZ,QAAQ,gBAAgB,EAAE,WAAW;AACrC,QAAQ,QAAQ;AAChB,QAAQ,QAAQ;AAChB,QAAQ,UAAU;AAClB,QAAQ,YAAY;AACpB,QAAQ,cAAc;AACtB,QAAQ;AACR;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,EAAE,iBAAiB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACvD,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC;AAC9D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,qCAAqC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClF;AACA;;;;"}