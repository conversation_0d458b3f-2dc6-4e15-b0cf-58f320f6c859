{"version": 3, "file": "_server.ts-DL927elr.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/_server.ts.js"], "sourcesContent": ["function GET() {\n  console.log(\"Root endpoint accessed at:\", (/* @__PURE__ */ new Date()).toISOString());\n  return new Response(`\n    <!DOCTYPE html>\n    <html>\n      <head>\n        <title>Auto Apply API</title>\n      </head>\n      <body>\n        <h1>Auto Apply API</h1>\n        <p>Server is running. Visit <a href=\"/health\">/health</a> for health check.</p>\n        <p>Current time: ${(/* @__PURE__ */ new Date()).toISOString()}</p>\n      </body>\n    </html>\n  `, {\n    status: 200,\n    headers: {\n      \"Content-Type\": \"text/html\"\n    }\n  });\n}\nexport {\n  GET\n};\n"], "names": [], "mappings": "AAAA,SAAS,GAAG,GAAG;AACf,EAAE,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW,EAAE,CAAC;AACvF,EAAE,OAAO,IAAI,QAAQ,CAAC;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW,EAAE,CAAC;AACtE;AACA;AACA,EAAE,CAAC,EAAE;AACL,IAAI,MAAM,EAAE,GAAG;AACf,IAAI,OAAO,EAAE;AACb,MAAM,cAAc,EAAE;AACtB;AACA,GAAG,CAAC;AACJ;;;;"}