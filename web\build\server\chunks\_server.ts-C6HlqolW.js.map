{"version": 3, "file": "_server.ts-C6HlqolW.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/upgrade/_server.ts.js"], "sourcesContent": ["import { v as verifySessionToken, c as createSessionToken } from \"../../../../chunks/auth.js\";\nconst POST = async ({ request, cookies }) => {\n  const body = await request.json();\n  const { role } = body;\n  const token = cookies.get(\"auth_token\");\n  if (!token) return new Response(\"Unauthorized\", { status: 401 });\n  const user = await verifySessionToken(token);\n  if (!user || !user.id) return new Response(\"Unauthorized\", { status: 401 });\n  const newToken = await createSessionToken({\n    email: user.email,\n    name: user.name || \"\",\n    picture: user.image || \"\",\n    role,\n    id: user.id\n  });\n  cookies.set(\"auth_token\", newToken, {\n    path: \"/\",\n    httpOnly: true,\n    sameSite: \"lax\",\n    secure: process.env.NODE_ENV === \"production\",\n    maxAge: 60 * 60 * 24 * 7\n  });\n  return new Response(JSON.stringify({ ok: true }), {\n    headers: { \"Content-Type\": \"application/json\" }\n  });\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;AACK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AAC7C,EAAE,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACnC,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI;AACvB,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE,EAAE,MAAM,IAAI,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AAC9C,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7E,EAAE,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC;AAC5C,IAAI,KAAK,EAAE,IAAI,CAAC,KAAK;AACrB,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,EAAE;AACzB,IAAI,OAAO,EAAE,IAAI,CAAC,KAAK,IAAI,EAAE;AAC7B,IAAI,IAAI;AACR,IAAI,EAAE,EAAE,IAAI,CAAC;AACb,GAAG,CAAC;AACJ,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,QAAQ,EAAE;AACtC,IAAI,IAAI,EAAE,GAAG;AACb,IAAI,QAAQ,EAAE,IAAI;AAClB,IAAI,QAAQ,EAAE,KAAK;AACnB,IAAI,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;AACjD,IAAI,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;AAC3B,GAAG,CAAC;AACJ,EAAE,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE;AACpD,IAAI,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB;AACjD,GAAG,CAAC;AACJ;;;;"}