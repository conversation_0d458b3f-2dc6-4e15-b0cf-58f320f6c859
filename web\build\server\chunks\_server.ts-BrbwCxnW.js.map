{"version": 3, "file": "_server.ts-BrbwCxnW.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/admin/feature-usage/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../../../chunks/auth.js\";\nimport { featureTablesExist } from \"../../../../../chunks/feature-usage.js\";\nconst GET = async ({ cookies, url }) => {\n  const token = cookies.get(\"auth_token\");\n  if (!token) return new Response(\"Unauthorized\", { status: 401 });\n  const userData = await verifySessionToken(token);\n  if (!userData?.id) return new Response(\"Unauthorized\", { status: 401 });\n  const user = await prisma.user.findUnique({ where: { id: userData.id } });\n  if (!user || user.role !== \"admin\") {\n    return new Response(\"Unauthorized\", { status: 401 });\n  }\n  try {\n    const tablesExist = await featureTablesExist();\n    console.log(\"Feature tables exist:\", tablesExist);\n    if (!tablesExist) {\n      return json([]);\n    }\n    const featureId = url.searchParams.get(\"featureId\");\n    const limitId = url.searchParams.get(\"limitId\");\n    const period = url.searchParams.get(\"period\");\n    const userId = url.searchParams.get(\"userId\");\n    const planId = url.searchParams.get(\"planId\");\n    const page = parseInt(url.searchParams.get(\"page\") || \"1\");\n    const limit = parseInt(url.searchParams.get(\"limit\") || \"50\");\n    const skip = (page - 1) * limit;\n    const query = {\n      where: {},\n      include: {\n        user: {\n          select: {\n            id: true,\n            email: true,\n            firstName: true,\n            lastName: true,\n            role: true,\n            subscriptions: {\n              orderBy: { createdAt: \"desc\" },\n              take: 1,\n              include: {\n                plan: true\n              }\n            }\n          }\n        },\n        feature: true,\n        limit: true\n      },\n      orderBy: {\n        updatedAt: \"desc\"\n      },\n      skip,\n      take: limit\n    };\n    if (featureId) query.where.featureId = featureId;\n    if (limitId) query.where.limitId = limitId;\n    if (period) query.where.period = period;\n    if (userId) query.where.userId = userId;\n    if (planId) {\n      query.where.user = {\n        subscriptions: {\n          some: {\n            planId\n          }\n        }\n      };\n    }\n    const [featureUsage, total] = await Promise.all([\n      prisma.featureUsage.findMany(query),\n      prisma.featureUsage.count({ where: query.where })\n    ]);\n    const formattedUsage = featureUsage.map((usage) => {\n      const currentPlan = usage.user.subscriptions[0]?.plan;\n      return {\n        id: usage.id,\n        userId: usage.userId,\n        user: {\n          id: usage.user.id,\n          email: usage.user.email,\n          firstName: usage.user.firstName,\n          lastName: usage.user.lastName,\n          role: usage.user.role,\n          plan: currentPlan ? {\n            id: currentPlan.id,\n            name: currentPlan.name\n          } : null\n        },\n        featureId: usage.featureId,\n        featureName: usage.feature.name,\n        limitId: usage.limitId,\n        limitName: usage.limit.name,\n        used: usage.used,\n        period: usage.period,\n        updatedAt: usage.updatedAt\n      };\n    });\n    return json(formattedUsage);\n  } catch (error) {\n    console.error(\"Error getting feature usage:\", error);\n    return json({ error: \"Failed to get feature usage\" }, { status: 500 });\n  }\n};\nconst POST = async ({ cookies, url }) => {\n  const token = cookies.get(\"auth_token\");\n  if (!token) return new Response(\"Unauthorized\", { status: 401 });\n  const userData = await verifySessionToken(token);\n  if (!userData?.id) return new Response(\"Unauthorized\", { status: 401 });\n  const user = await prisma.user.findUnique({ where: { id: userData.id } });\n  if (!user || user.role !== \"admin\") {\n    return new Response(\"Unauthorized\", { status: 401 });\n  }\n  try {\n    const tablesExist = await featureTablesExist();\n    console.log(\"Feature tables exist (summary):\", tablesExist);\n    if (!tablesExist) {\n      return json([]);\n    }\n    const groupBy = url.searchParams.get(\"groupBy\") || \"feature\";\n    const period = url.searchParams.get(\"period\");\n    const query = {\n      where: {}\n    };\n    if (period) query.where.period = period;\n    const featureUsage = await prisma.featureUsage.findMany({\n      ...query,\n      include: {\n        feature: true,\n        limit: true\n      }\n    });\n    const groupedUsage = featureUsage.reduce((acc, usage) => {\n      const key = groupBy === \"feature\" ? usage.featureId : usage.limitId;\n      const name = groupBy === \"feature\" ? usage.feature.name : usage.limit.name;\n      if (!acc[key]) {\n        acc[key] = {\n          id: key,\n          name,\n          totalUsed: 0,\n          userCount: /* @__PURE__ */ new Set(),\n          periods: {}\n        };\n      }\n      acc[key].totalUsed += usage.used;\n      acc[key].userCount.add(usage.userId);\n      if (usage.period) {\n        if (!acc[key].periods[usage.period]) {\n          acc[key].periods[usage.period] = 0;\n        }\n        acc[key].periods[usage.period] += usage.used;\n      }\n      return acc;\n    }, {});\n    const summary = Object.values(groupedUsage).map((item) => ({\n      id: item.id,\n      name: item.name,\n      totalUsed: item.totalUsed,\n      userCount: item.userCount.size,\n      periods: Object.entries(item.periods).map(([period2, used]) => ({\n        period: period2,\n        used\n      }))\n    }));\n    return json(summary);\n  } catch (error) {\n    console.error(\"Error getting feature usage summary:\", error);\n    return json({ error: \"Failed to get feature usage summary\" }, { status: 500 });\n  }\n};\nconst PUT = async ({ cookies, url }) => {\n  const token = cookies.get(\"auth_token\");\n  if (!token) return new Response(\"Unauthorized\", { status: 401 });\n  const userData = await verifySessionToken(token);\n  if (!userData?.id) return new Response(\"Unauthorized\", { status: 401 });\n  const user = await prisma.user.findUnique({ where: { id: userData.id } });\n  if (!user || user.role !== \"admin\") {\n    return new Response(\"Unauthorized\", { status: 401 });\n  }\n  try {\n    const tablesExist = await featureTablesExist();\n    if (!tablesExist) {\n      return json([]);\n    }\n    const featureId = url.searchParams.get(\"featureId\");\n    const limitId = url.searchParams.get(\"limitId\");\n    const period = url.searchParams.get(\"period\");\n    const userId = url.searchParams.get(\"userId\");\n    const planId = url.searchParams.get(\"planId\");\n    const format = url.searchParams.get(\"format\") || \"csv\";\n    const query = {\n      where: {},\n      include: {\n        user: {\n          select: {\n            id: true,\n            email: true,\n            firstName: true,\n            lastName: true\n          }\n        },\n        feature: true,\n        limit: true\n      },\n      orderBy: {\n        updatedAt: \"desc\"\n      }\n    };\n    if (featureId) query.where.featureId = featureId;\n    if (limitId) query.where.limitId = limitId;\n    if (period) query.where.period = period;\n    if (userId) query.where.userId = userId;\n    if (planId) {\n      query.where.user = {\n        subscriptions: {\n          some: {\n            planId\n          }\n        }\n      };\n    }\n    const featureUsage = await prisma.featureUsage.findMany(query);\n    const formattedData = featureUsage.map((usage) => ({\n      id: usage.id,\n      userId: usage.userId,\n      userEmail: usage.user.email,\n      userName: `${usage.user.firstName || \"\"} ${usage.user.lastName || \"\"}`.trim(),\n      featureId: usage.featureId,\n      featureName: usage.feature.name,\n      limitId: usage.limitId,\n      limitName: usage.limit.name,\n      used: usage.used,\n      period: usage.period,\n      updatedAt: usage.updatedAt\n    }));\n    if (format === \"json\") {\n      return json(formattedData);\n    } else {\n      const headers = [\n        \"ID\",\n        \"User ID\",\n        \"User Email\",\n        \"User Name\",\n        \"Feature ID\",\n        \"Feature Name\",\n        \"Limit ID\",\n        \"Limit Name\",\n        \"Used\",\n        \"Period\",\n        \"Updated At\"\n      ];\n      const rows = formattedData.map((item) => [\n        item.id,\n        item.userId,\n        item.userEmail,\n        item.userName,\n        item.featureId,\n        item.featureName,\n        item.limitId,\n        item.limitName,\n        item.used,\n        item.period,\n        item.updatedAt\n      ]);\n      const csv = [\n        headers.join(\",\"),\n        ...rows.map((row) => row.map((cell) => `\"${cell}\"`).join(\",\"))\n      ].join(\"\\n\");\n      return new Response(csv, {\n        headers: {\n          \"Content-Type\": \"text/csv\",\n          \"Content-Disposition\": 'attachment; filename=\"feature-usage.csv\"'\n        }\n      });\n    }\n  } catch (error) {\n    console.error(\"Error exporting feature usage:\", error);\n    return json({ error: \"Failed to export feature usage\" }, { status: 500 });\n  }\n};\nexport {\n  GET,\n  POST,\n  PUT\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAIK,MAAC,GAAG,GAAG,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK;AACxC,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE,EAAE,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AAClD,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE,EAAE,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE,EAAE,CAAC;AAC3E,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;AACtC,IAAI,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxD;AACA,EAAE,IAAI;AACN,IAAI,MAAM,WAAW,GAAG,MAAM,kBAAkB,EAAE;AAClD,IAAI,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,WAAW,CAAC;AACrD,IAAI,IAAI,CAAC,WAAW,EAAE;AACtB,MAAM,OAAO,IAAI,CAAC,EAAE,CAAC;AACrB;AACA,IAAI,MAAM,SAAS,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC;AACvD,IAAI,MAAM,OAAO,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC;AACnD,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC;AACjD,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC;AACjD,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC;AACjD,IAAI,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC;AAC9D,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC;AACjE,IAAI,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK;AACnC,IAAI,MAAM,KAAK,GAAG;AAClB,MAAM,KAAK,EAAE,EAAE;AACf,MAAM,OAAO,EAAE;AACf,QAAQ,IAAI,EAAE;AACd,UAAU,MAAM,EAAE;AAClB,YAAY,EAAE,EAAE,IAAI;AACpB,YAAY,KAAK,EAAE,IAAI;AACvB,YAAY,SAAS,EAAE,IAAI;AAC3B,YAAY,QAAQ,EAAE,IAAI;AAC1B,YAAY,IAAI,EAAE,IAAI;AACtB,YAAY,aAAa,EAAE;AAC3B,cAAc,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;AAC5C,cAAc,IAAI,EAAE,CAAC;AACrB,cAAc,OAAO,EAAE;AACvB,gBAAgB,IAAI,EAAE;AACtB;AACA;AACA;AACA,SAAS;AACT,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,KAAK,EAAE;AACf,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,SAAS,EAAE;AACnB,OAAO;AACP,MAAM,IAAI;AACV,MAAM,IAAI,EAAE;AACZ,KAAK;AACL,IAAI,IAAI,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,SAAS,GAAG,SAAS;AACpD,IAAI,IAAI,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO;AAC9C,IAAI,IAAI,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM;AAC3C,IAAI,IAAI,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM;AAC3C,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG;AACzB,QAAQ,aAAa,EAAE;AACvB,UAAU,IAAI,EAAE;AAChB,YAAY;AACZ;AACA;AACA,OAAO;AACP;AACA,IAAI,MAAM,CAAC,YAAY,EAAE,KAAK,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;AACpD,MAAM,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC;AACzC,MAAM,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE;AACtD,KAAK,CAAC;AACN,IAAI,MAAM,cAAc,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK;AACvD,MAAM,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI;AAC3D,MAAM,OAAO;AACb,QAAQ,EAAE,EAAE,KAAK,CAAC,EAAE;AACpB,QAAQ,MAAM,EAAE,KAAK,CAAC,MAAM;AAC5B,QAAQ,IAAI,EAAE;AACd,UAAU,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE;AAC3B,UAAU,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK;AACjC,UAAU,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,SAAS;AACzC,UAAU,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,QAAQ;AACvC,UAAU,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI;AAC/B,UAAU,IAAI,EAAE,WAAW,GAAG;AAC9B,YAAY,EAAE,EAAE,WAAW,CAAC,EAAE;AAC9B,YAAY,IAAI,EAAE,WAAW,CAAC;AAC9B,WAAW,GAAG;AACd,SAAS;AACT,QAAQ,SAAS,EAAE,KAAK,CAAC,SAAS;AAClC,QAAQ,WAAW,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI;AACvC,QAAQ,OAAO,EAAE,KAAK,CAAC,OAAO;AAC9B,QAAQ,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI;AACnC,QAAQ,IAAI,EAAE,KAAK,CAAC,IAAI;AACxB,QAAQ,MAAM,EAAE,KAAK,CAAC,MAAM;AAC5B,QAAQ,SAAS,EAAE,KAAK,CAAC;AACzB,OAAO;AACP,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,cAAc,CAAC;AAC/B,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC;AACxD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,6BAA6B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1E;AACA;AACK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK;AACzC,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE,EAAE,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AAClD,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE,EAAE,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE,EAAE,CAAC;AAC3E,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;AACtC,IAAI,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxD;AACA,EAAE,IAAI;AACN,IAAI,MAAM,WAAW,GAAG,MAAM,kBAAkB,EAAE;AAClD,IAAI,OAAO,CAAC,GAAG,CAAC,iCAAiC,EAAE,WAAW,CAAC;AAC/D,IAAI,IAAI,CAAC,WAAW,EAAE;AACtB,MAAM,OAAO,IAAI,CAAC,EAAE,CAAC;AACrB;AACA,IAAI,MAAM,OAAO,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,SAAS;AAChE,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC;AACjD,IAAI,MAAM,KAAK,GAAG;AAClB,MAAM,KAAK,EAAE;AACb,KAAK;AACL,IAAI,IAAI,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM;AAC3C,IAAI,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;AAC5D,MAAM,GAAG,KAAK;AACd,MAAM,OAAO,EAAE;AACf,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,KAAK,EAAE;AACf;AACA,KAAK,CAAC;AACN,IAAI,MAAM,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,KAAK;AAC7D,MAAM,MAAM,GAAG,GAAG,OAAO,KAAK,SAAS,GAAG,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,OAAO;AACzE,MAAM,MAAM,IAAI,GAAG,OAAO,KAAK,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI;AAChF,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AACrB,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG;AACnB,UAAU,EAAE,EAAE,GAAG;AACjB,UAAU,IAAI;AACd,UAAU,SAAS,EAAE,CAAC;AACtB,UAAU,SAAS,kBAAkB,IAAI,GAAG,EAAE;AAC9C,UAAU,OAAO,EAAE;AACnB,SAAS;AACT;AACA,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,SAAS,IAAI,KAAK,CAAC,IAAI;AACtC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC;AAC1C,MAAM,IAAI,KAAK,CAAC,MAAM,EAAE;AACxB,QAAQ,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;AAC7C,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC;AAC5C;AACA,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,IAAI;AACpD;AACA,MAAM,OAAO,GAAG;AAChB,KAAK,EAAE,EAAE,CAAC;AACV,IAAI,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,MAAM;AAC/D,MAAM,EAAE,EAAE,IAAI,CAAC,EAAE;AACjB,MAAM,IAAI,EAAE,IAAI,CAAC,IAAI;AACrB,MAAM,SAAS,EAAE,IAAI,CAAC,SAAS;AAC/B,MAAM,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI;AACpC,MAAM,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM;AACtE,QAAQ,MAAM,EAAE,OAAO;AACvB,QAAQ;AACR,OAAO,CAAC;AACR,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC;AACxB,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC;AAChE,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,qCAAqC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClF;AACA;AACK,MAAC,GAAG,GAAG,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK;AACxC,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE,EAAE,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AAClD,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE,EAAE,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE,EAAE,CAAC;AAC3E,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;AACtC,IAAI,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxD;AACA,EAAE,IAAI;AACN,IAAI,MAAM,WAAW,GAAG,MAAM,kBAAkB,EAAE;AAClD,IAAI,IAAI,CAAC,WAAW,EAAE;AACtB,MAAM,OAAO,IAAI,CAAC,EAAE,CAAC;AACrB;AACA,IAAI,MAAM,SAAS,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC;AACvD,IAAI,MAAM,OAAO,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC;AACnD,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC;AACjD,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC;AACjD,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC;AACjD,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,KAAK;AAC1D,IAAI,MAAM,KAAK,GAAG;AAClB,MAAM,KAAK,EAAE,EAAE;AACf,MAAM,OAAO,EAAE;AACf,QAAQ,IAAI,EAAE;AACd,UAAU,MAAM,EAAE;AAClB,YAAY,EAAE,EAAE,IAAI;AACpB,YAAY,KAAK,EAAE,IAAI;AACvB,YAAY,SAAS,EAAE,IAAI;AAC3B,YAAY,QAAQ,EAAE;AACtB;AACA,SAAS;AACT,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,KAAK,EAAE;AACf,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,SAAS,EAAE;AACnB;AACA,KAAK;AACL,IAAI,IAAI,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,SAAS,GAAG,SAAS;AACpD,IAAI,IAAI,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO;AAC9C,IAAI,IAAI,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM;AAC3C,IAAI,IAAI,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM;AAC3C,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG;AACzB,QAAQ,aAAa,EAAE;AACvB,UAAU,IAAI,EAAE;AAChB,YAAY;AACZ;AACA;AACA,OAAO;AACP;AACA,IAAI,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC;AAClE,IAAI,MAAM,aAAa,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK,MAAM;AACvD,MAAM,EAAE,EAAE,KAAK,CAAC,EAAE;AAClB,MAAM,MAAM,EAAE,KAAK,CAAC,MAAM;AAC1B,MAAM,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK;AACjC,MAAM,QAAQ,EAAE,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE;AACnF,MAAM,SAAS,EAAE,KAAK,CAAC,SAAS;AAChC,MAAM,WAAW,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI;AACrC,MAAM,OAAO,EAAE,KAAK,CAAC,OAAO;AAC5B,MAAM,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI;AACjC,MAAM,IAAI,EAAE,KAAK,CAAC,IAAI;AACtB,MAAM,MAAM,EAAE,KAAK,CAAC,MAAM;AAC1B,MAAM,SAAS,EAAE,KAAK,CAAC;AACvB,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,MAAM,KAAK,MAAM,EAAE;AAC3B,MAAM,OAAO,IAAI,CAAC,aAAa,CAAC;AAChC,KAAK,MAAM;AACX,MAAM,MAAM,OAAO,GAAG;AACtB,QAAQ,IAAI;AACZ,QAAQ,SAAS;AACjB,QAAQ,YAAY;AACpB,QAAQ,WAAW;AACnB,QAAQ,YAAY;AACpB,QAAQ,cAAc;AACtB,QAAQ,UAAU;AAClB,QAAQ,YAAY;AACpB,QAAQ,MAAM;AACd,QAAQ,QAAQ;AAChB,QAAQ;AACR,OAAO;AACP,MAAM,MAAM,IAAI,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK;AAC/C,QAAQ,IAAI,CAAC,EAAE;AACf,QAAQ,IAAI,CAAC,MAAM;AACnB,QAAQ,IAAI,CAAC,SAAS;AACtB,QAAQ,IAAI,CAAC,QAAQ;AACrB,QAAQ,IAAI,CAAC,SAAS;AACtB,QAAQ,IAAI,CAAC,WAAW;AACxB,QAAQ,IAAI,CAAC,OAAO;AACpB,QAAQ,IAAI,CAAC,SAAS;AACtB,QAAQ,IAAI,CAAC,IAAI;AACjB,QAAQ,IAAI,CAAC,MAAM;AACnB,QAAQ,IAAI,CAAC;AACb,OAAO,CAAC;AACR,MAAM,MAAM,GAAG,GAAG;AAClB,QAAQ,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC;AACzB,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;AACrE,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;AAClB,MAAM,OAAO,IAAI,QAAQ,CAAC,GAAG,EAAE;AAC/B,QAAQ,OAAO,EAAE;AACjB,UAAU,cAAc,EAAE,UAAU;AACpC,UAAU,qBAAqB,EAAE;AACjC;AACA,OAAO,CAAC;AACR;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC;AAC1D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,gCAAgC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7E;AACA;;;;"}