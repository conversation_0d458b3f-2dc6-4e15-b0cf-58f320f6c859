{"version": 3, "file": "_server.ts-zWKEIwip.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/automation/runs/_id_/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../../chunks/prisma.js\";\nimport { g as getUserFromToken } from \"../../../../../../chunks/auth.js\";\nconst GET = async ({ params, cookies }) => {\n  const user = getUserFromToken(cookies);\n  const { id } = params;\n  if (!user) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  try {\n    const automationRun = await prisma.automationRun.findFirst({\n      where: {\n        id,\n        OR: [\n          { userId: user.id },\n          {\n            profile: {\n              team: {\n                members: {\n                  some: { userId: user.id }\n                }\n              }\n            }\n          }\n        ]\n      },\n      include: {\n        profile: {\n          include: {\n            data: true,\n            resumes: {\n              include: {\n                document: true\n              }\n            }\n          }\n        },\n        jobs: {\n          orderBy: {\n            createdAt: \"desc\"\n          }\n        }\n      }\n    });\n    if (!automationRun) {\n      return json({ error: \"Automation run not found\" }, { status: 404 });\n    }\n    return json(automationRun);\n  } catch (error) {\n    console.error(\"Error fetching automation run:\", error);\n    return json({ error: \"Failed to fetch automation run\" }, { status: 500 });\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;;;;;;AAGK,MAAC,GAAG,GAAG,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK;AAC3C,EAAE,MAAM,IAAI,GAAG,gBAAgB,CAAC,OAAO,CAAC;AACxC,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;AACvB,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC;AAC/D,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE;AACV,QAAQ,EAAE,EAAE;AACZ,UAAU,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;AAC7B,UAAU;AACV,YAAY,OAAO,EAAE;AACrB,cAAc,IAAI,EAAE;AACpB,gBAAgB,OAAO,EAAE;AACzB,kBAAkB,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE;AACzC;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,OAAO,EAAE;AACjB,UAAU,OAAO,EAAE;AACnB,YAAY,IAAI,EAAE,IAAI;AACtB,YAAY,OAAO,EAAE;AACrB,cAAc,OAAO,EAAE;AACvB,gBAAgB,QAAQ,EAAE;AAC1B;AACA;AACA;AACA,SAAS;AACT,QAAQ,IAAI,EAAE;AACd,UAAU,OAAO,EAAE;AACnB,YAAY,SAAS,EAAE;AACvB;AACA;AACA;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,aAAa,EAAE;AACxB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,0BAA0B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE;AACA,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC;AAC9B,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC;AAC1D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,gCAAgC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7E;AACA;;;;"}