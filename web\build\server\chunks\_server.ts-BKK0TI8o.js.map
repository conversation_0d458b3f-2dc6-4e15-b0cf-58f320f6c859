{"version": 3, "file": "_server.ts-BKK0TI8o.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/admin/plans/sync-stripe/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../../../../chunks/auth.js\";\nimport { a as syncPlanWithStripe } from \"../../../../../../chunks/stripe.js\";\nconst POST = async ({ cookies, request }) => {\n  const token = cookies.get(\"auth_token\");\n  if (!token) return json({ error: \"Unauthorized\", details: \"No token provided\" }, { status: 401 });\n  console.log(\"Verifying token:\", token ? token.substring(0, 10) + \"...\" : \"no token\");\n  const userData = await verifySessionToken(token);\n  console.log(\n    \"User data from token:\",\n    userData ? { id: userData.id, email: userData.email, role: userData.role, isAdmin: userData.isAdmin } : \"null\"\n  );\n  if (!userData?.id) {\n    console.log(\"Authentication failed: Invalid or expired token\");\n    return json({ error: \"Unauthorized\", details: \"Invalid token\" }, { status: 401 });\n  }\n  console.log(\"Checking admin status for user ID:\", userData.id);\n  const user = await prisma.user.findUnique({\n    where: { id: userData.id },\n    select: { isAdmin: true, role: true, email: true }\n  });\n  console.log(\"User database record:\", user);\n  if (!user) {\n    console.log(\"User not found in database\");\n    return json({ error: \"Unauthorized\", details: \"User not found\" }, { status: 401 });\n  }\n  if (!user.isAdmin && user.role !== \"admin\") {\n    console.log(\"User is not an admin:\", {\n      userId: userData.id,\n      email: user.email,\n      isAdmin: user.isAdmin,\n      role: user.role\n    });\n    return json({ error: \"Unauthorized\", details: \"User is not an admin\" }, { status: 401 });\n  }\n  console.log(\"Admin check passed for user:\", {\n    userId: userData.id,\n    email: user.email,\n    isAdmin: user.isAdmin,\n    role: user.role\n  });\n  try {\n    const data = await request.json();\n    const plan = data.planId ? await prisma.plan.findUnique({\n      where: { id: data.planId },\n      include: {\n        features: {\n          include: {\n            limits: true\n          }\n        }\n      }\n    }) : data.plan;\n    if (!plan || !plan.id) {\n      return json({ error: \"Invalid plan data\" }, { status: 400 });\n    }\n    const updatedPlan = await syncPlanWithStripe(plan);\n    await prisma.plan.update({\n      where: { id: plan.id },\n      data: {\n        stripePriceMonthlyId: updatedPlan.stripePriceMonthlyId,\n        stripePriceYearlyId: updatedPlan.stripePriceYearlyId\n      }\n    });\n    return json({\n      success: true,\n      plan: updatedPlan,\n      message: `Plan \"${plan.name}\" successfully synced with Stripe`\n    });\n  } catch (error) {\n    console.error(\"Error syncing plan with Stripe:\", error);\n    return json(\n      {\n        success: false,\n        error: error.message\n      },\n      { status: 500 }\n    );\n  }\n};\nconst PUT = async ({ cookies }) => {\n  const token = cookies.get(\"auth_token\");\n  if (!token) return json({ error: \"Unauthorized\", details: \"No token provided\" }, { status: 401 });\n  console.log(\n    \"Verifying token for PUT request:\",\n    token ? token.substring(0, 10) + \"...\" : \"no token\"\n  );\n  const userData = await verifySessionToken(token);\n  console.log(\n    \"User data from token (PUT):\",\n    userData ? { id: userData.id, email: userData.email, role: userData.role, isAdmin: userData.isAdmin } : \"null\"\n  );\n  if (!userData?.id) {\n    console.log(\"Authentication failed: Invalid or expired token (PUT)\");\n    return json({ error: \"Unauthorized\", details: \"Invalid token\" }, { status: 401 });\n  }\n  const user = await prisma.user.findUnique({\n    where: { id: userData.id },\n    select: { isAdmin: true, role: true }\n  });\n  if (!user) {\n    return json({ error: \"Unauthorized\", details: \"User not found\" }, { status: 401 });\n  }\n  if (!user.isAdmin && user.role !== \"admin\") {\n    console.log(\"User is not an admin:\", {\n      userId: userData.id,\n      isAdmin: user.isAdmin,\n      role: user.role\n    });\n    return json({ error: \"Unauthorized\", details: \"User is not an admin\" }, { status: 401 });\n  }\n  try {\n    const plans = await prisma.plan.findMany({\n      where: {\n        // Skip free plans (price = 0)\n        OR: [{ monthlyPrice: { gt: 0 } }, { annualPrice: { gt: 0 } }]\n      },\n      include: {\n        features: {\n          include: {\n            limits: true\n          }\n        }\n      }\n    });\n    const planTiers = plans.map((plan) => ({\n      id: plan.id,\n      name: plan.name,\n      description: plan.description,\n      section: plan.section,\n      monthlyPrice: plan.monthlyPrice,\n      annualPrice: plan.annualPrice,\n      stripePriceMonthlyId: plan.stripePriceMonthlyId || void 0,\n      stripePriceYearlyId: plan.stripePriceYearlyId || void 0,\n      popular: plan.popular,\n      features: plan.features.map((feature) => ({\n        featureId: feature.featureId,\n        accessLevel: feature.accessLevel,\n        limits: feature.limits.map((limit) => ({\n          limitId: limit.limitId,\n          value: limit.value\n        }))\n      }))\n    }));\n    const results = await Promise.all(\n      planTiers.map(async (plan) => {\n        try {\n          const updatedPlan = await syncPlanWithStripe(plan);\n          await prisma.plan.update({\n            where: { id: plan.id },\n            data: {\n              stripePriceMonthlyId: updatedPlan.stripePriceMonthlyId,\n              stripePriceYearlyId: updatedPlan.stripePriceYearlyId\n            }\n          });\n          return {\n            id: plan.id,\n            name: plan.name,\n            success: true,\n            stripePriceMonthlyId: updatedPlan.stripePriceMonthlyId,\n            stripePriceYearlyId: updatedPlan.stripePriceYearlyId\n          };\n        } catch (error) {\n          return {\n            id: plan.id,\n            name: plan.name,\n            success: false,\n            error: error.message\n          };\n        }\n      })\n    );\n    return json({\n      success: true,\n      results,\n      message: `${results.filter((r) => r.success).length} of ${results.length} plans successfully synced with Stripe`\n    });\n  } catch (error) {\n    console.error(\"Error syncing plans with Stripe:\", error);\n    return json(\n      {\n        success: false,\n        error: error.message\n      },\n      { status: 500 }\n    );\n  }\n};\nexport {\n  POST,\n  PUT\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAIK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AAC7C,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,OAAO,EAAE,mBAAmB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnG,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAG,UAAU,CAAC;AACtF,EAAE,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AAClD,EAAE,OAAO,CAAC,GAAG;AACb,IAAI,uBAAuB;AAC3B,IAAI,QAAQ,GAAG,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,CAAC,OAAO,EAAE,GAAG;AAC5G,GAAG;AACH,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE;AACrB,IAAI,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC;AAClE,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACrF;AACA,EAAE,OAAO,CAAC,GAAG,CAAC,oCAAoC,EAAE,QAAQ,CAAC,EAAE,CAAC;AAChE,EAAE,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAC5C,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE;AAC9B,IAAI,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI;AACpD,GAAG,CAAC;AACJ,EAAE,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC;AAC5C,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC;AAC7C,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,OAAO,EAAE,gBAAgB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtF;AACA,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;AAC9C,IAAI,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE;AACzC,MAAM,MAAM,EAAE,QAAQ,CAAC,EAAE;AACzB,MAAM,KAAK,EAAE,IAAI,CAAC,KAAK;AACvB,MAAM,OAAO,EAAE,IAAI,CAAC,OAAO;AAC3B,MAAM,IAAI,EAAE,IAAI,CAAC;AACjB,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,OAAO,EAAE,sBAAsB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC5F;AACA,EAAE,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE;AAC9C,IAAI,MAAM,EAAE,QAAQ,CAAC,EAAE;AACvB,IAAI,KAAK,EAAE,IAAI,CAAC,KAAK;AACrB,IAAI,OAAO,EAAE,IAAI,CAAC,OAAO;AACzB,IAAI,IAAI,EAAE,IAAI,CAAC;AACf,GAAG,CAAC;AACJ,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAC5D,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE;AAChC,MAAM,OAAO,EAAE;AACf,QAAQ,QAAQ,EAAE;AAClB,UAAU,OAAO,EAAE;AACnB,YAAY,MAAM,EAAE;AACpB;AACA;AACA;AACA,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI;AAClB,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;AAC3B,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE;AACA,IAAI,MAAM,WAAW,GAAG,MAAM,kBAAkB,CAAC,IAAI,CAAC;AACtD,IAAI,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AAC7B,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;AAC5B,MAAM,IAAI,EAAE;AACZ,QAAQ,oBAAoB,EAAE,WAAW,CAAC,oBAAoB;AAC9D,QAAQ,mBAAmB,EAAE,WAAW,CAAC;AACzC;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,IAAI,EAAE,WAAW;AACvB,MAAM,OAAO,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,iCAAiC;AACnE,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC;AAC3D,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,KAAK,EAAE,KAAK,CAAC;AACrB,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;AACK,MAAC,GAAG,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK;AACnC,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,OAAO,EAAE,mBAAmB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnG,EAAE,OAAO,CAAC,GAAG;AACb,IAAI,kCAAkC;AACtC,IAAI,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAG;AAC7C,GAAG;AACH,EAAE,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AAClD,EAAE,OAAO,CAAC,GAAG;AACb,IAAI,6BAA6B;AACjC,IAAI,QAAQ,GAAG,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,CAAC,OAAO,EAAE,GAAG;AAC5G,GAAG;AACH,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE;AACrB,IAAI,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC;AACxE,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACrF;AACA,EAAE,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAC5C,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE;AAC9B,IAAI,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AACvC,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,OAAO,EAAE,gBAAgB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtF;AACA,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;AAC9C,IAAI,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE;AACzC,MAAM,MAAM,EAAE,QAAQ,CAAC,EAAE;AACzB,MAAM,OAAO,EAAE,IAAI,CAAC,OAAO;AAC3B,MAAM,IAAI,EAAE,IAAI,CAAC;AACjB,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,OAAO,EAAE,sBAAsB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC5F;AACA,EAAE,IAAI;AACN,IAAI,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC7C,MAAM,KAAK,EAAE;AACb;AACA,QAAQ,EAAE,EAAE,CAAC,EAAE,YAAY,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;AACpE,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,QAAQ,EAAE;AAClB,UAAU,OAAO,EAAE;AACnB,YAAY,MAAM,EAAE;AACpB;AACA;AACA;AACA,KAAK,CAAC;AACN,IAAI,MAAM,SAAS,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,MAAM;AAC3C,MAAM,EAAE,EAAE,IAAI,CAAC,EAAE;AACjB,MAAM,IAAI,EAAE,IAAI,CAAC,IAAI;AACrB,MAAM,WAAW,EAAE,IAAI,CAAC,WAAW;AACnC,MAAM,OAAO,EAAE,IAAI,CAAC,OAAO;AAC3B,MAAM,YAAY,EAAE,IAAI,CAAC,YAAY;AACrC,MAAM,WAAW,EAAE,IAAI,CAAC,WAAW;AACnC,MAAM,oBAAoB,EAAE,IAAI,CAAC,oBAAoB,IAAI,KAAK,CAAC;AAC/D,MAAM,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,IAAI,KAAK,CAAC;AAC7D,MAAM,OAAO,EAAE,IAAI,CAAC,OAAO;AAC3B,MAAM,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,MAAM;AAChD,QAAQ,SAAS,EAAE,OAAO,CAAC,SAAS;AACpC,QAAQ,WAAW,EAAE,OAAO,CAAC,WAAW;AACxC,QAAQ,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,MAAM;AAC/C,UAAU,OAAO,EAAE,KAAK,CAAC,OAAO;AAChC,UAAU,KAAK,EAAE,KAAK,CAAC;AACvB,SAAS,CAAC;AACV,OAAO,CAAC;AACR,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG;AACrC,MAAM,SAAS,CAAC,GAAG,CAAC,OAAO,IAAI,KAAK;AACpC,QAAQ,IAAI;AACZ,UAAU,MAAM,WAAW,GAAG,MAAM,kBAAkB,CAAC,IAAI,CAAC;AAC5D,UAAU,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AACnC,YAAY,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;AAClC,YAAY,IAAI,EAAE;AAClB,cAAc,oBAAoB,EAAE,WAAW,CAAC,oBAAoB;AACpE,cAAc,mBAAmB,EAAE,WAAW,CAAC;AAC/C;AACA,WAAW,CAAC;AACZ,UAAU,OAAO;AACjB,YAAY,EAAE,EAAE,IAAI,CAAC,EAAE;AACvB,YAAY,IAAI,EAAE,IAAI,CAAC,IAAI;AAC3B,YAAY,OAAO,EAAE,IAAI;AACzB,YAAY,oBAAoB,EAAE,WAAW,CAAC,oBAAoB;AAClE,YAAY,mBAAmB,EAAE,WAAW,CAAC;AAC7C,WAAW;AACX,SAAS,CAAC,OAAO,KAAK,EAAE;AACxB,UAAU,OAAO;AACjB,YAAY,EAAE,EAAE,IAAI,CAAC,EAAE;AACvB,YAAY,IAAI,EAAE,IAAI,CAAC,IAAI;AAC3B,YAAY,OAAO,EAAE,KAAK;AAC1B,YAAY,KAAK,EAAE,KAAK,CAAC;AACzB,WAAW;AACX;AACA,OAAO;AACP,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,OAAO;AACb,MAAM,OAAO,EAAE,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,sCAAsC;AACrH,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC;AAC5D,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,KAAK,EAAE,KAAK,CAAC;AACrB,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;;;;"}