{"version": 3, "file": "_server.ts-CIS6dLdH.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/usage/analytics/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { v as verifySessionToken } from \"../../../../../chunks/auth.js\";\nimport { getFeatureUsage } from \"../../../../../chunks/feature-usage.js\";\nconst GET = async ({ cookies, url }) => {\n  const token = cookies.get(\"auth_token\");\n  if (!token) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  const userData = verifySessionToken(token);\n  if (!(await userData)?.id) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  try {\n    const period = url.searchParams.get(\"period\") ?? \"monthly\";\n    const featureId = url.searchParams.get(\"featureId\");\n    const limitId = url.searchParams.get(\"limitId\");\n    const usageData = await getFeatureUsage((await userData).id);\n    let filteredData = usageData;\n    if (featureId) {\n      filteredData = filteredData.filter((item) => item.featureId === featureId);\n    }\n    if (limitId) {\n      filteredData = filteredData.filter((item) => item.limitId === limitId);\n    }\n    const timeSeriesData = generateTimeSeriesData(filteredData, period);\n    const analytics = {\n      timeSeries: timeSeriesData,\n      summary: {\n        totalUsage: filteredData.reduce((sum, item) => sum + (item.used ?? 0), 0),\n        averageUsage: filteredData.length > 0 ? Math.round(\n          filteredData.reduce((sum, item) => sum + (item.used ?? 0), 0) / filteredData.length\n        ) : 0,\n        peakUsage: Math.max(...filteredData.map((item) => item.used ?? 0), 0),\n        activeFeatures: new Set(filteredData.map((item) => item.featureId)).size,\n        period\n      },\n      distribution: calculateUsageDistribution(usageData),\n      trends: calculateUsageTrends(filteredData)\n    };\n    return json(analytics);\n  } catch (error) {\n    console.error(\"Error fetching usage analytics:\", error);\n    return json({ error: \"Failed to fetch analytics data\" }, { status: 500 });\n  }\n};\nfunction generateTimeSeriesData(usageData, period) {\n  const now = /* @__PURE__ */ new Date();\n  const periods = [];\n  let periodCount;\n  if (period === \"daily\") {\n    periodCount = 30;\n  } else if (period === \"weekly\") {\n    periodCount = 12;\n  } else {\n    periodCount = 6;\n  }\n  for (let i = 0; i < periodCount; i++) {\n    const date = /* @__PURE__ */ new Date();\n    let periodString = \"\";\n    if (period === \"daily\") {\n      date.setDate(now.getDate() - i);\n      periodString = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, \"0\")}-${String(date.getDate()).padStart(2, \"0\")}`;\n    } else if (period === \"weekly\") {\n      date.setDate(now.getDate() - i * 7);\n      const weekStart = new Date(date);\n      weekStart.setDate(date.getDate() - date.getDay());\n      periodString = `${weekStart.getFullYear()}-W${Math.ceil((weekStart.getDate() + weekStart.getDay()) / 7)}`;\n    } else {\n      date.setMonth(now.getMonth() - i);\n      periodString = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, \"0\")}`;\n    }\n    const usage = usageData.find((item) => item.period === periodString);\n    periods.unshift({\n      date,\n      period: periodString,\n      used: usage ? usage.used : 0\n    });\n  }\n  return periods;\n}\nfunction calculateUsageDistribution(usageData) {\n  const featureUsage = /* @__PURE__ */ new Map();\n  usageData.forEach((item) => {\n    const key = item.featureId;\n    if (featureUsage.has(key)) {\n      featureUsage.get(key).used += item.used ?? 0;\n    } else {\n      featureUsage.set(key, {\n        name: item.featureName ?? item.featureId,\n        used: item.used ?? 0,\n        category: item.category ?? \"other\"\n      });\n    }\n  });\n  const distribution = Array.from(featureUsage.values()).filter((item) => item.used > 0).sort((a, b) => b.used - a.used);\n  const totalUsage = distribution.reduce((sum, item) => sum + item.used, 0);\n  return distribution.map((item) => ({\n    ...item,\n    percentage: totalUsage > 0 ? Math.round(item.used / totalUsage * 100) : 0\n  }));\n}\nfunction calculateUsageTrends(usageData) {\n  if (usageData.length < 2) {\n    return {\n      direction: \"stable\",\n      change: 0,\n      percentChange: 0\n    };\n  }\n  const sortedData = usageData.slice().sort((a, b) => {\n    if (a.period && b.period) {\n      return a.period.localeCompare(b.period);\n    }\n    return 0;\n  });\n  const recent = sortedData.slice(-2);\n  if (recent.length < 2) {\n    return {\n      direction: \"stable\",\n      change: 0,\n      percentChange: 0\n    };\n  }\n  const current = recent[1].used ?? 0;\n  const previous = recent[0].used ?? 0;\n  const change = current - previous;\n  const percentChange = previous > 0 ? Math.round(change / previous * 100) : 0;\n  let direction;\n  if (change > 0) {\n    direction = \"up\";\n  } else if (change < 0) {\n    direction = \"down\";\n  } else {\n    direction = \"stable\";\n  }\n  return {\n    direction,\n    change,\n    percentChange\n  };\n}\nconst POST = async ({ cookies, request }) => {\n  const token = cookies.get(\"auth_token\");\n  if (!token) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  const userData = verifySessionToken(token);\n  if (!(await userData)?.id) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  try {\n    const { format = \"csv\" } = await request.json();\n    const usageData = await getFeatureUsage((await userData).id);\n    if (format === \"csv\") {\n      const csvHeaders = [\n        \"Feature Name\",\n        \"Feature ID\",\n        \"Limit Name\",\n        \"Limit ID\",\n        \"Used\",\n        \"Limit\",\n        \"Remaining\",\n        \"Percentage Used\",\n        \"Period\",\n        \"Category\"\n      ];\n      const csvRows = usageData.map((item) => [\n        item.featureName ?? \"\",\n        item.featureId ?? \"\",\n        item.limitName ?? \"\",\n        item.limitId ?? \"\",\n        item.used ?? 0,\n        item.limit ?? \"\",\n        item.remaining ?? \"\",\n        item.percentUsed ?? 0,\n        item.period ?? \"\",\n        item.category ?? \"\"\n      ]);\n      const csvContent = [\n        csvHeaders.join(\",\"),\n        ...csvRows.map((row) => row.map((cell) => `\"${cell}\"`).join(\",\"))\n      ].join(\"\\n\");\n      return new Response(csvContent, {\n        headers: {\n          \"Content-Type\": \"text/csv\",\n          \"Content-Disposition\": `attachment; filename=\"usage-data-${(/* @__PURE__ */ new Date()).toISOString().split(\"T\")[0]}.csv\"`\n        }\n      });\n    } else {\n      return json(usageData);\n    }\n  } catch (error) {\n    console.error(\"Error exporting usage data:\", error);\n    return json({ error: \"Failed to export data\" }, { status: 500 });\n  }\n};\nexport {\n  GET,\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAGK,MAAC,GAAG,GAAG,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK;AACxC,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE;AACd,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,MAAM,QAAQ,GAAG,kBAAkB,CAAC,KAAK,CAAC;AAC5C,EAAE,IAAI,CAAC,CAAC,MAAM,QAAQ,GAAG,EAAE,EAAE;AAC7B,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,SAAS;AAC9D,IAAI,MAAM,SAAS,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC;AACvD,IAAI,MAAM,OAAO,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC;AACnD,IAAI,MAAM,SAAS,GAAG,MAAM,eAAe,CAAC,CAAC,MAAM,QAAQ,EAAE,EAAE,CAAC;AAChE,IAAI,IAAI,YAAY,GAAG,SAAS;AAChC,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,SAAS,KAAK,SAAS,CAAC;AAChF;AACA,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,OAAO,KAAK,OAAO,CAAC;AAC5E;AACA,IAAI,MAAM,cAAc,GAAG,sBAAsB,CAAC,YAAY,EAAE,MAAM,CAAC;AACvE,IAAI,MAAM,SAAS,GAAG;AACtB,MAAM,UAAU,EAAE,cAAc;AAChC,MAAM,OAAO,EAAE;AACf,QAAQ,UAAU,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;AACjF,QAAQ,YAAY,EAAE,YAAY,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK;AAC1D,UAAU,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,YAAY,CAAC;AACvF,SAAS,GAAG,CAAC;AACb,QAAQ,SAAS,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;AAC7E,QAAQ,cAAc,EAAE,IAAI,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI;AAChF,QAAQ;AACR,OAAO;AACP,MAAM,YAAY,EAAE,0BAA0B,CAAC,SAAS,CAAC;AACzD,MAAM,MAAM,EAAE,oBAAoB,CAAC,YAAY;AAC/C,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC;AAC1B,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC;AAC3D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,gCAAgC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7E;AACA;AACA,SAAS,sBAAsB,CAAC,SAAS,EAAE,MAAM,EAAE;AACnD,EAAE,MAAM,GAAG,mBAAmB,IAAI,IAAI,EAAE;AACxC,EAAE,MAAM,OAAO,GAAG,EAAE;AACpB,EAAE,IAAI,WAAW;AACjB,EAAE,IAAI,MAAM,KAAK,OAAO,EAAE;AAC1B,IAAI,WAAW,GAAG,EAAE;AACpB,GAAG,MAAM,IAAI,MAAM,KAAK,QAAQ,EAAE;AAClC,IAAI,WAAW,GAAG,EAAE;AACpB,GAAG,MAAM;AACT,IAAI,WAAW,GAAG,CAAC;AACnB;AACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE;AACxC,IAAI,MAAM,IAAI,mBAAmB,IAAI,IAAI,EAAE;AAC3C,IAAI,IAAI,YAAY,GAAG,EAAE;AACzB,IAAI,IAAI,MAAM,KAAK,OAAO,EAAE;AAC5B,MAAM,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AACrC,MAAM,YAAY,GAAG,CAAC,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AACvI,KAAK,MAAM,IAAI,MAAM,KAAK,QAAQ,EAAE;AACpC,MAAM,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;AACzC,MAAM,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC;AACtC,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AACvD,MAAM,YAAY,GAAG,CAAC,EAAE,SAAS,CAAC,WAAW,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC/G,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;AACvC,MAAM,YAAY,GAAG,CAAC,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAC5F;AACA,IAAI,MAAM,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,KAAK,YAAY,CAAC;AACxE,IAAI,OAAO,CAAC,OAAO,CAAC;AACpB,MAAM,IAAI;AACV,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,IAAI,EAAE,KAAK,GAAG,KAAK,CAAC,IAAI,GAAG;AACjC,KAAK,CAAC;AACN;AACA,EAAE,OAAO,OAAO;AAChB;AACA,SAAS,0BAA0B,CAAC,SAAS,EAAE;AAC/C,EAAE,MAAM,YAAY,mBAAmB,IAAI,GAAG,EAAE;AAChD,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AAC9B,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS;AAC9B,IAAI,IAAI,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AAC/B,MAAM,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC;AAClD,KAAK,MAAM;AACX,MAAM,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE;AAC5B,QAAQ,IAAI,EAAE,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,SAAS;AAChD,QAAQ,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC;AAC5B,QAAQ,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI;AACnC,OAAO,CAAC;AACR;AACA,GAAG,CAAC;AACJ,EAAE,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;AACxH,EAAE,MAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,KAAK,GAAG,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;AAC3E,EAAE,OAAO,YAAY,CAAC,GAAG,CAAC,CAAC,IAAI,MAAM;AACrC,IAAI,GAAG,IAAI;AACX,IAAI,UAAU,EAAE,UAAU,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,UAAU,GAAG,GAAG,CAAC,GAAG;AAC5E,GAAG,CAAC,CAAC;AACL;AACA,SAAS,oBAAoB,CAAC,SAAS,EAAE;AACzC,EAAE,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;AAC5B,IAAI,OAAO;AACX,MAAM,SAAS,EAAE,QAAQ;AACzB,MAAM,MAAM,EAAE,CAAC;AACf,MAAM,aAAa,EAAE;AACrB,KAAK;AACL;AACA,EAAE,MAAM,UAAU,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;AACtD,IAAI,IAAI,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,MAAM,EAAE;AAC9B,MAAM,OAAO,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC;AAC7C;AACA,IAAI,OAAO,CAAC;AACZ,GAAG,CAAC;AACJ,EAAE,MAAM,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;AACrC,EAAE,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;AACzB,IAAI,OAAO;AACX,MAAM,SAAS,EAAE,QAAQ;AACzB,MAAM,MAAM,EAAE,CAAC;AACf,MAAM,aAAa,EAAE;AACrB,KAAK;AACL;AACA,EAAE,MAAM,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;AACrC,EAAE,MAAM,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;AACtC,EAAE,MAAM,MAAM,GAAG,OAAO,GAAG,QAAQ;AACnC,EAAE,MAAM,aAAa,GAAG,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,QAAQ,GAAG,GAAG,CAAC,GAAG,CAAC;AAC9E,EAAE,IAAI,SAAS;AACf,EAAE,IAAI,MAAM,GAAG,CAAC,EAAE;AAClB,IAAI,SAAS,GAAG,IAAI;AACpB,GAAG,MAAM,IAAI,MAAM,GAAG,CAAC,EAAE;AACzB,IAAI,SAAS,GAAG,MAAM;AACtB,GAAG,MAAM;AACT,IAAI,SAAS,GAAG,QAAQ;AACxB;AACA,EAAE,OAAO;AACT,IAAI,SAAS;AACb,IAAI,MAAM;AACV,IAAI;AACJ,GAAG;AACH;AACK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AAC7C,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE;AACd,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,MAAM,QAAQ,GAAG,kBAAkB,CAAC,KAAK,CAAC;AAC5C,EAAE,IAAI,CAAC,CAAC,MAAM,QAAQ,GAAG,EAAE,EAAE;AAC7B,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,EAAE,MAAM,GAAG,KAAK,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACnD,IAAI,MAAM,SAAS,GAAG,MAAM,eAAe,CAAC,CAAC,MAAM,QAAQ,EAAE,EAAE,CAAC;AAChE,IAAI,IAAI,MAAM,KAAK,KAAK,EAAE;AAC1B,MAAM,MAAM,UAAU,GAAG;AACzB,QAAQ,cAAc;AACtB,QAAQ,YAAY;AACpB,QAAQ,YAAY;AACpB,QAAQ,UAAU;AAClB,QAAQ,MAAM;AACd,QAAQ,OAAO;AACf,QAAQ,WAAW;AACnB,QAAQ,iBAAiB;AACzB,QAAQ,QAAQ;AAChB,QAAQ;AACR,OAAO;AACP,MAAM,MAAM,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK;AAC9C,QAAQ,IAAI,CAAC,WAAW,IAAI,EAAE;AAC9B,QAAQ,IAAI,CAAC,SAAS,IAAI,EAAE;AAC5B,QAAQ,IAAI,CAAC,SAAS,IAAI,EAAE;AAC5B,QAAQ,IAAI,CAAC,OAAO,IAAI,EAAE;AAC1B,QAAQ,IAAI,CAAC,IAAI,IAAI,CAAC;AACtB,QAAQ,IAAI,CAAC,KAAK,IAAI,EAAE;AACxB,QAAQ,IAAI,CAAC,SAAS,IAAI,EAAE;AAC5B,QAAQ,IAAI,CAAC,WAAW,IAAI,CAAC;AAC7B,QAAQ,IAAI,CAAC,MAAM,IAAI,EAAE;AACzB,QAAQ,IAAI,CAAC,QAAQ,IAAI;AACzB,OAAO,CAAC;AACR,MAAM,MAAM,UAAU,GAAG;AACzB,QAAQ,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC;AAC5B,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;AACxE,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;AAClB,MAAM,OAAO,IAAI,QAAQ,CAAC,UAAU,EAAE;AACtC,QAAQ,OAAO,EAAE;AACjB,UAAU,cAAc,EAAE,UAAU;AACpC,UAAU,qBAAqB,EAAE,CAAC,iCAAiC,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK;AACnI;AACA,OAAO,CAAC;AACR,KAAK,MAAM;AACX,MAAM,OAAO,IAAI,CAAC,SAAS,CAAC;AAC5B;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC;AACvD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpE;AACA;;;;"}