import { json } from '@sveltejs/kit';
import type { RequestHandler } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';

export const GET: RequestHandler = async () => {
  try {
    console.log('🔍 Testing database connection...');
    
    if (!prisma) {
      console.error('❌ Prisma client is not initialized');
      return json({ error: 'Database connection not available' }, { status: 500 });
    }

    console.log('✅ Prisma client is available');

    // Test basic connection
    const totalCompanies = await prisma.company.count();
    console.log(`📊 Total companies: ${totalCompanies}`);

    // Get first 5 companies for testing
    const companies = await prisma.company.findMany({
      take: 5,
      select: {
        id: true,
        name: true,
        logoUrl: true,
        activeJobCount: true,
      },
    });

    console.log(`📋 Sample companies:`, companies);

    return json({
      success: true,
      totalCompanies,
      sampleCompanies: companies,
    });
  } catch (error) {
    console.error('💥 Database test error:', error);
    return json({ error: 'Database test failed', details: error }, { status: 500 });
  }
};
