{"version": 3, "file": "_page.svelte-BXtwj-Zm.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/studio/_page.svelte.js"], "sourcesContent": ["import \"clsx\";\nimport { y as pop, w as push } from \"../../../chunks/index3.js\";\nimport \"../../../chunks/client.js\";\nfunction _page($$payload, $$props) {\n  push();\n  $$payload.out += `<div class=\"container svelte-1yqb9ci\"><h1 class=\"svelte-1yqb9ci\">Redirecting to Sanity Studio...</h1> <p class=\"svelte-1yqb9ci\">If you are not redirected automatically, please click the button below:</p> <a href=\"/static/studio/index.html\" class=\"button svelte-1yqb9ci\">Open Sanity Studio</a></div>`;\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;AAGA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,0SAA0S,CAAC;AAC/T,EAAE,GAAG,EAAE;AACP;;;;"}