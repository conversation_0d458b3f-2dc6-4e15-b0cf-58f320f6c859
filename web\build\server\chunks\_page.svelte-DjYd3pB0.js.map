{"version": 3, "file": "_page.svelte-DjYd3pB0.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/auth/verify/_page.svelte.js"], "sourcesContent": ["import \"clsx\";\nimport { y as pop, w as push } from \"../../../../chunks/index3.js\";\nimport \"../../../../chunks/client.js\";\nimport { S as SEO } from \"../../../../chunks/SEO.js\";\nimport \"../../../../chunks/button.js\";\nimport { C as Card } from \"../../../../chunks/card.js\";\nimport { C as Card_content } from \"../../../../chunks/card-content.js\";\nimport { L as Loader_circle } from \"../../../../chunks/loader-circle.js\";\nfunction _page($$payload, $$props) {\n  push();\n  SEO($$payload, {\n    title: \"Verify Email | Auto Apply\",\n    description: \"Verify your email address\"\n  });\n  $$payload.out += `<!----> <div class=\"flex min-h-[calc(100vh-200px)] flex-col items-center justify-center p-4\"><div class=\"w-full max-w-md\">`;\n  {\n    $$payload.out += \"<!--[-->\";\n    Card($$payload, {\n      class: \"text-center\",\n      children: ($$payload2) => {\n        Card_content($$payload2, {\n          class: \"pt-6\",\n          children: ($$payload3) => {\n            $$payload3.out += `<div class=\"mb-4 flex justify-center\">`;\n            Loader_circle($$payload3, { class: \"text-primary h-12 w-12 animate-spin\" });\n            $$payload3.out += `<!----></div> <h1 class=\"mb-2 text-2xl font-bold\">Verifying Your Email</h1> <p class=\"text-muted-foreground\">Please wait while we verify your email address...</p>`;\n          },\n          $$slots: { default: true }\n        });\n      },\n      $$slots: { default: true }\n    });\n  }\n  $$payload.out += `<!--]--></div></div>`;\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAQA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,GAAG,CAAC,SAAS,EAAE;AACjB,IAAI,KAAK,EAAE,2BAA2B;AACtC,IAAI,WAAW,EAAE;AACjB,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,0HAA0H,CAAC;AAC/I,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,IAAI,CAAC,SAAS,EAAE;AACpB,MAAM,KAAK,EAAE,aAAa;AAC1B,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,YAAY,CAAC,UAAU,EAAE;AACjC,UAAU,KAAK,EAAE,MAAM;AACvB,UAAU,QAAQ,EAAE,CAAC,UAAU,KAAK;AACpC,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,sCAAsC,CAAC;AACtE,YAAY,aAAa,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,qCAAqC,EAAE,CAAC;AACvF,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,kKAAkK,CAAC;AAClM,WAAW;AACX,UAAU,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAClC,SAAS,CAAC;AACV,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B,KAAK,CAAC;AACN;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC;AACzC,EAAE,GAAG,EAAE;AACP;;;;"}