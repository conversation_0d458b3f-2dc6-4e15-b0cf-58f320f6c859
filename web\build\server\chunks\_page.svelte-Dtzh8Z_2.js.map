{"version": 3, "file": "_page.svelte-Dtzh8Z_2.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/settings/_page.svelte.js"], "sourcesContent": ["import { _ as store_get, U as ensure_array_like, V as escape_html, a1 as unsubscribe_stores, y as pop, w as push } from \"../../../../chunks/index3.js\";\nimport { B as Button } from \"../../../../chunks/button.js\";\nimport { g as goto } from \"../../../../chunks/client.js\";\nimport { S as SEO } from \"../../../../chunks/SEO.js\";\nimport { g as getStores } from \"../../../../chunks/stores.js\";\nimport { U as User } from \"../../../../chunks/user.js\";\nimport { B as Bell } from \"../../../../chunks/bell.js\";\nimport { S as Shield } from \"../../../../chunks/shield.js\";\nimport { S as Sparkles } from \"../../../../chunks/sparkles.js\";\nimport { C as Credit_card } from \"../../../../chunks/credit-card.js\";\nimport { A as Activity } from \"../../../../chunks/activity.js\";\nimport { B as Bell_ring } from \"../../../../chunks/bell-ring.js\";\nimport { S as Share_2 } from \"../../../../chunks/share-2.js\";\nimport { U as Users } from \"../../../../chunks/users.js\";\nimport { C as Chevron_right } from \"../../../../chunks/chevron-right2.js\";\nfunction _page($$payload, $$props) {\n  push();\n  var $$store_subs;\n  let userData, hasTeamAccess, settingCards;\n  const { page } = getStores();\n  const baseSettingCards = [\n    {\n      title: \"Profile\",\n      description: \"Manage your personal information\",\n      content: \"Update your name, email, profile picture and other personal details.\",\n      icon: User,\n      href: \"/dashboard/settings/profile\"\n    },\n    {\n      title: \"Account\",\n      description: \"Manage your account preferences\",\n      content: \"Update your notification preferences, language settings, and accessibility options.\",\n      icon: Bell,\n      href: \"/dashboard/settings/account\"\n    },\n    {\n      title: \"Security\",\n      description: \"Manage your security settings\",\n      content: \"Update your password, enable two-factor authentication, and manage your sessions.\",\n      icon: Shield,\n      href: \"/dashboard/settings/security\"\n    },\n    {\n      title: \"AI Coach\",\n      description: \"Practice for your interviews\",\n      content: \"Use AI to practice for your interviews with personalized feedback and suggestions.\",\n      icon: Sparkles,\n      href: \"/dashboard/settings/interview-coach\"\n    },\n    {\n      title: \"Billing\",\n      description: \"Manage your subscription and payments\",\n      content: \"View your current subscription, payment methods, and billing history.\",\n      icon: Credit_card,\n      href: \"/dashboard/settings/billing\"\n    },\n    {\n      title: \"Usage\",\n      description: \"Monitor your feature usage\",\n      content: \"Track your feature usage and subscription limits across the platform.\",\n      icon: Activity,\n      href: \"/dashboard/settings/usage\"\n    },\n    {\n      title: \"Notifications\",\n      description: \"Manage your notification preferences\",\n      content: \"Control how and when you receive notifications across email, browser, and more.\",\n      icon: Bell_ring,\n      href: \"/dashboard/settings/notifications\"\n    },\n    {\n      title: \"Referrals\",\n      description: \"Share and earn rewards\",\n      content: \"Invite friends to join Hirli and earn rewards for successful referrals.\",\n      icon: Share_2,\n      href: \"/dashboard/settings/referrals\"\n    }\n  ];\n  const teamCard = {\n    title: \"Team\",\n    description: \"Manage your team members\",\n    content: \"Invite team members, manage permissions, and organize your team.\",\n    icon: Users,\n    href: \"/dashboard/settings/team\"\n  };\n  userData = store_get($$store_subs ??= {}, \"$page\", page).data.user;\n  hasTeamAccess = userData?.teamId || userData?.hasTeamFeature || false;\n  settingCards = hasTeamAccess ? [...baseSettingCards, teamCard] : baseSettingCards;\n  const each_array = ensure_array_like(settingCards);\n  SEO($$payload, {\n    title: \"Account Settings | Hirli\",\n    description: \"Manage your Hirli account settings, including profile information, security preferences, and notification settings.\",\n    keywords: \"account settings, profile settings, security settings, notification preferences, account management\"\n  });\n  $$payload.out += `<!----> <div class=\"flex h-full flex-col\"><div class=\"border-border flex flex-col justify-between border-b p-6\"><h2 class=\"text-lg font-semibold\">General Settings</h2> <p class=\"text-muted-foreground\">Manage your account settings and preferences.</p></div> <div class=\"divide-y\"><!--[-->`;\n  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n    let { title, description, content, icon, href } = each_array[$$index];\n    $$payload.out += `<div class=\"hover:bg-muted/50 flex items-center justify-between p-6 transition-colors\"><div class=\"flex items-center gap-4\"><div class=\"bg-primary/10 flex h-10 w-10 items-center justify-center rounded-full\"><!---->`;\n    icon?.($$payload, { class: \"text-primary h-5 w-5\" });\n    $$payload.out += `<!----></div> <div class=\"space-y-1\"><h3 class=\"font-medium\">${escape_html(title)}</h3> <p class=\"text-muted-foreground text-sm\">${escape_html(description)}</p></div></div> <div class=\"flex items-center gap-2\">`;\n    Button($$payload, {\n      variant: \"outline\",\n      onclick: () => goto(),\n      children: ($$payload2) => {\n        $$payload2.out += `<!---->Manage`;\n      },\n      $$slots: { default: true }\n    });\n    $$payload.out += `<!----> `;\n    Chevron_right($$payload, { class: \"text-muted-foreground h-4 w-4\" });\n    $$payload.out += `<!----></div></div>`;\n  }\n  $$payload.out += `<!--]--></div></div>`;\n  if ($$store_subs) unsubscribe_stores($$store_subs);\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAeA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,YAAY;AAClB,EAAE,IAAI,QAAQ,EAAE,aAAa,EAAE,YAAY;AAC3C,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,SAAS,EAAE;AAC9B,EAAE,MAAM,gBAAgB,GAAG;AAC3B,IAAI;AACJ,MAAM,KAAK,EAAE,SAAS;AACtB,MAAM,WAAW,EAAE,kCAAkC;AACrD,MAAM,OAAO,EAAE,sEAAsE;AACrF,MAAM,IAAI,EAAE,IAAI;AAChB,MAAM,IAAI,EAAE;AACZ,KAAK;AACL,IAAI;AACJ,MAAM,KAAK,EAAE,SAAS;AACtB,MAAM,WAAW,EAAE,iCAAiC;AACpD,MAAM,OAAO,EAAE,qFAAqF;AACpG,MAAM,IAAI,EAAE,IAAI;AAChB,MAAM,IAAI,EAAE;AACZ,KAAK;AACL,IAAI;AACJ,MAAM,KAAK,EAAE,UAAU;AACvB,MAAM,WAAW,EAAE,+BAA+B;AAClD,MAAM,OAAO,EAAE,mFAAmF;AAClG,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE;AACZ,KAAK;AACL,IAAI;AACJ,MAAM,KAAK,EAAE,UAAU;AACvB,MAAM,WAAW,EAAE,8BAA8B;AACjD,MAAM,OAAO,EAAE,oFAAoF;AACnG,MAAM,IAAI,EAAE,QAAQ;AACpB,MAAM,IAAI,EAAE;AACZ,KAAK;AACL,IAAI;AACJ,MAAM,KAAK,EAAE,SAAS;AACtB,MAAM,WAAW,EAAE,uCAAuC;AAC1D,MAAM,OAAO,EAAE,uEAAuE;AACtF,MAAM,IAAI,EAAE,WAAW;AACvB,MAAM,IAAI,EAAE;AACZ,KAAK;AACL,IAAI;AACJ,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,WAAW,EAAE,4BAA4B;AAC/C,MAAM,OAAO,EAAE,uEAAuE;AACtF,MAAM,IAAI,EAAE,QAAQ;AACpB,MAAM,IAAI,EAAE;AACZ,KAAK;AACL,IAAI;AACJ,MAAM,KAAK,EAAE,eAAe;AAC5B,MAAM,WAAW,EAAE,sCAAsC;AACzD,MAAM,OAAO,EAAE,iFAAiF;AAChG,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,IAAI,EAAE;AACZ,KAAK;AACL,IAAI;AACJ,MAAM,KAAK,EAAE,WAAW;AACxB,MAAM,WAAW,EAAE,wBAAwB;AAC3C,MAAM,OAAO,EAAE,yEAAyE;AACxF,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,IAAI,EAAE;AACZ;AACA,GAAG;AACH,EAAE,MAAM,QAAQ,GAAG;AACnB,IAAI,KAAK,EAAE,MAAM;AACjB,IAAI,WAAW,EAAE,0BAA0B;AAC3C,IAAI,OAAO,EAAE,kEAAkE;AAC/E,IAAI,IAAI,EAAE,KAAK;AACf,IAAI,IAAI,EAAE;AACV,GAAG;AACH,EAAE,QAAQ,GAAG,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI;AACpE,EAAE,aAAa,GAAG,QAAQ,EAAE,MAAM,IAAI,QAAQ,EAAE,cAAc,IAAI,KAAK;AACvE,EAAE,YAAY,GAAG,aAAa,GAAG,CAAC,GAAG,gBAAgB,EAAE,QAAQ,CAAC,GAAG,gBAAgB;AACnF,EAAE,MAAM,UAAU,GAAG,iBAAiB,CAAC,YAAY,CAAC;AACpD,EAAE,GAAG,CAAC,SAAS,EAAE;AACjB,IAAI,KAAK,EAAE,0BAA0B;AACrC,IAAI,WAAW,EAAE,qHAAqH;AACtI,IAAI,QAAQ,EAAE;AACd,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,+RAA+R,CAAC;AACpT,EAAE,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACrF,IAAI,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,UAAU,CAAC,OAAO,CAAC;AACzE,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,sNAAsN,CAAC;AAC7O,IAAI,IAAI,GAAG,SAAS,EAAE,EAAE,KAAK,EAAE,sBAAsB,EAAE,CAAC;AACxD,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,6DAA6D,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,+CAA+C,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC,sDAAsD,CAAC;AACzO,IAAI,MAAM,CAAC,SAAS,EAAE;AACtB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,OAAO,EAAE,MAAM,IAAI,EAAE;AAC3B,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACzC,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B,KAAK,CAAC;AACN,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC/B,IAAI,aAAa,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,+BAA+B,EAAE,CAAC;AACxE,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC;AAC1C;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC;AACzC,EAAE,IAAI,YAAY,EAAE,kBAAkB,CAAC,YAAY,CAAC;AACpD,EAAE,GAAG,EAAE;AACP;;;;"}