{"version": 3, "file": "_server.ts-D3wJ3b76.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/jobs/_id_/dismiss/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { PrismaClient } from \"@prisma/client\";\nimport { v as verifySessionToken } from \"../../../../../../chunks/auth.js\";\nconst prisma = new PrismaClient();\nconst POST = async ({ params, cookies }) => {\n  try {\n    let user = null;\n    const authToken = cookies.get(\"auth_token\");\n    const token = cookies.get(\"token\");\n    if (authToken) {\n      user = await verifySessionToken(authToken);\n    } else if (token) {\n      user = await verifySessionToken(token);\n    }\n    if (!user) {\n      return json({ error: \"Authentication required\" }, { status: 401 });\n    }\n    const { id } = params;\n    if (!id) {\n      return json({ error: \"Job ID is required\" }, { status: 400 });\n    }\n    const job = await prisma.job_listing.findUnique({\n      where: { id }\n    });\n    if (!job) {\n      return json({ error: \"Job not found\" }, { status: 404 });\n    }\n    const deletedMatches = await prisma.job_match_result.deleteMany({\n      where: {\n        userId: user.id,\n        jobId: id\n      }\n    });\n    return json({\n      success: true,\n      message: \"Job dismissed successfully\",\n      deletedCount: deletedMatches.count\n    });\n  } catch (error) {\n    console.error(\"Error dismissing job:\", error);\n    return json({ error: \"Failed to dismiss job\" }, { status: 500 });\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;AAGA,MAAM,MAAM,GAAG,IAAI,YAAY,EAAE;AAC5B,MAAC,IAAI,GAAG,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK;AAC5C,EAAE,IAAI;AACN,IAAI,IAAI,IAAI,GAAG,IAAI;AACnB,IAAI,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AAC/C,IAAI,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC;AACtC,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,IAAI,GAAG,MAAM,kBAAkB,CAAC,SAAS,CAAC;AAChD,KAAK,MAAM,IAAI,KAAK,EAAE;AACtB,MAAM,IAAI,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AAC5C;AACA,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA,IAAI,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;AACzB,IAAI,IAAI,CAAC,EAAE,EAAE;AACb,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oBAAoB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnE;AACA,IAAI,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;AACpD,MAAM,KAAK,EAAE,EAAE,EAAE;AACjB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,GAAG,EAAE;AACd,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9D;AACA,IAAI,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC;AACpE,MAAM,KAAK,EAAE;AACb,QAAQ,MAAM,EAAE,IAAI,CAAC,EAAE;AACvB,QAAQ,KAAK,EAAE;AACf;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,OAAO,EAAE,4BAA4B;AAC3C,MAAM,YAAY,EAAE,cAAc,CAAC;AACnC,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC;AACjD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpE;AACA;;;;"}