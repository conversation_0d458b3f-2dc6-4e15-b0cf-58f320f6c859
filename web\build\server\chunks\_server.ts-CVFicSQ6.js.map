{"version": 3, "file": "_server.ts-CVFicSQ6.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/auth/refresh-session/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { v as verifySessionToken, c as createSessionToken } from \"../../../../../chunks/auth.js\";\nconst POST = async ({ cookies }) => {\n  const token = cookies.get(\"auth_token\");\n  if (!token) {\n    console.log(\"No auth_token cookie found\");\n    return json({ error: \"No authentication token found\" }, { status: 401 });\n  }\n  console.log(\"Verifying token from cookie\");\n  const userData = await verifySessionToken(token);\n  if (!userData || !userData.id) {\n    console.log(\"Invalid or expired token\");\n    return json({ error: \"Invalid or expired authentication token\" }, { status: 401 });\n  }\n  console.log(\"Token verified successfully for user:\", userData.id);\n  try {\n    const user = await prisma.user.findUnique({\n      where: { id: userData.id },\n      select: {\n        id: true,\n        email: true,\n        name: true,\n        image: true,\n        role: true\n      }\n    });\n    if (!user) {\n      return new Response(\"User not found\", { status: 404 });\n    }\n    const newToken = await createSessionToken({\n      email: user.email,\n      name: user.name || \"\",\n      picture: user.image || \"\",\n      role: user.role,\n      id: user.id\n    });\n    cookies.set(\"auth_token\", newToken, {\n      path: \"/\",\n      httpOnly: true,\n      sameSite: \"lax\",\n      secure: process.env.NODE_ENV === \"production\",\n      maxAge: 60 * 60 * 24 * 7\n      // 7 days\n    });\n    return json({\n      success: true,\n      user: {\n        id: user.id,\n        email: user.email,\n        name: user.name,\n        image: user.image,\n        role: user.role\n      }\n    });\n  } catch (error) {\n    console.error(\"Error refreshing session:\", error);\n    return new Response(\"Failed to refresh session\", { status: 500 });\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;AAGK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK;AACpC,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE;AACd,IAAI,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC;AAC7C,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,+BAA+B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC5E;AACA,EAAE,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC;AAC5C,EAAE,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AAClD,EAAE,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;AACjC,IAAI,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC;AAC3C,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yCAAyC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtF;AACA,EAAE,OAAO,CAAC,GAAG,CAAC,uCAAuC,EAAE,QAAQ,CAAC,EAAE,CAAC;AACnE,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAC9C,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE;AAChC,MAAM,MAAM,EAAE;AACd,QAAQ,EAAE,EAAE,IAAI;AAChB,QAAQ,KAAK,EAAE,IAAI;AACnB,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,KAAK,EAAE,IAAI;AACnB,QAAQ,IAAI,EAAE;AACd;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,OAAO,IAAI,QAAQ,CAAC,gBAAgB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC5D;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC;AAC9C,MAAM,KAAK,EAAE,IAAI,CAAC,KAAK;AACvB,MAAM,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,EAAE;AAC3B,MAAM,OAAO,EAAE,IAAI,CAAC,KAAK,IAAI,EAAE;AAC/B,MAAM,IAAI,EAAE,IAAI,CAAC,IAAI;AACrB,MAAM,EAAE,EAAE,IAAI,CAAC;AACf,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,QAAQ,EAAE;AACxC,MAAM,IAAI,EAAE,GAAG;AACf,MAAM,QAAQ,EAAE,IAAI;AACpB,MAAM,QAAQ,EAAE,KAAK;AACrB,MAAM,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;AACnD,MAAM,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;AAC7B;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,IAAI,EAAE;AACZ,QAAQ,EAAE,EAAE,IAAI,CAAC,EAAE;AACnB,QAAQ,KAAK,EAAE,IAAI,CAAC,KAAK;AACzB,QAAQ,IAAI,EAAE,IAAI,CAAC,IAAI;AACvB,QAAQ,KAAK,EAAE,IAAI,CAAC,KAAK;AACzB,QAAQ,IAAI,EAAE,IAAI,CAAC;AACnB;AACA,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC;AACrD,IAAI,OAAO,IAAI,QAAQ,CAAC,2BAA2B,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACrE;AACA;;;;"}