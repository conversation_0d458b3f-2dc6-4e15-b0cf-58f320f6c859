{"version": 3, "file": "_server.ts-ZA4BnfcS.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/billing/cancel-subscription/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../../../chunks/auth.js\";\nconst POST = async ({ cookies, request }) => {\n  const token = cookies.get(\"auth_token\");\n  const isProd = process.env.NODE_ENV === \"production\";\n  if (!token) return new Response(\"Unauthorized\", { status: 401 });\n  const userData = await verifySessionToken(token);\n  if (!userData?.id) return new Response(\"Unauthorized\", { status: 401 });\n  const {\n    cancelAtPeriodEnd = true,\n    pauseCollection = false,\n    pauseAtPeriodEnd = false\n  } = await request.json();\n  const stripeSecret = isProd ? process.env.STRIPE_SECRET_KEY_LIVE || \"sk_live_placeholder\" : process.env.STRIPE_SECRET_KEY_TEST || \"sk_test_placeholder\";\n  const Stripe = (await import(\"stripe\")).default;\n  const stripe = new Stripe(stripeSecret, {\n    apiVersion: \"2023-10-16\"\n  });\n  let user = await prisma.user.findUnique({ where: { id: userData.id } });\n  if (!user) return new Response(\"User not found\", { status: 404 });\n  if (!user.stripeCustomerId) {\n    return new Response(\"No subscription found\", { status: 404 });\n  }\n  try {\n    const keyPrefix = stripeSecret.substring(0, 4);\n    console.log(\n      `Using Stripe API key with prefix: ${keyPrefix}... (${isProd ? \"LIVE\" : \"TEST\"} mode)`\n    );\n    const subscriptions = await stripe.subscriptions.list({\n      customer: user.stripeCustomerId,\n      status: \"active\",\n      limit: 1\n    });\n    if (subscriptions.data.length === 0) {\n      return new Response(\"No active subscription found\", { status: 404 });\n    }\n    const subscription = subscriptions.data[0];\n    if (pauseCollection) {\n      const updatedSubscription = await stripe.subscriptions.update(subscription.id, {\n        pause_collection: {\n          behavior: \"void\"\n          // or 'keep_as_draft' to keep generating draft invoices\n        }\n      });\n      return json({\n        success: true,\n        status: \"paused\",\n        subscription: updatedSubscription\n      });\n    } else if (pauseAtPeriodEnd) {\n      const currentMetadata = subscription.metadata || {};\n      const newMetadata = {\n        ...currentMetadata,\n        pause_at_period_end: \"true\",\n        action_at_period_end: \"pause\"\n      };\n      console.log(\"Setting pause metadata:\", newMetadata);\n      const updatedSubscription = await stripe.subscriptions.update(subscription.id, {\n        cancel_at_period_end: true,\n        // We use cancel_at_period_end as a mechanism\n        metadata: newMetadata\n      });\n      console.log(\"Updated subscription with pause metadata:\", {\n        id: updatedSubscription.id,\n        metadata: updatedSubscription.metadata,\n        cancel_at_period_end: updatedSubscription.cancel_at_period_end\n      });\n      return json({\n        success: true,\n        status: \"pausing_at_period_end\",\n        subscription: updatedSubscription\n      });\n    } else {\n      const updatedSubscription = await stripe.subscriptions.update(subscription.id, {\n        cancel_at_period_end: cancelAtPeriodEnd,\n        metadata: {\n          action_at_period_end: cancelAtPeriodEnd ? \"cancel\" : null\n        }\n      });\n      return json({\n        success: true,\n        status: cancelAtPeriodEnd ? \"canceling\" : \"canceled\",\n        subscription: updatedSubscription\n      });\n    }\n  } catch (error) {\n    console.error(\"Error canceling/pausing subscription:\", error);\n    const errorMessage = error.message || \"Failed to cancel/pause subscription\";\n    const errorType = error.type || \"unknown_error\";\n    if (error.raw) {\n      console.error(\"Stripe error details:\", error.raw);\n    }\n    return json(\n      {\n        error: errorMessage,\n        details: errorType,\n        code: error.code || \"unknown_code\"\n      },\n      { status: 500 }\n    );\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;AAGK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AAC7C,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;AACtD,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE,EAAE,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AAClD,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE,EAAE,MAAM;AACR,IAAI,iBAAiB,GAAG,IAAI;AAC5B,IAAI,eAAe,GAAG,KAAK;AAC3B,IAAI,gBAAgB,GAAG;AACvB,GAAG,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AAC1B,EAAE,MAAM,YAAY,GAAG,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,qBAAqB,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,qBAAqB;AACzJ,EAAE,MAAM,MAAM,GAAG,CAAC,MAAM,OAAO,+BAAQ,CAAC,EAAE,OAAO;AACjD,EAAE,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,YAAY,EAAE;AAC1C,IAAI,UAAU,EAAE;AAChB,GAAG,CAAC;AACJ,EAAE,IAAI,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE,EAAE,CAAC;AACzE,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,QAAQ,CAAC,gBAAgB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnE,EAAE,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;AAC9B,IAAI,OAAO,IAAI,QAAQ,CAAC,uBAAuB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE;AACA,EAAE,IAAI;AACN,IAAI,MAAM,SAAS,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;AAClD,IAAI,OAAO,CAAC,GAAG;AACf,MAAM,CAAC,kCAAkC,EAAE,SAAS,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC,MAAM;AAC3F,KAAK;AACL,IAAI,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC;AAC1D,MAAM,QAAQ,EAAE,IAAI,CAAC,gBAAgB;AACrC,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,KAAK,EAAE;AACb,KAAK,CAAC;AACN,IAAI,IAAI,aAAa,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;AACzC,MAAM,OAAO,IAAI,QAAQ,CAAC,8BAA8B,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1E;AACA,IAAI,MAAM,YAAY,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;AAC9C,IAAI,IAAI,eAAe,EAAE;AACzB,MAAM,MAAM,mBAAmB,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,EAAE;AACrF,QAAQ,gBAAgB,EAAE;AAC1B,UAAU,QAAQ,EAAE;AACpB;AACA;AACA,OAAO,CAAC;AACR,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,MAAM,EAAE,QAAQ;AACxB,QAAQ,YAAY,EAAE;AACtB,OAAO,CAAC;AACR,KAAK,MAAM,IAAI,gBAAgB,EAAE;AACjC,MAAM,MAAM,eAAe,GAAG,YAAY,CAAC,QAAQ,IAAI,EAAE;AACzD,MAAM,MAAM,WAAW,GAAG;AAC1B,QAAQ,GAAG,eAAe;AAC1B,QAAQ,mBAAmB,EAAE,MAAM;AACnC,QAAQ,oBAAoB,EAAE;AAC9B,OAAO;AACP,MAAM,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,WAAW,CAAC;AACzD,MAAM,MAAM,mBAAmB,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,EAAE;AACrF,QAAQ,oBAAoB,EAAE,IAAI;AAClC;AACA,QAAQ,QAAQ,EAAE;AAClB,OAAO,CAAC;AACR,MAAM,OAAO,CAAC,GAAG,CAAC,2CAA2C,EAAE;AAC/D,QAAQ,EAAE,EAAE,mBAAmB,CAAC,EAAE;AAClC,QAAQ,QAAQ,EAAE,mBAAmB,CAAC,QAAQ;AAC9C,QAAQ,oBAAoB,EAAE,mBAAmB,CAAC;AAClD,OAAO,CAAC;AACR,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,MAAM,EAAE,uBAAuB;AACvC,QAAQ,YAAY,EAAE;AACtB,OAAO,CAAC;AACR,KAAK,MAAM;AACX,MAAM,MAAM,mBAAmB,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,EAAE;AACrF,QAAQ,oBAAoB,EAAE,iBAAiB;AAC/C,QAAQ,QAAQ,EAAE;AAClB,UAAU,oBAAoB,EAAE,iBAAiB,GAAG,QAAQ,GAAG;AAC/D;AACA,OAAO,CAAC;AACR,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,MAAM,EAAE,iBAAiB,GAAG,WAAW,GAAG,UAAU;AAC5D,QAAQ,YAAY,EAAE;AACtB,OAAO,CAAC;AACR;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC;AACjE,IAAI,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,IAAI,qCAAqC;AAC/E,IAAI,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,IAAI,eAAe;AACnD,IAAI,IAAI,KAAK,CAAC,GAAG,EAAE;AACnB,MAAM,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,GAAG,CAAC;AACvD;AACA,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,KAAK,EAAE,YAAY;AAC3B,QAAQ,OAAO,EAAE,SAAS;AAC1B,QAAQ,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI;AAC5B,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;;;;"}