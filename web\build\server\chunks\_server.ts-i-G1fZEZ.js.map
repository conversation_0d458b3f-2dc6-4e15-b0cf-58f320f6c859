{"version": 3, "file": "_server.ts-i-G1fZEZ.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/admin/mock-users/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nconst GET = async () => {\n  const users = [\n    {\n      id: \"1\",\n      email: \"<EMAIL>\",\n      name: \"User One\",\n      role: \"pro\",\n      isAdmin: false,\n      createdAt: (/* @__PURE__ */ new Date(\"2023-01-01\")).toISOString(),\n      updatedAt: (/* @__PURE__ */ new Date(\"2023-01-01\")).toISOString(),\n      stripeCustomerId: \"cus_123456\",\n      subscription: {\n        id: \"sub_123456\",\n        status: \"active\",\n        currentPeriodStart: (/* @__PURE__ */ new Date(\"2023-05-01\")).toISOString(),\n        currentPeriodEnd: (/* @__PURE__ */ new Date(\"2023-06-01\")).toISOString(),\n        cancelAtPeriodEnd: false\n      }\n    },\n    {\n      id: \"2\",\n      email: \"<EMAIL>\",\n      name: \"User Two\",\n      role: \"basic\",\n      isAdmin: false,\n      createdAt: (/* @__PURE__ */ new Date(\"2023-02-01\")).toISOString(),\n      updatedAt: (/* @__PURE__ */ new Date(\"2023-02-01\")).toISOString(),\n      stripeCustomerId: \"cus_234567\",\n      subscription: {\n        id: \"sub_234567\",\n        status: \"trialing\",\n        currentPeriodStart: (/* @__PURE__ */ new Date(\"2023-05-15\")).toISOString(),\n        currentPeriodEnd: (/* @__PURE__ */ new Date(\"2023-06-15\")).toISOString(),\n        cancelAtPeriodEnd: false\n      }\n    },\n    {\n      id: \"3\",\n      email: \"<EMAIL>\",\n      name: \"User Three\",\n      role: \"pro\",\n      isAdmin: false,\n      createdAt: (/* @__PURE__ */ new Date(\"2023-03-01\")).toISOString(),\n      updatedAt: (/* @__PURE__ */ new Date(\"2023-03-01\")).toISOString(),\n      stripeCustomerId: \"cus_345678\",\n      subscription: {\n        id: \"sub_345678\",\n        status: \"canceled\",\n        currentPeriodStart: (/* @__PURE__ */ new Date(\"2023-04-01\")).toISOString(),\n        currentPeriodEnd: (/* @__PURE__ */ new Date(\"2023-05-01\")).toISOString(),\n        cancelAtPeriodEnd: true\n      }\n    },\n    {\n      id: \"4\",\n      email: \"<EMAIL>\",\n      name: \"User Four\",\n      role: \"free\",\n      isAdmin: false,\n      createdAt: (/* @__PURE__ */ new Date(\"2023-04-01\")).toISOString(),\n      updatedAt: (/* @__PURE__ */ new Date(\"2023-04-01\")).toISOString(),\n      stripeCustomerId: null,\n      subscription: null\n    },\n    {\n      id: \"5\",\n      email: \"<EMAIL>\",\n      name: \"Admin User\",\n      role: \"admin\",\n      isAdmin: true,\n      createdAt: (/* @__PURE__ */ new Date(\"2023-01-01\")).toISOString(),\n      updatedAt: (/* @__PURE__ */ new Date(\"2023-01-01\")).toISOString(),\n      stripeCustomerId: \"cus_456789\",\n      subscription: {\n        id: \"sub_456789\",\n        status: \"active\",\n        currentPeriodStart: (/* @__PURE__ */ new Date(\"2023-05-01\")).toISOString(),\n        currentPeriodEnd: (/* @__PURE__ */ new Date(\"2023-06-01\")).toISOString(),\n        cancelAtPeriodEnd: false\n      }\n    }\n  ];\n  return json(users);\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;AACK,MAAC,GAAG,GAAG,YAAY;AACxB,EAAE,MAAM,KAAK,GAAG;AAChB,IAAI;AACJ,MAAM,EAAE,EAAE,GAAG;AACb,MAAM,KAAK,EAAE,mBAAmB;AAChC,MAAM,IAAI,EAAE,UAAU;AACtB,MAAM,IAAI,EAAE,KAAK;AACjB,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,SAAS,EAAE,iBAAiB,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,WAAW,EAAE;AACvE,MAAM,SAAS,EAAE,iBAAiB,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,WAAW,EAAE;AACvE,MAAM,gBAAgB,EAAE,YAAY;AACpC,MAAM,YAAY,EAAE;AACpB,QAAQ,EAAE,EAAE,YAAY;AACxB,QAAQ,MAAM,EAAE,QAAQ;AACxB,QAAQ,kBAAkB,EAAE,iBAAiB,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,WAAW,EAAE;AAClF,QAAQ,gBAAgB,EAAE,iBAAiB,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,WAAW,EAAE;AAChF,QAAQ,iBAAiB,EAAE;AAC3B;AACA,KAAK;AACL,IAAI;AACJ,MAAM,EAAE,EAAE,GAAG;AACb,MAAM,KAAK,EAAE,mBAAmB;AAChC,MAAM,IAAI,EAAE,UAAU;AACtB,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,SAAS,EAAE,iBAAiB,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,WAAW,EAAE;AACvE,MAAM,SAAS,EAAE,iBAAiB,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,WAAW,EAAE;AACvE,MAAM,gBAAgB,EAAE,YAAY;AACpC,MAAM,YAAY,EAAE;AACpB,QAAQ,EAAE,EAAE,YAAY;AACxB,QAAQ,MAAM,EAAE,UAAU;AAC1B,QAAQ,kBAAkB,EAAE,iBAAiB,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,WAAW,EAAE;AAClF,QAAQ,gBAAgB,EAAE,iBAAiB,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,WAAW,EAAE;AAChF,QAAQ,iBAAiB,EAAE;AAC3B;AACA,KAAK;AACL,IAAI;AACJ,MAAM,EAAE,EAAE,GAAG;AACb,MAAM,KAAK,EAAE,mBAAmB;AAChC,MAAM,IAAI,EAAE,YAAY;AACxB,MAAM,IAAI,EAAE,KAAK;AACjB,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,SAAS,EAAE,iBAAiB,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,WAAW,EAAE;AACvE,MAAM,SAAS,EAAE,iBAAiB,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,WAAW,EAAE;AACvE,MAAM,gBAAgB,EAAE,YAAY;AACpC,MAAM,YAAY,EAAE;AACpB,QAAQ,EAAE,EAAE,YAAY;AACxB,QAAQ,MAAM,EAAE,UAAU;AAC1B,QAAQ,kBAAkB,EAAE,iBAAiB,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,WAAW,EAAE;AAClF,QAAQ,gBAAgB,EAAE,iBAAiB,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,WAAW,EAAE;AAChF,QAAQ,iBAAiB,EAAE;AAC3B;AACA,KAAK;AACL,IAAI;AACJ,MAAM,EAAE,EAAE,GAAG;AACb,MAAM,KAAK,EAAE,mBAAmB;AAChC,MAAM,IAAI,EAAE,WAAW;AACvB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,SAAS,EAAE,iBAAiB,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,WAAW,EAAE;AACvE,MAAM,SAAS,EAAE,iBAAiB,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,WAAW,EAAE;AACvE,MAAM,gBAAgB,EAAE,IAAI;AAC5B,MAAM,YAAY,EAAE;AACpB,KAAK;AACL,IAAI;AACJ,MAAM,EAAE,EAAE,GAAG;AACb,MAAM,KAAK,EAAE,mBAAmB;AAChC,MAAM,IAAI,EAAE,YAAY;AACxB,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,SAAS,EAAE,iBAAiB,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,WAAW,EAAE;AACvE,MAAM,SAAS,EAAE,iBAAiB,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,WAAW,EAAE;AACvE,MAAM,gBAAgB,EAAE,YAAY;AACpC,MAAM,YAAY,EAAE;AACpB,QAAQ,EAAE,EAAE,YAAY;AACxB,QAAQ,MAAM,EAAE,QAAQ;AACxB,QAAQ,kBAAkB,EAAE,iBAAiB,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,WAAW,EAAE;AAClF,QAAQ,gBAAgB,EAAE,iBAAiB,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,WAAW,EAAE;AAChF,QAAQ,iBAAiB,EAAE;AAC3B;AACA;AACA,GAAG;AACH,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC;AACpB;;;;"}