# Apply Low Memory Configuration Script (PowerShell)
# This script applies memory-optimized settings for 512MB systems

Write-Host "🔧 Applying low-memory configuration for 512MB systems..." -ForegroundColor Yellow

# Copy the low-memory environment configuration
if (Test-Path "cron\.env.low-memory") {
    Write-Host "📋 Copying low-memory environment configuration..." -ForegroundColor Blue
    Copy-Item "cron\.env.low-memory" "cron\.env" -Force
    Write-Host "✅ Environment configuration applied" -ForegroundColor Green
} else {
    Write-Host "❌ Low-memory configuration file not found" -ForegroundColor Red
    exit 1
}

# Set environment variables for current session
$env:CIRCUIT_BREAKER_MEMORY_THRESHOLD = "50"
$env:CIRCUIT_BREAKER_CPU_THRESHOLD = "50"
$env:CIRCUIT_BREAKER_DEGRADED_MEMORY_THRESHOLD = "30"
$env:CIRCUIT_BREAKER_DEGRADED_CPU_THRESHOLD = "30"
$env:ENRICH_JOBS_BATCH_SIZE = "5"
$env:ENRICH_JOBS_CONCURRENCY = "1"
$env:ENRICH_JOBS_MAX_MEMORY = "400"
$env:ENRICH_JOBS_WARNING_MEMORY = "300"
$env:JOB_DETAILS_BATCH_SIZE = "1"
$env:JOB_DETAILS_MAX_CONCURRENT = "1"
$env:JOB_DETAILS_MAX_JOBS = "20"
$env:SCRAPER_MAX_WORKERS = "1"
$env:SCRAPER_BATCH_SIZE = "2"
$env:SCRAPER_CONCURRENCY = "1"
$env:DISABLE_HTML_PREVIEWS = "true"
$env:DISABLE_SCREENSHOTS = "true"
$env:DISABLE_FILE_STORAGE = "true"
$env:NODE_OPTIONS = "--max-old-space-size=400"

Write-Host "🚀 Low-memory configuration applied successfully!" -ForegroundColor Green
Write-Host "📊 Key settings:" -ForegroundColor Cyan
Write-Host "   - Memory threshold: 50%" -ForegroundColor White
Write-Host "   - Batch size: 5" -ForegroundColor White
Write-Host "   - Concurrency: 1" -ForegroundColor White
Write-Host "   - Max memory: 400MB" -ForegroundColor White
Write-Host "   - HTML previews: Disabled" -ForegroundColor White
Write-Host ""
Write-Host "💡 To make these settings permanent, ensure the .env file is used in your deployment." -ForegroundColor Yellow
