{"version": 3, "file": "_server.ts-CjfjR6Ja.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/jobs/_id_/save/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { PrismaClient } from \"@prisma/client\";\nconst prisma = new PrismaClient();\nconst POST = async ({ params, request, locals }) => {\n  const user = locals.user;\n  if (!user) {\n    return json({ error: \"Authentication required\" }, { status: 401 });\n  }\n  try {\n    const jobId = params.id;\n    const { notes } = await request.json();\n    const job = await prisma.job_listing.findUnique({\n      where: { id: jobId }\n    });\n    if (!job) {\n      return json({ error: \"Job not found\" }, { status: 404 });\n    }\n    const appliedJob = await prisma.application.findFirst({\n      where: {\n        url: job.url,\n        userId: user.id\n      }\n    });\n    if (appliedJob) {\n      return json(\n        {\n          success: false,\n          message: \"Cannot save job that is already applied to\",\n          isApplied: true\n        },\n        { status: 400 }\n      );\n    }\n    const existingSavedJob = await prisma.savedJob.findUnique({\n      where: {\n        userId_jobId: {\n          userId: user.id,\n          jobId\n        }\n      }\n    });\n    if (existingSavedJob) {\n      const updatedSavedJob = await prisma.savedJob.update({\n        where: {\n          id: existingSavedJob.id\n        },\n        data: {\n          notes: notes || existingSavedJob.notes,\n          updatedAt: /* @__PURE__ */ new Date()\n        }\n      });\n      return json({\n        success: true,\n        message: \"Job updated successfully\",\n        savedJob: updatedSavedJob\n      });\n    } else {\n      const savedJob = await prisma.savedJob.create({\n        data: {\n          userId: user.id,\n          jobId,\n          notes: notes || null,\n          updatedAt: /* @__PURE__ */ new Date()\n        }\n      });\n      return json({\n        success: true,\n        message: \"Job saved successfully\",\n        savedJob\n      });\n    }\n  } catch (error) {\n    console.error(\"Error saving job:\", error);\n    return json({ error: \"Failed to save job\" }, { status: 500 });\n  }\n};\nconst DELETE = async ({ params, locals }) => {\n  const user = locals.user;\n  if (!user) {\n    return json({ error: \"Authentication required\" }, { status: 401 });\n  }\n  try {\n    const jobId = params.id;\n    const job = await prisma.job_listing.findUnique({\n      where: { id: jobId }\n    });\n    if (!job) {\n      return json({ error: \"Job not found\" }, { status: 404 });\n    }\n    const appliedJob = await prisma.application.findFirst({\n      where: {\n        url: job.url,\n        userId: user.id\n      }\n    });\n    if (appliedJob) {\n      return json(\n        {\n          success: false,\n          message: \"Cannot unsave job that is already applied to\",\n          isApplied: true\n        },\n        { status: 400 }\n      );\n    }\n    const existingSavedJob = await prisma.savedJob.findUnique({\n      where: {\n        userId_jobId: {\n          userId: user.id,\n          jobId\n        }\n      }\n    });\n    if (!existingSavedJob) {\n      return json({ error: \"Job not found\" }, { status: 404 });\n    }\n    await prisma.savedJob.delete({\n      where: {\n        id: existingSavedJob.id\n      }\n    });\n    return json({\n      success: true,\n      message: \"Job removed successfully\"\n    });\n  } catch (error) {\n    console.error(\"Error removing saved job:\", error);\n    return json({ error: \"Failed to remove saved job\" }, { status: 500 });\n  }\n};\nexport {\n  DELETE,\n  POST\n};\n"], "names": [], "mappings": ";;;AAEA,MAAM,MAAM,GAAG,IAAI,YAAY,EAAE;AAC5B,MAAC,IAAI,GAAG,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AACpD,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtE;AACA,EAAE,IAAI;AACN,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,EAAE;AAC3B,IAAI,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AAC1C,IAAI,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;AACpD,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,KAAK;AACxB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,GAAG,EAAE;AACd,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9D;AACA,IAAI,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC;AAC1D,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,GAAG,CAAC,GAAG;AACpB,QAAQ,MAAM,EAAE,IAAI,CAAC;AACrB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,UAAU,EAAE;AACpB,MAAM,OAAO,IAAI;AACjB,QAAQ;AACR,UAAU,OAAO,EAAE,KAAK;AACxB,UAAU,OAAO,EAAE,4CAA4C;AAC/D,UAAU,SAAS,EAAE;AACrB,SAAS;AACT,QAAQ,EAAE,MAAM,EAAE,GAAG;AACrB,OAAO;AACP;AACA,IAAI,MAAM,gBAAgB,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;AAC9D,MAAM,KAAK,EAAE;AACb,QAAQ,YAAY,EAAE;AACtB,UAAU,MAAM,EAAE,IAAI,CAAC,EAAE;AACzB,UAAU;AACV;AACA;AACA,KAAK,CAAC;AACN,IAAI,IAAI,gBAAgB,EAAE;AAC1B,MAAM,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;AAC3D,QAAQ,KAAK,EAAE;AACf,UAAU,EAAE,EAAE,gBAAgB,CAAC;AAC/B,SAAS;AACT,QAAQ,IAAI,EAAE;AACd,UAAU,KAAK,EAAE,KAAK,IAAI,gBAAgB,CAAC,KAAK;AAChD,UAAU,SAAS,kBAAkB,IAAI,IAAI;AAC7C;AACA,OAAO,CAAC;AACR,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,OAAO,EAAE,0BAA0B;AAC3C,QAAQ,QAAQ,EAAE;AAClB,OAAO,CAAC;AACR,KAAK,MAAM;AACX,MAAM,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;AACpD,QAAQ,IAAI,EAAE;AACd,UAAU,MAAM,EAAE,IAAI,CAAC,EAAE;AACzB,UAAU,KAAK;AACf,UAAU,KAAK,EAAE,KAAK,IAAI,IAAI;AAC9B,UAAU,SAAS,kBAAkB,IAAI,IAAI;AAC7C;AACA,OAAO,CAAC;AACR,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,OAAO,EAAE,wBAAwB;AACzC,QAAQ;AACR,OAAO,CAAC;AACR;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC;AAC7C,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oBAAoB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE;AACA;AACK,MAAC,MAAM,GAAG,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK;AAC7C,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtE;AACA,EAAE,IAAI;AACN,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,EAAE;AAC3B,IAAI,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;AACpD,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,KAAK;AACxB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,GAAG,EAAE;AACd,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9D;AACA,IAAI,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC;AAC1D,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,GAAG,CAAC,GAAG;AACpB,QAAQ,MAAM,EAAE,IAAI,CAAC;AACrB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,UAAU,EAAE;AACpB,MAAM,OAAO,IAAI;AACjB,QAAQ;AACR,UAAU,OAAO,EAAE,KAAK;AACxB,UAAU,OAAO,EAAE,8CAA8C;AACjE,UAAU,SAAS,EAAE;AACrB,SAAS;AACT,QAAQ,EAAE,MAAM,EAAE,GAAG;AACrB,OAAO;AACP;AACA,IAAI,MAAM,gBAAgB,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;AAC9D,MAAM,KAAK,EAAE;AACb,QAAQ,YAAY,EAAE;AACtB,UAAU,MAAM,EAAE,IAAI,CAAC,EAAE;AACzB,UAAU;AACV;AACA;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,gBAAgB,EAAE;AAC3B,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9D;AACA,IAAI,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;AACjC,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE,gBAAgB,CAAC;AAC7B;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,OAAO,EAAE;AACf,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC;AACrD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,4BAA4B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE;AACA;;;;"}