{"version": 3, "file": "_server.ts-Dwlv5rUF.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/ai/ats/job-match/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../../../../chunks/auth.js\";\nconst POST = async ({ request, cookies }) => {\n  try {\n    const token = cookies.get(\"auth_token\");\n    if (!token) {\n      return json({ error: \"Unauthorized\" }, { status: 401 });\n    }\n    const tokenData = verifySessionToken(token);\n    if (!tokenData || !tokenData.id) {\n      return json({ error: \"Invalid token\" }, { status: 401 });\n    }\n    const userId = tokenData.id;\n    const body = await request.json();\n    const { resumeId, jobId } = body;\n    if (!resumeId || !jobId) {\n      return json(\n        { error: \"Missing required fields\", required: [\"resumeId\", \"jobId\"] },\n        { status: 400 }\n      );\n    }\n    const resume = await prisma.resume.findFirst({\n      where: {\n        id: resumeId,\n        document: {\n          userId\n        }\n      }\n    });\n    if (!resume) {\n      return json({ error: \"Resume not found or access denied\" }, { status: 404 });\n    }\n    const job = await prisma.job.findUnique({\n      where: { id: jobId }\n    });\n    if (!job) {\n      return json({ error: \"Job not found\" }, { status: 404 });\n    }\n    const user = await prisma.user.findUnique({\n      where: { id: userId },\n      include: {\n        subscriptions: {\n          where: { status: \"active\" },\n          include: {\n            plan: {\n              include: {\n                features: {\n                  where: { featureId: \"resume_analysis\" }\n                }\n              }\n            }\n          }\n        }\n      }\n    });\n    const hasAccess = user?.subscriptions.some(\n      (sub) => sub.plan.features.some((feature) => feature.featureId === \"resume_analysis\")\n    );\n    if (!hasAccess && process.env.NODE_ENV === \"production\") {\n      return json({ error: \"Feature not available in your plan\" }, { status: 403 });\n    }\n    let analysis = await prisma.jobSpecificATSAnalysis.findFirst({\n      where: {\n        atsAnalysisId: `ats-${resumeId}`,\n        jobId\n      }\n    });\n    if (!analysis) {\n      const mockAnalysis = generateMockJobSpecificATSAnalysis(job.title);\n      let baseAnalysis = await prisma.atsAnalysis.findUnique({\n        where: { resumeId }\n      });\n      if (!baseAnalysis) {\n        const mockBaseAnalysis = generateMockBaseATSAnalysis();\n        baseAnalysis = await prisma.atsAnalysis.create({\n          data: {\n            id: `ats-${resumeId}`,\n            resumeId,\n            overallScore: mockBaseAnalysis.overallScore,\n            keywordScore: mockBaseAnalysis.keywordScore,\n            formatScore: mockBaseAnalysis.formatScore,\n            contentScore: mockBaseAnalysis.contentScore,\n            readabilityScore: mockBaseAnalysis.readabilityScore,\n            detectedIssues: mockBaseAnalysis.detectedIssues,\n            suggestedKeywords: mockBaseAnalysis.suggestedKeywords,\n            createdAt: /* @__PURE__ */ new Date(),\n            updatedAt: /* @__PURE__ */ new Date()\n          }\n        });\n      }\n      analysis = await prisma.jobSpecificATSAnalysis.create({\n        data: {\n          id: `job-ats-${Date.now()}`,\n          atsAnalysisId: baseAnalysis.id,\n          jobId,\n          jobTitle: job.title,\n          jobDescription: job.description || \"\",\n          overallScore: mockAnalysis.overallScore,\n          keywordScore: mockAnalysis.keywordScore,\n          formatScore: mockAnalysis.formatScore,\n          contentScore: mockAnalysis.contentScore,\n          readabilityScore: mockAnalysis.readabilityScore,\n          matchPercentage: mockAnalysis.matchPercentage,\n          detectedIssues: mockAnalysis.detectedIssues,\n          suggestedKeywords: mockAnalysis.suggestedKeywords,\n          recommendations: mockAnalysis.recommendations,\n          createdAt: /* @__PURE__ */ new Date(),\n          updatedAt: /* @__PURE__ */ new Date()\n        }\n      });\n    }\n    return json({ success: true, analysis });\n  } catch (error) {\n    console.error(\"Error getting job-specific ATS analysis:\", error);\n    return json({ error: \"Internal server error\" }, { status: 500 });\n  }\n};\nfunction generateMockBaseATSAnalysis() {\n  return {\n    overallScore: 75,\n    keywordScore: 80,\n    formatScore: 70,\n    contentScore: 85,\n    readabilityScore: 65,\n    detectedIssues: [\n      { section: \"format\", message: \"Resume exceeds one page\", severity: \"medium\" },\n      { section: \"content\", message: \"Summary section is too generic\", severity: \"medium\" },\n      { section: \"readability\", message: \"Some sentences are too long\", severity: \"low\" }\n    ],\n    suggestedKeywords: [\n      \"JavaScript\",\n      \"React\",\n      \"Node.js\",\n      \"TypeScript\",\n      \"REST API\",\n      \"GraphQL\",\n      \"CI/CD\",\n      \"Agile\",\n      \"AWS\",\n      \"Docker\"\n    ]\n  };\n}\nfunction generateMockJobSpecificATSAnalysis(jobTitle) {\n  let matchPercentage = 70;\n  let keywordScore = 75;\n  if (jobTitle.toLowerCase().includes(\"senior\")) {\n    matchPercentage = 65;\n    keywordScore = 70;\n  } else if (jobTitle.toLowerCase().includes(\"junior\")) {\n    matchPercentage = 80;\n    keywordScore = 85;\n  }\n  return {\n    overallScore: 75,\n    keywordScore,\n    formatScore: 70,\n    contentScore: 85,\n    readabilityScore: 65,\n    matchPercentage,\n    detectedIssues: [\n      { section: \"format\", message: \"Resume exceeds one page\", severity: \"medium\" },\n      { section: \"content\", message: \"Summary section is too generic\", severity: \"medium\" },\n      { section: \"readability\", message: \"Some sentences are too long\", severity: \"low\" },\n      { section: \"match\", message: `Missing key skills required for ${jobTitle}`, severity: \"high\" }\n    ],\n    suggestedKeywords: [\n      \"JavaScript\",\n      \"React\",\n      \"Node.js\",\n      \"TypeScript\",\n      \"REST API\",\n      \"GraphQL\",\n      \"CI/CD\",\n      \"Agile\",\n      \"AWS\",\n      \"Docker\"\n    ],\n    recommendations: [\n      `Highlight your experience with technologies mentioned in the ${jobTitle} job description`,\n      \"Add more quantifiable achievements to demonstrate impact\",\n      \"Include specific projects relevant to the role\",\n      \"Tailor your summary to match the job requirements\"\n    ]\n  };\n}\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;AAGK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AAC7C,EAAE,IAAI;AACN,IAAI,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AAC3C,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7D;AACA,IAAI,MAAM,SAAS,GAAG,kBAAkB,CAAC,KAAK,CAAC;AAC/C,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE;AACrC,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9D;AACA,IAAI,MAAM,MAAM,GAAG,SAAS,CAAC,EAAE;AAC/B,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,IAAI;AACpC,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,EAAE;AAC7B,MAAM,OAAO,IAAI;AACjB,QAAQ,EAAE,KAAK,EAAE,yBAAyB,EAAE,QAAQ,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE;AAC7E,QAAQ,EAAE,MAAM,EAAE,GAAG;AACrB,OAAO;AACP;AACA,IAAI,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC;AACjD,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE,QAAQ;AACpB,QAAQ,QAAQ,EAAE;AAClB,UAAU;AACV;AACA;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mCAAmC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClF;AACA,IAAI,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC;AAC5C,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,KAAK;AACxB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,GAAG,EAAE;AACd,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9D;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAC9C,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;AAC3B,MAAM,OAAO,EAAE;AACf,QAAQ,aAAa,EAAE;AACvB,UAAU,KAAK,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE;AACrC,UAAU,OAAO,EAAE;AACnB,YAAY,IAAI,EAAE;AAClB,cAAc,OAAO,EAAE;AACvB,gBAAgB,QAAQ,EAAE;AAC1B,kBAAkB,KAAK,EAAE,EAAE,SAAS,EAAE,iBAAiB;AACvD;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,CAAC;AACN,IAAI,MAAM,SAAS,GAAG,IAAI,EAAE,aAAa,CAAC,IAAI;AAC9C,MAAM,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,SAAS,KAAK,iBAAiB;AAC1F,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE;AAC7D,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oCAAoC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnF;AACA,IAAI,IAAI,QAAQ,GAAG,MAAM,MAAM,CAAC,sBAAsB,CAAC,SAAS,CAAC;AACjE,MAAM,KAAK,EAAE;AACb,QAAQ,aAAa,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AACxC,QAAQ;AACR;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,MAAM,YAAY,GAAG,kCAAkC,CAAC,GAAG,CAAC,KAAK,CAAC;AACxE,MAAM,IAAI,YAAY,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;AAC7D,QAAQ,KAAK,EAAE,EAAE,QAAQ;AACzB,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,YAAY,EAAE;AACzB,QAAQ,MAAM,gBAAgB,GAAG,2BAA2B,EAAE;AAC9D,QAAQ,YAAY,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;AACvD,UAAU,IAAI,EAAE;AAChB,YAAY,EAAE,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AACjC,YAAY,QAAQ;AACpB,YAAY,YAAY,EAAE,gBAAgB,CAAC,YAAY;AACvD,YAAY,YAAY,EAAE,gBAAgB,CAAC,YAAY;AACvD,YAAY,WAAW,EAAE,gBAAgB,CAAC,WAAW;AACrD,YAAY,YAAY,EAAE,gBAAgB,CAAC,YAAY;AACvD,YAAY,gBAAgB,EAAE,gBAAgB,CAAC,gBAAgB;AAC/D,YAAY,cAAc,EAAE,gBAAgB,CAAC,cAAc;AAC3D,YAAY,iBAAiB,EAAE,gBAAgB,CAAC,iBAAiB;AACjE,YAAY,SAAS,kBAAkB,IAAI,IAAI,EAAE;AACjD,YAAY,SAAS,kBAAkB,IAAI,IAAI;AAC/C;AACA,SAAS,CAAC;AACV;AACA,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,sBAAsB,CAAC,MAAM,CAAC;AAC5D,QAAQ,IAAI,EAAE;AACd,UAAU,EAAE,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;AACrC,UAAU,aAAa,EAAE,YAAY,CAAC,EAAE;AACxC,UAAU,KAAK;AACf,UAAU,QAAQ,EAAE,GAAG,CAAC,KAAK;AAC7B,UAAU,cAAc,EAAE,GAAG,CAAC,WAAW,IAAI,EAAE;AAC/C,UAAU,YAAY,EAAE,YAAY,CAAC,YAAY;AACjD,UAAU,YAAY,EAAE,YAAY,CAAC,YAAY;AACjD,UAAU,WAAW,EAAE,YAAY,CAAC,WAAW;AAC/C,UAAU,YAAY,EAAE,YAAY,CAAC,YAAY;AACjD,UAAU,gBAAgB,EAAE,YAAY,CAAC,gBAAgB;AACzD,UAAU,eAAe,EAAE,YAAY,CAAC,eAAe;AACvD,UAAU,cAAc,EAAE,YAAY,CAAC,cAAc;AACrD,UAAU,iBAAiB,EAAE,YAAY,CAAC,iBAAiB;AAC3D,UAAU,eAAe,EAAE,YAAY,CAAC,eAAe;AACvD,UAAU,SAAS,kBAAkB,IAAI,IAAI,EAAE;AAC/C,UAAU,SAAS,kBAAkB,IAAI,IAAI;AAC7C;AACA,OAAO,CAAC;AACR;AACA,IAAI,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;AAC5C,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC;AACpE,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpE;AACA;AACA,SAAS,2BAA2B,GAAG;AACvC,EAAE,OAAO;AACT,IAAI,YAAY,EAAE,EAAE;AACpB,IAAI,YAAY,EAAE,EAAE;AACpB,IAAI,WAAW,EAAE,EAAE;AACnB,IAAI,YAAY,EAAE,EAAE;AACpB,IAAI,gBAAgB,EAAE,EAAE;AACxB,IAAI,cAAc,EAAE;AACpB,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,yBAAyB,EAAE,QAAQ,EAAE,QAAQ,EAAE;AACnF,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,gCAAgC,EAAE,QAAQ,EAAE,QAAQ,EAAE;AAC3F,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,OAAO,EAAE,6BAA6B,EAAE,QAAQ,EAAE,KAAK;AACvF,KAAK;AACL,IAAI,iBAAiB,EAAE;AACvB,MAAM,YAAY;AAClB,MAAM,OAAO;AACb,MAAM,SAAS;AACf,MAAM,YAAY;AAClB,MAAM,UAAU;AAChB,MAAM,SAAS;AACf,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,KAAK;AACX,MAAM;AACN;AACA,GAAG;AACH;AACA,SAAS,kCAAkC,CAAC,QAAQ,EAAE;AACtD,EAAE,IAAI,eAAe,GAAG,EAAE;AAC1B,EAAE,IAAI,YAAY,GAAG,EAAE;AACvB,EAAE,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;AACjD,IAAI,eAAe,GAAG,EAAE;AACxB,IAAI,YAAY,GAAG,EAAE;AACrB,GAAG,MAAM,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;AACxD,IAAI,eAAe,GAAG,EAAE;AACxB,IAAI,YAAY,GAAG,EAAE;AACrB;AACA,EAAE,OAAO;AACT,IAAI,YAAY,EAAE,EAAE;AACpB,IAAI,YAAY;AAChB,IAAI,WAAW,EAAE,EAAE;AACnB,IAAI,YAAY,EAAE,EAAE;AACpB,IAAI,gBAAgB,EAAE,EAAE;AACxB,IAAI,eAAe;AACnB,IAAI,cAAc,EAAE;AACpB,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,yBAAyB,EAAE,QAAQ,EAAE,QAAQ,EAAE;AACnF,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,gCAAgC,EAAE,QAAQ,EAAE,QAAQ,EAAE;AAC3F,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,OAAO,EAAE,6BAA6B,EAAE,QAAQ,EAAE,KAAK,EAAE;AACzF,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,gCAAgC,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,EAAE,MAAM;AAClG,KAAK;AACL,IAAI,iBAAiB,EAAE;AACvB,MAAM,YAAY;AAClB,MAAM,OAAO;AACb,MAAM,SAAS;AACf,MAAM,YAAY;AAClB,MAAM,UAAU;AAChB,MAAM,SAAS;AACf,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,KAAK;AACX,MAAM;AACN,KAAK;AACL,IAAI,eAAe,EAAE;AACrB,MAAM,CAAC,6DAA6D,EAAE,QAAQ,CAAC,gBAAgB,CAAC;AAChG,MAAM,0DAA0D;AAChE,MAAM,gDAAgD;AACtD,MAAM;AACN;AACA,GAAG;AACH;;;;"}