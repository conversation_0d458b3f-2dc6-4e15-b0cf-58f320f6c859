{"version": 3, "file": "_page.svelte-BlAShcT0.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/auth/sign-up/_page.svelte.js"], "sourcesContent": ["import { O as copy_payload, P as assign_payload, y as pop, w as push, W as stringify, R as attr, S as attr_class, V as escape_html } from \"../../../../chunks/index3.js\";\nimport \"../../../../chunks/client.js\";\nimport { B as Button } from \"../../../../chunks/button.js\";\nimport { I as Input } from \"../../../../chunks/input.js\";\nimport { C as Checkbox } from \"../../../../chunks/checkbox.js\";\nimport \"../../../../chunks/Toaster.svelte_svelte_type_style_lang.js\";\nimport { S as SEO } from \"../../../../chunks/SEO.js\";\nfunction _page($$payload, $$props) {\n  push();\n  let email = \"\";\n  let password = \"\";\n  let confirmPassword = \"\";\n  let isLoading = false;\n  let termsAccepted = false;\n  let referralCode = \"\";\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    SEO($$payload2, {\n      title: \"Sign Up | Hirli\",\n      description: \"Create your Hirli account to automate your job applications and streamline your job search process.\",\n      keywords: \"sign up, create account, job application, career, automation, Hirli account\"\n    });\n    $$payload2.out += `<!----> <div class=\"mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]\"><div class=\"mb-8 flex flex-col space-y-2 text-left\"><h1 class=\"text-3xl font-semibold tracking-tight\">Sign up</h1> <p class=\"text-muted-foreground text-md font-light\">Already have an account? <a class=\"underline\" href=\"/auth/sign-in\">Sign in</a></p></div> `;\n    {\n      $$payload2.out += \"<!--[-->\";\n      $$payload2.out += `<div class=\"grid gap-6\"><div class=\"flex flex-col items-center justify-center gap-4\">`;\n      Button($$payload2, {\n        type: \"button\",\n        class: \"focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex w-full items-center justify-center whitespace-nowrap rounded-md border p-4 text-sm font-medium shadow-sm transition-colors disabled:pointer-events-none disabled:opacity-50\",\n        children: ($$payload3) => {\n          $$payload3.out += `<img alt=\"Google\" src=\"/assets/svg/google.svg\" class=\"mr-2\"/> Sign up with Google`;\n        },\n        $$slots: { default: true }\n      });\n      $$payload2.out += `<!----> `;\n      Button($$payload2, {\n        type: \"button\",\n        class: \"focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex w-full items-center justify-center whitespace-nowrap rounded-md border p-4 text-sm font-medium shadow-sm transition-colors disabled:pointer-events-none disabled:opacity-50\",\n        children: ($$payload3) => {\n          $$payload3.out += `<img alt=\"LinkedIn\" src=\"/assets/svg/linkedin.svg\" class=\"mr-2\"/> Sign up with LinkedIn`;\n        },\n        $$slots: { default: true }\n      });\n      $$payload2.out += `<!----></div> <div class=\"relative\"><div class=\"absolute inset-0 flex items-center\"><span class=\"border-border w-full border-2 border-t\"></span></div> <div class=\"relative z-10 flex justify-center text-xs uppercase\"><span class=\"text-muted-foreground bg-background px-2\">Or</span></div></div></div> <form class=\"grid gap-6\"><div class=\"grid gap-2\">`;\n      Input($$payload2, {\n        id: \"email\",\n        type: \"email\",\n        required: true,\n        placeholder: \"<EMAIL>\",\n        class: \"border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm\",\n        get value() {\n          return email;\n        },\n        set value($$value) {\n          email = $$value;\n          $$settled = false;\n        }\n      });\n      $$payload2.out += `<!----> `;\n      {\n        $$payload2.out += \"<!--[!-->\";\n      }\n      $$payload2.out += `<!--]--></div> <div class=\"relative\">`;\n      Input($$payload2, {\n        id: \"password\",\n        type: \"password\",\n        placeholder: \"Password\",\n        disabled: isLoading,\n        class: `border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border ${stringify(\"border-destructive\")} bg-transparent px-3 py-1 text-sm shadow-sm`,\n        get value() {\n          return password;\n        },\n        set value($$value) {\n          password = $$value;\n          $$settled = false;\n        }\n      });\n      $$payload2.out += `<!----> <button type=\"button\" class=\"absolute right-0 top-0.5 p-2\"${attr(\"aria-label\", \"Show password\")}><i${attr_class(`fa-solid ${\"fa-eye\"}`)}></i></button></div> `;\n      {\n        $$payload2.out += \"<!--[!-->\";\n      }\n      $$payload2.out += `<!--]--> <div class=\"relative\">`;\n      Input($$payload2, {\n        id: \"confirm-password\",\n        type: \"password\",\n        placeholder: \"Confirm Password\",\n        disabled: isLoading,\n        class: `border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border ${stringify(\"border-destructive\")} bg-transparent px-3 py-1 text-sm shadow-sm`,\n        get value() {\n          return confirmPassword;\n        },\n        set value($$value) {\n          confirmPassword = $$value;\n          $$settled = false;\n        }\n      });\n      $$payload2.out += `<!----> <button type=\"button\" class=\"absolute right-0 top-0.5 p-2\"${attr(\"aria-label\", \"Show confirm password\")}><i${attr_class(`fa-solid ${\"fa-eye\"}`)}></i></button></div> `;\n      if (confirmPassword !== \"\") {\n        $$payload2.out += \"<!--[-->\";\n        $$payload2.out += `<p class=\"text-destructive text-sm\">Passwords do not match.</p>`;\n      } else {\n        $$payload2.out += \"<!--[!-->\";\n      }\n      $$payload2.out += `<!--]--> <div class=\"grid gap-2\"><label for=\"referralCode\" class=\"text-sm font-medium\">Referral Code <span class=\"text-muted-foreground\">(Optional)</span></label> `;\n      Input($$payload2, {\n        id: \"referralCode\",\n        type: \"text\",\n        placeholder: \"Enter referral code\",\n        class: \"border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm\",\n        get value() {\n          return referralCode;\n        },\n        set value($$value) {\n          referralCode = $$value;\n          $$settled = false;\n        }\n      });\n      $$payload2.out += `<!----> `;\n      {\n        $$payload2.out += \"<!--[!-->\";\n      }\n      $$payload2.out += `<!--]--> `;\n      {\n        $$payload2.out += \"<!--[!-->\";\n      }\n      $$payload2.out += `<!--]--></div> <div class=\"flex items-center space-x-4\">`;\n      Checkbox($$payload2, {\n        get checked() {\n          return termsAccepted;\n        },\n        set checked($$value) {\n          termsAccepted = $$value;\n          $$settled = false;\n        }\n      });\n      $$payload2.out += `<!----> <label for=\"terms\" class=\"text-sm\">Agree to our <a href=\"/legal/terms\" class=\"hover:text-primary underline underline-offset-4\">Terms of Service</a> and <a href=\"/legal/privacy\" class=\"hover:text-primary underline underline-offset-4\">Privacy Policy</a>.</label></div> `;\n      Button($$payload2, {\n        class: \"focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-9 items-center justify-center whitespace-nowrap rounded-md border px-4 py-2 text-sm font-medium shadow-sm transition-colors disabled:pointer-events-none disabled:opacity-50\",\n        type: \"submit\",\n        disabled: true,\n        children: ($$payload3) => {\n          $$payload3.out += `<!---->${escape_html(\"Sign Up\")}`;\n        },\n        $$slots: { default: true }\n      });\n      $$payload2.out += `<!----></form>`;\n    }\n    $$payload2.out += `<!--]--></div>`;\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAOA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,KAAK,GAAG,EAAE;AAChB,EAAE,IAAI,QAAQ,GAAG,EAAE;AACnB,EAAE,IAAI,eAAe,GAAG,EAAE;AAC1B,EAAE,IAAI,SAAS,GAAG,KAAK;AACvB,EAAE,IAAI,aAAa,GAAG,KAAK;AAC3B,EAAE,IAAI,YAAY,GAAG,EAAE;AACvB,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,GAAG,CAAC,UAAU,EAAE;AACpB,MAAM,KAAK,EAAE,iBAAiB;AAC9B,MAAM,WAAW,EAAE,qGAAqG;AACxH,MAAM,QAAQ,EAAE;AAChB,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,wVAAwV,CAAC;AAChX,IAAI;AACJ,MAAM,UAAU,CAAC,GAAG,IAAI,UAAU;AAClC,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,qFAAqF,CAAC;AAC/G,MAAM,MAAM,CAAC,UAAU,EAAE;AACzB,QAAQ,IAAI,EAAE,QAAQ;AACtB,QAAQ,KAAK,EAAE,yRAAyR;AACxS,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,iFAAiF,CAAC;AAC/G,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC,OAAO,CAAC;AACR,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAClC,MAAM,MAAM,CAAC,UAAU,EAAE;AACzB,QAAQ,IAAI,EAAE,QAAQ;AACtB,QAAQ,KAAK,EAAE,yRAAyR;AACxS,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,uFAAuF,CAAC;AACrH,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC,OAAO,CAAC;AACR,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,4VAA4V,CAAC;AACtX,MAAM,KAAK,CAAC,UAAU,EAAE;AACxB,QAAQ,EAAE,EAAE,OAAO;AACnB,QAAQ,IAAI,EAAE,OAAO;AACrB,QAAQ,QAAQ,EAAE,IAAI;AACtB,QAAQ,WAAW,EAAE,kBAAkB;AACvC,QAAQ,KAAK,EAAE,qJAAqJ;AACpK,QAAQ,IAAI,KAAK,GAAG;AACpB,UAAU,OAAO,KAAK;AACtB,SAAS;AACT,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE;AAC3B,UAAU,KAAK,GAAG,OAAO;AACzB,UAAU,SAAS,GAAG,KAAK;AAC3B;AACA,OAAO,CAAC;AACR,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAClC,MAAM;AACN,QAAQ,UAAU,CAAC,GAAG,IAAI,WAAW;AACrC;AACA,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,qCAAqC,CAAC;AAC/D,MAAM,KAAK,CAAC,UAAU,EAAE;AACxB,QAAQ,EAAE,EAAE,UAAU;AACtB,QAAQ,IAAI,EAAE,UAAU;AACxB,QAAQ,WAAW,EAAE,UAAU;AAC/B,QAAQ,QAAQ,EAAE,SAAS;AAC3B,QAAQ,KAAK,EAAE,CAAC,yGAAyG,EAAE,SAAS,CAAC,oBAAoB,CAAC,CAAC,2CAA2C,CAAC;AACvM,QAAQ,IAAI,KAAK,GAAG;AACpB,UAAU,OAAO,QAAQ;AACzB,SAAS;AACT,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE;AAC3B,UAAU,QAAQ,GAAG,OAAO;AAC5B,UAAU,SAAS,GAAG,KAAK;AAC3B;AACA,OAAO,CAAC;AACR,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,kEAAkE,EAAE,IAAI,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC;AAC/L,MAAM;AACN,QAAQ,UAAU,CAAC,GAAG,IAAI,WAAW;AACrC;AACA,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,+BAA+B,CAAC;AACzD,MAAM,KAAK,CAAC,UAAU,EAAE;AACxB,QAAQ,EAAE,EAAE,kBAAkB;AAC9B,QAAQ,IAAI,EAAE,UAAU;AACxB,QAAQ,WAAW,EAAE,kBAAkB;AACvC,QAAQ,QAAQ,EAAE,SAAS;AAC3B,QAAQ,KAAK,EAAE,CAAC,yGAAyG,EAAE,SAAS,CAAC,oBAAoB,CAAC,CAAC,2CAA2C,CAAC;AACvM,QAAQ,IAAI,KAAK,GAAG;AACpB,UAAU,OAAO,eAAe;AAChC,SAAS;AACT,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE;AAC3B,UAAU,eAAe,GAAG,OAAO;AACnC,UAAU,SAAS,GAAG,KAAK;AAC3B;AACA,OAAO,CAAC;AACR,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,kEAAkE,EAAE,IAAI,CAAC,YAAY,EAAE,uBAAuB,CAAC,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC;AACvM,MAAM,IAAI,eAAe,KAAK,EAAE,EAAE;AAClC,QAAQ,UAAU,CAAC,GAAG,IAAI,UAAU;AACpC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,+DAA+D,CAAC;AAC3F,OAAO,MAAM;AACb,QAAQ,UAAU,CAAC,GAAG,IAAI,WAAW;AACrC;AACA,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,mKAAmK,CAAC;AAC7L,MAAM,KAAK,CAAC,UAAU,EAAE;AACxB,QAAQ,EAAE,EAAE,cAAc;AAC1B,QAAQ,IAAI,EAAE,MAAM;AACpB,QAAQ,WAAW,EAAE,qBAAqB;AAC1C,QAAQ,KAAK,EAAE,qJAAqJ;AACpK,QAAQ,IAAI,KAAK,GAAG;AACpB,UAAU,OAAO,YAAY;AAC7B,SAAS;AACT,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE;AAC3B,UAAU,YAAY,GAAG,OAAO;AAChC,UAAU,SAAS,GAAG,KAAK;AAC3B;AACA,OAAO,CAAC;AACR,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAClC,MAAM;AACN,QAAQ,UAAU,CAAC,GAAG,IAAI,WAAW;AACrC;AACA,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AACnC,MAAM;AACN,QAAQ,UAAU,CAAC,GAAG,IAAI,WAAW;AACrC;AACA,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,wDAAwD,CAAC;AAClF,MAAM,QAAQ,CAAC,UAAU,EAAE;AAC3B,QAAQ,IAAI,OAAO,GAAG;AACtB,UAAU,OAAO,aAAa;AAC9B,SAAS;AACT,QAAQ,IAAI,OAAO,CAAC,OAAO,EAAE;AAC7B,UAAU,aAAa,GAAG,OAAO;AACjC,UAAU,SAAS,GAAG,KAAK;AAC3B;AACA,OAAO,CAAC;AACR,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,mRAAmR,CAAC;AAC7S,MAAM,MAAM,CAAC,UAAU,EAAE;AACzB,QAAQ,KAAK,EAAE,4RAA4R;AAC3S,QAAQ,IAAI,EAAE,QAAQ;AACtB,QAAQ,QAAQ,EAAE,IAAI;AACtB,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC;AAC9D,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC,OAAO,CAAC;AACR,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACxC;AACA,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACtC;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,GAAG,EAAE;AACP;;;;"}