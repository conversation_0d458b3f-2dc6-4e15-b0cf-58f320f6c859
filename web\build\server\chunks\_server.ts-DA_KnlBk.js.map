{"version": 3, "file": "_server.ts-DA_KnlBk.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/metrics/_service_/history/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { l as logger } from \"../../../../../../chunks/logger.js\";\nfunction generateMockHistoryData(service, days = 30) {\n  const data = [];\n  const now = /* @__PURE__ */ new Date();\n  const baseMetrics = {\n    \"resume-builder\": {\n      successRate: 98,\n      responseTime: 2500,\n      dailyUsage: 120\n    },\n    \"resume-scanner\": {\n      successRate: 96,\n      responseTime: 1800,\n      dailyUsage: 85\n    },\n    \"job-search\": {\n      successRate: 99,\n      responseTime: 900,\n      dailyUsage: 350\n    },\n    \"application-system\": {\n      successRate: 97,\n      responseTime: 3200,\n      dailyUsage: 180\n    },\n    \"account-services\": {\n      successRate: 99.5,\n      responseTime: 300,\n      dailyUsage: 850\n    }\n  };\n  const metrics = baseMetrics[service] || {\n    successRate: 99,\n    responseTime: 500,\n    dailyUsage: 100\n  };\n  for (let i = 0; i < days; i++) {\n    const date = new Date(now);\n    date.setDate(date.getDate() - (days - i - 1));\n    const variation = Math.random() * 0.1 - 0.05;\n    const statusVariation = Math.random() > 0.9 ? 0.85 : 1;\n    data.push({\n      date: date.toISOString().split(\"T\")[0],\n      successRate: Math.min(100, Math.max(80, metrics.successRate * (1 + variation * 0.5) * statusVariation)),\n      responseTime: Math.max(100, metrics.responseTime * (1 + variation)),\n      dailyUsage: Math.round(metrics.dailyUsage * (1 + variation * 2)),\n      status: statusVariation < 1 ? \"degraded\" : \"operational\"\n    });\n  }\n  return data;\n}\nconst GET = async ({ params, url }) => {\n  try {\n    const service = params.service;\n    const days = parseInt(url.searchParams.get(\"days\") || \"30\");\n    const limitedDays = Math.min(90, Math.max(7, days));\n    const historyData = generateMockHistoryData(service, limitedDays);\n    return json({\n      service,\n      days: limitedDays,\n      data: historyData,\n      timestamp: (/* @__PURE__ */ new Date()).toISOString()\n    });\n  } catch (error) {\n    logger.error(`Error fetching metrics history for ${params.service}:`, error);\n    return json(\n      {\n        error: `Failed to fetch metrics history for ${params.service}`,\n        timestamp: (/* @__PURE__ */ new Date()).toISOString()\n      },\n      { status: 500 }\n    );\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;AAEA,SAAS,uBAAuB,CAAC,OAAO,EAAE,IAAI,GAAG,EAAE,EAAE;AACrD,EAAE,MAAM,IAAI,GAAG,EAAE;AACjB,EAAE,MAAM,GAAG,mBAAmB,IAAI,IAAI,EAAE;AACxC,EAAE,MAAM,WAAW,GAAG;AACtB,IAAI,gBAAgB,EAAE;AACtB,MAAM,WAAW,EAAE,EAAE;AACrB,MAAM,YAAY,EAAE,IAAI;AACxB,MAAM,UAAU,EAAE;AAClB,KAAK;AACL,IAAI,gBAAgB,EAAE;AACtB,MAAM,WAAW,EAAE,EAAE;AACrB,MAAM,YAAY,EAAE,IAAI;AACxB,MAAM,UAAU,EAAE;AAClB,KAAK;AACL,IAAI,YAAY,EAAE;AAClB,MAAM,WAAW,EAAE,EAAE;AACrB,MAAM,YAAY,EAAE,GAAG;AACvB,MAAM,UAAU,EAAE;AAClB,KAAK;AACL,IAAI,oBAAoB,EAAE;AAC1B,MAAM,WAAW,EAAE,EAAE;AACrB,MAAM,YAAY,EAAE,IAAI;AACxB,MAAM,UAAU,EAAE;AAClB,KAAK;AACL,IAAI,kBAAkB,EAAE;AACxB,MAAM,WAAW,EAAE,IAAI;AACvB,MAAM,YAAY,EAAE,GAAG;AACvB,MAAM,UAAU,EAAE;AAClB;AACA,GAAG;AACH,EAAE,MAAM,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI;AAC1C,IAAI,WAAW,EAAE,EAAE;AACnB,IAAI,YAAY,EAAE,GAAG;AACrB,IAAI,UAAU,EAAE;AAChB,GAAG;AACH,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;AACjC,IAAI,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC;AAC9B,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACjD,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,IAAI;AAChD,IAAI,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,IAAI,GAAG,CAAC;AAC1D,IAAI,IAAI,CAAC,IAAI,CAAC;AACd,MAAM,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC5C,MAAM,WAAW,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,WAAW,IAAI,CAAC,GAAG,SAAS,GAAG,GAAG,CAAC,GAAG,eAAe,CAAC,CAAC;AAC7G,MAAM,YAAY,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,YAAY,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC;AACzE,MAAM,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,IAAI,CAAC,GAAG,SAAS,GAAG,CAAC,CAAC,CAAC;AACtE,MAAM,MAAM,EAAE,eAAe,GAAG,CAAC,GAAG,UAAU,GAAG;AACjD,KAAK,CAAC;AACN;AACA,EAAE,OAAO,IAAI;AACb;AACK,MAAC,GAAG,GAAG,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK;AACvC,EAAE,IAAI;AACN,IAAI,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO;AAClC,IAAI,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;AAC/D,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AACvD,IAAI,MAAM,WAAW,GAAG,uBAAuB,CAAC,OAAO,EAAE,WAAW,CAAC;AACrE,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO;AACb,MAAM,IAAI,EAAE,WAAW;AACvB,MAAM,IAAI,EAAE,WAAW;AACvB,MAAM,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AACzD,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,mCAAmC,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;AAChF,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,KAAK,EAAE,CAAC,oCAAoC,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;AACtE,QAAQ,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC3D,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;;;;"}