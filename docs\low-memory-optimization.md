# Low Memory Optimization for 512MB Systems

## Overview
This document outlines the changes made to optimize the application for systems with limited memory (512MB or less). The primary goal is to prevent the 99-100% memory usage that was causing system instability.

## Changes Made

### 1. HTML Preview Disabled
**Files Modified:**
- `scraper/lib/search/scrapJobDetailFromUrl.ts`
- `cron/lib/search/scrapJobDetailFromUrl.ts`

**Changes:**
- Added `DISABLE_HTML_PREVIEWS` environment variable check
- HTML content extraction is now skipped when this flag is set to `true`
- Reduces memory usage by avoiding large HTML string storage

### 2. Circuit Breaker Thresholds Reduced
**File Modified:** `cron/config.ts`

**Changes:**
- Memory threshold: 75% → 50%
- CPU threshold: 75% → 50%
- Degraded memory threshold: 60% → 30%
- Degraded CPU threshold: 60% → 30%

**Impact:** More aggressive resource management to prevent system overload

### 3. Job Processing Limits Reduced
**Files Modified:**
- `cron/config.ts`
- `cron/jobs/enrichJobDetails.ts`
- `scraper/config.ts`

**Changes:**
- Batch size: 100 → 5-10
- Concurrency: 2 → 1
- Max memory threshold: 1500MB → 400MB
- Warning memory threshold: 1000MB → 300MB
- Max jobs per run: 200-1000 → 20-50

### 4. Environment Configuration
**Files Created:**
- `cron/.env.low-memory` - Template configuration for low-memory systems
- `cron/.env` - Applied configuration
- `scripts/apply-low-memory-config.sh` - Linux/Mac setup script
- `scripts/apply-low-memory-config.ps1` - Windows setup script

## Key Environment Variables

```bash
# Circuit Breaker
CIRCUIT_BREAKER_MEMORY_THRESHOLD=50
CIRCUIT_BREAKER_CPU_THRESHOLD=50
CIRCUIT_BREAKER_DEGRADED_MEMORY_THRESHOLD=30
CIRCUIT_BREAKER_DEGRADED_CPU_THRESHOLD=30

# Job Processing
ENRICH_JOBS_BATCH_SIZE=5
ENRICH_JOBS_CONCURRENCY=1
ENRICH_JOBS_MAX_MEMORY=400
ENRICH_JOBS_WARNING_MEMORY=300

# Resource Intensive Features
DISABLE_HTML_PREVIEWS=true
DISABLE_SCREENSHOTS=true
DISABLE_FILE_STORAGE=true

# Node.js Memory Limit
NODE_OPTIONS="--max-old-space-size=400"
```

## Expected Results

1. **Reduced Memory Usage:** HTML previews and large content extraction eliminated
2. **Better Resource Management:** Lower thresholds trigger circuit breaker earlier
3. **Smaller Processing Batches:** Reduced memory spikes during job processing
4. **Single-threaded Processing:** Eliminates memory contention between concurrent processes

## Monitoring

Watch for these improvements in the logs:
- Memory usage should stay below 50% most of the time
- Circuit breaker should trigger less frequently
- "HTML Preview disabled to save memory" messages instead of large HTML dumps
- Smaller batch processing messages

## Reverting Changes

To revert to higher-performance settings for systems with more memory:
1. Delete or rename `cron/.env`
2. The application will fall back to default values in the code
3. Or create a new `.env` file with higher thresholds

## Performance Trade-offs

- **Slower Processing:** Smaller batches and single-threaded processing will be slower
- **Less Debugging Info:** HTML previews disabled reduces troubleshooting capabilities
- **More Conservative:** System will pause processing more frequently to preserve stability

These trade-offs are necessary for system stability on memory-constrained environments.
