{"version": 3, "file": "_server.ts-Ce6WAvjv.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/resume/_id_/download/_server.ts.js"], "sourcesContent": ["import { p as prisma } from \"../../../../../../chunks/prisma.js\";\nconst GET = async ({ params, locals }) => {\n  try {\n    console.log(\"Downloading resume with ID:\", params.id);\n    const resume = await prisma.resume.findUnique({\n      where: { id: params.id },\n      include: {\n        document: true\n      }\n    });\n    if (!resume) {\n      return new Response(\"Resume not found\", { status: 404 });\n    }\n    const pdfContent = \"Placeholder PDF content\";\n    const fileName = `${resume.document.label || \"resume\"}.pdf`.replace(/[^a-zA-Z0-9_\\-\\.]/g, \"_\");\n    const fileUrl = `/uploads/generated/${fileName}`;\n    await prisma.document.update({\n      where: { id: resume.document.id },\n      data: {\n        fileUrl,\n        fileName,\n        filePath: fileUrl,\n        updatedAt: /* @__PURE__ */ new Date()\n      }\n    });\n    console.log(`Updated document ${resume.document.id} with fileUrl: ${fileUrl}`);\n    const headers = new Headers();\n    headers.append(\"Content-Disposition\", `attachment; filename=\"${fileName}\"`);\n    headers.append(\"Content-Type\", \"application/pdf\");\n    return new Response(pdfContent, { headers });\n  } catch (error) {\n    console.error(\"Error downloading resume:\", error);\n    return new Response(\"Error downloading resume\", { status: 500 });\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;AACK,MAAC,GAAG,GAAG,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK;AAC1C,EAAE,IAAI;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,MAAM,CAAC,EAAE,CAAC;AACzD,IAAI,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;AAClD,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE;AAC9B,MAAM,OAAO,EAAE;AACf,QAAQ,QAAQ,EAAE;AAClB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,OAAO,IAAI,QAAQ,CAAC,kBAAkB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9D;AACA,IAAI,MAAM,UAAU,GAAG,yBAAyB;AAChD,IAAI,MAAM,QAAQ,GAAG,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,oBAAoB,EAAE,GAAG,CAAC;AAClG,IAAI,MAAM,OAAO,GAAG,CAAC,mBAAmB,EAAE,QAAQ,CAAC,CAAC;AACpD,IAAI,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;AACjC,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,QAAQ,CAAC,EAAE,EAAE;AACvC,MAAM,IAAI,EAAE;AACZ,QAAQ,OAAO;AACf,QAAQ,QAAQ;AAChB,QAAQ,QAAQ,EAAE,OAAO;AACzB,QAAQ,SAAS,kBAAkB,IAAI,IAAI;AAC3C;AACA,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC,CAAC;AAClF,IAAI,MAAM,OAAO,GAAG,IAAI,OAAO,EAAE;AACjC,IAAI,OAAO,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC,sBAAsB,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC/E,IAAI,OAAO,CAAC,MAAM,CAAC,cAAc,EAAE,iBAAiB,CAAC;AACrD,IAAI,OAAO,IAAI,QAAQ,CAAC,UAAU,EAAE,EAAE,OAAO,EAAE,CAAC;AAChD,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC;AACrD,IAAI,OAAO,IAAI,QAAQ,CAAC,0BAA0B,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpE;AACA;;;;"}