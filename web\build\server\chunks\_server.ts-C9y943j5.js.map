{"version": 3, "file": "_server.ts-C9y943j5.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/jobs/_id_/report/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../../../../chunks/auth.js\";\nasync function POST({ request, params, cookies }) {\n  try {\n    const token = cookies.get(\"auth_token\");\n    const user = token && verifySessionToken(token);\n    if (!user) {\n      return json({ error: \"Unauthorized\" }, { status: 401 });\n    }\n    const { id } = params;\n    const { reason } = await request.json();\n    if (!reason || reason.trim() === \"\") {\n      return json({ error: \"Reason is required\" }, { status: 400 });\n    }\n    const job = await prisma.job_listing.findUnique({\n      where: { id }\n    });\n    if (!job) {\n      return json({ error: \"Job not found\" }, { status: 404 });\n    }\n    console.log(`Job reported: ${id} by user ${user.id} - Reason: ${reason}`);\n    return json({\n      success: true,\n      message: \"Job reported successfully\"\n    });\n  } catch (error) {\n    console.error(\"Error reporting job:\", error);\n    return json({ error: \"Failed to report job\" }, { status: 500 });\n  }\n}\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;AAGA,eAAe,IAAI,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE;AAClD,EAAE,IAAI;AACN,IAAI,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AAC3C,IAAI,MAAM,IAAI,GAAG,KAAK,IAAI,kBAAkB,CAAC,KAAK,CAAC;AACnD,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7D;AACA,IAAI,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;AACzB,IAAI,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AAC3C,IAAI,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;AACzC,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oBAAoB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnE;AACA,IAAI,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;AACpD,MAAM,KAAK,EAAE,EAAE,EAAE;AACjB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,GAAG,EAAE;AACd,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9D;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,CAAC;AAC7E,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,OAAO,EAAE;AACf,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC;AAChD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,sBAAsB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnE;AACA;;;;"}