{"version": 3, "file": "_server.ts-Swbi3I9g.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/usage/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../chunks/prisma.js\";\nimport { Roles } from \"../../../../chunks/roles.js\";\nconst GET = async ({ locals }) => {\n  const user = locals.user;\n  if (!user) return new Response(\"Unauthorized\", { status: 401 });\n  const startOfMonth = /* @__PURE__ */ new Date();\n  startOfMonth.setDate(1);\n  startOfMonth.setHours(0, 0, 0, 0);\n  const used = await prisma.resumeSubmission.count({\n    where: {\n      userId: user.id,\n      createdAt: {\n        gte: startOfMonth\n      }\n    }\n  });\n  const limit = Roles[user.role]?.limits.resumesPerMonth ?? null;\n  const remaining = limit !== null ? Math.max(0, limit - used) : null;\n  return json({ used, limit, remaining });\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;;AAGK,MAAC,GAAG,GAAG,OAAO,EAAE,MAAM,EAAE,KAAK;AAClC,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE,EAAE,MAAM,YAAY,mBAAmB,IAAI,IAAI,EAAE;AACjD,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC;AACzB,EAAE,YAAY,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACnC,EAAE,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC;AACnD,IAAI,KAAK,EAAE;AACX,MAAM,MAAM,EAAE,IAAI,CAAC,EAAE;AACrB,MAAM,SAAS,EAAE;AACjB,QAAQ,GAAG,EAAE;AACb;AACA;AACA,GAAG,CAAC;AACJ,EAAE,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC,eAAe,IAAI,IAAI;AAChE,EAAE,MAAM,SAAS,GAAG,KAAK,KAAK,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI;AACrE,EAAE,OAAO,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AACzC;;;;"}