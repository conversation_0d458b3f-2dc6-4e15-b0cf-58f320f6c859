# Render Low Memory Deployment Configuration

## Overview
This document outlines the changes made to `render.yaml` to optimize the application for <PERSON>der's 512MB memory constraints. The configuration disables HTML previews and other high-utilization features that were causing 99-100% memory usage.

## Changes Made to render.yaml

### 1. Scheduler Service (auto-apply-scheduler)
**Added Low Memory Environment Variables:**
```yaml
# Low Memory Configuration for 512MB Systems
- key: CIRCUIT_BREAKER_MEMORY_THRESHOLD
  value: "50"
- key: CIRCUIT_BREAKER_CPU_THRESHOLD
  value: "50"
- key: CIRCUIT_BREAKER_DEGRADED_MEMORY_THRESHOLD
  value: "30"
- key: CIRCUIT_BREAKER_DEGRADED_CPU_THRESHOLD
  value: "30"
- key: ENRICH_JOBS_BATCH_SIZE
  value: "5"
- key: ENRICH_JOBS_CONCURRENCY
  value: "1"
- key: ENRICH_JOBS_MAX_MEMORY
  value: "400"
- key: ENRICH_JOBS_WARNING_MEMORY
  value: "300"
- key: ENRICH_JOBS_MAX_PER_RUN
  value: "20"
- key: DISABLE_HTML_PREVIEWS
  value: "true"
- key: DISABLE_SCREENSHOTS
  value: "true"
- key: DISABLE_FILE_STORAGE
  value: "true"
- key: NODE_OPTIONS
  value: "--max-old-space-size=400"
```

### 2. Scraper Service (auto-apply-scraper)
**Updated Existing Values:**
```yaml
- key: SCRAPER_MAX_WORKERS
  value: "1"          # Reduced from "2"
- key: SCRAPER_BATCH_SIZE
  value: "2"          # Reduced from "3"
- key: JOB_DETAILS_BATCH_SIZE
  value: "1"          # Reduced from "2"
- key: JOB_DETAILS_MAX_JOBS
  value: "20"         # Reduced from "100"
```

**Added New Variables:**
```yaml
# Low Memory Configuration
- key: DISABLE_HTML_PREVIEWS
  value: "true"
- key: DISABLE_SCREENSHOTS
  value: "true"
- key: DISABLE_FILE_STORAGE
  value: "true"
- key: NODE_OPTIONS
  value: "--max-old-space-size=400"
```

## Key Optimizations

### Memory Management
- **Circuit Breaker Thresholds:** Reduced from 75% to 50% for both memory and CPU
- **Degraded Thresholds:** Reduced from 60% to 30%
- **Node.js Memory Limit:** Set to 400MB (`--max-old-space-size=400`)

### Processing Limits
- **Batch Sizes:** Significantly reduced (100 → 5, 3 → 2, 2 → 1)
- **Concurrency:** Reduced to single-threaded processing (2 → 1)
- **Max Jobs:** Reduced from 100 to 20 per run

### Resource-Intensive Features Disabled
- **HTML Previews:** Disabled via `DISABLE_HTML_PREVIEWS=true`
- **Screenshots:** Disabled via `DISABLE_SCREENSHOTS=true`
- **File Storage:** Disabled via `DISABLE_FILE_STORAGE=true`

## Expected Results

### Before Optimization
```
Memory usage: 536870912 / 536870912 = 100.00%
📄 HTML Preview: <!DOCTYPE html><html lang="en">...
🚫 File storage (HTML and screenshots) disabled to save disk space and memory
```

### After Optimization
```
Memory usage: < 50% (target)
🚫 HTML Preview disabled to save memory
🚫 File storage and screenshots disabled to save disk space and memory
```

## Deployment Instructions

1. **Commit the changes** to your repository
2. **Push to your main branch** (Render will auto-deploy)
3. **Monitor the logs** for the new memory-optimized messages
4. **Verify circuit breaker** triggers at lower thresholds

## Monitoring

Watch for these improvements in Render logs:
- Memory usage should stay below 50%
- "HTML Preview disabled to save memory" messages
- Smaller batch processing (5 jobs instead of 100)
- Circuit breaker triggering earlier to prevent overload

## Rollback Plan

If you need to revert to higher-performance settings:
1. Remove or comment out the low-memory environment variables
2. Restore original batch sizes and concurrency values
3. Set `DISABLE_HTML_PREVIEWS=false` or remove the variable

## Performance Trade-offs

- **Slower Processing:** Smaller batches mean longer overall processing time
- **Less Debugging Info:** HTML previews disabled reduces troubleshooting data
- **More Conservative:** System will pause more frequently to preserve stability

These trade-offs are necessary for system stability on Render's memory-constrained environment.
