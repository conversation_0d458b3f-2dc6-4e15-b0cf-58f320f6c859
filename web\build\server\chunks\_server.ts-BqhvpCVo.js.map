{"version": 3, "file": "_server.ts-BqhvpCVo.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/auth/passkey/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../chunks/prisma.js\";\nimport { a as generatePasskeyAuthenticationOptions, b as verifyPasskeyAuthentication } from \"../../../../chunks/webauthn.js\";\nimport { a as getAuthLocation, c as createSessionToken } from \"../../../../chunks/auth.js\";\nimport { d as dev } from \"../../../../chunks/index4.js\";\nconst challenges = /* @__PURE__ */ new Map();\nasync function POST({ request, cookies }) {\n  try {\n    const { action } = await request.json();\n    if (action === \"getOptions\") {\n      const users = await prisma.user.findMany({\n        where: {\n          preferences: {\n            path: [\"security\", \"passkeys\"],\n            not: null\n          }\n        },\n        select: {\n          id: true,\n          email: true,\n          preferences: true\n        }\n      });\n      const allPasskeys = [];\n      for (const user of users) {\n        const preferences = user.preferences;\n        const securityPrefs = preferences?.security || {};\n        const passkeys = securityPrefs.passkeys || [];\n        if (passkeys && passkeys.length > 0) {\n          const userPasskeys = passkeys.map((passkey) => ({\n            ...passkey,\n            userId: user.id\n          }));\n          allPasskeys.push(...userPasskeys);\n        }\n      }\n      console.log(`Found ${allPasskeys.length} registered passkeys in the database`);\n      allPasskeys.forEach((passkey, index) => {\n        console.log(`Passkey ${index + 1}:`, {\n          userId: passkey.userId,\n          credentialID: passkey.credentialID,\n          id: passkey.id,\n          name: passkey.name,\n          transports: passkey.transports\n        });\n      });\n      console.log(\"Raw passkey data from database:\");\n      allPasskeys.forEach((passkey, index) => {\n        console.log(`Passkey ${index + 1} raw data:`, passkey);\n      });\n      const options = await generatePasskeyAuthenticationOptions(allPasskeys);\n      const challengeBase64 = Buffer.from(options.challenge).toString(\"base64\");\n      challenges.set(\"current\", challengeBase64);\n      console.log(\"Generated authentication options with challenge:\", challengeBase64);\n      console.log(\"Final authentication options:\", {\n        ...options,\n        challenge: \"ArrayBuffer (binary data)\",\n        allowCredentials: options.allowCredentials ? `${options.allowCredentials.length} credentials` : \"omitted\"\n      });\n      return json(options);\n    } else if (action === \"verify\") {\n      const { authenticationResponse } = await request.json();\n      if (!authenticationResponse) {\n        return json({ error: \"Invalid authentication response\" }, { status: 400 });\n      }\n      const challengeBase64 = challenges.get(\"current\");\n      if (!challengeBase64) {\n        return json({ error: \"No challenge found\" }, { status: 400 });\n      }\n      console.log(\"Retrieved stored challenge:\", challengeBase64);\n      const users = await prisma.user.findMany({\n        where: {\n          preferences: {\n            path: [\"security\", \"passkeys\"],\n            not: null\n          }\n        },\n        select: {\n          id: true,\n          email: true,\n          name: true,\n          preferences: true\n        }\n      });\n      let matchedUser = null;\n      let matchedPasskey = null;\n      for (const user of users) {\n        const preferences2 = user.preferences;\n        const securityPrefs2 = preferences2?.security || {};\n        const passkeys2 = securityPrefs2.passkeys || [];\n        console.log(\"Looking for credential ID:\", authenticationResponse.id);\n        passkeys2.forEach((p, i) => {\n          console.log(`Passkey ${i} credentialID:`, p.credentialID);\n          console.log(`Passkey ${i} id:`, p.id);\n        });\n        const passkey = passkeys2.find(\n          (p) => p.credentialID === authenticationResponse.id || p.id === authenticationResponse.id\n        );\n        if (passkey) {\n          matchedUser = user;\n          matchedPasskey = passkey;\n          break;\n        }\n      }\n      if (!matchedUser || !matchedPasskey) {\n        return json({ error: \"Passkey not found\" }, { status: 404 });\n      }\n      const verification = await verifyPasskeyAuthentication(\n        authenticationResponse,\n        challengeBase64,\n        matchedPasskey.credentialPublicKey,\n        matchedPasskey.counter\n      );\n      console.log(\"Verification result:\", verification);\n      if (!verification.verified) {\n        return json({ error: \"Passkey verification failed\" }, { status: 400 });\n      }\n      const preferences = matchedUser.preferences;\n      const securityPrefs = preferences.security || {};\n      const passkeys = securityPrefs.passkeys || [];\n      const updatedPasskeys = passkeys.map((p) => {\n        if (p.credentialID === authenticationResponse.id) {\n          return {\n            ...p,\n            counter: verification.authenticationInfo.newCounter,\n            lastUsed: (/* @__PURE__ */ new Date()).toISOString()\n          };\n        }\n        return p;\n      });\n      await prisma.user.update({\n        where: { id: matchedUser.id },\n        data: {\n          preferences: {\n            ...preferences,\n            security: {\n              ...securityPrefs,\n              passkeys: updatedPasskeys\n            }\n          }\n        }\n      });\n      const { ip, location } = await getAuthLocation(request);\n      try {\n        await prisma.$executeRaw`INSERT INTO security_logs (user_id, action, ip, location, user_agent, details, created_at)\n          VALUES (${matchedUser.id}, 'LOGIN_SUCCESS', ${ip}, ${location}, ${request.headers.get(\"user-agent\") || \"Unknown\"},\n          ${JSON.stringify({ method: \"PASSKEY\" })}, ${/* @__PURE__ */ new Date()})`;\n      } catch (error) {\n        console.error(\"Failed to log security event:\", error);\n      }\n      const token = await createSessionToken(matchedUser, request);\n      cookies.set(\"session\", token, {\n        path: \"/\",\n        httpOnly: true,\n        sameSite: \"strict\",\n        secure: !dev,\n        maxAge: 60 * 60 * 24 * 30\n        // 30 days\n      });\n      challenges.delete(\"current\");\n      return json({\n        success: true,\n        user: {\n          name: matchedUser.name || matchedUser.email,\n          email: matchedUser.email\n        },\n        redirectUrl: \"/dashboard\"\n      });\n    }\n    return json({ error: \"Invalid action\" }, { status: 400 });\n  } catch (error) {\n    console.error(\"Passkey authentication error:\", error);\n    return json({ error: \"An error occurred during passkey authentication\" }, { status: 500 });\n  }\n}\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAKA,MAAM,UAAU,mBAAmB,IAAI,GAAG,EAAE;AAC5C,eAAe,IAAI,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE;AAC1C,EAAE,IAAI;AACN,IAAI,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AAC3C,IAAI,IAAI,MAAM,KAAK,YAAY,EAAE;AACjC,MAAM,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC/C,QAAQ,KAAK,EAAE;AACf,UAAU,WAAW,EAAE;AACvB,YAAY,IAAI,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;AAC1C,YAAY,GAAG,EAAE;AACjB;AACA,SAAS;AACT,QAAQ,MAAM,EAAE;AAChB,UAAU,EAAE,EAAE,IAAI;AAClB,UAAU,KAAK,EAAE,IAAI;AACrB,UAAU,WAAW,EAAE;AACvB;AACA,OAAO,CAAC;AACR,MAAM,MAAM,WAAW,GAAG,EAAE;AAC5B,MAAM,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;AAChC,QAAQ,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW;AAC5C,QAAQ,MAAM,aAAa,GAAG,WAAW,EAAE,QAAQ,IAAI,EAAE;AACzD,QAAQ,MAAM,QAAQ,GAAG,aAAa,CAAC,QAAQ,IAAI,EAAE;AACrD,QAAQ,IAAI,QAAQ,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;AAC7C,UAAU,MAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,MAAM;AAC1D,YAAY,GAAG,OAAO;AACtB,YAAY,MAAM,EAAE,IAAI,CAAC;AACzB,WAAW,CAAC,CAAC;AACb,UAAU,WAAW,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC;AAC3C;AACA;AACA,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,WAAW,CAAC,MAAM,CAAC,oCAAoC,CAAC,CAAC;AACpF,MAAM,WAAW,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,KAAK;AAC9C,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;AAC7C,UAAU,MAAM,EAAE,OAAO,CAAC,MAAM;AAChC,UAAU,YAAY,EAAE,OAAO,CAAC,YAAY;AAC5C,UAAU,EAAE,EAAE,OAAO,CAAC,EAAE;AACxB,UAAU,IAAI,EAAE,OAAO,CAAC,IAAI;AAC5B,UAAU,UAAU,EAAE,OAAO,CAAC;AAC9B,SAAS,CAAC;AACV,OAAO,CAAC;AACR,MAAM,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC;AACpD,MAAM,WAAW,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,KAAK;AAC9C,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,KAAK,GAAG,CAAC,CAAC,UAAU,CAAC,EAAE,OAAO,CAAC;AAC9D,OAAO,CAAC;AACR,MAAM,MAAM,OAAO,GAAG,MAAM,oCAAoC,CAAC,WAAW,CAAC;AAC7E,MAAM,MAAM,eAAe,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC;AAC/E,MAAM,UAAU,CAAC,GAAG,CAAC,SAAS,EAAE,eAAe,CAAC;AAChD,MAAM,OAAO,CAAC,GAAG,CAAC,kDAAkD,EAAE,eAAe,CAAC;AACtF,MAAM,OAAO,CAAC,GAAG,CAAC,+BAA+B,EAAE;AACnD,QAAQ,GAAG,OAAO;AAClB,QAAQ,SAAS,EAAE,2BAA2B;AAC9C,QAAQ,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,GAAG,CAAC,EAAE,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG;AACxG,OAAO,CAAC;AACR,MAAM,OAAO,IAAI,CAAC,OAAO,CAAC;AAC1B,KAAK,MAAM,IAAI,MAAM,KAAK,QAAQ,EAAE;AACpC,MAAM,MAAM,EAAE,sBAAsB,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AAC7D,MAAM,IAAI,CAAC,sBAAsB,EAAE;AACnC,QAAQ,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,iCAAiC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClF;AACA,MAAM,MAAM,eAAe,GAAG,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC;AACvD,MAAM,IAAI,CAAC,eAAe,EAAE;AAC5B,QAAQ,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oBAAoB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACrE;AACA,MAAM,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,eAAe,CAAC;AACjE,MAAM,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC/C,QAAQ,KAAK,EAAE;AACf,UAAU,WAAW,EAAE;AACvB,YAAY,IAAI,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;AAC1C,YAAY,GAAG,EAAE;AACjB;AACA,SAAS;AACT,QAAQ,MAAM,EAAE;AAChB,UAAU,EAAE,EAAE,IAAI;AAClB,UAAU,KAAK,EAAE,IAAI;AACrB,UAAU,IAAI,EAAE,IAAI;AACpB,UAAU,WAAW,EAAE;AACvB;AACA,OAAO,CAAC;AACR,MAAM,IAAI,WAAW,GAAG,IAAI;AAC5B,MAAM,IAAI,cAAc,GAAG,IAAI;AAC/B,MAAM,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;AAChC,QAAQ,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW;AAC7C,QAAQ,MAAM,cAAc,GAAG,YAAY,EAAE,QAAQ,IAAI,EAAE;AAC3D,QAAQ,MAAM,SAAS,GAAG,cAAc,CAAC,QAAQ,IAAI,EAAE;AACvD,QAAQ,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE,sBAAsB,CAAC,EAAE,CAAC;AAC5E,QAAQ,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;AACpC,UAAU,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,YAAY,CAAC;AACnE,UAAU,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;AAC/C,SAAS,CAAC;AACV,QAAQ,MAAM,OAAO,GAAG,SAAS,CAAC,IAAI;AACtC,UAAU,CAAC,CAAC,KAAK,CAAC,CAAC,YAAY,KAAK,sBAAsB,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,KAAK,sBAAsB,CAAC;AACjG,SAAS;AACT,QAAQ,IAAI,OAAO,EAAE;AACrB,UAAU,WAAW,GAAG,IAAI;AAC5B,UAAU,cAAc,GAAG,OAAO;AAClC,UAAU;AACV;AACA;AACA,MAAM,IAAI,CAAC,WAAW,IAAI,CAAC,cAAc,EAAE;AAC3C,QAAQ,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpE;AACA,MAAM,MAAM,YAAY,GAAG,MAAM,2BAA2B;AAC5D,QAAQ,sBAAsB;AAC9B,QAAQ,eAAe;AACvB,QAAQ,cAAc,CAAC,mBAAmB;AAC1C,QAAQ,cAAc,CAAC;AACvB,OAAO;AACP,MAAM,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,YAAY,CAAC;AACvD,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE;AAClC,QAAQ,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,6BAA6B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9E;AACA,MAAM,MAAM,WAAW,GAAG,WAAW,CAAC,WAAW;AACjD,MAAM,MAAM,aAAa,GAAG,WAAW,CAAC,QAAQ,IAAI,EAAE;AACtD,MAAM,MAAM,QAAQ,GAAG,aAAa,CAAC,QAAQ,IAAI,EAAE;AACnD,MAAM,MAAM,eAAe,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK;AAClD,QAAQ,IAAI,CAAC,CAAC,YAAY,KAAK,sBAAsB,CAAC,EAAE,EAAE;AAC1D,UAAU,OAAO;AACjB,YAAY,GAAG,CAAC;AAChB,YAAY,OAAO,EAAE,YAAY,CAAC,kBAAkB,CAAC,UAAU;AAC/D,YAAY,QAAQ,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC9D,WAAW;AACX;AACA,QAAQ,OAAO,CAAC;AAChB,OAAO,CAAC;AACR,MAAM,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AAC/B,QAAQ,KAAK,EAAE,EAAE,EAAE,EAAE,WAAW,CAAC,EAAE,EAAE;AACrC,QAAQ,IAAI,EAAE;AACd,UAAU,WAAW,EAAE;AACvB,YAAY,GAAG,WAAW;AAC1B,YAAY,QAAQ,EAAE;AACtB,cAAc,GAAG,aAAa;AAC9B,cAAc,QAAQ,EAAE;AACxB;AACA;AACA;AACA,OAAO,CAAC;AACR,MAAM,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,GAAG,MAAM,eAAe,CAAC,OAAO,CAAC;AAC7D,MAAM,IAAI;AACV,QAAQ,MAAM,MAAM,CAAC,WAAW,CAAC;AACjC,kBAAkB,EAAE,WAAW,CAAC,EAAE,CAAC,mBAAmB,EAAE,EAAE,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,SAAS,CAAC;AAC3H,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,kBAAkB,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC;AACnF,OAAO,CAAC,OAAO,KAAK,EAAE;AACtB,QAAQ,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC;AAC7D;AACA,MAAM,MAAM,KAAK,GAAG,MAAM,kBAAkB,CAAC,WAAW,EAAE,OAAO,CAAC;AAClE,MAAM,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,EAAE;AACpC,QAAQ,IAAI,EAAE,GAAG;AACjB,QAAQ,QAAQ,EAAE,IAAI;AACtB,QAAQ,QAAQ,EAAE,QAAQ;AAC1B,QAAQ,MAAM,EAAE,CAAC,GAAG;AACpB,QAAQ,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;AAC/B;AACA,OAAO,CAAC;AACR,MAAM,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC;AAClC,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,IAAI,EAAE;AACd,UAAU,IAAI,EAAE,WAAW,CAAC,IAAI,IAAI,WAAW,CAAC,KAAK;AACrD,UAAU,KAAK,EAAE,WAAW,CAAC;AAC7B,SAAS;AACT,QAAQ,WAAW,EAAE;AACrB,OAAO,CAAC;AACR;AACA,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,gBAAgB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7D,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC;AACzD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,iDAAiD,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9F;AACA;;;;"}