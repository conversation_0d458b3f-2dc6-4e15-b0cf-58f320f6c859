{"version": 3, "file": "_server.ts-C9VrYwuF.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/applications/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../../chunks/auth.js\";\nconst GET = async ({ cookies }) => {\n  try {\n    const token = cookies.get(\"auth_token\");\n    const user = token && verifySessionToken(token);\n    if (!user) {\n      return json({ error: \"Authentication required\" }, { status: 401 });\n    }\n    const applications = await prisma.application.findMany({\n      where: {\n        userId: user.id\n      },\n      orderBy: {\n        appliedDate: \"desc\"\n      }\n    });\n    return json({\n      success: true,\n      applications\n    });\n  } catch (error) {\n    console.error(\"Error getting applications:\", error);\n    return json({ error: \"Failed to get applications\" }, { status: 500 });\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;;;;;;AAGK,MAAC,GAAG,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK;AACnC,EAAE,IAAI;AACN,IAAI,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AAC3C,IAAI,MAAM,IAAI,GAAG,KAAK,IAAI,kBAAkB,CAAC,KAAK,CAAC;AACnD,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA,IAAI,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;AAC3D,MAAM,KAAK,EAAE;AACb,QAAQ,MAAM,EAAE,IAAI,CAAC;AACrB,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,WAAW,EAAE;AACrB;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM;AACN,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC;AACvD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,4BAA4B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE;AACA;;;;"}