{"version": 3, "file": "_server.ts-BaWwEAwq.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/user/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../chunks/index.js\";\nimport { v as verifySessionToken } from \"../../../../chunks/auth.js\";\nimport { p as prisma } from \"../../../../chunks/prisma.js\";\nconst GET = async ({ cookies }) => {\n  const token = cookies.get(\"auth_token\");\n  if (!token) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  try {\n    const userData = verifySessionToken(token);\n    if (!userData?.id) {\n      return json({ error: \"Invalid token\" }, { status: 401 });\n    }\n    const user = await prisma.user.findUnique({\n      where: { id: userData.id },\n      include: {\n        subscriptions: {\n          orderBy: { createdAt: \"desc\" },\n          take: 1\n        }\n      }\n    });\n    if (!user) {\n      return json({ error: \"User not found\" }, { status: 404 });\n    }\n    const activeSub = user.subscriptions[0];\n    const planInfo = activeSub?.planId ? {\n      id: activeSub.planId,\n      name: activeSub.planId.charAt(0).toUpperCase() + activeSub.planId.slice(1),\n      description: `${activeSub.planId.charAt(0).toUpperCase() + activeSub.planId.slice(1)} Plan`,\n      monthlyPrice: activeSub.amount || 0,\n      annualPrice: (activeSub.amount || 0) * 10\n    } : {\n      id: user.role || \"free\",\n      name: (user.role || \"free\").charAt(0).toUpperCase() + (user.role || \"free\").slice(1),\n      description: `${(user.role || \"free\").charAt(0).toUpperCase() + (user.role || \"free\").slice(1)} Plan`,\n      monthlyPrice: 0,\n      annualPrice: 0\n    };\n    return json({\n      id: user.id,\n      email: user.email,\n      name: user.name || user.email,\n      firstName: user.firstName,\n      lastName: user.lastName,\n      role: user.role,\n      plan: planInfo,\n      subscription: activeSub ? {\n        id: activeSub.id,\n        planId: activeSub.planId,\n        currentPeriodStart: activeSub.currentPeriodStart,\n        currentPeriodEnd: activeSub.currentPeriodEnd,\n        cancelAtPeriodEnd: activeSub.cancelAtPeriodEnd\n      } : null\n    });\n  } catch (error) {\n    console.error(\"Error getting user data:\", error);\n    return json({ error: \"Failed to get user data\" }, { status: 500 });\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;;;;;;AAGK,MAAC,GAAG,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK;AACnC,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE;AACd,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,QAAQ,GAAG,kBAAkB,CAAC,KAAK,CAAC;AAC9C,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE;AACvB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9D;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAC9C,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE;AAChC,MAAM,OAAO,EAAE;AACf,QAAQ,aAAa,EAAE;AACvB,UAAU,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;AACxC,UAAU,IAAI,EAAE;AAChB;AACA;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,gBAAgB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC/D;AACA,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;AAC3C,IAAI,MAAM,QAAQ,GAAG,SAAS,EAAE,MAAM,GAAG;AACzC,MAAM,EAAE,EAAE,SAAS,CAAC,MAAM;AAC1B,MAAM,IAAI,EAAE,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AAChF,MAAM,WAAW,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;AACjG,MAAM,YAAY,EAAE,SAAS,CAAC,MAAM,IAAI,CAAC;AACzC,MAAM,WAAW,EAAE,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,IAAI;AAC7C,KAAK,GAAG;AACR,MAAM,EAAE,EAAE,IAAI,CAAC,IAAI,IAAI,MAAM;AAC7B,MAAM,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,IAAI,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;AAC1F,MAAM,WAAW,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,IAAI,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;AAC3G,MAAM,YAAY,EAAE,CAAC;AACrB,MAAM,WAAW,EAAE;AACnB,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,EAAE,EAAE,IAAI,CAAC,EAAE;AACjB,MAAM,KAAK,EAAE,IAAI,CAAC,KAAK;AACvB,MAAM,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK;AACnC,MAAM,SAAS,EAAE,IAAI,CAAC,SAAS;AAC/B,MAAM,QAAQ,EAAE,IAAI,CAAC,QAAQ;AAC7B,MAAM,IAAI,EAAE,IAAI,CAAC,IAAI;AACrB,MAAM,IAAI,EAAE,QAAQ;AACpB,MAAM,YAAY,EAAE,SAAS,GAAG;AAChC,QAAQ,EAAE,EAAE,SAAS,CAAC,EAAE;AACxB,QAAQ,MAAM,EAAE,SAAS,CAAC,MAAM;AAChC,QAAQ,kBAAkB,EAAE,SAAS,CAAC,kBAAkB;AACxD,QAAQ,gBAAgB,EAAE,SAAS,CAAC,gBAAgB;AACpD,QAAQ,iBAAiB,EAAE,SAAS,CAAC;AACrC,OAAO,GAAG;AACV,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC;AACpD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtE;AACA;;;;"}