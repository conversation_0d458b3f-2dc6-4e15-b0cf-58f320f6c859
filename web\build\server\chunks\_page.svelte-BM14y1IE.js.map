{"version": 3, "file": "_page.svelte-BM14y1IE.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/settings/email/_page.svelte.js"], "sourcesContent": ["import \"clsx\";\nimport { y as pop, w as push } from \"../../../../../chunks/index3.js\";\nimport \"../../../../../chunks/client.js\";\nfunction _page($$payload, $$props) {\n  push();\n  $$payload.out += `<div class=\"container mx-auto py-8\"><div class=\"flex h-64 items-center justify-center\"><div class=\"border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent\"></div> <p class=\"ml-4\">Redirecting to email settings...</p></div></div>`;\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;AAGA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,0PAA0P,CAAC;AAC/Q,EAAE,GAAG,EAAE;AACP;;;;"}