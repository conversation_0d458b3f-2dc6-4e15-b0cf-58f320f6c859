{"version": 3, "file": "_server.ts-J5Q1LjYg.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/admin/make-admin/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../../../chunks/auth.js\";\nimport { d as dev } from \"../../../../../chunks/index4.js\";\nconst POST = async ({ cookies, request }) => {\n  const token = cookies.get(\"auth_token\");\n  if (!token) return new Response(\"Unauthorized\", { status: 401 });\n  const userData = await verifySessionToken(token);\n  if (!userData?.id) return new Response(\"Unauthorized\", { status: 401 });\n  try {\n    const user = await prisma.user.findUnique({\n      where: { id: userData.id },\n      select: { id: true, email: true, isAdmin: true }\n    });\n    if (!user) {\n      return json({ error: \"User not found\" }, { status: 404 });\n    }\n    if (!dev && !user.isAdmin) {\n      return json({ error: \"Unauthorized\" }, { status: 403 });\n    }\n    const { email } = await request.json();\n    const targetEmail = email || user.email;\n    if (!targetEmail) {\n      return json({ error: \"Email is required\" }, { status: 400 });\n    }\n    const updatedUser = await prisma.user.update({\n      where: { email: targetEmail },\n      data: { isAdmin: true },\n      select: { id: true, email: true, isAdmin: true }\n    });\n    return json({\n      success: true,\n      message: `User ${updatedUser.email} is now an admin`,\n      user: updatedUser\n    });\n  } catch (error) {\n    console.error(\"Error making user admin:\", error);\n    return json({ error: \"Failed to make user admin\" }, { status: 500 });\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;AAIK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AAC7C,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE,EAAE,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AAClD,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAC9C,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE;AAChC,MAAM,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI;AACpD,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,gBAAgB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC/D;AACA,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AAC/B,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7D;AACA,IAAI,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AAC1C,IAAI,MAAM,WAAW,GAAG,KAAK,IAAI,IAAI,CAAC,KAAK;AAC3C,IAAI,IAAI,CAAC,WAAW,EAAE;AACtB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE;AACA,IAAI,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AACjD,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE;AACnC,MAAM,IAAI,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;AAC7B,MAAM,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI;AACpD,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,OAAO,EAAE,CAAC,KAAK,EAAE,WAAW,CAAC,KAAK,CAAC,gBAAgB,CAAC;AAC1D,MAAM,IAAI,EAAE;AACZ,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC;AACpD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,2BAA2B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA;;;;"}