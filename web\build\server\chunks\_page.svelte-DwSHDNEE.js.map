{"version": 3, "file": "_page.svelte-DwSHDNEE.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/jobs/_page.svelte.js"], "sourcesContent": ["import \"clsx\";\nimport { y as pop, w as push } from \"../../../../chunks/index3.js\";\nimport { S as SEO } from \"../../../../chunks/SEO.js\";\nimport { g as goto } from \"../../../../chunks/client.js\";\nimport { a as toast } from \"../../../../chunks/Toaster.svelte_svelte_type_style_lang.js\";\nimport { J as JobSearch, a as JobFeed } from \"../../../../chunks/JobFeed.js\";\nimport { d as debounce } from \"../../../../chunks/utils.js\";\nasync function searchJobs(params) {\n  try {\n    if (!params.title && !params.location && !params.companies?.length) {\n      throw new Error(\"At least one search parameter is required\");\n    }\n    const response = await fetch(\"/api/jobs/search\", {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\"\n      },\n      body: JSON.stringify(params)\n    });\n    const data = await response.json();\n    if (!response.ok) {\n      console.error(\"Job search error:\", data.error);\n      return {\n        results: [],\n        error: data.error || \"Failed to search jobs\",\n        limitReached: data.limitReached || false\n      };\n    }\n    return {\n      searchId: data.searchId || null,\n      results: data.results || [],\n      saved: data.saved || false\n    };\n  } catch (error) {\n    console.error(\"Error searching jobs:\", error);\n    return {\n      results: [],\n      error: error instanceof Error ? error.message : \"Unknown error occurred\"\n    };\n  }\n}\nfunction _page($$payload, $$props) {\n  push();\n  const { data } = $$props;\n  let isSearching = false;\n  let jobs = [];\n  let isLoading = false;\n  let selectedJob = null;\n  let totalJobCount = 0;\n  let savedJobs = [];\n  let appliedJobs = [];\n  let searchParams = {\n    ...data.searchParams,\n    // Convert location string to locations array if needed\n    locations: data.searchParams?.location ? [data.searchParams.location] : [],\n    // Ensure these arrays exist\n    locationType: data.searchParams?.locationType || [],\n    experience: data.searchParams?.experience || [],\n    category: [],\n    education: []\n  };\n  async function handleSearch(params) {\n    Object.keys(params).forEach((key) => {\n      searchParams[key] = params[key];\n    });\n    if (!searchParams.title) searchParams.title = \"\";\n    if (!searchParams.locations) searchParams.locations = [];\n    if (!searchParams.locationType) searchParams.locationType = [];\n    if (!searchParams.experience) searchParams.experience = [];\n    if (!searchParams.salary) searchParams.salary = \"\";\n    isSearching = true;\n    try {\n      debouncedUpdateUrl(searchParams);\n      if (!params.saveSearch || !params.title || !data.user) {\n        return;\n      }\n      let location = null;\n      if (params.locations?.length > 0) {\n        try {\n          const loc = params.locations[0];\n          if (loc.includes(\"|\")) {\n            const [_, name, stateCode] = loc.split(\"|\");\n            location = `${name}, ${stateCode}`;\n          } else {\n            location = loc;\n          }\n        } catch (error) {\n          console.error(\"Error extracting location:\", error);\n        }\n      }\n      const userId = data.user.id;\n      if (!userId) return;\n      if (params.saveSearch) {\n        try {\n          const searchParams2 = {\n            title: params.title,\n            location,\n            locationType: params.locationType || [],\n            experience: params.experience || [],\n            category: params.category || [],\n            education: params.education || [],\n            salary: params.salary || \"\",\n            saveSearch: true\n            // Explicitly set to true since we're in this block\n          };\n          const result = await searchJobs(searchParams2);\n          console.log(\"Search completed successfully\");\n          if (result.error) {\n            if (result.limitReached) {\n              toast.error(\"You have reached your search limit\");\n              console.warn(\"Search limit reached\");\n            } else {\n              console.warn(\"Failed to perform search:\", result.error);\n            }\n            return;\n          }\n          if (result.saved && result.searchId) {\n            toast.success(\"Search saved as job alert\", {\n              description: \"View your saved search\",\n              action: {\n                label: \"View\",\n                onClick: () => goto(`/dashboard/jobs/${result.searchId}`)\n              }\n            });\n          }\n        } catch (error) {\n          console.warn(\"Error performing search:\", error);\n        }\n      }\n    } catch (error) {\n      console.error(\"Search error:\", error);\n      toast.error(\"Failed to complete search\", { description: \"Please try again later\" });\n    } finally {\n      isSearching = false;\n    }\n  }\n  function updateUrlFromParams(params) {\n    return;\n  }\n  async function loadMore() {\n    return [];\n  }\n  async function handleApply(job) {\n    if (appliedJobs.includes(job.id)) {\n      goto();\n      return;\n    }\n    try {\n      const response = await fetch(`/api/jobs/${job.id}/apply`, {\n        method: \"POST\",\n        headers: { \"Content-Type\": \"application/json\" },\n        body: JSON.stringify({})\n      });\n      if (!response.ok) {\n        const data2 = await response.json();\n        throw new Error(data2.error || \"Failed to apply to job\");\n      }\n      appliedJobs = [...appliedJobs, job.id];\n      toast.success(\"Application started\", {\n        description: \"Tracking this application in your dashboard\"\n      });\n      setTimeout(\n        () => {\n          goto(\"/dashboard/tracker\");\n        },\n        1500\n      );\n    } catch (error) {\n      console.error(\"Error applying to job:\", error);\n      toast.error(\"Failed to apply to job\");\n    }\n  }\n  async function handleSave(job) {\n    if (!data.user) {\n      window.location.href = \"/auth/sign-in\";\n      return;\n    }\n    try {\n      const response = await fetch(`/api/jobs/${job.id}/save`, {\n        method: \"POST\",\n        headers: { \"Content-Type\": \"application/json\" },\n        body: JSON.stringify({ notes: \"\" })\n      });\n      if (!response.ok) {\n        const data2 = await response.json();\n        throw new Error(data2.error || \"Failed to save job\");\n      }\n      if (!savedJobs.includes(job.id)) {\n        savedJobs = [...savedJobs, job.id];\n      }\n      toast.success(\"Job saved\", { description: \"Added to your saved jobs\" });\n    } catch (error) {\n      console.error(\"Error saving job:\", error);\n      toast.error(\"Failed to save job\");\n    }\n  }\n  function handleJobSelect(job) {\n    selectedJob = job;\n  }\n  let isUrlUpdateInProgress = false;\n  const debouncedUpdateUrl = debounce(\n    (params) => {\n      if (!isUrlUpdateInProgress) {\n        isUrlUpdateInProgress = true;\n        console.log(\"Debounced URL update triggered\");\n        try {\n          updateUrlFromParams(params);\n        } finally {\n          isUrlUpdateInProgress = false;\n        }\n      } else {\n        console.log(\"Skipping debounced URL update - another update is in progress\");\n      }\n    },\n    800\n  );\n  SEO($$payload, {\n    title: \"Job Search | Hirli\",\n    description: \"Search for jobs that match your profile and experience. Track your job applications and get insights on your job search progress.\",\n    keywords: \"job search, job applications, job tracking, career search, job matching, application tracking\"\n  });\n  $$payload.out += `<!----> `;\n  JobSearch($$payload, {\n    onSearch: handleSearch,\n    isSearching,\n    initialParams: searchParams,\n    user: data.user\n  });\n  $$payload.out += `<!----> `;\n  JobFeed($$payload, {\n    jobs,\n    isAuthenticated: true,\n    isLoading,\n    onLoadMore: loadMore,\n    onApply: handleApply,\n    onSave: handleSave,\n    selectedJob,\n    onSelectJob: handleJobSelect,\n    searchParams,\n    totalJobCount,\n    savedJobs,\n    appliedJobs\n  });\n  $$payload.out += `<!---->`;\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,eAAe,UAAU,CAAC,MAAM,EAAE;AAClC,EAAE,IAAI;AACN,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,EAAE;AACxE,MAAM,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC;AAClE;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,kBAAkB,EAAE;AACrD,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,OAAO,EAAE;AACf,QAAQ,cAAc,EAAE;AACxB,OAAO;AACP,MAAM,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM;AACjC,KAAK,CAAC;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE;AACtC,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;AACtB,MAAM,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC;AACpD,MAAM,OAAO;AACb,QAAQ,OAAO,EAAE,EAAE;AACnB,QAAQ,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,uBAAuB;AACpD,QAAQ,YAAY,EAAE,IAAI,CAAC,YAAY,IAAI;AAC3C,OAAO;AACP;AACA,IAAI,OAAO;AACX,MAAM,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,IAAI;AACrC,MAAM,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,EAAE;AACjC,MAAM,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI;AAC3B,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC;AACjD,IAAI,OAAO;AACX,MAAM,OAAO,EAAE,EAAE;AACjB,MAAM,KAAK,EAAE,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG;AACtD,KAAK;AACL;AACA;AACA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO;AAC1B,EAAE,IAAI,WAAW,GAAG,KAAK;AACzB,EAAE,IAAI,IAAI,GAAG,EAAE;AACf,EAAE,IAAI,SAAS,GAAG,KAAK;AACvB,EAAE,IAAI,WAAW,GAAG,IAAI;AACxB,EAAE,IAAI,aAAa,GAAG,CAAC;AACvB,EAAE,IAAI,SAAS,GAAG,EAAE;AACpB,EAAE,IAAI,WAAW,GAAG,EAAE;AACtB,EAAE,IAAI,YAAY,GAAG;AACrB,IAAI,GAAG,IAAI,CAAC,YAAY;AACxB;AACA,IAAI,SAAS,EAAE,IAAI,CAAC,YAAY,EAAE,QAAQ,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,EAAE;AAC9E;AACA,IAAI,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE,YAAY,IAAI,EAAE;AACvD,IAAI,UAAU,EAAE,IAAI,CAAC,YAAY,EAAE,UAAU,IAAI,EAAE;AACnD,IAAI,QAAQ,EAAE,EAAE;AAChB,IAAI,SAAS,EAAE;AACf,GAAG;AACH,EAAE,eAAe,YAAY,CAAC,MAAM,EAAE;AACtC,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;AACzC,MAAM,YAAY,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC;AACrC,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,YAAY,CAAC,KAAK,GAAG,EAAE;AACpD,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,YAAY,CAAC,SAAS,GAAG,EAAE;AAC5D,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,YAAY,CAAC,YAAY,GAAG,EAAE;AAClE,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,YAAY,CAAC,UAAU,GAAG,EAAE;AAC9D,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,YAAY,CAAC,MAAM,GAAG,EAAE;AACtD,IAAI,WAAW,GAAG,IAAI;AACtB,IAAI,IAAI;AACR,MAAM,kBAAkB,CAAC,YAAY,CAAC;AACtC,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;AAC7D,QAAQ;AACR;AACA,MAAM,IAAI,QAAQ,GAAG,IAAI;AACzB,MAAM,IAAI,MAAM,CAAC,SAAS,EAAE,MAAM,GAAG,CAAC,EAAE;AACxC,QAAQ,IAAI;AACZ,UAAU,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;AACzC,UAAU,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AACjC,YAAY,MAAM,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC;AACvD,YAAY,QAAQ,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;AAC9C,WAAW,MAAM;AACjB,YAAY,QAAQ,GAAG,GAAG;AAC1B;AACA,SAAS,CAAC,OAAO,KAAK,EAAE;AACxB,UAAU,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC;AAC5D;AACA;AACA,MAAM,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE;AACjC,MAAM,IAAI,CAAC,MAAM,EAAE;AACnB,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE;AAC7B,QAAQ,IAAI;AACZ,UAAU,MAAM,aAAa,GAAG;AAChC,YAAY,KAAK,EAAE,MAAM,CAAC,KAAK;AAC/B,YAAY,QAAQ;AACpB,YAAY,YAAY,EAAE,MAAM,CAAC,YAAY,IAAI,EAAE;AACnD,YAAY,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,EAAE;AAC/C,YAAY,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,EAAE;AAC3C,YAAY,SAAS,EAAE,MAAM,CAAC,SAAS,IAAI,EAAE;AAC7C,YAAY,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,EAAE;AACvC,YAAY,UAAU,EAAE;AACxB;AACA,WAAW;AACX,UAAU,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,aAAa,CAAC;AACxD,UAAU,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC;AACtD,UAAU,IAAI,MAAM,CAAC,KAAK,EAAE;AAC5B,YAAY,IAAI,MAAM,CAAC,YAAY,EAAE;AACrC,cAAc,KAAK,CAAC,KAAK,CAAC,oCAAoC,CAAC;AAC/D,cAAc,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC;AAClD,aAAa,MAAM;AACnB,cAAc,OAAO,CAAC,IAAI,CAAC,2BAA2B,EAAE,MAAM,CAAC,KAAK,CAAC;AACrE;AACA,YAAY;AACZ;AACA,UAAU,IAAI,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,QAAQ,EAAE;AAC/C,YAAY,KAAK,CAAC,OAAO,CAAC,2BAA2B,EAAE;AACvD,cAAc,WAAW,EAAE,wBAAwB;AACnD,cAAc,MAAM,EAAE;AACtB,gBAAgB,KAAK,EAAE,MAAM;AAC7B,gBAAgB,OAAO,EAAE,MAAM,IAAI,CAAC,CAAC,gBAAgB,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;AACxE;AACA,aAAa,CAAC;AACd;AACA,SAAS,CAAC,OAAO,KAAK,EAAE;AACxB,UAAU,OAAO,CAAC,IAAI,CAAC,0BAA0B,EAAE,KAAK,CAAC;AACzD;AACA;AACA,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,eAAe,EAAE,KAAK,CAAC;AAC3C,MAAM,KAAK,CAAC,KAAK,CAAC,2BAA2B,EAAE,EAAE,WAAW,EAAE,wBAAwB,EAAE,CAAC;AACzF,KAAK,SAAS;AACd,MAAM,WAAW,GAAG,KAAK;AACzB;AACA;AACA,EAAE,SAAS,mBAAmB,CAAC,MAAM,EAAE;AACvC,IAAI;AACJ;AACA,EAAE,eAAe,QAAQ,GAAG;AAC5B,IAAI,OAAO,EAAE;AACb;AACA,EAAE,eAAe,WAAW,CAAC,GAAG,EAAE;AAClC,IAAI,IAAI,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;AACtC,MAAM,IAAI,EAAE;AACZ,MAAM;AACN;AACA,IAAI,IAAI;AACR,MAAM,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,CAAC,UAAU,EAAE,GAAG,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE;AAChE,QAAQ,MAAM,EAAE,MAAM;AACtB,QAAQ,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;AACvD,QAAQ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE;AAC/B,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;AACxB,QAAQ,MAAM,KAAK,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE;AAC3C,QAAQ,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,KAAK,IAAI,wBAAwB,CAAC;AAChE;AACA,MAAM,WAAW,GAAG,CAAC,GAAG,WAAW,EAAE,GAAG,CAAC,EAAE,CAAC;AAC5C,MAAM,KAAK,CAAC,OAAO,CAAC,qBAAqB,EAAE;AAC3C,QAAQ,WAAW,EAAE;AACrB,OAAO,CAAC;AACR,MAAM,UAAU;AAChB,QAAQ,MAAM;AACd,UAAU,IAAI,CAAC,oBAAoB,CAAC;AACpC,SAAS;AACT,QAAQ;AACR,OAAO;AACP,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC;AACpD,MAAM,KAAK,CAAC,KAAK,CAAC,wBAAwB,CAAC;AAC3C;AACA;AACA,EAAE,eAAe,UAAU,CAAC,GAAG,EAAE;AACjC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;AACpB,MAAM,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,eAAe;AAC5C,MAAM;AACN;AACA,IAAI,IAAI;AACR,MAAM,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,CAAC,UAAU,EAAE,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE;AAC/D,QAAQ,MAAM,EAAE,MAAM;AACtB,QAAQ,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;AACvD,QAAQ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE;AAC1C,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;AACxB,QAAQ,MAAM,KAAK,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE;AAC3C,QAAQ,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,KAAK,IAAI,oBAAoB,CAAC;AAC5D;AACA,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;AACvC,QAAQ,SAAS,GAAG,CAAC,GAAG,SAAS,EAAE,GAAG,CAAC,EAAE,CAAC;AAC1C;AACA,MAAM,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,WAAW,EAAE,0BAA0B,EAAE,CAAC;AAC7E,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC;AAC/C,MAAM,KAAK,CAAC,KAAK,CAAC,oBAAoB,CAAC;AACvC;AACA;AACA,EAAE,SAAS,eAAe,CAAC,GAAG,EAAE;AAChC,IAAI,WAAW,GAAG,GAAG;AACrB;AACA,EAAE,IAAI,qBAAqB,GAAG,KAAK;AACnC,EAAE,MAAM,kBAAkB,GAAG,QAAQ;AACrC,IAAI,CAAC,MAAM,KAAK;AAChB,MAAM,IAAI,CAAC,qBAAqB,EAAE;AAClC,QAAQ,qBAAqB,GAAG,IAAI;AACpC,QAAQ,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC;AACrD,QAAQ,IAAI;AACZ,UAAU,mBAAmB,CAAC,MAAM,CAAC;AACrC,SAAS,SAAS;AAClB,UAAU,qBAAqB,GAAG,KAAK;AACvC;AACA,OAAO,MAAM;AACb,QAAQ,OAAO,CAAC,GAAG,CAAC,+DAA+D,CAAC;AACpF;AACA,KAAK;AACL,IAAI;AACJ,GAAG;AACH,EAAE,GAAG,CAAC,SAAS,EAAE;AACjB,IAAI,KAAK,EAAE,oBAAoB;AAC/B,IAAI,WAAW,EAAE,mIAAmI;AACpJ,IAAI,QAAQ,EAAE;AACd,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,SAAS,CAAC,SAAS,EAAE;AACvB,IAAI,QAAQ,EAAE,YAAY;AAC1B,IAAI,WAAW;AACf,IAAI,aAAa,EAAE,YAAY;AAC/B,IAAI,IAAI,EAAE,IAAI,CAAC;AACf,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,OAAO,CAAC,SAAS,EAAE;AACrB,IAAI,IAAI;AACR,IAAI,eAAe,EAAE,IAAI;AACzB,IAAI,SAAS;AACb,IAAI,UAAU,EAAE,QAAQ;AACxB,IAAI,OAAO,EAAE,WAAW;AACxB,IAAI,MAAM,EAAE,UAAU;AACtB,IAAI,WAAW;AACf,IAAI,WAAW,EAAE,eAAe;AAChC,IAAI,YAAY;AAChB,IAAI,aAAa;AACjB,IAAI,SAAS;AACb,IAAI;AACJ,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC5B,EAAE,GAAG,EAAE;AACP;;;;"}