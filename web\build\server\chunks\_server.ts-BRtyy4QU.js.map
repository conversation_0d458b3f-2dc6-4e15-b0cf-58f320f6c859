{"version": 3, "file": "_server.ts-BRtyy4QU.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/email/queue/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { l as logger } from \"../../../../../chunks/logger.js\";\nimport { R as RedisConnection } from \"../../../../../chunks/redis.js\";\nimport { E as EmailTemplate } from \"../../../../../chunks/types.js\";\nasync function getEmailJobStatus(jobId) {\n  try {\n    logger.info(`Checking status for email job: ${jobId}`);\n    return {\n      jobId,\n      status: \"completed\",\n      timestamp: (/* @__PURE__ */ new Date()).toISOString()\n    };\n  } catch (error) {\n    logger.error(\"Error getting email job status:\", error);\n    throw error;\n  }\n}\nconst EMAIL_QUEUE_KEY = \"email:queue\";\nasync function GET({ url }) {\n  try {\n    const jobId = url.searchParams.get(\"jobId\");\n    if (!jobId) {\n      return json({ error: \"Job ID is required\" }, { status: 400 });\n    }\n    const status = await getEmailJobStatus(jobId);\n    return json(status);\n  } catch (error) {\n    logger.error(\"Error checking email job status:\", error);\n    return json({ error: \"Failed to check email job status\" }, { status: 500 });\n  }\n}\nasync function POST({ request }) {\n  try {\n    const body = await request.json();\n    const { template, to, data, options } = body;\n    if (!template) {\n      return json({ error: \"Template is required\" }, { status: 400 });\n    }\n    if (!to) {\n      return json({ error: \"Recipient is required\" }, { status: 400 });\n    }\n    if (!data) {\n      return json({ error: \"Data is required\" }, { status: 400 });\n    }\n    const validTemplates = Object.values(EmailTemplate);\n    if (!validTemplates.includes(template)) {\n      return json({ error: \"Invalid template\" }, { status: 400 });\n    }\n    if (options?.category && ![\"general\", \"marketing\", \"transactional\", \"notification\"].includes(options.category)) {\n      return json({ error: \"Invalid category\" }, { status: 400 });\n    }\n    if (!RedisConnection) {\n      logger.error(\"Redis connection not available\");\n      return json({ error: \"Redis connection not available\" }, { status: 500 });\n    }\n    logger.info(`Redis connection status: ${RedisConnection.status}`);\n    const id = `email_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;\n    const emailJob = {\n      id,\n      template,\n      to: Array.isArray(to) ? to : [to],\n      data,\n      options: {\n        category: options?.category || \"general\",\n        priority: options?.priority || 3,\n        // Default to medium priority\n        retries: options?.retries ?? 3,\n        // Default to 3 retries\n        tags: options?.tags || [],\n        delay: options?.delay || 0\n      },\n      createdAt: (/* @__PURE__ */ new Date()).toISOString()\n    };\n    try {\n      logger.info(`Attempting to add email to Redis queue: ${EMAIL_QUEUE_KEY}`);\n      const result = await RedisConnection.zadd(\n        EMAIL_QUEUE_KEY,\n        options?.priority || 3,\n        JSON.stringify(emailJob)\n      );\n      logger.info(`Redis zadd result: ${result}`);\n      const queueSize = await RedisConnection.zcard(EMAIL_QUEUE_KEY);\n      logger.info(`Queue size after adding email: ${queueSize}`);\n      logger.info(\n        `Email queued in Redis: ${id} (${template} to ${Array.isArray(to) ? to.join(\", \") : to})`\n      );\n    } catch (error) {\n      logger.error(`Error adding email to Redis queue: ${error}`);\n      throw error;\n    }\n    return json({ success: true, jobId: id });\n  } catch (error) {\n    logger.error(\"Error queuing email:\", error);\n    return json({ error: \"Failed to queue email\" }, { status: 500 });\n  }\n}\nexport {\n  GET,\n  POST\n};\n"], "names": [], "mappings": ";;;;;;AAIA,eAAe,iBAAiB,CAAC,KAAK,EAAE;AACxC,EAAE,IAAI;AACN,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC,CAAC;AAC1D,IAAI,OAAO;AACX,MAAM,KAAK;AACX,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AACzD,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC;AAC1D,IAAI,MAAM,KAAK;AACf;AACA;AACA,MAAM,eAAe,GAAG,aAAa;AACrC,eAAe,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE;AAC5B,EAAE,IAAI;AACN,IAAI,MAAM,KAAK,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC;AAC/C,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oBAAoB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnE;AACA,IAAI,MAAM,MAAM,GAAG,MAAM,iBAAiB,CAAC,KAAK,CAAC;AACjD,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC;AACvB,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC;AAC3D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,kCAAkC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC/E;AACA;AACA,eAAe,IAAI,CAAC,EAAE,OAAO,EAAE,EAAE;AACjC,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,MAAM,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,IAAI;AAChD,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,sBAAsB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACrE;AACA,IAAI,IAAI,CAAC,EAAE,EAAE;AACb,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtE;AACA,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,kBAAkB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE;AACA,IAAI,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC;AACvD,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;AAC5C,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,kBAAkB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE;AACA,IAAI,IAAI,OAAO,EAAE,QAAQ,IAAI,CAAC,CAAC,SAAS,EAAE,WAAW,EAAE,eAAe,EAAE,cAAc,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AACpH,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,kBAAkB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE;AACA,IAAI,IAAI,CAAC,eAAe,EAAE;AAC1B,MAAM,MAAM,CAAC,KAAK,CAAC,gCAAgC,CAAC;AACpD,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,gCAAgC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC/E;AACA,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,yBAAyB,EAAE,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC;AACrE,IAAI,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AACnF,IAAI,MAAM,QAAQ,GAAG;AACrB,MAAM,EAAE;AACR,MAAM,QAAQ;AACd,MAAM,EAAE,EAAE,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC;AACvC,MAAM,IAAI;AACV,MAAM,OAAO,EAAE;AACf,QAAQ,QAAQ,EAAE,OAAO,EAAE,QAAQ,IAAI,SAAS;AAChD,QAAQ,QAAQ,EAAE,OAAO,EAAE,QAAQ,IAAI,CAAC;AACxC;AACA,QAAQ,OAAO,EAAE,OAAO,EAAE,OAAO,IAAI,CAAC;AACtC;AACA,QAAQ,IAAI,EAAE,OAAO,EAAE,IAAI,IAAI,EAAE;AACjC,QAAQ,KAAK,EAAE,OAAO,EAAE,KAAK,IAAI;AACjC,OAAO;AACP,MAAM,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AACzD,KAAK;AACL,IAAI,IAAI;AACR,MAAM,MAAM,CAAC,IAAI,CAAC,CAAC,wCAAwC,EAAE,eAAe,CAAC,CAAC,CAAC;AAC/E,MAAM,MAAM,MAAM,GAAG,MAAM,eAAe,CAAC,IAAI;AAC/C,QAAQ,eAAe;AACvB,QAAQ,OAAO,EAAE,QAAQ,IAAI,CAAC;AAC9B,QAAQ,IAAI,CAAC,SAAS,CAAC,QAAQ;AAC/B,OAAO;AACP,MAAM,MAAM,CAAC,IAAI,CAAC,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAC,CAAC;AACjD,MAAM,MAAM,SAAS,GAAG,MAAM,eAAe,CAAC,KAAK,CAAC,eAAe,CAAC;AACpE,MAAM,MAAM,CAAC,IAAI,CAAC,CAAC,+BAA+B,EAAE,SAAS,CAAC,CAAC,CAAC;AAChE,MAAM,MAAM,CAAC,IAAI;AACjB,QAAQ,CAAC,uBAAuB,EAAE,EAAE,CAAC,EAAE,EAAE,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;AAChG,OAAO;AACP,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,MAAM,CAAC,KAAK,CAAC,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC,CAAC;AACjE,MAAM,MAAM,KAAK;AACjB;AACA,IAAI,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;AAC7C,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC;AAC/C,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpE;AACA;;;;"}