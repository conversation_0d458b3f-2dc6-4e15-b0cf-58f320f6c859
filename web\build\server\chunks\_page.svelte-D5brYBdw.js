import { p as push, V as copy_payload, W as assign_payload, q as pop, S as store_get, T as unsubscribe_stores, M as ensure_array_like, N as attr, O as escape_html, K as fallback, a0 as slot, Q as bind_props, ab as store_mutate, P as stringify, R as spread_props } from './index3-CqUPEnZw.js';
import { a as toast } from './Toaster.svelte_svelte_type_style_lang-C29KBcns.js';
import { w as writable } from './index2-Cut0V_vU.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { R as Root$3, P as Portal, S as Sheet_overlay, a as Sheet_content, b as Sheet_header, c as Sheet_title, d as Sheet_description } from './index10-F28UXWIO.js';
import { B as Button } from './button-CrucCo1G.js';
import { B as Badge } from './badge-C9pSznab.js';
import { P as Progress } from './progress-DR0SfStT.js';
import { S as Switch } from './switch-CwRjBz3R.js';
import { R as Root$4, P as Portal$1, A as Alert_dialog_overlay, a as Alert_dialog_content, b as Alert_dialog_header, c as Alert_dialog_title, d as Alert_dialog_description, e as Alert_dialog_footer, f as Alert_dialog_cancel, g as Alert_dialog_action } from './index11-Dmn3AdIN.js';
import { A as Accordion_root, a as Accordion_item, b as Accordion_trigger, c as Accordion_content } from './accordion-trigger-DwieKZVA.js';
import { S as Scroll_area } from './scroll-area-Dn69zlyp.js';
import { f as formatDistanceToNow } from './utils-pWl1tgmi.js';
import { R as ResolvedKeywords } from './ResolvedKeywords-DvTGVMv0.js';
import { R as ResolvedLocations } from './ResolvedLocations-hOYLx-F1.js';
import { D as Dialog_trigger } from './dialog-trigger-CNXm7UD7.js';
import { C as Circle_stop } from './circle-stop-DEPHLFHi.js';
import { R as Refresh_cw } from './refresh-cw-Dvfix_NJ.js';
import { F as File_text } from './file-text-HttY5S5h.js';
import { T as Target } from './target-VMK77SRs.js';
import { B as Briefcase } from './briefcase-DBFF7i-g.js';
import { B as Building } from './building-8WHBOPYC.js';
import { M as Map_pin } from './map-pin-BBU2RKZV.js';
import { D as Dollar_sign } from './dollar-sign-CXBwKToB.js';
import { C as Calendar } from './calendar-S3GMzTWi.js';
import { S as Sheet_footer } from './sheet-footer-B80ycEhL.js';
import { C as Clock } from './clock-BHOPwoCS.js';
import { C as Circle_x } from './circle-x-DFm9GVXv.js';
import { C as Circle_check_big } from './circle-check-big-DBrIQZ7A.js';
import { P as Play } from './play-DKNYqs4c.js';
import { I as Input } from './input-DF0gPqYN.js';
import { C as Card } from './card-D-TLkt4h.js';
import { C as Card_content } from './card-content-CrxB5iaZ.js';
import { C as Card_description } from './card-description-CMuO6f9m.js';
import { C as Card_footer } from './card-footer-Bs6oLfVt.js';
import { C as Card_header } from './card-header-BSbSWnCH.js';
import { C as Card_title } from './card-title-DNJv4RN2.js';
import { R as Root$2, S as Select_trigger, a as Select_content, b as Select_item } from './index12-H6t3LX3-.js';
import { S as Slider } from './slider-D7_iPc_Q.js';
import { L as Label } from './label-Dt8gTF_8.js';
import { M as Multi_combobox } from './multi-combobox-BJ-pW9qf.js';
import { c as createFeatureAccess } from './index13-DOBlGKWb.js';
import { o as openPricingModal } from './pricing-D13CEnfk.js';
import { isFeatureEnabled, shouldBypassLimits, getFeatureConfig } from './feature-flags-Dcd1_1ov.js';
import { T as Triangle_alert } from './triangle-alert-DOwM8mYc.js';
import { L as Lock } from './lock-Dkt3avTK.js';
import { formatDistance } from 'date-fns';
import { g as goto } from './client-dNyMPa8V.js';
import { R as Root$1, d as Dialog_overlay, D as Dialog_content } from './index7-BURUpWjT.js';
import { S as Search } from './search-B0oHlTPS.js';
import { S as Select_value } from './select-value-nUrqCsCq.js';
import { D as Dialog_header, a as Dialog_title, b as Dialog_description, c as Dialog_footer } from './dialog-description-CxPAHL_4.js';
import { X } from './x-DwZgpWRG.js';
import { P as Plus } from './plus-e8i_Czzl.js';
import { a as checkAutomationEligibility } from './profileHelpers-m3Uw-RPd.js';
import { j as zodClient } from './zod-DfpldWlD.js';
import { s as superForm } from './superForm-CVYoTAIb.js';
import { a as automationFormSchema } from './27-DdP7DGH-.js';
import { R as Root, T as Tabs_list, a as Tabs_content } from './index9-3zbfQ0pE.js';
import { T as Tabs_trigger } from './tabs-trigger-Zq-B9CEL.js';
import { U as User } from './user-DpDpidvb.js';
import 'clsx';
import './false-CRHihH2U.js';
import './dialog-overlay-CspOQRJq.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './use-id-CcFpwo20.js';
import './context-oepKpCf5.js';
import './kbd-constants-Ch6RKbNZ.js';
import './presence-layer-B0FVaAYL.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './index-server-CezSOnuG.js';
import './index-DjwFQdT_.js';
import 'tailwind-merge';
import './scroll-lock-BkBz2nVp.js';
import './is-mzPc4wSG.js';
import './noop-n4I-x7yK.js';
import './dialog-description2-rfr-pd9k.js';
import './hidden-input-1eDzjGOB.js';
import './use-roving-focus.svelte-BzQ2WziA.js';
import './chevron-down-xGjWLrZH.js';
import './Icon-A4vzmk-O.js';
import './use-debounce.svelte-gxToHznJ.js';
import './mounted-BL5aWRUY.js';
import './box-auto-reset.svelte-BDripiF0.js';
import './check2-Bg6barQb.js';
import './Icon2-DkOdBr51.js';
import './popper-layer-force-mount-GhIXXB9T.js';
import './index14-C2WSwUih.js';
import './dropdown-store-B4Dfz2ZI.js';
import './clone-BRGVxGEr.js';
import './check-WP_4Msti.js';
import './features-SWeUHekJ.js';
import './dynamic-registry-Cmy1Wm2Q.js';
import './index4-HpJcNJHQ.js';
import './constants-BaiUsPxc.js';
import './types-D78SXuvm.js';
import './stores-DSLMNPqo.js';
import './stringify-DWCARkQV.js';
import './index-Ddp2AB5f.js';
import './prisma-Cit_HrSw.js';
import '@prisma/client';

function Sheet_trigger($$payload, $$props) {
  push();
  let { ref = null, $$slots, $$events, ...restProps } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<!---->`;
    Dialog_trigger($$payload2, spread_props([
      { "data-slot": "sheet-trigger" },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        }
      }
    ]));
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function AutomationRunSheet($$payload, $$props) {
  push();
  var $$store_subs;
  const {
    open = false,
    automationRun,
    onClose = () => {
    },
    onRefresh = () => {
    },
    onStop = () => {
    }
  } = $$props;
  let isLoading = false;
  let isStoppingRun = false;
  let selectedJobsForAutoApply = /* @__PURE__ */ new Set();
  let showAutoApplyConfirm = false;
  let jobListings = [];
  let isLoadingJobs = false;
  const runStore = writable(automationRun);
  async function fetchJobListings() {
    if (!store_get($$store_subs ??= {}, "$runStore", runStore) || !store_get($$store_subs ??= {}, "$runStore", runStore).id || !store_get($$store_subs ??= {}, "$runStore", runStore).matchedJobIds || store_get($$store_subs ??= {}, "$runStore", runStore).matchedJobIds.length === 0) {
      jobListings = [];
      return;
    }
    isLoadingJobs = true;
    try {
      const response = await fetch(`/api/automation/runs/${store_get($$store_subs ??= {}, "$runStore", runStore).id}/jobs`);
      if (response.ok) {
        const jobs = await response.json();
        jobListings = jobs.map((job) => ({
          ...job,
          matchScore: getJobMatchScore(job.id),
          postedAt: job.postedDate || job.createdAt
        }));
      } else {
        console.error("Failed to fetch job listings");
        jobListings = [];
      }
    } catch (error) {
      console.error("Error fetching job listings:", error);
      jobListings = [];
    } finally {
      isLoadingJobs = false;
    }
  }
  function getJobMatchScore(jobId) {
    if (!store_get($$store_subs ??= {}, "$runStore", runStore)?.jobMatchData) return 0;
    const matchData = store_get($$store_subs ??= {}, "$runStore", runStore).jobMatchData[jobId];
    return matchData?.matchScore || 0;
  }
  function formatSalary(job) {
    if (job.salary) return job.salary;
    if (job.salaryMin && job.salaryMax) {
      const min = Math.round(job.salaryMin / 1e3);
      const max = Math.round(job.salaryMax / 1e3);
      return `$${min}k - $${max}k`;
    }
    if (job.salaryMin) {
      const min = Math.round(job.salaryMin / 1e3);
      return `$${min}k+`;
    }
    return "";
  }
  const jobsToDisplay = jobListings;
  function toggleJobSelection(jobId) {
    if (selectedJobsForAutoApply.has(jobId)) {
      selectedJobsForAutoApply.delete(jobId);
    } else {
      selectedJobsForAutoApply.add(jobId);
    }
    selectedJobsForAutoApply = new Set(selectedJobsForAutoApply);
  }
  function showAutoApplyConfirmation() {
    if (selectedJobsForAutoApply.size === 0) {
      toast.error("Please select at least one job to enable auto-apply");
      return;
    }
    showAutoApplyConfirm = true;
  }
  async function confirmAutoApply() {
    if (!store_get($$store_subs ??= {}, "$runStore", runStore) || !store_get($$store_subs ??= {}, "$runStore", runStore).id) return;
    try {
      const selectedJobs = Array.from(selectedJobsForAutoApply);
      console.log("Enabling auto-apply for jobs:", selectedJobs);
      const response = await fetch(`/api/automation/runs/${store_get($$store_subs ??= {}, "$runStore", runStore).id}/settings`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          autoApplyEnabled: true,
          selectedJobIds: selectedJobs
        })
      });
      if (response.ok) {
        const updatedRun = await response.json();
        runStore.set(updatedRun);
        toast.success(`Auto-apply enabled for ${selectedJobs.length} job${selectedJobs.length === 1 ? "" : "s"}`);
        showAutoApplyConfirm = false;
        selectedJobsForAutoApply.clear();
      } else {
        const error = await response.json();
        toast.error(error.message || "Failed to enable auto-apply");
      }
    } catch (error) {
      console.error("Error enabling auto-apply:", error);
      toast.error("Failed to enable auto-apply");
    }
  }
  async function stopAutomationRun() {
    if (!store_get($$store_subs ??= {}, "$runStore", runStore) || !store_get($$store_subs ??= {}, "$runStore", runStore).id) return;
    isStoppingRun = true;
    try {
      const response = await fetch(`/api/automation/runs/${store_get($$store_subs ??= {}, "$runStore", runStore).id}/stop`, { method: "POST" });
      if (response.ok) {
        const updatedRun = await response.json();
        runStore.update((run) => ({
          ...run,
          status: "stopped",
          stoppedAt: updatedRun.stoppedAt
        }));
        toast.success("Automation run stopped");
        onStop(store_get($$store_subs ??= {}, "$runStore", runStore).id);
      } else {
        const error = await response.json();
        toast.error(error.message || "Failed to stop automation run");
      }
    } catch (error) {
      console.error("Error stopping automation run:", error);
      toast.error("An error occurred while stopping the automation run");
    } finally {
      isStoppingRun = false;
    }
  }
  async function refreshData() {
    if (!store_get($$store_subs ??= {}, "$runStore", runStore) || !store_get($$store_subs ??= {}, "$runStore", runStore).id) return;
    isLoading = true;
    try {
      const response = await fetch(`/api/automation/runs/${store_get($$store_subs ??= {}, "$runStore", runStore).id}`);
      if (response.ok) {
        const updatedRun = await response.json();
        runStore.set(updatedRun);
        await fetchJobListings();
        toast.success("Data refreshed");
        onRefresh(updatedRun);
      } else {
        toast.error("Failed to refresh data");
      }
    } catch (error) {
      console.error("Error refreshing data:", error);
      toast.error("An error occurred while refreshing data");
    } finally {
      isLoading = false;
    }
  }
  function getStatusVariant(status) {
    switch (status) {
      case "start":
      case "running":
        return "default";
      case "completed":
        return "outline";
      case "failed":
        return "destructive";
      case "stopped":
        return "secondary";
      case "in progress":
      case "pending":
        return "secondary";
      default:
        return "secondary";
    }
  }
  function getStatusIcon(status) {
    switch (status) {
      case "start":
      case "running":
        return Play;
      case "completed":
        return Circle_check_big;
      case "failed":
        return Circle_x;
      case "stopped":
        return Circle_stop;
      case "in progress":
      case "pending":
        return Clock;
      default:
        return Clock;
    }
  }
  function getStatusLabel(status) {
    switch (status) {
      case "start":
        return "Starting";
      case "running":
        return "Running";
      case "completed":
        return "Completed";
      case "failed":
        return "Failed";
      case "stopped":
        return "Stopped";
      case "in progress":
        return "In Progress";
      case "pending":
        return "Pending";
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  }
  function calculateProgress(run) {
    if (!run) return 0;
    if (run.status === "completed") return 100;
    if (["failed", "stopped"].includes(run.status)) return run.progress || 0;
    if (run.status === "start") return 5;
    if (run.status === "in progress") return run.progress || 50;
    return run.progress || 0;
  }
  function getProfileData2(profile) {
    if (!profile?.data) return {};
    try {
      if (typeof profile.data === "string") {
        return JSON.parse(profile.data);
      }
      return profile.data;
    } catch (e) {
      console.error("Error parsing profile data:", e);
      return {};
    }
  }
  function handleSheetClose() {
    onClose();
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<!---->`;
    Root$3($$payload2, {
      open,
      onOpenChange: handleSheetClose,
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        Sheet_trigger($$payload3, {});
        $$payload3.out += `<!----> <!---->`;
        Portal($$payload3, {
          children: ($$payload4) => {
            $$payload4.out += `<!---->`;
            Sheet_overlay($$payload4, {});
            $$payload4.out += `<!----> <!---->`;
            Sheet_content($$payload4, {
              side: "right",
              class: "w-full gap-0 sm:max-w-xl md:max-w-2xl lg:max-w-3xl",
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Sheet_header($$payload5, {
                  class: "border-border m-0 flex flex-col gap-0 border-b",
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->`;
                    Sheet_title($$payload6, {
                      class: "text-lg",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->${escape_html(getProfileData2(store_get($$store_subs ??= {}, "$runStore", runStore).profile).fullName || "Unnamed Profile")}`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> <!---->`;
                    Sheet_description($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->${escape_html(getProfileData2(store_get($$store_subs ??= {}, "$runStore", runStore).profile).title || getProfileData2(store_get($$store_subs ??= {}, "$runStore", runStore).profile).headline || "No title specified")}`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Progress($$payload5, {
                  value: calculateProgress(store_get($$store_subs ??= {}, "$runStore", runStore)),
                  max: 100,
                  class: "mb-0 rounded-none"
                });
                $$payload5.out += `<!----> <div class="flex flex-row justify-between p-4 pt-2"><div class="text-xs font-medium text-gray-400">Progress</div> <div class="text-right text-xs text-gray-400">${escape_html(calculateProgress(store_get($$store_subs ??= {}, "$runStore", runStore)))}% Complete</div></div> `;
                Scroll_area($$payload5, {
                  class: "h-[calc(100vh-100px)] w-full",
                  children: ($$payload6) => {
                    if (store_get($$store_subs ??= {}, "$runStore", runStore)) {
                      $$payload6.out += "<!--[-->";
                      $$payload6.out += `<div class="space-y-6 px-4"><div class="flex items-center justify-between"><div class="flex items-center gap-2">`;
                      Badge($$payload6, {
                        variant: getStatusVariant(store_get($$store_subs ??= {}, "$runStore", runStore).status),
                        class: "text-sm",
                        children: ($$payload7) => {
                          const StatusIcon = getStatusIcon(store_get($$store_subs ??= {}, "$runStore", runStore).status);
                          $$payload7.out += `<!---->`;
                          StatusIcon($$payload7, { class: "mr-1 h-3 w-3" });
                          $$payload7.out += `<!----> ${escape_html(getStatusLabel(store_get($$store_subs ??= {}, "$runStore", runStore).status))}`;
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out += `<!----> `;
                      if (store_get($$store_subs ??= {}, "$runStore", runStore).createdAt) {
                        $$payload6.out += "<!--[-->";
                        $$payload6.out += `<span class="text-xs text-gray-400">Started ${escape_html(formatDistanceToNow(new Date(store_get($$store_subs ??= {}, "$runStore", runStore).createdAt)))} ago</span>`;
                      } else {
                        $$payload6.out += "<!--[!-->";
                      }
                      $$payload6.out += `<!--]--></div> <div class="flex items-center gap-2">`;
                      if ([
                        "running",
                        "pending",
                        "start",
                        "in progress"
                      ].includes(store_get($$store_subs ??= {}, "$runStore", runStore).status)) {
                        $$payload6.out += "<!--[-->";
                        Button($$payload6, {
                          variant: "outline",
                          size: "sm",
                          onclick: stopAutomationRun,
                          disabled: isStoppingRun,
                          children: ($$payload7) => {
                            Circle_stop($$payload7, { class: "mr-2 h-4 w-4" });
                            $$payload7.out += `<!----> ${escape_html(isStoppingRun ? "Stopping..." : "Stop Run")}`;
                          },
                          $$slots: { default: true }
                        });
                      } else {
                        $$payload6.out += "<!--[!-->";
                      }
                      $$payload6.out += `<!--]--> `;
                      Button($$payload6, {
                        variant: "outline",
                        size: "sm",
                        onclick: refreshData,
                        disabled: isLoading,
                        children: ($$payload7) => {
                          Refresh_cw($$payload7, {
                            class: `mr-2 h-4 w-4 ${stringify(isLoading ? "animate-spin" : "")}`
                          });
                          $$payload7.out += `<!----> ${escape_html(isLoading ? "Refreshing..." : "Refresh")}`;
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out += `<!----></div></div> <!---->`;
                      Accordion_root($$payload6, {
                        type: "single",
                        class: "border-border w-full rounded-md border",
                        children: ($$payload7) => {
                          $$payload7.out += `<!---->`;
                          Accordion_item($$payload7, {
                            value: "run-info",
                            children: ($$payload8) => {
                              $$payload8.out += `<!---->`;
                              Accordion_trigger($$payload8, {
                                class: "p-4 text-left",
                                children: ($$payload9) => {
                                  $$payload9.out += `<h3 class="text-sm font-medium text-gray-400">Search Parameters</h3>`;
                                },
                                $$slots: { default: true }
                              });
                              $$payload8.out += `<!----> <!---->`;
                              Accordion_content($$payload8, {
                                children: ($$payload9) => {
                                  $$payload9.out += `<div class="grid grid-cols-1 gap-4 sm:grid-cols-2"><div class="rounded-lg border p-4"><h4 class="mb-2 text-sm font-medium text-gray-400">Profile</h4> `;
                                  if (store_get($$store_subs ??= {}, "$runStore", runStore).profile) {
                                    $$payload9.out += "<!--[-->";
                                    $$payload9.out += `<div class="mb-1 text-base font-medium">${escape_html(getProfileData2(store_get($$store_subs ??= {}, "$runStore", runStore).profile).fullName || "Unnamed Profile")}</div> <div class="mb-2 text-sm text-gray-400">${escape_html(getProfileData2(store_get($$store_subs ??= {}, "$runStore", runStore).profile).title || getProfileData2(store_get($$store_subs ??= {}, "$runStore", runStore).profile).headline || "No title specified")}</div> `;
                                    if (store_get($$store_subs ??= {}, "$runStore", runStore).profile.documents && store_get($$store_subs ??= {}, "$runStore", runStore).profile.documents.length > 0) {
                                      $$payload9.out += "<!--[-->";
                                      Badge($$payload9, {
                                        variant: "outline",
                                        class: "text-xs",
                                        children: ($$payload10) => {
                                          File_text($$payload10, { class: "mr-1 h-3 w-3" });
                                          $$payload10.out += `<!----> ${escape_html(store_get($$store_subs ??= {}, "$runStore", runStore).profile.documents.length)}
                            ${escape_html(store_get($$store_subs ??= {}, "$runStore", runStore).profile.documents.length === 1 ? "resume" : "resumes")}`;
                                        },
                                        $$slots: { default: true }
                                      });
                                    } else {
                                      $$payload9.out += "<!--[!-->";
                                    }
                                    $$payload9.out += `<!--]-->`;
                                  } else {
                                    $$payload9.out += "<!--[!-->";
                                    $$payload9.out += `<div class="text-sm text-gray-400">Profile information not available</div>`;
                                  }
                                  $$payload9.out += `<!--]--></div> <div class="rounded-lg border p-4"><h4 class="mb-2 text-sm font-medium text-gray-400">Search Criteria</h4> <div class="space-y-2"><div><span class="text-xs text-gray-400">Keywords:</span> <div class="text-sm">`;
                                  ResolvedKeywords($$payload9, {
                                    keywordIds: store_get($$store_subs ??= {}, "$runStore", runStore).keywords || "",
                                    fallback: "None specified"
                                  });
                                  $$payload9.out += `<!----></div></div> <div><span class="text-xs text-gray-400">Location:</span> <div class="text-sm">`;
                                  ResolvedLocations($$payload9, {
                                    locationIds: store_get($$store_subs ??= {}, "$runStore", runStore).location || "",
                                    fallback: "None specified"
                                  });
                                  $$payload9.out += `<!----></div></div></div></div></div>`;
                                },
                                $$slots: { default: true }
                              });
                              $$payload8.out += `<!---->`;
                            },
                            $$slots: { default: true }
                          });
                          $$payload7.out += `<!---->`;
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out += `<!----> <div><div class="mb-4 flex items-center justify-between"><h3 class="text-sm font-medium text-gray-400">Jobs Found (${escape_html(isLoadingJobs ? "..." : jobsToDisplay.length)})</h3> `;
                      if (jobsToDisplay.length > 0 && !store_get($$store_subs ??= {}, "$runStore", runStore).autoApplyEnabled) {
                        $$payload6.out += "<!--[-->";
                        Button($$payload6, {
                          variant: "outline",
                          size: "sm",
                          onclick: showAutoApplyConfirmation,
                          disabled: selectedJobsForAutoApply.size === 0,
                          class: "h-8 text-xs",
                          children: ($$payload7) => {
                            Target($$payload7, { class: "mr-1 h-3 w-3" });
                            $$payload7.out += `<!----> Auto Apply (${escape_html(selectedJobsForAutoApply.size)})`;
                          },
                          $$slots: { default: true }
                        });
                      } else {
                        $$payload6.out += "<!--[!-->";
                      }
                      $$payload6.out += `<!--]--></div> `;
                      if (isLoadingJobs) {
                        $$payload6.out += "<!--[-->";
                        $$payload6.out += `<div class="flex flex-col items-center justify-center rounded-lg border border-dashed p-6 text-center">`;
                        Refresh_cw($$payload6, {
                          class: "mb-2 h-8 w-8 animate-spin text-gray-400"
                        });
                        $$payload6.out += `<!----> <p class="text-sm text-gray-400">Loading jobs...</p></div>`;
                      } else if (jobsToDisplay.length === 0) {
                        $$payload6.out += "<!--[1-->";
                        $$payload6.out += `<div class="flex flex-col items-center justify-center rounded-lg border border-dashed p-6 text-center">`;
                        Briefcase($$payload6, { class: "mb-2 h-8 w-8 text-gray-400" });
                        $$payload6.out += `<!----> <p class="text-sm text-gray-400">`;
                        if ([
                          "running",
                          "pending",
                          "start",
                          "in progress"
                        ].includes(store_get($$store_subs ??= {}, "$runStore", runStore).status)) {
                          $$payload6.out += "<!--[-->";
                          $$payload6.out += `Jobs will appear here as they are found`;
                        } else {
                          $$payload6.out += "<!--[!-->";
                          $$payload6.out += `No jobs were found during this automation run`;
                        }
                        $$payload6.out += `<!--]--></p></div>`;
                      } else {
                        $$payload6.out += "<!--[!-->";
                        const each_array = ensure_array_like(jobsToDisplay);
                        $$payload6.out += `<div class="grid grid-cols-2 gap-4"><!--[-->`;
                        for (let $$index_1 = 0, $$length = each_array.length; $$index_1 < $$length; $$index_1++) {
                          let job = each_array[$$index_1];
                          $$payload6.out += `<div class="flex flex-col space-y-3 rounded-lg border p-4"><div class="flex items-start justify-between"><div class="min-w-0 flex-1"><div class="flex items-center gap-2"><a${attr("href", job.applyLink)} target="_blank" class="font-medium">${escape_html(job.title)}</a> `;
                          if (store_get($$store_subs ??= {}, "$runStore", runStore).autoApplyEnabled && store_get($$store_subs ??= {}, "$runStore", runStore).selectedJobIds?.includes(job.id)) {
                            $$payload6.out += "<!--[-->";
                            Badge($$payload6, {
                              variant: "default",
                              class: "bg-blue-600 text-xs",
                              children: ($$payload7) => {
                                $$payload7.out += `<!---->Auto-Apply`;
                              },
                              $$slots: { default: true }
                            });
                          } else {
                            $$payload6.out += "<!--[!-->";
                          }
                          $$payload6.out += `<!--]--></div> `;
                          if (job.company) {
                            $$payload6.out += "<!--[-->";
                            $$payload6.out += `<div class="flex items-center text-sm text-gray-400">`;
                            Building($$payload6, { class: "mr-1 h-3 w-3" });
                            $$payload6.out += `<!----> ${escape_html(job.company)}</div>`;
                          } else {
                            $$payload6.out += "<!--[!-->";
                          }
                          $$payload6.out += `<!--]--></div> <div class="flex items-center gap-2">`;
                          if (!store_get($$store_subs ??= {}, "$runStore", runStore).autoApplyEnabled) {
                            $$payload6.out += "<!--[-->";
                            Switch($$payload6, {
                              checked: selectedJobsForAutoApply.has(job.id),
                              onCheckedChange: (checked) => {
                                if (checked) {
                                  toggleJobSelection(job.id);
                                } else {
                                  toggleJobSelection(job.id);
                                }
                              }
                            });
                          } else {
                            $$payload6.out += "<!--[!-->";
                          }
                          $$payload6.out += `<!--]--></div></div> <div class="grid grid-cols-2 gap-2 text-xs">`;
                          if (job.matchScore) {
                            $$payload6.out += "<!--[-->";
                            Badge($$payload6, {
                              variant: "outline",
                              class: "text-xs",
                              children: ($$payload7) => {
                                $$payload7.out += `<!---->${escape_html(job.matchScore)}% match`;
                              },
                              $$slots: { default: true }
                            });
                          } else {
                            $$payload6.out += "<!--[!-->";
                          }
                          $$payload6.out += `<!--]--> `;
                          if (job.location) {
                            $$payload6.out += "<!--[-->";
                            $$payload6.out += `<div class="flex items-center text-gray-400">`;
                            Map_pin($$payload6, { class: "mr-1 h-3 w-3" });
                            $$payload6.out += `<!----> ${escape_html(job.location)}</div>`;
                          } else {
                            $$payload6.out += "<!--[!-->";
                          }
                          $$payload6.out += `<!--]--> `;
                          if (formatSalary(job)) {
                            $$payload6.out += "<!--[-->";
                            const formattedSalary = formatSalary(job);
                            $$payload6.out += `<div class="flex items-center text-gray-400">`;
                            Dollar_sign($$payload6, { class: "mr-1 h-3 w-3" });
                            $$payload6.out += `<!----> ${escape_html(formattedSalary)}</div>`;
                          } else {
                            $$payload6.out += "<!--[!-->";
                          }
                          $$payload6.out += `<!--]--> `;
                          if (job.postedAt) {
                            $$payload6.out += "<!--[-->";
                            $$payload6.out += `<div class="flex items-center text-gray-400">`;
                            Calendar($$payload6, { class: "mr-1 h-3 w-3" });
                            $$payload6.out += `<!----> Posted ${escape_html(formatDistanceToNow(new Date(job.postedAt)))} ago</div>`;
                          } else {
                            $$payload6.out += "<!--[!-->";
                          }
                          $$payload6.out += `<!--]--></div> <div class="space-y-2">`;
                          if (job.remoteType) {
                            $$payload6.out += "<!--[-->";
                            $$payload6.out += `<div class="flex items-center gap-1">`;
                            Badge($$payload6, {
                              variant: "outline",
                              class: "text-xs",
                              children: ($$payload7) => {
                                $$payload7.out += `<!---->${escape_html(job.remoteType)}`;
                              },
                              $$slots: { default: true }
                            });
                            $$payload6.out += `<!----></div>`;
                          } else {
                            $$payload6.out += "<!--[!-->";
                          }
                          $$payload6.out += `<!--]--> `;
                          if (job.benefits && job.benefits.length > 0) {
                            $$payload6.out += "<!--[-->";
                            const each_array_1 = ensure_array_like(job.benefits.slice(0, 2));
                            $$payload6.out += `<div class="flex flex-wrap gap-1"><!--[-->`;
                            for (let $$index = 0, $$length2 = each_array_1.length; $$index < $$length2; $$index++) {
                              let benefit = each_array_1[$$index];
                              Badge($$payload6, {
                                variant: "secondary",
                                class: "text-xs",
                                children: ($$payload7) => {
                                  $$payload7.out += `<!---->${escape_html(benefit)}`;
                                },
                                $$slots: { default: true }
                              });
                            }
                            $$payload6.out += `<!--]--> `;
                            if (job.benefits.length > 2) {
                              $$payload6.out += "<!--[-->";
                              Badge($$payload6, {
                                variant: "secondary",
                                class: "text-xs",
                                children: ($$payload7) => {
                                  $$payload7.out += `<!---->+${escape_html(job.benefits.length - 2)} more`;
                                },
                                $$slots: { default: true }
                              });
                            } else {
                              $$payload6.out += "<!--[!-->";
                            }
                            $$payload6.out += `<!--]--></div>`;
                          } else {
                            $$payload6.out += "<!--[!-->";
                          }
                          $$payload6.out += `<!--]--></div></div>`;
                        }
                        $$payload6.out += `<!--]--></div>`;
                      }
                      $$payload6.out += `<!--]--></div></div>`;
                    } else {
                      $$payload6.out += "<!--[!-->";
                      $$payload6.out += `<div class="flex h-40 items-center justify-center"><p class="text-gray-400">No automation run data available</p></div>`;
                    }
                    $$payload6.out += `<!--]-->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <!---->`;
                Sheet_footer($$payload5, {
                  class: "border-border m-0 grid grid-cols-4 flex-col-reverse gap-4 border-t p-2 sm:flex-row sm:justify-end",
                  children: ($$payload6) => {
                    Button($$payload6, {
                      variant: "outline",
                      onclick: handleSheetClose,
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Close`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Button($$payload6, {
                      variant: "default",
                      onclick: () => {
                        handleSheetClose();
                        if (store_get($$store_subs ??= {}, "$runStore", runStore) && store_get($$store_subs ??= {}, "$runStore", runStore).id) {
                          window.location.href = `/dashboard/automation/${store_get($$store_subs ??= {}, "$runStore", runStore).id}`;
                        }
                      },
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->View Full Details`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> <!---->`;
    Root$4($$payload2, {
      get open() {
        return showAutoApplyConfirm;
      },
      set open($$value) {
        showAutoApplyConfirm = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        Portal$1($$payload3, {
          children: ($$payload4) => {
            $$payload4.out += `<!---->`;
            Alert_dialog_overlay($$payload4, {});
            $$payload4.out += `<!----> <!---->`;
            Alert_dialog_content($$payload4, {
              class: "gap-0 p-0 sm:max-w-[425px]",
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Alert_dialog_header($$payload5, {
                  class: "border-border border-b p-4",
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->`;
                    Alert_dialog_title($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Confirm Auto-Apply`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <!---->`;
                Alert_dialog_description($$payload5, {
                  class: "p-4",
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Are you sure you want to enable auto-apply for ${escape_html(selectedJobsForAutoApply.size)} selected job${escape_html(selectedJobsForAutoApply.size === 1 ? "" : "s")}? <br/><br/> This will automatically submit applications to the selected jobs using your profile and resume.`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> <!---->`;
                Alert_dialog_footer($$payload5, {
                  class: "border-border flex justify-end gap-4 border-t p-2",
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->`;
                    Alert_dialog_cancel($$payload6, {
                      onclick: () => showAutoApplyConfirm = false,
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Cancel`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> <!---->`;
                    Alert_dialog_action($$payload6, {
                      onclick: confirmAutoApply,
                      children: ($$payload7) => {
                        Target($$payload7, { class: "mr-2 h-4 w-4" });
                        $$payload7.out += `<!----> Enable Auto-Apply`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, { open });
  pop();
}
function EnhancedFeatureGuard($$payload, $$props) {
  push();
  let featureEnabled, bypassLimits, featureConfig, featureAccess, canAccess, blockReason, debugInfo;
  let userData = $$props["userData"];
  let featureId = $$props["featureId"];
  let limitId = fallback($$props["limitId"], void 0);
  let showUpgradePrompt = fallback($$props["showUpgradePrompt"], true);
  let fallbackMessage = fallback($$props["fallbackMessage"], void 0);
  let debugMode = fallback($$props["debugMode"], false);
  function handleUpgrade() {
    openPricingModal({
      section: "pro",
      currentPlanId: userData.role || userData.subscription?.planId || "free"
    });
  }
  featureEnabled = isFeatureEnabled(featureId);
  bypassLimits = shouldBypassLimits(featureId);
  featureConfig = getFeatureConfig(featureId);
  featureAccess = featureEnabled ? createFeatureAccess(userData) : null;
  canAccess = (() => {
    if (!featureEnabled) {
      return false;
    }
    if (bypassLimits) {
      return true;
    }
    if (!featureAccess) {
      return false;
    }
    return limitId ? featureAccess.canPerformAction(featureId, limitId) : featureAccess.hasAccess(featureId);
  })();
  blockReason = (() => {
    if (!featureEnabled) {
      return fallbackMessage || `The ${featureConfig?.description || featureId} feature is currently disabled.`;
    }
    if (bypassLimits) {
      return "";
    }
    if (!featureAccess) {
      return "Unable to check feature access.";
    }
    return limitId ? featureAccess.getBlockReason(featureId, limitId) : featureAccess.getBlockReason(featureId);
  })();
  debugInfo = debugMode ? {
    featureId,
    featureEnabled,
    bypassLimits,
    canAccess,
    blockReason,
    limitId,
    userRole: userData.role,
    planId: userData.subscription?.planId
  } : null;
  if (debugMode && debugInfo) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="mb-4 rounded border border-yellow-500 bg-yellow-50 p-2 text-xs"><strong>FeatureGuard Debug:</strong> <pre>${escape_html(JSON.stringify(debugInfo, null, 2))}</pre></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (canAccess) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<!---->`;
    slot($$payload, $$props, "default", {}, null);
    $$payload.out += `<!---->`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<div class="flex flex-col items-center justify-center rounded-md border border-dashed p-8 text-center"><div class="bg-muted mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full">`;
    if (!featureEnabled) {
      $$payload.out += "<!--[-->";
      Triangle_alert($$payload, { class: "text-warning h-6 w-6" });
    } else {
      $$payload.out += "<!--[!-->";
      Lock($$payload, { class: "text-muted-foreground h-6 w-6" });
    }
    $$payload.out += `<!--]--></div> <h3 class="mb-2 text-lg font-medium">`;
    if (!featureEnabled) {
      $$payload.out += "<!--[-->";
      $$payload.out += `Feature Unavailable`;
    } else {
      $$payload.out += "<!--[!-->";
      $$payload.out += `Access Restricted`;
    }
    $$payload.out += `<!--]--></h3> <p class="text-muted-foreground mb-4 max-w-md">${escape_html(blockReason)}</p> `;
    if (showUpgradePrompt && featureEnabled) {
      $$payload.out += "<!--[-->";
      Button($$payload, {
        variant: "outline",
        onclick: handleUpgrade,
        children: ($$payload2) => {
          $$payload2.out += `<!---->Upgrade Plan`;
        },
        $$slots: { default: true }
      });
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--></div>`;
  }
  $$payload.out += `<!--]-->`;
  bind_props($$props, {
    userData,
    featureId,
    limitId,
    showUpgradePrompt,
    fallbackMessage,
    debugMode
  });
  pop();
}
function getProfileData(profile) {
  if (!profile) return {};
  try {
    if (profile.ProfileData) {
      return {
        fullName: profile.ProfileData.fullName,
        email: profile.ProfileData.email,
        phone: profile.ProfileData.phone,
        location: profile.ProfileData.address,
        summary: profile.ProfileData.summary,
        skills: profile.ProfileData.skills ? typeof profile.ProfileData.skills === "string" ? JSON.parse(profile.ProfileData.skills) : profile.ProfileData.skills : void 0
      };
    }
    if (profile.data) {
      if (typeof profile.data === "string") {
        return JSON.parse(profile.data);
      }
      if (profile.data.data && typeof profile.data.data === "string") {
        return JSON.parse(profile.data.data);
      }
      return profile.data;
    }
    return {};
  } catch (e) {
    console.error("Error parsing profile data:", e);
    return {};
  }
}
function AutomationRunsTab($$payload, $$props) {
  push();
  var $$store_subs;
  const {
    userData,
    automationRuns,
    profiles,
    form,
    submitting,
    occupationOptions,
    locationOptions,
    searchOccupations,
    searchLocations,
    isFormValid,
    profileSuggestions,
    applySuggestions,
    checkAutomationEligibility: checkAutomationEligibility2,
    isProfileEligible,
    onRunSelect,
    onCreateRun
  } = $$props;
  let runStatusFilter = "all";
  let runSearchQuery = "";
  let createDialogOpen = false;
  const statusFilterOptions = [
    { value: "all", label: "All Status" },
    { value: "pending", label: "Pending" },
    { value: "start", label: "Starting" },
    { value: "in progress", label: "In Progress" },
    { value: "running", label: "Running" },
    { value: "completed", label: "Completed" },
    { value: "failed", label: "Failed" },
    { value: "stopped", label: "Stopped" }
  ];
  const currentStatusOption = () => {
    return statusFilterOptions.find((option) => option.value === runStatusFilter) || statusFilterOptions[0];
  };
  const filteredAutomationRuns = () => {
    return automationRuns.filter((run) => {
      if (runStatusFilter !== "all" && run.status !== runStatusFilter) {
        return false;
      }
      if (runSearchQuery.trim()) {
        const query = runSearchQuery.toLowerCase();
        const profileName = run.profile ? getProfileData(run.profile).fullName || "" : "";
        const keywords = run.keywords || "";
        const location = run.location || "";
        return profileName.toLowerCase().includes(query) || keywords.toLowerCase().includes(query) || location.toLowerCase().includes(query);
      }
      return true;
    });
  };
  function formatDistanceToNow2(date) {
    if (!date) return "";
    const dateObj = typeof date === "string" ? new Date(date) : date;
    return formatDistance(dateObj, /* @__PURE__ */ new Date(), { addSuffix: true });
  }
  function getStatusBadgeVariant(status) {
    switch (status) {
      case "completed":
        return "default";
      case "start":
      case "running":
        return "secondary";
      case "failed":
        return "destructive";
      case "stopped":
        return "outline";
      case "in progress":
      case "pending":
        return "outline";
      default:
        return "outline";
    }
  }
  function getStatusIcon(status) {
    switch (status) {
      case "completed":
        return Circle_check_big;
      case "start":
      case "running":
        return Play;
      case "failed":
        return Circle_x;
      case "stopped":
        return Circle_stop;
      case "in progress":
      case "pending":
        return Clock;
      default:
        return Clock;
    }
  }
  function calculateProgress(run) {
    if (run.status === "completed") return 100;
    if (run.status === "failed" || run.status === "stopped") return run.progress || 0;
    if (run.status === "start") return 5;
    if (run.status === "in progress") return run.progress || 50;
    if (run.status === "running") return run.progress || 50;
    return run.progress || 0;
  }
  function getStatusLabel(status) {
    switch (status) {
      case "start":
        return "Starting";
      case "in progress":
        return "In Progress";
      case "running":
        return "Running";
      case "completed":
        return "Completed";
      case "failed":
        return "Failed";
      case "stopped":
        return "Stopped";
      case "pending":
        return "Pending";
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    EnhancedFeatureGuard($$payload2, {
      userData,
      featureId: "automation",
      limitId: "automation_runs_per_month",
      showUpgradePrompt: true,
      fallbackMessage: "Automation features are not available in your current plan",
      children: ($$payload3) => {
        $$payload3.out += `<div class="border-border flex flex-wrap items-center justify-between gap-4 border-b p-2"><div class="flex flex-wrap items-center gap-2"><div class="relative flex items-center gap-2">`;
        Search($$payload3, {
          class: "text-muted-foreground absolute left-2.5 top-3 h-4 w-4"
        });
        $$payload3.out += `<!----> `;
        Input($$payload3, {
          placeholder: "Search runs...",
          class: "h-9 w-[200px] pl-9",
          get value() {
            return runSearchQuery;
          },
          set value($$value) {
            runSearchQuery = $$value;
            $$settled = false;
          }
        });
        $$payload3.out += `<!----></div> <!---->`;
        Root$2($$payload3, {
          type: "single",
          value: runStatusFilter,
          onValueChange: (value) => {
            runStatusFilter = value || "all";
          },
          children: ($$payload4) => {
            $$payload4.out += `<!---->`;
            Select_trigger($$payload4, {
              class: "w-[140px] p-2",
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Select_value($$payload5, { placeholder: currentStatusOption().label });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <!---->`;
            Select_content($$payload4, {
              class: "w-[140px]",
              children: ($$payload5) => {
                const each_array = ensure_array_like(statusFilterOptions);
                $$payload5.out += `<!--[-->`;
                for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                  let option = each_array[$$index];
                  $$payload5.out += `<!---->`;
                  Select_item($$payload5, {
                    value: option.value,
                    children: ($$payload6) => {
                      $$payload6.out += `<!---->${escape_html(option.label)}`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!---->`;
                }
                $$payload5.out += `<!--]-->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----></div> `;
        Button($$payload3, {
          size: "default",
          onclick: () => createDialogOpen = true,
          children: ($$payload4) => {
            Play($$payload4, { class: "mr-1 h-3 w-3" });
            $$payload4.out += `<!----> New Run`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----></div> <div class="p-2">`;
        if (automationRuns.length === 0) {
          $$payload3.out += "<!--[-->";
          $$payload3.out += `<div class="flex flex-col items-center justify-center rounded-lg border border-dashed border-gray-600 p-12 text-center">`;
          Search($$payload3, { class: "mb-4 h-12 w-12 text-gray-400" });
          $$payload3.out += `<!----> <h3 class="text-xl font-semibold text-gray-300">No automation runs yet</h3> <p class="mt-2 text-gray-400">Create your first automation run to start searching for jobs</p> `;
          Button($$payload3, {
            variant: "default",
            onclick: onCreateRun,
            class: "mt-4",
            children: ($$payload4) => {
              Play($$payload4, { class: "mr-2 h-4 w-4" });
              $$payload4.out += `<!----> New Automation Run`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----></div>`;
        } else if (filteredAutomationRuns().length === 0) {
          $$payload3.out += "<!--[1-->";
          $$payload3.out += `<div class="flex flex-col items-center justify-center rounded-lg border border-dashed border-gray-600 p-12 text-center">`;
          Search($$payload3, { class: "mb-4 h-12 w-12 text-gray-400" });
          $$payload3.out += `<!----> <h3 class="text-xl font-semibold text-gray-300">No runs match your filters</h3> <p class="mt-2 text-gray-400">Try adjusting your search or filter criteria</p></div>`;
        } else {
          $$payload3.out += "<!--[!-->";
          const each_array_1 = ensure_array_like(filteredAutomationRuns());
          $$payload3.out += `<div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3"><!--[-->`;
          for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
            let run = each_array_1[$$index_1];
            $$payload3.out += `<!---->`;
            Card($$payload3, {
              class: "gap-0 overflow-hidden p-0",
              children: ($$payload4) => {
                $$payload4.out += `<!---->`;
                Card_header($$payload4, {
                  class: "border-border border-b !p-4",
                  children: ($$payload5) => {
                    $$payload5.out += `<div class="flex items-center justify-between"><!---->`;
                    Card_title($$payload5, {
                      children: ($$payload6) => {
                        if (run.profile) {
                          $$payload6.out += "<!--[-->";
                          $$payload6.out += `${escape_html(getProfileData(run.profile).fullName || "Unnamed Profile")}`;
                        } else {
                          $$payload6.out += "<!--[!-->";
                          $$payload6.out += `Automation Run`;
                        }
                        $$payload6.out += `<!--]-->`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!----> `;
                    Badge($$payload5, {
                      variant: getStatusBadgeVariant(run.status),
                      class: "ml-2",
                      children: ($$payload6) => {
                        if (getStatusIcon(run.status)) {
                          $$payload6.out += "<!--[-->";
                          const Icon = getStatusIcon(run.status);
                          $$payload6.out += `<!---->`;
                          Icon($$payload6, { class: "mr-1 h-3 w-3" });
                          $$payload6.out += `<!---->`;
                        } else {
                          $$payload6.out += "<!--[!-->";
                        }
                        $$payload6.out += `<!--]--> ${escape_html(getStatusLabel(run.status))}`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!----></div> <!---->`;
                    Card_description($$payload5, {
                      children: ($$payload6) => {
                        if (run.createdAt) {
                          $$payload6.out += "<!--[-->";
                          $$payload6.out += `Started ${escape_html(formatDistanceToNow2(new Date(run.createdAt)))} ago`;
                        } else {
                          $$payload6.out += "<!--[!-->";
                        }
                        $$payload6.out += `<!--]-->`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----> `;
                Progress($$payload4, {
                  value: calculateProgress(run),
                  max: 100,
                  class: "rounded-none"
                });
                $$payload4.out += `<!----> <!---->`;
                Card_content($$payload4, {
                  class: "flex flex-col gap-4 p-4 pt-3",
                  children: ($$payload5) => {
                    $$payload5.out += `<div class="flex flex-row justify-between text-xs"><div class="text-primary/50">Progress</div> <div class="text-primary/50 text-right">${escape_html(calculateProgress(run))}% Complete</div></div> <div class="grid grid-cols-2 gap-4 text-sm"><div><div class="font-medium text-gray-400">Keywords</div> <div class="truncate">`;
                    ResolvedKeywords($$payload5, {
                      keywordIds: run.keywords || "",
                      fallback: "None"
                    });
                    $$payload5.out += `<!----></div></div> <div><div class="font-medium text-gray-400">Location</div> <div class="truncate">`;
                    ResolvedLocations($$payload5, {
                      locationIds: run.location || "",
                      fallback: "None"
                    });
                    $$payload5.out += `<!----></div></div></div> <div class="flex flex-col"><div class="font-medium text-gray-400">Jobs Found</div> <div class="flex items-center gap-2"><span class="text-lg font-semibold">${escape_html(run.matchedJobIds?.length || run.jobsFound || 0)}</span> `;
                    if ([
                      "running",
                      "pending",
                      "start",
                      "in progress"
                    ].includes(run.status)) {
                      $$payload5.out += "<!--[-->";
                      $$payload5.out += `<span class="text-xs text-gray-400">(in progress)</span>`;
                    } else {
                      $$payload5.out += "<!--[!-->";
                    }
                    $$payload5.out += `<!--]--></div></div>`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----> <!---->`;
                Card_footer($$payload4, {
                  class: "grid grid-cols-2 gap-4 border-t !p-2",
                  children: ($$payload5) => {
                    Button($$payload5, {
                      variant: "outline",
                      size: "sm",
                      onclick: () => onRunSelect(run),
                      children: ($$payload6) => {
                        $$payload6.out += `<!---->View Details`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!----> `;
                    Button($$payload5, {
                      variant: "outline",
                      size: "sm",
                      onclick: () => goto(`/dashboard/automation/${run.id}`),
                      children: ($$payload6) => {
                        $$payload6.out += `<!---->Full View`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!---->`;
          }
          $$payload3.out += `<!--]--></div>`;
        }
        $$payload3.out += `<!--]--></div>`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> <!---->`;
    Root$1($$payload2, {
      get open() {
        return createDialogOpen;
      },
      set open($$value) {
        createDialogOpen = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        Dialog_overlay($$payload3, {});
        $$payload3.out += `<!----> <!---->`;
        Dialog_content($$payload3, {
          class: "max-h-[90vh] max-w-4xl gap-0 p-0",
          children: ($$payload4) => {
            EnhancedFeatureGuard($$payload4, {
              userData,
              featureId: "automation",
              limitId: "automation_runs_per_month",
              showUpgradePrompt: true,
              fallbackMessage: "Automation features are not available in your current plan",
              children: ($$payload5) => {
                $$payload5.out += `<!---->`;
                Dialog_header($$payload5, {
                  class: "border-border gap-1 border-b p-4",
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->`;
                    Dialog_title($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Configure Automation Run`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> <!---->`;
                    Dialog_description($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Set up detailed automation specifications for intelligent job matching and application.`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Scroll_area($$payload5, {
                  orientation: "vertical",
                  class: "max-h-[calc(100vh-200px)] overflow-hidden",
                  children: ($$payload6) => {
                    $$payload6.out += `<form method="POST"><input type="hidden" name="profileId"${attr("value", store_get($$store_subs ??= {}, "$form", form).profileId)}/> <input type="hidden" name="keywords"${attr("value", JSON.stringify(store_get($$store_subs ??= {}, "$form", form).keywords))}/> <input type="hidden" name="locations"${attr("value", JSON.stringify(store_get($$store_subs ??= {}, "$form", form).locations))}/> <input type="hidden" name="maxJobsToApply"${attr("value", store_get($$store_subs ??= {}, "$form", form).maxJobsToApply)}/> <input type="hidden" name="minMatchScore"${attr("value", store_get($$store_subs ??= {}, "$form", form).minMatchScore)}/> <input type="hidden" name="autoApplyEnabled"${attr("value", store_get($$store_subs ??= {}, "$form", form).autoApplyEnabled)}/> <input type="hidden" name="salaryRange"${attr("value", JSON.stringify(store_get($$store_subs ??= {}, "$form", form).salaryRange))}/> <input type="hidden" name="experienceRange"${attr("value", JSON.stringify(store_get($$store_subs ??= {}, "$form", form).experienceRange))}/> <input type="hidden" name="jobTypes"${attr("value", JSON.stringify(store_get($$store_subs ??= {}, "$form", form).jobTypes))}/> <input type="hidden" name="remotePreference"${attr("value", store_get($$store_subs ??= {}, "$form", form).remotePreference)}/> <input type="hidden" name="companySizePreference"${attr("value", JSON.stringify(store_get($$store_subs ??= {}, "$form", form).companySizePreference))}/> <input type="hidden" name="excludeCompanies"${attr("value", JSON.stringify(store_get($$store_subs ??= {}, "$form", form).excludeCompanies))}/> <input type="hidden" name="preferredCompanies"${attr("value", JSON.stringify(store_get($$store_subs ??= {}, "$form", form).preferredCompanies))}/> <div class="mb-0 grid gap-4 p-4"><div class="grid gap-1"><div class="flex items-center justify-between"><label for="profile" class="text-sm font-medium">Profile *</label> `;
                    Button($$payload6, {
                      variant: "link",
                      size: "sm",
                      onclick: () => goto(),
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Manage Profiles`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----></div> <!---->`;
                    Root$2($$payload6, {
                      type: "single",
                      value: store_get($$store_subs ??= {}, "$form", form).profileId,
                      onValueChange: (value) => {
                        store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).profileId = value || "");
                      },
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->`;
                        Select_trigger($$payload7, {
                          class: "w-full p-2",
                          children: ($$payload8) => {
                            $$payload8.out += `<!---->`;
                            Select_value($$payload8, {
                              placeholder: profiles.find((p) => p.id === store_get($$store_subs ??= {}, "$form", form).profileId)?.name || "Select a profile"
                            });
                            $$payload8.out += `<!---->`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload7.out += `<!----> <!---->`;
                        Select_content($$payload7, {
                          class: "max-h-60",
                          children: ($$payload8) => {
                            const each_array_2 = ensure_array_like(profiles);
                            $$payload8.out += `<!--[-->`;
                            for (let $$index_2 = 0, $$length = each_array_2.length; $$index_2 < $$length; $$index_2++) {
                              let profile = each_array_2[$$index_2];
                              $$payload8.out += `<!---->`;
                              Select_item($$payload8, {
                                value: profile.id,
                                children: ($$payload9) => {
                                  $$payload9.out += `<!---->${escape_html(profile.name)}`;
                                },
                                $$slots: { default: true }
                              });
                              $$payload8.out += `<!---->`;
                            }
                            $$payload8.out += `<!--]-->`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload7.out += `<!---->`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----></div> `;
                    if (store_get($$store_subs ??= {}, "$form", form).profileId) {
                      $$payload6.out += "<!--[-->";
                      const selectedProfile = profiles.find((p) => p.id === store_get($$store_subs ??= {}, "$form", form).profileId);
                      if (selectedProfile) {
                        $$payload6.out += "<!--[-->";
                        const eligibility = checkAutomationEligibility2(selectedProfile);
                        $$payload6.out += `<div class="rounded-lg border p-4"><div class="mb-2 flex items-center gap-2 text-sm">`;
                        if (eligibility.isEligible) {
                          $$payload6.out += "<!--[-->";
                          Circle_check_big($$payload6, { class: "h-4 w-4 text-green-500" });
                          $$payload6.out += `<!----> <span class="text-green-700">Profile Eligible for Automation</span>`;
                        } else {
                          $$payload6.out += "<!--[!-->";
                          Triangle_alert($$payload6, { class: "h-4 w-4 text-orange-500" });
                          $$payload6.out += `<!----> <span class="text-orange-700">Profile Needs Completion</span>`;
                        }
                        $$payload6.out += `<!--]--></div> <div class="mb-3"><div class="mb-1 flex items-center justify-between text-sm"><span>Profile Completion</span> <span>${escape_html(eligibility.completionPercentage)}%</span></div> `;
                        Progress($$payload6, {
                          value: eligibility.completionPercentage,
                          max: 100
                        });
                        $$payload6.out += `<!----></div> `;
                        if (!eligibility.isEligible) {
                          $$payload6.out += "<!--[-->";
                          const each_array_3 = ensure_array_like(eligibility.missingRequirements);
                          $$payload6.out += `<div class="space-y-1"><p class="text-sm font-medium text-gray-700">Missing Requirements:</p> <!--[-->`;
                          for (let $$index_3 = 0, $$length = each_array_3.length; $$index_3 < $$length; $$index_3++) {
                            let requirement = each_array_3[$$index_3];
                            $$payload6.out += `<div class="flex items-center gap-2 text-sm text-gray-600">`;
                            X($$payload6, { class: "h-3 w-3 text-red-500" });
                            $$payload6.out += `<!----> ${escape_html(requirement)}</div>`;
                          }
                          $$payload6.out += `<!--]--></div>`;
                        } else {
                          $$payload6.out += "<!--[!-->";
                        }
                        $$payload6.out += `<!--]--></div>`;
                      } else {
                        $$payload6.out += "<!--[!-->";
                      }
                      $$payload6.out += `<!--]-->`;
                    } else {
                      $$payload6.out += "<!--[!-->";
                    }
                    $$payload6.out += `<!--]--> `;
                    if (store_get($$store_subs ??= {}, "$form", form).profileId && isProfileEligible()) {
                      $$payload6.out += "<!--[-->";
                      $$payload6.out += `<div class="space-y-6"><div class="mb-4 flex items-center justify-between"><h4 class="text-md font-light">Search Criteria</h4> `;
                      if (profileSuggestions() && profileSuggestions().jobTitles.length > 0) {
                        $$payload6.out += "<!--[-->";
                        Button($$payload6, {
                          variant: "outline",
                          size: "sm",
                          onclick: applySuggestions,
                          class: "text-xs",
                          children: ($$payload7) => {
                            Plus($$payload7, { class: "mr-1 h-3 w-3" });
                            $$payload7.out += `<!----> Use Profile Suggestions`;
                          },
                          $$slots: { default: true }
                        });
                      } else {
                        $$payload6.out += "<!--[!-->";
                      }
                      $$payload6.out += `<!--]--></div> <div class="grid gap-6 md:grid-cols-2"><div class="flex flex-col space-y-2"><label for="keywords" class="text-sm font-normal">Job Keywords *</label> `;
                      Multi_combobox($$payload6, {
                        placeholder: "Search for occupations...",
                        selectedValues: store_get($$store_subs ??= {}, "$form", form).keywords,
                        options: occupationOptions(),
                        onSelectedValuesChange: (values) => store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).keywords = values),
                        searchOptions: searchOccupations,
                        maxDisplayItems: 1,
                        width: "w-55"
                      });
                      $$payload6.out += `<!----> `;
                      if (profileSuggestions() && profileSuggestions().jobTitles.length > 0) {
                        $$payload6.out += "<!--[-->";
                        $$payload6.out += `<p class="text-muted-foreground text-xs">Suggestions: ${escape_html(profileSuggestions().jobTitles.join(", "))}</p>`;
                      } else {
                        $$payload6.out += "<!--[!-->";
                      }
                      $$payload6.out += `<!--]--></div> <div class="flex flex-col space-y-2"><label for="location" class="text-sm font-normal">Locations</label> `;
                      Multi_combobox($$payload6, {
                        placeholder: "Search for cities...",
                        selectedValues: store_get($$store_subs ??= {}, "$form", form).locations,
                        options: locationOptions(),
                        onSelectedValuesChange: (values) => store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).locations = values),
                        searchOptions: searchLocations,
                        maxDisplayItems: 1,
                        width: "w-55"
                      });
                      $$payload6.out += `<!----> `;
                      if (profileSuggestions() && profileSuggestions().location) {
                        $$payload6.out += "<!--[-->";
                        $$payload6.out += `<p class="text-muted-foreground text-xs">From profile: ${escape_html(profileSuggestions().location)}</p>`;
                      } else {
                        $$payload6.out += "<!--[!-->";
                      }
                      $$payload6.out += `<!--]--></div></div> <h4 class="text-md mb-4 font-light">Automation Settings</h4> <div class="grid gap-6 md:grid-cols-2"><div class="space-y-6"><div class="space-y-3">`;
                      Label($$payload6, {
                        class: "text-xs font-normal",
                        children: ($$payload7) => {
                          $$payload7.out += `<!---->Maximum Jobs to Apply: <span class="text-primary font-semibold">${escape_html(store_get($$store_subs ??= {}, "$form", form).maxJobsToApply)}</span>`;
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out += `<!----> `;
                      Slider($$payload6, {
                        type: "single",
                        min: 1,
                        max: 50,
                        step: 1,
                        class: "w-full",
                        get value() {
                          return store_get($$store_subs ??= {}, "$form", form).maxJobsToApply;
                        },
                        set value($$value) {
                          store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).maxJobsToApply = $$value);
                          $$settled = false;
                        }
                      });
                      $$payload6.out += `<!----> <div class="text-muted-foreground flex justify-between text-xs"><span>1 job</span> <span>50 jobs</span></div></div> <div class="space-y-3">`;
                      Label($$payload6, {
                        class: "text-xs font-normal",
                        children: ($$payload7) => {
                          $$payload7.out += `<!---->Minimum Match Score: <span class="text-primary font-semibold">${escape_html(store_get($$store_subs ??= {}, "$form", form).minMatchScore)}%</span>`;
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out += `<!----> `;
                      Slider($$payload6, {
                        type: "single",
                        min: 60,
                        max: 95,
                        step: 5,
                        class: "w-full",
                        get value() {
                          return store_get($$store_subs ??= {}, "$form", form).minMatchScore;
                        },
                        set value($$value) {
                          store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).minMatchScore = $$value);
                          $$settled = false;
                        }
                      });
                      $$payload6.out += `<!----> <div class="text-muted-foreground flex justify-between text-xs"><span>60%</span> <span>95%</span></div></div></div> <div class="space-y-6"><div class="space-y-3">`;
                      Label($$payload6, {
                        class: "text-xs font-normal",
                        children: ($$payload7) => {
                          $$payload7.out += `<!---->Salary Range: <span class="text-primary font-semibold">$${escape_html(store_get($$store_subs ??= {}, "$form", form).salaryRange[0])}k - $${escape_html(store_get($$store_subs ??= {}, "$form", form).salaryRange[1])}k</span>`;
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out += `<!----> `;
                      Slider($$payload6, {
                        type: "multiple",
                        min: 30,
                        max: 250,
                        step: 5,
                        class: "w-full",
                        get value() {
                          return store_get($$store_subs ??= {}, "$form", form).salaryRange;
                        },
                        set value($$value) {
                          store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).salaryRange = $$value);
                          $$settled = false;
                        }
                      });
                      $$payload6.out += `<!----> <div class="text-muted-foreground flex justify-between text-xs"><span>$30k</span> <span>$250k+</span></div></div> <div class="space-y-3">`;
                      Label($$payload6, {
                        class: "text-xs font-medium",
                        children: ($$payload7) => {
                          $$payload7.out += `<!---->Experience Range: <span class="text-primary font-normal">${escape_html(store_get($$store_subs ??= {}, "$form", form).experienceRange[0])} - ${escape_html(store_get($$store_subs ??= {}, "$form", form).experienceRange[1])} years</span>`;
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out += `<!----> `;
                      Slider($$payload6, {
                        type: "multiple",
                        min: 0,
                        max: 15,
                        step: 1,
                        class: "w-full",
                        get value() {
                          return store_get($$store_subs ??= {}, "$form", form).experienceRange;
                        },
                        set value($$value) {
                          store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).experienceRange = $$value);
                          $$settled = false;
                        }
                      });
                      $$payload6.out += `<!----> <div class="text-muted-foreground flex justify-between text-xs"><span>0 years</span> <span>15+ years</span></div></div></div></div> <div class="bg-muted/50 mt-6 flex items-center justify-between rounded-lg border p-4"><div>`;
                      Label($$payload6, {
                        for: "auto-apply",
                        class: "text-sm font-medium",
                        children: ($$payload7) => {
                          $$payload7.out += `<!---->Enable Automatic Applications`;
                        },
                        $$slots: { default: true }
                      });
                      $$payload6.out += `<!----> <p class="text-muted-foreground text-xs">Automatically apply to jobs that match your criteria</p></div> `;
                      Switch($$payload6, {
                        id: "auto-apply",
                        checked: Boolean(store_get($$store_subs ??= {}, "$form", form).autoApplyEnabled),
                        onCheckedChange: (checked) => {
                          store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).autoApplyEnabled = checked);
                        }
                      });
                      $$payload6.out += `<!----></div></div>`;
                    } else {
                      $$payload6.out += "<!--[!-->";
                    }
                    $$payload6.out += `<!--]--></div> <!---->`;
                    Dialog_footer($$payload6, {
                      class: "border-border grid grid-cols-3 gap-4 border-t p-2",
                      children: ($$payload7) => {
                        Button($$payload7, {
                          variant: "outline",
                          onclick: () => createDialogOpen = false,
                          children: ($$payload8) => {
                            $$payload8.out += `<!---->Cancel`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload7.out += `<!----> `;
                        Button($$payload7, {
                          type: "submit",
                          variant: "default",
                          disabled: !isFormValid() || store_get($$store_subs ??= {}, "$submitting", submitting),
                          children: ($$payload8) => {
                            if (store_get($$store_subs ??= {}, "$submitting", submitting)) {
                              $$payload8.out += "<!--[-->";
                              $$payload8.out += `Creating...`;
                            } else {
                              $$payload8.out += "<!--[!-->";
                              $$payload8.out += `Start Automation`;
                            }
                            $$payload8.out += `<!--]-->`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload7.out += `<!---->`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----></form>`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
function ProfilesTab($$payload, $$props) {
  push();
  const { userData, profiles, onProfileSelect } = $$props;
  let profileSearchQuery = "";
  const filteredProfiles = () => {
    return profiles.filter((profile) => {
      if (profileSearchQuery.trim()) {
        const query = profileSearchQuery.toLowerCase();
        const profileData = getProfileData(profile);
        const name = profileData.fullName || profile.name || "";
        const title = profileData.title || "";
        return name.toLowerCase().includes(query) || title.toLowerCase().includes(query);
      }
      return true;
    });
  };
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<div class="border-border flex flex-wrap items-center justify-between gap-4 border-b p-2"><div class="relative flex items-center gap-2">`;
    Search($$payload2, {
      class: "text-muted-foreground absolute left-2.5 top-3 h-4 w-4"
    });
    $$payload2.out += `<!----> `;
    Input($$payload2, {
      placeholder: "Search profiles...",
      class: "h-9 w-[200px] pl-9",
      get value() {
        return profileSearchQuery;
      },
      set value($$value) {
        profileSearchQuery = $$value;
        $$settled = false;
      }
    });
    $$payload2.out += `<!----></div> `;
    Button($$payload2, {
      variant: "default",
      onclick: () => window.location.href = "/dashboard/settings/profile",
      children: ($$payload3) => {
        Plus($$payload3, { class: "mr-1 h-4 w-4" });
        $$payload3.out += `<!----> Manage Profiles`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----></div> <div class="p-2">`;
    if (profiles.length === 0) {
      $$payload2.out += "<!--[-->";
      $$payload2.out += `<div class="flex flex-col items-center justify-center rounded-lg border border-dashed border-gray-600 p-12 text-center">`;
      File_text($$payload2, { class: "mb-4 h-12 w-12 text-gray-400" });
      $$payload2.out += `<!----> <h3 class="text-xl font-semibold text-gray-300">No profiles available</h3> <p class="mt-2 text-gray-400">Create a profile in Settings to start using automation</p> `;
      Button($$payload2, {
        variant: "default",
        onclick: () => window.location.href = "/dashboard/settings/profile",
        class: "mt-4",
        children: ($$payload3) => {
          $$payload3.out += `<!---->Go to Profile Settings`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----></div>`;
    } else if (filteredProfiles().length === 0) {
      $$payload2.out += "<!--[1-->";
      $$payload2.out += `<div class="flex flex-col items-center justify-center rounded-lg border border-dashed border-gray-600 p-12 text-center">`;
      Search($$payload2, { class: "mb-4 h-12 w-12 text-gray-400" });
      $$payload2.out += `<!----> <h3 class="text-xl font-semibold text-gray-300">No profiles match your search</h3> <p class="mt-2 text-gray-400">Try adjusting your search criteria</p></div>`;
    } else {
      $$payload2.out += "<!--[!-->";
      const each_array = ensure_array_like(filteredProfiles());
      $$payload2.out += `<div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3"><!--[-->`;
      for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
        let profile = each_array[$$index];
        $$payload2.out += `<!---->`;
        Card($$payload2, {
          class: "gap-0 p-0",
          children: ($$payload3) => {
            const eligibility = checkAutomationEligibility(profile);
            $$payload3.out += `<!---->`;
            Card_header($$payload3, {
              class: "border-border flex flex-col gap-1 border-b !p-4",
              children: ($$payload4) => {
                $$payload4.out += `<!---->`;
                Card_title($$payload4, {
                  children: ($$payload5) => {
                    $$payload5.out += `<a${attr("href", `/dashboard/automation/profile/${profile.id}`)} class="hover:underline">${escape_html(getProfileData(profile).fullName || "Unnamed Profile")}</a>`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----> <!---->`;
                Card_description($$payload4, {
                  children: ($$payload5) => {
                    $$payload5.out += `<!---->${escape_html(getProfileData(profile).title || "No title specified")}`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!----> `;
            Progress($$payload3, {
              value: eligibility.completionPercentage,
              max: 100,
              class: "h-2 rounded-none"
            });
            $$payload3.out += `<!----> <div class="flex items-center justify-between px-4 py-2 text-xs"><span class="text-primary/50">Profile Completion</span> <span class="text-primary/50">${escape_html(eligibility.completionPercentage)}%</span></div> <!---->`;
            Card_content($$payload3, {
              class: "p-4 pt-0",
              children: ($$payload4) => {
                $$payload4.out += `<div class="mt-2 flex flex-row items-center gap-4">`;
                if (eligibility.isEligible) {
                  $$payload4.out += "<!--[-->";
                  Circle_check_big($$payload4, { class: "h-4 w-4 text-green-500" });
                  $$payload4.out += `<!----> <span class="text-sm font-medium text-green-700">Automation Ready</span>`;
                } else {
                  $$payload4.out += "<!--[!-->";
                  Triangle_alert($$payload4, { class: "h-4 w-4 text-orange-500" });
                  $$payload4.out += `<!----> <span class="text-sm font-medium text-orange-700">Needs Completion</span>`;
                }
                $$payload4.out += `<!--]--></div>`;
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!----> <!---->`;
            Card_footer($$payload3, {
              class: "border-border border-t !p-2",
              children: ($$payload4) => {
                EnhancedFeatureGuard($$payload4, {
                  userData,
                  featureId: "automation",
                  limitId: "automation_runs_per_month",
                  showUpgradePrompt: true,
                  fallbackMessage: "Automation features are not available in your current plan",
                  children: ($$payload5) => {
                    const eligibility2 = checkAutomationEligibility(profile);
                    Button($$payload5, {
                      variant: "outline",
                      class: "flex w-full flex-row gap-2",
                      disabled: !eligibility2.isEligible,
                      onclick: () => onProfileSelect(profile.id),
                      children: ($$payload6) => {
                        Play($$payload6, { class: "font-lighter h-2 w-2" });
                        $$payload6.out += `<!----> `;
                        if (eligibility2.isEligible) {
                          $$payload6.out += "<!--[-->";
                          $$payload6.out += `Run Automation`;
                        } else {
                          $$payload6.out += "<!--[!-->";
                          $$payload6.out += `Complete Profile First`;
                        }
                        $$payload6.out += `<!--]-->`;
                      },
                      $$slots: { default: true }
                    });
                  },
                  $$slots: { default: true }
                });
              },
              $$slots: { default: true }
            });
            $$payload3.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!---->`;
      }
      $$payload2.out += `<!--]--></div>`;
    }
    $$payload2.out += `<!--]--></div>`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  pop();
}
function _page($$payload, $$props) {
  push();
  var $$store_subs;
  const { data } = $$props;
  let profiles = data.profiles || [];
  const automationRuns = writable(data.automationRuns || []);
  let selectedRun = null;
  let isSheetOpen = false;
  let selectedTab = "runs";
  const occupationOptions = () => {
    return data.occupations.map((occupation) => ({ value: occupation.id, label: occupation.title }));
  };
  const locationOptions = () => {
    return data.locations.map((city) => ({
      value: `${city.id}|${city.name}|${city.state.code}|${city.country}`,
      label: `${city.name}, ${city.state.code}`
    }));
  };
  const { form, enhance, submitting } = superForm(data.form, {
    validators: zodClient(automationFormSchema),
    dataType: "json",
    resetForm: true,
    onSubmit: () => {
      toast.loading("Creating automation run...");
    },
    onResult: ({ result }) => {
      toast.dismiss();
      if (result.type === "redirect") {
        toast.success("Automation run created successfully");
      } else if (result.type === "failure") {
        toast.error(result.data?.error || "Failed to create automation run");
      }
    },
    onError: () => {
      toast.dismiss();
      toast.error("An error occurred while creating the automation run");
    }
  });
  const selectedProfile = () => {
    return profiles.find((p) => p.id === store_get($$store_subs ??= {}, "$form", form).profileId);
  };
  const isProfileEligible = () => {
    if (!selectedProfile()) return false;
    const eligibility = checkAutomationEligibility(selectedProfile());
    return eligibility.isEligible;
  };
  async function searchOccupations(search = "") {
    try {
      const response = await fetch(`/api/occupations?search=${encodeURIComponent(search)}&limit=20`);
      if (response.ok) {
        const searchData = await response.json();
        return searchData.map((occupation) => ({ value: occupation.id, label: occupation.title }));
      }
    } catch (error) {
      console.error("Error searching occupations:", error);
    }
    return [];
  }
  async function searchLocations(search = "") {
    try {
      const response = await fetch(`/api/locations?search=${encodeURIComponent(search)}&limit=20`);
      if (response.ok) {
        const searchData = await response.json();
        return searchData.map((city) => ({
          value: `${city.id}|${city.name}|${city.state.code}|${city.country}`,
          label: `${city.name}, ${city.state.code}`
        }));
      }
    } catch (error) {
      console.error("Error searching locations:", error);
    }
    return [];
  }
  const isFormValid = () => {
    if (!store_get($$store_subs ??= {}, "$form", form).profileId || !isProfileEligible()) return false;
    if (store_get($$store_subs ??= {}, "$form", form).keywords.length === 0 && store_get($$store_subs ??= {}, "$form", form).locations.length === 0) return false;
    if (store_get($$store_subs ??= {}, "$form", form).salaryRange[0] > store_get($$store_subs ??= {}, "$form", form).salaryRange[1]) return false;
    if (store_get($$store_subs ??= {}, "$form", form).experienceRange[0] > store_get($$store_subs ??= {}, "$form", form).experienceRange[1]) return false;
    return true;
  };
  const profileSuggestions = () => {
    if (!selectedProfile()?.data?.data) return null;
    const profileData = selectedProfile().data.data;
    const jobTitles = profileData.workExperience?.map((exp) => exp.title).filter(Boolean) || [];
    const skills = profileData.skillsData?.list || profileData.skills || [];
    let totalExperience = 0;
    if (profileData.workExperience) {
      profileData.workExperience.forEach((exp) => {
        if (exp.startDate && exp.endDate) {
          const start = new Date(exp.startDate);
          const end = exp.current ? /* @__PURE__ */ new Date() : new Date(exp.endDate);
          const years = (end.getTime() - start.getTime()) / (1e3 * 60 * 60 * 24 * 365);
          totalExperience += years;
        }
      });
    }
    const location = profileData.personalInfo?.city || profileData.personalInfo?.address || "";
    return {
      jobTitles: [...new Set(jobTitles)].slice(0, 3),
      // Top 3 unique job titles
      skills: Array.isArray(skills) ? skills.slice(0, 5) : [],
      // Top 5 skills
      experienceYears: Math.floor(totalExperience),
      location
    };
  };
  function applySuggestions() {
    const suggestions = profileSuggestions();
    if (!suggestions) return;
    const currentOccupationOptions = occupationOptions();
    if (suggestions.jobTitles.length > 0 && suggestions.skills.length > 0) {
      const suggestedTitles = [
        suggestions.jobTitles[0],
        ...suggestions.skills.slice(0, 2)
      ];
      const matchingOccupations = [];
      for (const title of suggestedTitles) {
        const match = currentOccupationOptions.find((opt) => opt.label.toLowerCase().includes(title.toLowerCase()) || title.toLowerCase().includes(opt.label.toLowerCase()));
        if (match) {
          matchingOccupations.push(match.value);
        }
      }
      if (matchingOccupations.length > 0) {
        store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).keywords = matchingOccupations);
      }
    }
    if (suggestions.location) {
      store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).locations = [
        `custom|${suggestions.location}|${suggestions.location}|US`
      ]);
    }
    if (suggestions.experienceYears > 0) {
      const minExp = Math.max(0, suggestions.experienceYears - 2);
      const maxExp = Math.min(15, suggestions.experienceYears + 3);
      store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).experienceRange = [minExp, maxExp]);
    }
  }
  function handleRunRefresh(updatedRun) {
    automationRuns.update((runs) => runs.map((run) => run.id === updatedRun.id ? updatedRun : run));
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    SEO($$payload2, {
      title: "Job Automation | Hirli",
      description: "Automate your job search and application process with Hirli's intelligent automation tools.",
      keywords: "job automation, automated job search, job application automation, resume matching, career automation, job search tools"
    });
    $$payload2.out += `<!----> <!---->`;
    Root($$payload2, {
      class: "",
      get value() {
        return selectedTab;
      },
      set value($$value) {
        selectedTab = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        $$payload3.out += `<!---->`;
        Tabs_list($$payload3, {
          class: "border-t-0",
          children: ($$payload4) => {
            $$payload4.out += `<!---->`;
            Tabs_trigger($$payload4, {
              value: "runs",
              children: ($$payload5) => {
                Play($$payload5, { class: "h-3 w-3" });
                $$payload5.out += `<!----> Automation Runs`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <!---->`;
            Tabs_trigger($$payload4, {
              value: "profiles",
              children: ($$payload5) => {
                User($$payload5, { class: "h-3 w-3" });
                $$payload5.out += `<!----> Available Profiles`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> <!---->`;
        Tabs_content($$payload3, {
          value: "runs",
          children: ($$payload4) => {
            AutomationRunsTab($$payload4, {
              userData: data.user,
              automationRuns: store_get($$store_subs ??= {}, "$automationRuns", automationRuns),
              profiles,
              form,
              submitting,
              occupationOptions,
              locationOptions,
              searchOccupations,
              searchLocations,
              isFormValid,
              profileSuggestions,
              applySuggestions,
              checkAutomationEligibility,
              isProfileEligible,
              onRunSelect: (run) => {
                selectedRun = run;
                isSheetOpen = true;
              },
              onCreateRun: () => true
            });
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!----> <!---->`;
        Tabs_content($$payload3, {
          value: "profiles",
          children: ($$payload4) => {
            ProfilesTab($$payload4, {
              userData: data.user,
              profiles,
              onProfileSelect: (profileId) => {
                store_mutate($$store_subs ??= {}, "$form", form, store_get($$store_subs ??= {}, "$form", form).profileId = profileId);
              }
            });
          },
          $$slots: { default: true }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    if (selectedRun) {
      $$payload2.out += "<!--[-->";
      AutomationRunSheet($$payload2, {
        automationRun: selectedRun,
        onClose: () => {
          selectedRun = null;
        },
        onRefresh: handleRunRefresh,
        onStop: () => {
          automationRuns.update((runs) => runs.map((run) => {
            if (run.id === selectedRun.id) {
              return { ...run, status: "stopped" };
            }
            return run;
          }));
        },
        get open() {
          return isSheetOpen;
        },
        set open($$value) {
          isSheetOpen = $$value;
          $$settled = false;
        }
      });
    } else {
      $$payload2.out += "<!--[!-->";
    }
    $$payload2.out += `<!--]-->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-D5brYBdw.js.map
