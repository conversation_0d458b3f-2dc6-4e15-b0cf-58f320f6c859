{"version": 3, "file": "_server.ts-556S24fi.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/feature-usage/reset/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { v as verifySessionToken } from \"../../../../../chunks/auth.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { featureTablesExist } from \"../../../../../chunks/feature-usage.js\";\nconst POST = async ({ cookies, request }) => {\n  const token = cookies.get(\"auth_token\");\n  if (!token) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  const userData = verifySessionToken(token);\n  if (!userData?.id) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  try {\n    const tablesExist = await featureTablesExist();\n    if (!tablesExist) {\n      return json({ error: \"Feature usage tables do not exist yet\" }, { status: 400 });\n    }\n    const { featureId, limitId } = await request.json();\n    if (featureId && limitId) {\n      await prisma.featureUsage.updateMany({\n        where: {\n          userId: userData.id,\n          featureId,\n          limitId\n        },\n        data: {\n          used: 0,\n          updatedAt: /* @__PURE__ */ new Date()\n        }\n      });\n      return json({\n        success: true,\n        message: `Usage for feature ${featureId} and limit ${limitId} has been reset`\n      });\n    } else if (featureId) {\n      await prisma.featureUsage.updateMany({\n        where: {\n          userId: userData.id,\n          featureId\n        },\n        data: {\n          used: 0,\n          updatedAt: /* @__PURE__ */ new Date()\n        }\n      });\n      return json({\n        success: true,\n        message: `All usage for feature ${featureId} has been reset`\n      });\n    } else {\n      await prisma.featureUsage.updateMany({\n        where: {\n          userId: userData.id\n        },\n        data: {\n          used: 0,\n          updatedAt: /* @__PURE__ */ new Date()\n        }\n      });\n      return json({\n        success: true,\n        message: \"All feature usage has been reset\"\n      });\n    }\n  } catch (error) {\n    console.error(\"Error resetting feature usage:\", error);\n    return json(\n      {\n        success: false,\n        error: error.message || \"An error occurred while resetting feature usage\"\n      },\n      { status: 500 }\n    );\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAIK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AAC7C,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE;AACd,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,MAAM,QAAQ,GAAG,kBAAkB,CAAC,KAAK,CAAC;AAC5C,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,WAAW,GAAG,MAAM,kBAAkB,EAAE;AAClD,IAAI,IAAI,CAAC,WAAW,EAAE;AACtB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uCAAuC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtF;AACA,IAAI,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACvD,IAAI,IAAI,SAAS,IAAI,OAAO,EAAE;AAC9B,MAAM,MAAM,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC;AAC3C,QAAQ,KAAK,EAAE;AACf,UAAU,MAAM,EAAE,QAAQ,CAAC,EAAE;AAC7B,UAAU,SAAS;AACnB,UAAU;AACV,SAAS;AACT,QAAQ,IAAI,EAAE;AACd,UAAU,IAAI,EAAE,CAAC;AACjB,UAAU,SAAS,kBAAkB,IAAI,IAAI;AAC7C;AACA,OAAO,CAAC;AACR,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,OAAO,EAAE,CAAC,kBAAkB,EAAE,SAAS,CAAC,WAAW,EAAE,OAAO,CAAC,eAAe;AACpF,OAAO,CAAC;AACR,KAAK,MAAM,IAAI,SAAS,EAAE;AAC1B,MAAM,MAAM,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC;AAC3C,QAAQ,KAAK,EAAE;AACf,UAAU,MAAM,EAAE,QAAQ,CAAC,EAAE;AAC7B,UAAU;AACV,SAAS;AACT,QAAQ,IAAI,EAAE;AACd,UAAU,IAAI,EAAE,CAAC;AACjB,UAAU,SAAS,kBAAkB,IAAI,IAAI;AAC7C;AACA,OAAO,CAAC;AACR,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,OAAO,EAAE,CAAC,sBAAsB,EAAE,SAAS,CAAC,eAAe;AACnE,OAAO,CAAC;AACR,KAAK,MAAM;AACX,MAAM,MAAM,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC;AAC3C,QAAQ,KAAK,EAAE;AACf,UAAU,MAAM,EAAE,QAAQ,CAAC;AAC3B,SAAS;AACT,QAAQ,IAAI,EAAE;AACd,UAAU,IAAI,EAAE,CAAC;AACjB,UAAU,SAAS,kBAAkB,IAAI,IAAI;AAC7C;AACA,OAAO,CAAC;AACR,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO,CAAC;AACR;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC;AAC1D,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,KAAK,EAAE,KAAK,CAAC,OAAO,IAAI;AAChC,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;;;;"}