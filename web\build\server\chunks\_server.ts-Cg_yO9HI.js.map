{"version": 3, "file": "_server.ts-Cg_yO9HI.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/resume/_id_/parsing-status/_server.ts.js"], "sourcesContent": ["import { p as prisma } from \"../../../../../../chunks/prisma.js\";\nimport { j as json } from \"../../../../../../chunks/index.js\";\nconst GET = async ({ params, locals }) => {\n  const user = locals.user;\n  if (!user) return new Response(\"Unauthorized\", { status: 401 });\n  const resumeId = params.id;\n  if (!resumeId) {\n    return json({ error: \"Resume ID is required\" }, { status: 400 });\n  }\n  try {\n    const resume = await prisma.resume.findUnique({\n      where: { id: resumeId },\n      include: {\n        document: true\n      }\n    });\n    if (!resume) {\n      return json({ error: \"Resume not found\" }, { status: 404 });\n    }\n    if (resume.document.userId !== user.id) {\n      return json({ error: \"Unauthorized\" }, { status: 401 });\n    }\n    const activeWorkerProcess = await prisma.workerProcess.findFirst({\n      where: {\n        type: \"resume-parsing\",\n        status: {\n          in: [\"pending\", \"processing\"]\n        },\n        data: {\n          path: [\"resumeId\"],\n          equals: resumeId\n        }\n      },\n      orderBy: {\n        createdAt: \"desc\"\n      }\n    });\n    return json({\n      isParsing: !!activeWorkerProcess,\n      workerProcess: activeWorkerProcess ? {\n        id: activeWorkerProcess.id,\n        status: activeWorkerProcess.status,\n        createdAt: activeWorkerProcess.createdAt,\n        updatedAt: activeWorkerProcess.updatedAt\n      } : null\n    });\n  } catch (error) {\n    console.error(\"Error checking resume parsing status:\", error);\n    return json({ error: \"Internal server error\" }, { status: 500 });\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;AAEK,MAAC,GAAG,GAAG,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK;AAC1C,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE,EAAE,MAAM,QAAQ,GAAG,MAAM,CAAC,EAAE;AAC5B,EAAE,IAAI,CAAC,QAAQ,EAAE;AACjB,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpE;AACA,EAAE,IAAI;AACN,IAAI,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;AAClD,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE;AAC7B,MAAM,OAAO,EAAE;AACf,QAAQ,QAAQ,EAAE;AAClB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,kBAAkB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE;AACA,IAAI,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,KAAK,IAAI,CAAC,EAAE,EAAE;AAC5C,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7D;AACA,IAAI,MAAM,mBAAmB,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC;AACrE,MAAM,KAAK,EAAE;AACb,QAAQ,IAAI,EAAE,gBAAgB;AAC9B,QAAQ,MAAM,EAAE;AAChB,UAAU,EAAE,EAAE,CAAC,SAAS,EAAE,YAAY;AACtC,SAAS;AACT,QAAQ,IAAI,EAAE;AACd,UAAU,IAAI,EAAE,CAAC,UAAU,CAAC;AAC5B,UAAU,MAAM,EAAE;AAClB;AACA,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,SAAS,EAAE;AACnB;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,SAAS,EAAE,CAAC,CAAC,mBAAmB;AACtC,MAAM,aAAa,EAAE,mBAAmB,GAAG;AAC3C,QAAQ,EAAE,EAAE,mBAAmB,CAAC,EAAE;AAClC,QAAQ,MAAM,EAAE,mBAAmB,CAAC,MAAM;AAC1C,QAAQ,SAAS,EAAE,mBAAmB,CAAC,SAAS;AAChD,QAAQ,SAAS,EAAE,mBAAmB,CAAC;AACvC,OAAO,GAAG;AACV,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC;AACjE,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpE;AACA;;;;"}