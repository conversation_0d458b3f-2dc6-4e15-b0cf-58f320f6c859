{"version": 3, "file": "_server.ts-DUZVqk9U.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/documents/_id_/view/_server.ts.js"], "sourcesContent": ["import { e as error, j as json } from \"../../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../../chunks/prisma.js\";\nimport fs from \"fs\";\nimport path from \"path\";\nconst GET = async ({ params, locals }) => {\n  if (!locals.user) {\n    throw error(401, \"Unauthorized\");\n  }\n  try {\n    const { id } = params;\n    if (!id) {\n      throw error(400, \"Document ID is required\");\n    }\n    const document = await prisma.document.findUnique({\n      where: { id }\n    });\n    if (!document || document.userId !== locals.user.id) {\n      throw error(404, \"Document not found\");\n    }\n    let filePath = \"\";\n    if (document.fileUrl) {\n      const fileUrl = document.fileUrl.startsWith(\"/\") ? document.fileUrl.substring(1) : document.fileUrl;\n      if (fileUrl.startsWith(\"http://\") || fileUrl.startsWith(\"https://\")) {\n        return json({\n          success: true,\n          document: {\n            ...document,\n            directUrl: document.fileUrl,\n            isExternal: true\n          }\n        });\n      } else {\n        filePath = path.join(process.cwd(), \"static\", fileUrl);\n      }\n    } else if (document.filePath) {\n      filePath = document.filePath;\n    } else {\n      filePath = path.join(process.cwd(), \"static\", \"placeholder.pdf\");\n    }\n    let fileExists = false;\n    try {\n      fileExists = fs.existsSync(filePath);\n    } catch (err) {\n      console.error(\"Error checking file existence:\", err);\n    }\n    let directUrl = \"\";\n    if (fileExists) {\n      const relativePath = path.relative(path.join(process.cwd(), \"static\"), filePath);\n      directUrl = \"/\" + relativePath.replace(/\\\\/g, \"/\");\n    } else {\n      directUrl = \"/placeholder.pdf\";\n    }\n    return json({\n      success: true,\n      document: {\n        ...document,\n        directUrl,\n        fileExists\n      }\n    });\n  } catch (err) {\n    console.error(\"Error serving document:\", err);\n    throw error(500, \"Failed to serve document\");\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;;;AAIK,MAAC,GAAG,GAAG,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK;AAC1C,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;AACpB,IAAI,MAAM,KAAK,CAAC,GAAG,EAAE,cAAc,CAAC;AACpC;AACA,EAAE,IAAI;AACN,IAAI,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;AACzB,IAAI,IAAI,CAAC,EAAE,EAAE;AACb,MAAM,MAAM,KAAK,CAAC,GAAG,EAAE,yBAAyB,CAAC;AACjD;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;AACtD,MAAM,KAAK,EAAE,EAAE,EAAE;AACjB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,KAAK,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE;AACzD,MAAM,MAAM,KAAK,CAAC,GAAG,EAAE,oBAAoB,CAAC;AAC5C;AACA,IAAI,IAAI,QAAQ,GAAG,EAAE;AACrB,IAAI,IAAI,QAAQ,CAAC,OAAO,EAAE;AAC1B,MAAM,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,OAAO;AACzG,MAAM,IAAI,OAAO,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;AAC3E,QAAQ,OAAO,IAAI,CAAC;AACpB,UAAU,OAAO,EAAE,IAAI;AACvB,UAAU,QAAQ,EAAE;AACpB,YAAY,GAAG,QAAQ;AACvB,YAAY,SAAS,EAAE,QAAQ,CAAC,OAAO;AACvC,YAAY,UAAU,EAAE;AACxB;AACA,SAAS,CAAC;AACV,OAAO,MAAM;AACb,QAAQ,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,QAAQ,EAAE,OAAO,CAAC;AAC9D;AACA,KAAK,MAAM,IAAI,QAAQ,CAAC,QAAQ,EAAE;AAClC,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ;AAClC,KAAK,MAAM;AACX,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,QAAQ,EAAE,iBAAiB,CAAC;AACtE;AACA,IAAI,IAAI,UAAU,GAAG,KAAK;AAC1B,IAAI,IAAI;AACR,MAAM,UAAU,GAAG,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC;AAC1C,KAAK,CAAC,OAAO,GAAG,EAAE;AAClB,MAAM,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,GAAG,CAAC;AAC1D;AACA,IAAI,IAAI,SAAS,GAAG,EAAE;AACtB,IAAI,IAAI,UAAU,EAAE;AACpB,MAAM,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC;AACtF,MAAM,SAAS,GAAG,GAAG,GAAG,YAAY,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;AACxD,KAAK,MAAM;AACX,MAAM,SAAS,GAAG,kBAAkB;AACpC;AACA,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,QAAQ,EAAE;AAChB,QAAQ,GAAG,QAAQ;AACnB,QAAQ,SAAS;AACjB,QAAQ;AACR;AACA,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,GAAG,EAAE;AAChB,IAAI,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,GAAG,CAAC;AACjD,IAAI,MAAM,KAAK,CAAC,GAAG,EAAE,0BAA0B,CAAC;AAChD;AACA;;;;"}