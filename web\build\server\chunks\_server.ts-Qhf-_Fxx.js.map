{"version": 3, "file": "_server.ts-Qhf-_Fxx.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/ai/interview/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { OpenAI } from \"openai\";\nfunction getOpenAIClient() {\n  if (!process.env.OPENAI_API_KEY) {\n    throw new Error(\"OpenAI API key is not configured\");\n  }\n  return new OpenAI({\n    apiKey: process.env.OPENAI_API_KEY\n  });\n}\nconst POST = async ({ request, locals }) => {\n  if (!locals.user) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  try {\n    const { applicationId, jobTitle, company, jobDescription } = await request.json();\n    if (!jobTitle) {\n      return json({ error: \"Job title is required\" }, { status: 400 });\n    }\n    const questions = await generateInterviewQuestions(jobTitle, company, jobDescription);\n    const session = await prisma.interviewCoachingSession.create({\n      data: {\n        userId: locals.user.id,\n        applicationId,\n        jobTitle,\n        company,\n        status: \"in_progress\",\n        questions: questions.map((q) => ({ question: q, type: \"behavioral\" })),\n        responses: [],\n        feedback: []\n      }\n    });\n    await prisma.featureUsage.upsert({\n      where: {\n        userId_featureId_limitId: {\n          userId: locals.user.id,\n          featureId: \"ai_interview_coach\",\n          limitId: \"ai_interview_sessions_monthly\"\n        }\n      },\n      update: {\n        usage: {\n          increment: 1\n        }\n      },\n      create: {\n        userId: locals.user.id,\n        featureId: \"ai_interview_coach\",\n        limitId: \"ai_interview_sessions_monthly\",\n        usage: 1\n      }\n    });\n    return json({ session });\n  } catch (error) {\n    console.error(\"Error creating interview coaching session:\", error);\n    return json({ error: \"Failed to create interview coaching session\" }, { status: 500 });\n  }\n};\nconst GET = async ({ locals }) => {\n  if (!locals.user) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  try {\n    const sessions = await prisma.interviewCoachingSession.findMany({\n      where: {\n        userId: locals.user.id\n      },\n      orderBy: {\n        updatedAt: \"desc\"\n      }\n    });\n    return json({ sessions });\n  } catch (error) {\n    console.error(\"Error fetching interview coaching sessions:\", error);\n    return json({ error: \"Failed to fetch interview coaching sessions\" }, { status: 500 });\n  }\n};\nasync function generateInterviewQuestions(jobTitle, company, jobDescription) {\n  try {\n    const prompt = `Generate 5 behavioral interview questions for a ${jobTitle} position${company ? ` at ${company}` : \"\"}. \n    ${jobDescription ? `The job description is: ${jobDescription}` : \"\"}\n    \n    Focus on questions that assess the candidate's experience, skills, and fit for the role.\n    Return only the questions as a numbered list, without any additional text.`;\n    const openai = getOpenAIClient();\n    const response = await openai.chat.completions.create({\n      model: \"gpt-4\",\n      messages: [\n        {\n          role: \"system\",\n          content: \"You are an expert interview coach helping to prepare candidates for job interviews.\"\n        },\n        { role: \"user\", content: prompt }\n      ],\n      temperature: 0.7,\n      max_tokens: 1e3\n    });\n    const content = response.choices[0]?.message?.content || \"\";\n    const questions = content.split(\"\\n\").filter((line) => line.trim().match(/^\\d+\\.\\s/)).map((line) => line.replace(/^\\d+\\.\\s/, \"\").trim());\n    if (questions.length === 0) {\n      return [\n        \"Tell me about a time when you faced a challenging situation at work and how you handled it.\",\n        `What experience do you have that makes you a good fit for this ${jobTitle} role?`,\n        \"Describe a time when you had to work under pressure to meet a deadline.\",\n        \"Give an example of a time when you had to adapt to a significant change at work.\",\n        \"Tell me about a time when you had to resolve a conflict with a colleague or client.\"\n      ];\n    }\n    return questions;\n  } catch (error) {\n    console.error(\"Error generating interview questions:\", error);\n    return [\n      \"Tell me about a time when you faced a challenging situation at work and how you handled it.\",\n      `What experience do you have that makes you a good fit for this ${jobTitle} role?`,\n      \"Describe a time when you had to work under pressure to meet a deadline.\",\n      \"Give an example of a time when you had to adapt to a significant change at work.\",\n      \"Tell me about a time when you had to resolve a conflict with a colleague or client.\"\n    ];\n  }\n}\nexport {\n  GET,\n  POST\n};\n"], "names": [], "mappings": ";;;;;AAGA,SAAS,eAAe,GAAG;AAC3B,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE;AACnC,IAAI,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC;AACvD;AACA,EAAE,OAAO,IAAI,MAAM,CAAC;AACpB,IAAI,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC;AACxB,GAAG,CAAC;AACJ;AACK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AAC5C,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;AACpB,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,OAAO,EAAE,cAAc,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrF,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtE;AACA,IAAI,MAAM,SAAS,GAAG,MAAM,0BAA0B,CAAC,QAAQ,EAAE,OAAO,EAAE,cAAc,CAAC;AACzF,IAAI,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,wBAAwB,CAAC,MAAM,CAAC;AACjE,MAAM,IAAI,EAAE;AACZ,QAAQ,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;AAC9B,QAAQ,aAAa;AACrB,QAAQ,QAAQ;AAChB,QAAQ,OAAO;AACf,QAAQ,MAAM,EAAE,aAAa;AAC7B,QAAQ,SAAS,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;AAC9E,QAAQ,SAAS,EAAE,EAAE;AACrB,QAAQ,QAAQ,EAAE;AAClB;AACA,KAAK,CAAC;AACN,IAAI,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AACrC,MAAM,KAAK,EAAE;AACb,QAAQ,wBAAwB,EAAE;AAClC,UAAU,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;AAChC,UAAU,SAAS,EAAE,oBAAoB;AACzC,UAAU,OAAO,EAAE;AACnB;AACA,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,KAAK,EAAE;AACf,UAAU,SAAS,EAAE;AACrB;AACA,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;AAC9B,QAAQ,SAAS,EAAE,oBAAoB;AACvC,QAAQ,OAAO,EAAE,+BAA+B;AAChD,QAAQ,KAAK,EAAE;AACf;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,CAAC;AAC5B,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC;AACtE,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,6CAA6C,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1F;AACA;AACK,MAAC,GAAG,GAAG,OAAO,EAAE,MAAM,EAAE,KAAK;AAClC,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;AACpB,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,wBAAwB,CAAC,QAAQ,CAAC;AACpE,MAAM,KAAK,EAAE;AACb,QAAQ,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC;AAC5B,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,SAAS,EAAE;AACnB;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,EAAE,QAAQ,EAAE,CAAC;AAC7B,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,6CAA6C,EAAE,KAAK,CAAC;AACvE,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,6CAA6C,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1F;AACA;AACA,eAAe,0BAA0B,CAAC,QAAQ,EAAE,OAAO,EAAE,cAAc,EAAE;AAC7E,EAAE,IAAI;AACN,IAAI,MAAM,MAAM,GAAG,CAAC,gDAAgD,EAAE,QAAQ,CAAC,SAAS,EAAE,OAAO,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;AAC1H,IAAI,EAAE,cAAc,GAAG,CAAC,wBAAwB,EAAE,cAAc,CAAC,CAAC,GAAG,EAAE;AACvE;AACA;AACA,8EAA8E,CAAC;AAC/E,IAAI,MAAM,MAAM,GAAG,eAAe,EAAE;AACpC,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;AAC1D,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,QAAQ,EAAE;AAChB,QAAQ;AACR,UAAU,IAAI,EAAE,QAAQ;AACxB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM;AACvC,OAAO;AACP,MAAM,WAAW,EAAE,GAAG;AACtB,MAAM,UAAU,EAAE;AAClB,KAAK,CAAC;AACN,IAAI,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,IAAI,EAAE;AAC/D,IAAI,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;AAC5I,IAAI,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;AAChC,MAAM,OAAO;AACb,QAAQ,6FAA6F;AACrG,QAAQ,CAAC,+DAA+D,EAAE,QAAQ,CAAC,MAAM,CAAC;AAC1F,QAAQ,yEAAyE;AACjF,QAAQ,kFAAkF;AAC1F,QAAQ;AACR,OAAO;AACP;AACA,IAAI,OAAO,SAAS;AACpB,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC;AACjE,IAAI,OAAO;AACX,MAAM,6FAA6F;AACnG,MAAM,CAAC,+DAA+D,EAAE,QAAQ,CAAC,MAAM,CAAC;AACxF,MAAM,yEAAyE;AAC/E,MAAM,kFAAkF;AACxF,MAAM;AACN,KAAK;AACL;AACA;;;;"}