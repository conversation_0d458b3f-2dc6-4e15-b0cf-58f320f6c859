{"version": 3, "file": "_server.ts-DDCa-AUQ.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/help/_slug_/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nconst GET = async ({ params }) => {\n  try {\n    const { slug } = params;\n    const article = await prisma.helpArticle.findUnique({\n      where: {\n        slug\n      },\n      include: {\n        category: true,\n        tags: true,\n        relatedArticles: {\n          include: {\n            category: true\n          }\n        }\n      }\n    });\n    if (!article) {\n      return json({ error: \"Article not found\" }, { status: 404 });\n    }\n    await prisma.helpArticle.update({\n      where: {\n        id: article.id\n      },\n      data: {\n        viewCount: {\n          increment: 1\n        }\n      }\n    });\n    return json(article);\n  } catch (error) {\n    console.error(\"Error fetching help article:\", error);\n    return json({ error: \"Failed to fetch help article\" }, { status: 500 });\n  }\n};\nconst PUT = async ({ params, request, locals }) => {\n  const user = locals.user;\n  if (!user || user.role !== \"ADMIN\") {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  try {\n    const { slug } = params;\n    const { title, content, excerpt, categoryId, tagIds, published, relatedArticleIds } = await request.json();\n    const existingArticle = await prisma.helpArticle.findUnique({\n      where: {\n        slug\n      }\n    });\n    if (!existingArticle) {\n      return json({ error: \"Article not found\" }, { status: 404 });\n    }\n    const article = await prisma.helpArticle.update({\n      where: {\n        slug\n      },\n      data: {\n        title,\n        content,\n        excerpt,\n        published,\n        categoryId,\n        tags: {\n          set: [],\n          // Clear existing tags\n          connect: tagIds?.map((id) => ({ id })) || []\n        },\n        relatedArticles: {\n          set: [],\n          // Clear existing related articles\n          connect: relatedArticleIds?.map((id) => ({ id })) || []\n        }\n      },\n      include: {\n        category: true,\n        tags: true,\n        relatedArticles: true\n      }\n    });\n    return json(article);\n  } catch (error) {\n    console.error(\"Error updating help article:\", error);\n    return json({ error: \"Failed to update help article\" }, { status: 500 });\n  }\n};\nconst DELETE = async ({ params, locals }) => {\n  const user = locals.user;\n  if (!user || user.role !== \"ADMIN\") {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  try {\n    const { slug } = params;\n    const existingArticle = await prisma.helpArticle.findUnique({\n      where: {\n        slug\n      }\n    });\n    if (!existingArticle) {\n      return json({ error: \"Article not found\" }, { status: 404 });\n    }\n    await prisma.helpArticle.delete({\n      where: {\n        slug\n      }\n    });\n    return json({ success: true });\n  } catch (error) {\n    console.error(\"Error deleting help article:\", error);\n    return json({ error: \"Failed to delete help article\" }, { status: 500 });\n  }\n};\nexport {\n  DELETE,\n  GET,\n  PUT\n};\n"], "names": [], "mappings": ";;;;AAEK,MAAC,GAAG,GAAG,OAAO,EAAE,MAAM,EAAE,KAAK;AAClC,EAAE,IAAI;AACN,IAAI,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;AAC3B,IAAI,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;AACxD,MAAM,KAAK,EAAE;AACb,QAAQ;AACR,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,QAAQ,EAAE,IAAI;AACtB,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,eAAe,EAAE;AACzB,UAAU,OAAO,EAAE;AACnB,YAAY,QAAQ,EAAE;AACtB;AACA;AACA;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,OAAO,EAAE;AAClB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE;AACA,IAAI,MAAM,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;AACpC,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE,OAAO,CAAC;AACpB,OAAO;AACP,MAAM,IAAI,EAAE;AACZ,QAAQ,SAAS,EAAE;AACnB,UAAU,SAAS,EAAE;AACrB;AACA;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC;AACxB,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC;AACxD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,8BAA8B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3E;AACA;AACK,MAAC,GAAG,GAAG,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AACnD,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;AACtC,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;AAC3B,IAAI,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,iBAAiB,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AAC9G,IAAI,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;AAChE,MAAM,KAAK,EAAE;AACb,QAAQ;AACR;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,eAAe,EAAE;AAC1B,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE;AACA,IAAI,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;AACpD,MAAM,KAAK,EAAE;AACb,QAAQ;AACR,OAAO;AACP,MAAM,IAAI,EAAE;AACZ,QAAQ,KAAK;AACb,QAAQ,OAAO;AACf,QAAQ,OAAO;AACf,QAAQ,SAAS;AACjB,QAAQ,UAAU;AAClB,QAAQ,IAAI,EAAE;AACd,UAAU,GAAG,EAAE,EAAE;AACjB;AACA,UAAU,OAAO,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC,IAAI;AACpD,SAAS;AACT,QAAQ,eAAe,EAAE;AACzB,UAAU,GAAG,EAAE,EAAE;AACjB;AACA,UAAU,OAAO,EAAE,iBAAiB,EAAE,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC,IAAI;AAC/D;AACA,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,QAAQ,EAAE,IAAI;AACtB,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,eAAe,EAAE;AACzB;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC;AACxB,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC;AACxD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,+BAA+B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC5E;AACA;AACK,MAAC,MAAM,GAAG,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK;AAC7C,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;AACtC,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;AAC3B,IAAI,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;AAChE,MAAM,KAAK,EAAE;AACb,QAAQ;AACR;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,eAAe,EAAE;AAC1B,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE;AACA,IAAI,MAAM,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;AACpC,MAAM,KAAK,EAAE;AACb,QAAQ;AACR;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;AAClC,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC;AACxD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,+BAA+B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC5E;AACA;;;;"}