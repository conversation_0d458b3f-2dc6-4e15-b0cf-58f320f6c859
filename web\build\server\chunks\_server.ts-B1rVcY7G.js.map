{"version": 3, "file": "_server.ts-B1rVcY7G.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/notifications/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../chunks/index.js\";\nimport { g as getUserFromToken } from \"../../../../chunks/auth.js\";\nimport { p as prisma } from \"../../../../chunks/prisma.js\";\nimport { g as getRedisClient } from \"../../../../chunks/redis.js\";\nimport { g as getUserNotifications, d as deleteNotification, m as markNotificationAsRead } from \"../../../../chunks/notification-service.js\";\nconst GET = async ({ cookies, url }) => {\n  const user = await getUserFromToken(cookies);\n  if (!user) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  try {\n    const limit = parseInt(url.searchParams.get(\"limit\") || \"50\");\n    const offset = parseInt(url.searchParams.get(\"offset\") || \"0\");\n    const includeRead = url.searchParams.get(\"includeRead\") === \"true\";\n    const type = url.searchParams.get(\"type\") || void 0;\n    const requestId = url.searchParams.get(\"requestId\");\n    console.log(`Notification API request from user ${user.id}:`, {\n      limit,\n      offset,\n      includeRead,\n      type,\n      requestId\n    });\n    const notifications = await getUserNotifications(user.id, {\n      limit,\n      offset,\n      includeRead,\n      type\n    });\n    const unreadCount = await prisma.notification.count({\n      where: {\n        OR: [{ userId: user.id }, { global: true }],\n        read: false\n      }\n    });\n    if (requestId) {\n      console.log(\n        `Responding to request with ID ${requestId} with ${notifications.length} notifications`\n      );\n      notifications.forEach((notification) => {\n        notification.requestId = requestId;\n      });\n    }\n    return json({\n      success: true,\n      notifications,\n      unreadCount,\n      requestId\n      // Include the request ID in the response\n    });\n  } catch (error) {\n    console.error(\"Error getting notifications:\", error);\n    return json({ error: \"Failed to get notifications\" }, { status: 500 });\n  }\n};\nconst POST = async ({ cookies, request }) => {\n  const user = await getUserFromToken(cookies);\n  if (!user) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  try {\n    const data = await request.json();\n    if (!data.action) {\n      return json({ error: \"Action is required\" }, { status: 400 });\n    }\n    if (data.action === \"markAllAsRead\") {\n      await prisma.notification.updateMany({\n        where: {\n          OR: [{ userId: user.id }, { global: true }],\n          read: false\n        },\n        data: {\n          read: true\n        }\n      });\n      try {\n        const redis = await getRedisClient();\n        if (redis) {\n          const requestId = `notification_read_all:${user.id}:${Date.now()}`;\n          console.log(\n            `Generated request ID: ${requestId} for marking all notifications as read for user ${user.id}`\n          );\n          console.log(\n            `Publishing mark all as read update to Redis channel user:${user.id}:notifications with request ID ${requestId}`\n          );\n          await redis.publish(\n            `user:${user.id}:notifications`,\n            JSON.stringify({\n              type: \"notification_read\",\n              id: \"all\",\n              timestamp: (/* @__PURE__ */ new Date()).toISOString(),\n              requestId\n              // Include the request ID\n            })\n          );\n        }\n      } catch (error) {\n        console.error(\"Error sending WebSocket notification for markAllAsRead:\", error);\n      }\n      return json({\n        success: true,\n        message: \"All notifications marked as read\"\n      });\n    }\n    if (!data.id) {\n      return json({ error: \"Notification ID is required\" }, { status: 400 });\n    }\n    const notification = await prisma.notification.findUnique({\n      where: { id: data.id }\n    });\n    if (!notification) {\n      return json({ error: \"Notification not found\" }, { status: 404 });\n    }\n    if (notification.userId !== user.id && !notification.global) {\n      return json({ error: \"Unauthorized to access this notification\" }, { status: 403 });\n    }\n    switch (data.action) {\n      case \"markAsRead\":\n        await markNotificationAsRead(data.id);\n        return json({\n          success: true,\n          message: \"Notification marked as read\"\n        });\n      case \"delete\":\n        await deleteNotification(data.id);\n        return json({\n          success: true,\n          message: \"Notification deleted\"\n        });\n      default:\n        return json({ error: \"Invalid action\" }, { status: 400 });\n    }\n  } catch (error) {\n    console.error(\"Error processing notification action:\", error);\n    return json({ error: \"Failed to process notification action\" }, { status: 500 });\n  }\n};\nexport {\n  GET,\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAKK,MAAC,GAAG,GAAG,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK;AACxC,EAAE,MAAM,IAAI,GAAG,MAAM,gBAAgB,CAAC,OAAO,CAAC;AAC9C,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC;AACjE,IAAI,MAAM,MAAM,GAAG,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC;AAClE,IAAI,MAAM,WAAW,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,CAAC,KAAK,MAAM;AACtE,IAAI,MAAM,IAAI,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC;AACvD,IAAI,MAAM,SAAS,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC;AACvD,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,mCAAmC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;AAClE,MAAM,KAAK;AACX,MAAM,MAAM;AACZ,MAAM,WAAW;AACjB,MAAM,IAAI;AACV,MAAM;AACN,KAAK,CAAC;AACN,IAAI,MAAM,aAAa,GAAG,MAAM,oBAAoB,CAAC,IAAI,CAAC,EAAE,EAAE;AAC9D,MAAM,KAAK;AACX,MAAM,MAAM;AACZ,MAAM,WAAW;AACjB,MAAM;AACN,KAAK,CAAC;AACN,IAAI,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC;AACxD,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;AACnD,QAAQ,IAAI,EAAE;AACd;AACA,KAAK,CAAC;AACN,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,OAAO,CAAC,GAAG;AACjB,QAAQ,CAAC,8BAA8B,EAAE,SAAS,CAAC,MAAM,EAAE,aAAa,CAAC,MAAM,CAAC,cAAc;AAC9F,OAAO;AACP,MAAM,aAAa,CAAC,OAAO,CAAC,CAAC,YAAY,KAAK;AAC9C,QAAQ,YAAY,CAAC,SAAS,GAAG,SAAS;AAC1C,OAAO,CAAC;AACR;AACA,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,aAAa;AACnB,MAAM,WAAW;AACjB,MAAM;AACN;AACA,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC;AACxD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,6BAA6B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1E;AACA;AACK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AAC7C,EAAE,MAAM,IAAI,GAAG,MAAM,gBAAgB,CAAC,OAAO,CAAC;AAC9C,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AACtB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oBAAoB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnE;AACA,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,eAAe,EAAE;AACzC,MAAM,MAAM,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC;AAC3C,QAAQ,KAAK,EAAE;AACf,UAAU,EAAE,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;AACrD,UAAU,IAAI,EAAE;AAChB,SAAS;AACT,QAAQ,IAAI,EAAE;AACd,UAAU,IAAI,EAAE;AAChB;AACA,OAAO,CAAC;AACR,MAAM,IAAI;AACV,QAAQ,MAAM,KAAK,GAAG,MAAM,cAAc,EAAE;AAC5C,QAAQ,IAAI,KAAK,EAAE;AACnB,UAAU,MAAM,SAAS,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;AAC5E,UAAU,OAAO,CAAC,GAAG;AACrB,YAAY,CAAC,sBAAsB,EAAE,SAAS,CAAC,gDAAgD,EAAE,IAAI,CAAC,EAAE,CAAC;AACzG,WAAW;AACX,UAAU,OAAO,CAAC,GAAG;AACrB,YAAY,CAAC,yDAAyD,EAAE,IAAI,CAAC,EAAE,CAAC,+BAA+B,EAAE,SAAS,CAAC;AAC3H,WAAW;AACX,UAAU,MAAM,KAAK,CAAC,OAAO;AAC7B,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC;AAC3C,YAAY,IAAI,CAAC,SAAS,CAAC;AAC3B,cAAc,IAAI,EAAE,mBAAmB;AACvC,cAAc,EAAE,EAAE,KAAK;AACvB,cAAc,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW,EAAE;AACnE,cAAc;AACd;AACA,aAAa;AACb,WAAW;AACX;AACA,OAAO,CAAC,OAAO,KAAK,EAAE;AACtB,QAAQ,OAAO,CAAC,KAAK,CAAC,yDAAyD,EAAE,KAAK,CAAC;AACvF;AACA,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO,CAAC;AACR;AACA,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;AAClB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,6BAA6B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC5E;AACA,IAAI,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC;AAC9D,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE;AAC1B,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,YAAY,EAAE;AACvB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,wBAAwB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACvE;AACA,IAAI,IAAI,YAAY,CAAC,MAAM,KAAK,IAAI,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;AACjE,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,0CAA0C,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzF;AACA,IAAI,QAAQ,IAAI,CAAC,MAAM;AACvB,MAAM,KAAK,YAAY;AACvB,QAAQ,MAAM,sBAAsB,CAAC,IAAI,CAAC,EAAE,CAAC;AAC7C,QAAQ,OAAO,IAAI,CAAC;AACpB,UAAU,OAAO,EAAE,IAAI;AACvB,UAAU,OAAO,EAAE;AACnB,SAAS,CAAC;AACV,MAAM,KAAK,QAAQ;AACnB,QAAQ,MAAM,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC;AACzC,QAAQ,OAAO,IAAI,CAAC;AACpB,UAAU,OAAO,EAAE,IAAI;AACvB,UAAU,OAAO,EAAE;AACnB,SAAS,CAAC;AACV,MAAM;AACN,QAAQ,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,gBAAgB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC;AACjE,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uCAAuC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpF;AACA;;;;"}