{"version": 3, "file": "_server.ts-CodQWVFj.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/saved-jobs/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../chunks/index.js\";\nimport { PrismaClient } from \"@prisma/client\";\nconst prisma = new PrismaClient();\nconst GET = async ({ locals }) => {\n  const user = locals.user;\n  if (!user) {\n    return json({ error: \"Authentication required\" }, { status: 401 });\n  }\n  try {\n    const savedJobs = await prisma.savedJob.findMany({\n      where: {\n        userId: user.id\n      },\n      orderBy: {\n        createdAt: \"desc\"\n      }\n    });\n    const jobIds = savedJobs.map((job) => job.jobId);\n    const jobListings = jobIds.length > 0 ? await prisma.job_listing.findMany({\n      where: {\n        id: {\n          in: jobIds\n        }\n      }\n    }) : [];\n    const savedJobsWithListings = savedJobs.map((savedJob) => {\n      const jobListing = jobListings.find((job) => job.id === savedJob.jobId);\n      return {\n        ...savedJob,\n        job_listing: jobListing || null\n      };\n    });\n    return json({\n      success: true,\n      savedJobs: savedJobsWithListings\n    });\n  } catch (error) {\n    console.error(\"Error fetching saved jobs:\", error);\n    return json({ error: \"Failed to fetch saved jobs\" }, { status: 500 });\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;AAEA,MAAM,MAAM,GAAG,IAAI,YAAY,EAAE;AAC5B,MAAC,GAAG,GAAG,OAAO,EAAE,MAAM,EAAE,KAAK;AAClC,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtE;AACA,EAAE,IAAI;AACN,IAAI,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;AACrD,MAAM,KAAK,EAAE;AACb,QAAQ,MAAM,EAAE,IAAI,CAAC;AACrB,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,SAAS,EAAE;AACnB;AACA,KAAK,CAAC;AACN,IAAI,MAAM,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,CAAC;AACpD,IAAI,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;AAC9E,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE;AACZ,UAAU,EAAE,EAAE;AACd;AACA;AACA,KAAK,CAAC,GAAG,EAAE;AACX,IAAI,MAAM,qBAAqB,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,KAAK;AAC9D,MAAM,MAAM,UAAU,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,KAAK,QAAQ,CAAC,KAAK,CAAC;AAC7E,MAAM,OAAO;AACb,QAAQ,GAAG,QAAQ;AACnB,QAAQ,WAAW,EAAE,UAAU,IAAI;AACnC,OAAO;AACP,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,SAAS,EAAE;AACjB,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC;AACtD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,4BAA4B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE;AACA;;;;"}