{"version": 3, "file": "_server.ts-PqF92J5B.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/email/audiences/contacts/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { Resend } from \"resend\";\nimport { l as logger } from \"../../../../../../chunks/logger.js\";\nimport { d as private_env } from \"../../../../../../chunks/shared-server.js\";\nconst resend = private_env.RESEND_API_KEY ? new Resend(private_env.RESEND_API_KEY) : null;\nasync function GET({ url }) {\n  try {\n    const audienceId = url.searchParams.get(\"audienceId\");\n    if (!audienceId) {\n      return json({ error: \"Audience ID is required\" }, { status: 400 });\n    }\n    if (!resend) {\n      logger.warn(\"Resend API key not configured, returning sample contacts\");\n      return json([\n        {\n          id: \"sample-contact-1\",\n          email: \"<EMAIL>\",\n          first_name: \"<PERSON>\",\n          last_name: \"<PERSON><PERSON>\",\n          created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1e3).toISOString()\n        },\n        {\n          id: \"sample-contact-2\",\n          email: \"<EMAIL>\",\n          first_name: \"<PERSON>\",\n          last_name: \"<PERSON>\",\n          created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1e3).toISOString()\n        }\n      ]);\n    }\n    const response = await resend.contacts.list({ audienceId });\n    if (response.error) {\n      logger.error(\"Error fetching contacts:\", response.error);\n      return json({ error: response.error.message }, { status: 500 });\n    }\n    if (response.data && Array.isArray(response.data)) {\n      return json(response.data);\n    } else if (response.data && response.data.data && Array.isArray(response.data.data)) {\n      return json(response.data.data);\n    } else {\n      return json([]);\n    }\n  } catch (error) {\n    logger.error(\"Error fetching contacts:\", error);\n    return json({ error: \"Failed to fetch contacts\" }, { status: 500 });\n  }\n}\nasync function POST({ request }) {\n  try {\n    const body = await request.json();\n    const { audienceId, email, firstName, lastName, data } = body;\n    if (!audienceId || !email) {\n      return json({ error: \"Audience ID and email are required\" }, { status: 400 });\n    }\n    const contactData = { email };\n    if (firstName) contactData.first_name = firstName;\n    if (lastName) contactData.last_name = lastName;\n    if (data) contactData.data = data;\n    if (!resend) {\n      logger.warn(\"Resend API key not configured, creating sample contact\");\n      return json({\n        id: `sample-contact-${Date.now()}`,\n        email,\n        first_name: firstName,\n        last_name: lastName,\n        created_at: (/* @__PURE__ */ new Date()).toISOString()\n      }, { status: 201 });\n    }\n    const response = await resend.contacts.create({\n      audienceId,\n      ...contactData\n    });\n    if (response.error) {\n      logger.error(\"Error creating contact:\", response.error);\n      return json({ error: response.error.message }, { status: 500 });\n    }\n    if (response.data && typeof response.data === \"object\") {\n      if (response.data.data && typeof response.data.data === \"object\") {\n        return json(response.data.data, { status: 201 });\n      } else {\n        return json(response.data, { status: 201 });\n      }\n    } else {\n      return json({ error: \"Unexpected response format\" }, { status: 500 });\n    }\n  } catch (error) {\n    logger.error(\"Error creating contact:\", error);\n    return json({ error: \"Failed to create contact\" }, { status: 500 });\n  }\n}\nasync function PUT({ request }) {\n  try {\n    const body = await request.json();\n    const { audienceId, id, email, firstName, lastName, data } = body;\n    if (!audienceId || !id) {\n      return json({ error: \"Audience ID and contact ID are required\" }, { status: 400 });\n    }\n    const contactData = {};\n    if (email) contactData.email = email;\n    if (firstName) contactData.first_name = firstName;\n    if (lastName) contactData.last_name = lastName;\n    if (data) contactData.data = data;\n    if (!resend) {\n      logger.warn(\"Resend API key not configured, updating sample contact\");\n      return json({\n        id,\n        email: email || \"<EMAIL>\",\n        first_name: firstName,\n        last_name: lastName,\n        created_at: (/* @__PURE__ */ new Date()).toISOString()\n      });\n    }\n    const response = await resend.contacts.update({\n      audienceId,\n      id,\n      ...contactData\n    });\n    if (response.error) {\n      logger.error(\"Error updating contact:\", response.error);\n      return json({ error: response.error.message }, { status: 500 });\n    }\n    if (response.data && typeof response.data === \"object\") {\n      if (response.data.data && typeof response.data.data === \"object\") {\n        return json(response.data.data);\n      } else {\n        return json(response.data);\n      }\n    } else {\n      return json({ error: \"Unexpected response format\" }, { status: 500 });\n    }\n  } catch (error) {\n    logger.error(\"Error updating contact:\", error);\n    return json({ error: \"Failed to update contact\" }, { status: 500 });\n  }\n}\nasync function DELETE({ url }) {\n  try {\n    const audienceId = url.searchParams.get(\"audienceId\");\n    const id = url.searchParams.get(\"id\");\n    if (!audienceId || !id) {\n      return json({ error: \"Audience ID and contact ID are required\" }, { status: 400 });\n    }\n    if (!resend) {\n      logger.warn(\"Resend API key not configured, simulating contact deletion\");\n      return json({ success: true });\n    }\n    const response = await resend.contacts.remove({\n      audienceId,\n      id\n    });\n    if (response.error) {\n      logger.error(\"Error deleting contact:\", response.error);\n      return json({ error: response.error.message }, { status: 500 });\n    }\n    return json({ success: true });\n  } catch (error) {\n    logger.error(\"Error deleting contact:\", error);\n    return json({ error: \"Failed to delete contact\" }, { status: 500 });\n  }\n}\nexport {\n  DELETE,\n  GET,\n  POST,\n  PUT\n};\n"], "names": [], "mappings": ";;;;;AAIA,MAAM,MAAM,GAAG,WAAW,CAAC,cAAc,GAAG,IAAI,MAAM,CAAC,WAAW,CAAC,cAAc,CAAC,GAAG,IAAI;AACzF,eAAe,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE;AAC5B,EAAE,IAAI;AACN,IAAI,MAAM,UAAU,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,YAAY,CAAC;AACzD,IAAI,IAAI,CAAC,UAAU,EAAE;AACrB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,MAAM,CAAC,IAAI,CAAC,0DAA0D,CAAC;AAC7E,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ;AACR,UAAU,EAAE,EAAE,kBAAkB;AAChC,UAAU,KAAK,EAAE,mBAAmB;AACpC,UAAU,UAAU,EAAE,MAAM;AAC5B,UAAU,SAAS,EAAE,KAAK;AAC1B,UAAU,UAAU,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,WAAW;AAC/E,SAAS;AACT,QAAQ;AACR,UAAU,EAAE,EAAE,kBAAkB;AAChC,UAAU,KAAK,EAAE,mBAAmB;AACpC,UAAU,UAAU,EAAE,MAAM;AAC5B,UAAU,SAAS,EAAE,OAAO;AAC5B,UAAU,UAAU,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,WAAW;AAC/E;AACA,OAAO,CAAC;AACR;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,CAAC;AAC/D,IAAI,IAAI,QAAQ,CAAC,KAAK,EAAE;AACxB,MAAM,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,QAAQ,CAAC,KAAK,CAAC;AAC9D,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACrE;AACA,IAAI,IAAI,QAAQ,CAAC,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AACvD,MAAM,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AAChC,KAAK,MAAM,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AACzF,MAAM,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;AACrC,KAAK,MAAM;AACX,MAAM,OAAO,IAAI,CAAC,EAAE,CAAC;AACrB;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC;AACnD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,0BAA0B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACvE;AACA;AACA,eAAe,IAAI,CAAC,EAAE,OAAO,EAAE,EAAE;AACjC,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,IAAI;AACjE,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,EAAE;AAC/B,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oCAAoC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnF;AACA,IAAI,MAAM,WAAW,GAAG,EAAE,KAAK,EAAE;AACjC,IAAI,IAAI,SAAS,EAAE,WAAW,CAAC,UAAU,GAAG,SAAS;AACrD,IAAI,IAAI,QAAQ,EAAE,WAAW,CAAC,SAAS,GAAG,QAAQ;AAClD,IAAI,IAAI,IAAI,EAAE,WAAW,CAAC,IAAI,GAAG,IAAI;AACrC,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,MAAM,CAAC,IAAI,CAAC,wDAAwD,CAAC;AAC3E,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,EAAE,EAAE,CAAC,eAAe,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;AAC1C,QAAQ,KAAK;AACb,QAAQ,UAAU,EAAE,SAAS;AAC7B,QAAQ,SAAS,EAAE,QAAQ;AAC3B,QAAQ,UAAU,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC5D,OAAO,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzB;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;AAClD,MAAM,UAAU;AAChB,MAAM,GAAG;AACT,KAAK,CAAC;AACN,IAAI,IAAI,QAAQ,CAAC,KAAK,EAAE;AACxB,MAAM,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,QAAQ,CAAC,KAAK,CAAC;AAC7D,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACrE;AACA,IAAI,IAAI,QAAQ,CAAC,IAAI,IAAI,OAAO,QAAQ,CAAC,IAAI,KAAK,QAAQ,EAAE;AAC5D,MAAM,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;AACxE,QAAQ,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxD,OAAO,MAAM;AACb,QAAQ,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnD;AACA,KAAK,MAAM;AACX,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,4BAA4B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3E;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC;AAClD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,0BAA0B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACvE;AACA;AACA,eAAe,GAAG,CAAC,EAAE,OAAO,EAAE,EAAE;AAChC,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,MAAM,EAAE,UAAU,EAAE,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,IAAI;AACrE,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,EAAE,EAAE;AAC5B,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yCAAyC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxF;AACA,IAAI,MAAM,WAAW,GAAG,EAAE;AAC1B,IAAI,IAAI,KAAK,EAAE,WAAW,CAAC,KAAK,GAAG,KAAK;AACxC,IAAI,IAAI,SAAS,EAAE,WAAW,CAAC,UAAU,GAAG,SAAS;AACrD,IAAI,IAAI,QAAQ,EAAE,WAAW,CAAC,SAAS,GAAG,QAAQ;AAClD,IAAI,IAAI,IAAI,EAAE,WAAW,CAAC,IAAI,GAAG,IAAI;AACrC,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,MAAM,CAAC,IAAI,CAAC,wDAAwD,CAAC;AAC3E,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,EAAE;AACV,QAAQ,KAAK,EAAE,KAAK,IAAI,qBAAqB;AAC7C,QAAQ,UAAU,EAAE,SAAS;AAC7B,QAAQ,SAAS,EAAE,QAAQ;AAC3B,QAAQ,UAAU,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC5D,OAAO,CAAC;AACR;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;AAClD,MAAM,UAAU;AAChB,MAAM,EAAE;AACR,MAAM,GAAG;AACT,KAAK,CAAC;AACN,IAAI,IAAI,QAAQ,CAAC,KAAK,EAAE;AACxB,MAAM,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,QAAQ,CAAC,KAAK,CAAC;AAC7D,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACrE;AACA,IAAI,IAAI,QAAQ,CAAC,IAAI,IAAI,OAAO,QAAQ,CAAC,IAAI,KAAK,QAAQ,EAAE;AAC5D,MAAM,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;AACxE,QAAQ,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;AACvC,OAAO,MAAM;AACb,QAAQ,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AAClC;AACA,KAAK,MAAM;AACX,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,4BAA4B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3E;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC;AAClD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,0BAA0B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACvE;AACA;AACA,eAAe,MAAM,CAAC,EAAE,GAAG,EAAE,EAAE;AAC/B,EAAE,IAAI;AACN,IAAI,MAAM,UAAU,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,YAAY,CAAC;AACzD,IAAI,MAAM,EAAE,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC;AACzC,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,EAAE,EAAE;AAC5B,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yCAAyC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxF;AACA,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,MAAM,CAAC,IAAI,CAAC,4DAA4D,CAAC;AAC/E,MAAM,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;AACpC;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;AAClD,MAAM,UAAU;AAChB,MAAM;AACN,KAAK,CAAC;AACN,IAAI,IAAI,QAAQ,CAAC,KAAK,EAAE;AACxB,MAAM,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,QAAQ,CAAC,KAAK,CAAC;AAC7D,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACrE;AACA,IAAI,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;AAClC,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC;AAClD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,0BAA0B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACvE;AACA;;;;"}