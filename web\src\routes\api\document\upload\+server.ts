import { prisma } from '$lib/server/prisma';
import type { RequestHand<PERSON> } from './$types';
import { uploadDocument } from '$lib/utils/documentUpload';
import { ensureUniqueDocumentName } from '$lib/utils/documentNameUniqueness';
import { determineDocumentSource } from '$lib/utils/documentSource';
import { trackDocumentUpload, canCreateResume } from '$lib/server/resume-usage';

// Define the Document type
interface Document {
  id: string;
  label: string;
  fileUrl: string;
  filePath: string;
  fileName: string;
  type: string;
  contentType: string;
  storageType: string;
  storageLocation: string;
  createdAt: Date;
  updatedAt: Date;
  isDefault: boolean;
  profileId: string | null;
  userId: string;
  teamId: string | null;
  fileSize: number;
  pageCount: number | null;
  source?: 'generated' | 'uploaded' | 'created';
}

// Using the shared Prisma client from $lib/server/prisma

export const POST: RequestHandler = async ({ request, locals }) => {
  const user = locals.user;
  console.log('User in document upload:', user);
  if (!user) return new Response('Unauthorized', { status: 401 });

  // Ensure user has an id
  if (!user.id) {
    console.error('User missing ID:', user);
    return new Response('User ID missing', { status: 400 });
  }

  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const profileId = formData.get('profileId') as string;
    let label = (formData.get('label') as string) || file.name;
    const documentType = (formData.get('documentType') as string) || 'document';

    if (!file) {
      return new Response('Missing file', { status: 400 });
    }

    // Only validate profile if profileId is provided
    if (profileId) {
      const profile = await prisma.profile.findUnique({ where: { id: profileId } });
      if (!profile) {
        return new Response('Invalid profileId', { status: 404 });
      }
    }

    // Ensure the document name is unique
    label = await ensureUniqueDocumentName(label, user.id, documentType);

    // Check if the user has reached their document limit
    // In development mode, we'll bypass this check
    const isDev =
      process.env.NODE_ENV === 'development' || process.env.VITE_DISABLE_FEATURE_LIMITS === 'true';

    if (!isDev) {
      const canCreate = await canCreateResume(user.id);
      if (!canCreate) {
        return new Response(
          JSON.stringify({
            error: 'Document limit reached',
            limitReached: true,
            message:
              'You have reached your document upload limit. Please upgrade your plan to upload more documents.',
          }),
          {
            status: 403,
            headers: { 'Content-Type': 'application/json' },
          }
        );
      }
    } else {
      console.log('Development mode: Bypassing document limit check in document upload API');
    }

    // Upload the document file
    try {
      console.log('Uploading document:', {
        fileName: file.name,
        fileType: file.type,
        fileSize: file.size,
        documentType,
      });

      const uploadResult = await uploadDocument(file, documentType, user.id);
      console.log('Upload result:', uploadResult);

      // Create a Document record
      console.log('Creating document with data:', {
        label,
        fileUrl: uploadResult.publicPath,
        userId: user.id,
        profileId: profileId || null,
        type: documentType,
      });

      console.log('User ID:', user.id);

      // Log the data we're about to send to Prisma
      console.log('Document data for Prisma:', {
        label,
        fileUrl: uploadResult.publicPath,
        filePath: uploadResult.filePath,
        fileName: uploadResult.originalFileName,
        type: documentType,
        contentType: uploadResult.contentType,
        fileSize: uploadResult.fileSize,
        storageType: 'local',
        storageLocation: documentType,
        userId: user.id,
        profileId: profileId || undefined,
      });

      // Declare document variable outside the try block
      let document: Document | undefined;

      try {
        document = await prisma.document.create({
          data: {
            label,
            fileUrl: uploadResult.publicPath,
            filePath: uploadResult.filePath,
            fileName: uploadResult.originalFileName,
            type: documentType,
            contentType: uploadResult.contentType,
            fileSize: uploadResult.fileSize,
            storageType: 'r2', // Updated to use R2 storage
            storageLocation: documentType,
            // Note: 'source' field is not in the Prisma schema, so we can't set it here
            userId: user.id,
            ...(profileId ? { profileId } : {}),
          },
        });

        console.log('Document created successfully:', document);

        // Track document upload for feature usage
        await trackDocumentUpload(user.id);
        console.log('Document upload tracked for feature usage');
      } catch (dbError) {
        console.error('Error creating document in database:', dbError);
        throw new Error(`Database error: ${dbError.message}`);
      }

      // Determine the source based on document properties
      const source = determineDocumentSource(document);

      // Return the document data with source information
      return new Response(
        JSON.stringify({
          document: {
            ...document,
            source, // Add the determined source information for the frontend
          },
          message: 'Document uploaded successfully.',
        }),
        {
          headers: { 'Content-Type': 'application/json' },
        }
      );
    } catch (error) {
      // Check if it's a file type error
      if (error.message && error.message.includes('File type')) {
        return new Response(
          JSON.stringify({
            error: 'Invalid file type',
            details: error.message,
          }),
          {
            status: 400,
            headers: { 'Content-Type': 'application/json' },
          }
        );
      }

      // Re-throw for general error handling
      throw error;
    }
  } catch (error) {
    console.error('Error creating document:', error);
    return new Response(
      JSON.stringify({
        error: 'Failed to create document',
        details: error.message,
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      }
    );
  }
};
