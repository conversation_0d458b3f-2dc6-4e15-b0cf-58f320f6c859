{"version": 3, "file": "_server.ts-B3Gq25tw.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/billing/set-default-payment-method/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../../../chunks/auth.js\";\nconst POST = async ({ cookies, request }) => {\n  const token = cookies.get(\"auth_token\");\n  const isProd = process.env.NODE_ENV === \"production\";\n  const { paymentMethodId } = await request.json();\n  if (!token) return new Response(\"Unauthorized\", { status: 401 });\n  if (!paymentMethodId) return new Response(\"Payment method ID is required\", { status: 400 });\n  const userData = await verifySessionToken(token);\n  if (!userData?.id) return new Response(\"Unauthorized\", { status: 401 });\n  const stripeSecret = isProd ? process.env.STRIPE_SECRET_KEY_LIVE || \"sk_live_placeholder\" : process.env.STRIPE_SECRET_KEY_TEST || \"sk_test_placeholder\";\n  const Stripe = (await import(\"stripe\")).default;\n  const stripe = new Stripe(stripeSecret, {\n    apiVersion: \"2025-04-30.basil\"\n  });\n  let user = await prisma.user.findUnique({ where: { id: userData.id } });\n  if (!user) return new Response(\"User not found\", { status: 404 });\n  if (!user.stripeCustomerId) return new Response(\"No customer found\", { status: 404 });\n  try {\n    await stripe.customers.update(user.stripeCustomerId, {\n      invoice_settings: {\n        default_payment_method: paymentMethodId\n      }\n    });\n    const subscriptions = await stripe.subscriptions.list({\n      customer: user.stripeCustomerId,\n      status: \"active\"\n    });\n    if (subscriptions.data.length > 0) {\n      await Promise.all(\n        subscriptions.data.map(\n          (subscription) => stripe.subscriptions.update(subscription.id, {\n            default_payment_method: paymentMethodId\n          })\n        )\n      );\n    }\n    const updatedPaymentMethodsResponse = await stripe.paymentMethods.list({\n      customer: user.stripeCustomerId,\n      type: \"card\"\n    });\n    const updatedPaymentMethods = updatedPaymentMethodsResponse.data.map((method) => ({\n      ...method,\n      isDefault: method.id === paymentMethodId\n    }));\n    return json({\n      success: true,\n      message: \"Default payment method updated\",\n      paymentMethods: updatedPaymentMethods\n    });\n  } catch (error) {\n    console.error(\"Error updating default payment method:\", error);\n    return new Response(\"Failed to update default payment method\", { status: 500 });\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;AAGK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AAC7C,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;AACtD,EAAE,MAAM,EAAE,eAAe,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AAClD,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE,EAAE,IAAI,CAAC,eAAe,EAAE,OAAO,IAAI,QAAQ,CAAC,+BAA+B,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7F,EAAE,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AAClD,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE,EAAE,MAAM,YAAY,GAAG,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,qBAAqB,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,qBAAqB;AACzJ,EAAE,MAAM,MAAM,GAAG,CAAC,MAAM,OAAO,+BAAQ,CAAC,EAAE,OAAO;AACjD,EAAE,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,YAAY,EAAE;AAC1C,IAAI,UAAU,EAAE;AAChB,GAAG,CAAC;AACJ,EAAE,IAAI,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE,EAAE,CAAC;AACzE,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,QAAQ,CAAC,gBAAgB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnE,EAAE,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,OAAO,IAAI,QAAQ,CAAC,mBAAmB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACvF,EAAE,IAAI;AACN,IAAI,MAAM,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,EAAE;AACzD,MAAM,gBAAgB,EAAE;AACxB,QAAQ,sBAAsB,EAAE;AAChC;AACA,KAAK,CAAC;AACN,IAAI,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC;AAC1D,MAAM,QAAQ,EAAE,IAAI,CAAC,gBAAgB;AACrC,MAAM,MAAM,EAAE;AACd,KAAK,CAAC;AACN,IAAI,IAAI,aAAa,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;AACvC,MAAM,MAAM,OAAO,CAAC,GAAG;AACvB,QAAQ,aAAa,CAAC,IAAI,CAAC,GAAG;AAC9B,UAAU,CAAC,YAAY,KAAK,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,EAAE;AACzE,YAAY,sBAAsB,EAAE;AACpC,WAAW;AACX;AACA,OAAO;AACP;AACA,IAAI,MAAM,6BAA6B,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC;AAC3E,MAAM,QAAQ,EAAE,IAAI,CAAC,gBAAgB;AACrC,MAAM,IAAI,EAAE;AACZ,KAAK,CAAC;AACN,IAAI,MAAM,qBAAqB,GAAG,6BAA6B,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,MAAM;AACtF,MAAM,GAAG,MAAM;AACf,MAAM,SAAS,EAAE,MAAM,CAAC,EAAE,KAAK;AAC/B,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,cAAc,EAAE;AACtB,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC;AAClE,IAAI,OAAO,IAAI,QAAQ,CAAC,yCAAyC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnF;AACA;;;;"}