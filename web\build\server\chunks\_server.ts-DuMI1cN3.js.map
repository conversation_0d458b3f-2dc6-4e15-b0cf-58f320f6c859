{"version": 3, "file": "_server.ts-DuMI1cN3.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/ai/ats/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { d as private_env } from \"../../../../../chunks/shared-server.js\";\nconst WORKER_API_URL = private_env.WORKER_API_URL ?? \"http://localhost:3002\";\nconst POST = async ({ request, locals }) => {\n  if (!locals.user) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  try {\n    const { resumeId, jobId } = await request.json();\n    if (!resumeId) {\n      return json({ error: \"Resume ID is required\" }, { status: 400 });\n    }\n    const resume = await prisma.document.findUnique({\n      where: {\n        id: resumeId,\n        userId: locals.user.id\n      },\n      include: {\n        resume: true\n      }\n    });\n    if (!resume) {\n      return json({ error: \"Resume not found\" }, { status: 404 });\n    }\n    let jobDetails = null;\n    if (jobId) {\n      const job = await prisma.jobListing.findUnique({\n        where: {\n          id: jobId\n        },\n        select: {\n          title: true,\n          company: true,\n          description: true,\n          requirements: true\n        }\n      });\n      if (job) {\n        jobDetails = job;\n      }\n    }\n    const analysis = await generateATSAnalysis(resume, jobDetails);\n    const savedAnalysis = await prisma.atsAnalysis.create({\n      data: {\n        userId: locals.user.id,\n        resumeId,\n        jobId,\n        overallScore: analysis.overallScore,\n        keywordScore: analysis.keywordScore,\n        formatScore: analysis.formatScore,\n        contentScore: analysis.contentScore,\n        readabilityScore: analysis.readabilityScore,\n        keywordMatches: analysis.keywordMatches,\n        missingKeywords: analysis.missingKeywords,\n        formatIssues: analysis.formatIssues,\n        contentSuggestions: analysis.contentSuggestions,\n        readabilitySuggestions: analysis.readabilitySuggestions,\n        jobSpecific: !!jobDetails\n      }\n    });\n    await prisma.featureUsage.upsert({\n      where: {\n        userId_featureId_limitId: {\n          userId: locals.user.id,\n          featureId: \"ats_optimization\",\n          limitId: \"ats_scans_monthly\"\n        }\n      },\n      update: {\n        usage: {\n          increment: 1\n        }\n      },\n      create: {\n        userId: locals.user.id,\n        featureId: \"ats_optimization\",\n        limitId: \"ats_scans_monthly\",\n        usage: 1\n      }\n    });\n    return json({ analysis: savedAnalysis });\n  } catch (error) {\n    console.error(\"Error generating ATS analysis:\", error);\n    return json({ error: \"Failed to generate ATS analysis\" }, { status: 500 });\n  }\n};\nconst GET = async ({ url, locals }) => {\n  if (!locals.user) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  try {\n    const resumeId = url.searchParams.get(\"resumeId\");\n    const jobId = url.searchParams.get(\"jobId\");\n    if (!resumeId) {\n      return json({ error: \"Resume ID is required\" }, { status: 400 });\n    }\n    const query = {\n      where: {\n        userId: locals.user.id,\n        resumeId\n      },\n      orderBy: {\n        createdAt: \"desc\"\n      },\n      take: 1\n    };\n    if (jobId) {\n      query.where.jobId = jobId;\n    }\n    const analysis = await prisma.atsAnalysis.findMany(query);\n    if (analysis.length === 0) {\n      return json({ error: \"No analysis found\" }, { status: 404 });\n    }\n    return json({ analysis: analysis[0] });\n  } catch (error) {\n    console.error(\"Error fetching ATS analysis:\", error);\n    return json({ error: \"Failed to fetch ATS analysis\" }, { status: 500 });\n  }\n};\nasync function generateATSAnalysis(resume, jobDetails = null) {\n  try {\n    const resumeContent = resume.resume ? JSON.stringify(resume.resume) : \"\";\n    const resumeText = resume.content || \"\";\n    const requestBody = {\n      resumeText\n    };\n    if (jobDetails) {\n      requestBody.jobDescription = `\nJob Title: ${jobDetails.title}\nCompany: ${jobDetails.company}\nDescription: ${jobDetails.description || \"\"}\nRequirements: ${jobDetails.requirements || \"\"}\n`;\n    }\n    const response = await fetch(`${WORKER_API_URL}/api/ai/resume-analysis`, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\"\n      },\n      body: JSON.stringify(requestBody)\n    });\n    if (!response.ok) {\n      throw new Error(`Worker API returned ${response.status}: ${response.statusText}`);\n    }\n    const data = await response.json();\n    return {\n      overallScore: data.analysis.overallScore ?? 0,\n      keywordScore: data.analysis.keywordScore ?? 0,\n      formatScore: data.analysis.formatScore ?? 0,\n      contentScore: data.analysis.contentScore ?? 0,\n      readabilityScore: data.analysis.readabilityScore ?? 0,\n      keywordMatches: data.analysis.keywordMatches ?? [],\n      missingKeywords: data.analysis.missingKeywords ?? [],\n      formatIssues: data.analysis.formatIssues ?? [],\n      contentSuggestions: data.analysis.contentSuggestions ?? [],\n      readabilitySuggestions: data.analysis.readabilitySuggestions ?? []\n    };\n  } catch (error) {\n    console.error(\"Error in ATS analysis generation:\", error);\n    return {\n      overallScore: 70,\n      keywordScore: 65,\n      formatScore: 75,\n      contentScore: 70,\n      readabilityScore: 80,\n      keywordMatches: [\"resume\", \"experience\", \"skills\"],\n      missingKeywords: [\"specific technical skills\", \"certifications\"],\n      formatIssues: [\"Consider using a more ATS-friendly format\"],\n      contentSuggestions: [\"Add more quantifiable achievements\"],\n      readabilitySuggestions: [\"Use more bullet points for better readability\"]\n    };\n  }\n}\nexport {\n  GET,\n  POST\n};\n"], "names": [], "mappings": ";;;;;AAGA,MAAM,cAAc,GAAG,WAAW,CAAC,cAAc,IAAI,uBAAuB;AACvE,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AAC5C,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;AACpB,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACpD,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtE;AACA,IAAI,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;AACpD,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE,QAAQ;AACpB,QAAQ,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC;AAC5B,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,MAAM,EAAE;AAChB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,kBAAkB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE;AACA,IAAI,IAAI,UAAU,GAAG,IAAI;AACzB,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC;AACrD,QAAQ,KAAK,EAAE;AACf,UAAU,EAAE,EAAE;AACd,SAAS;AACT,QAAQ,MAAM,EAAE;AAChB,UAAU,KAAK,EAAE,IAAI;AACrB,UAAU,OAAO,EAAE,IAAI;AACvB,UAAU,WAAW,EAAE,IAAI;AAC3B,UAAU,YAAY,EAAE;AACxB;AACA,OAAO,CAAC;AACR,MAAM,IAAI,GAAG,EAAE;AACf,QAAQ,UAAU,GAAG,GAAG;AACxB;AACA;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,mBAAmB,CAAC,MAAM,EAAE,UAAU,CAAC;AAClE,IAAI,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;AAC1D,MAAM,IAAI,EAAE;AACZ,QAAQ,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;AAC9B,QAAQ,QAAQ;AAChB,QAAQ,KAAK;AACb,QAAQ,YAAY,EAAE,QAAQ,CAAC,YAAY;AAC3C,QAAQ,YAAY,EAAE,QAAQ,CAAC,YAAY;AAC3C,QAAQ,WAAW,EAAE,QAAQ,CAAC,WAAW;AACzC,QAAQ,YAAY,EAAE,QAAQ,CAAC,YAAY;AAC3C,QAAQ,gBAAgB,EAAE,QAAQ,CAAC,gBAAgB;AACnD,QAAQ,cAAc,EAAE,QAAQ,CAAC,cAAc;AAC/C,QAAQ,eAAe,EAAE,QAAQ,CAAC,eAAe;AACjD,QAAQ,YAAY,EAAE,QAAQ,CAAC,YAAY;AAC3C,QAAQ,kBAAkB,EAAE,QAAQ,CAAC,kBAAkB;AACvD,QAAQ,sBAAsB,EAAE,QAAQ,CAAC,sBAAsB;AAC/D,QAAQ,WAAW,EAAE,CAAC,CAAC;AACvB;AACA,KAAK,CAAC;AACN,IAAI,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AACrC,MAAM,KAAK,EAAE;AACb,QAAQ,wBAAwB,EAAE;AAClC,UAAU,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;AAChC,UAAU,SAAS,EAAE,kBAAkB;AACvC,UAAU,OAAO,EAAE;AACnB;AACA,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,KAAK,EAAE;AACf,UAAU,SAAS,EAAE;AACrB;AACA,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;AAC9B,QAAQ,SAAS,EAAE,kBAAkB;AACrC,QAAQ,OAAO,EAAE,mBAAmB;AACpC,QAAQ,KAAK,EAAE;AACf;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,EAAE,QAAQ,EAAE,aAAa,EAAE,CAAC;AAC5C,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC;AAC1D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,iCAAiC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9E;AACA;AACK,MAAC,GAAG,GAAG,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK;AACvC,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;AACpB,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,QAAQ,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC;AACrD,IAAI,MAAM,KAAK,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC;AAC/C,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtE;AACA,IAAI,MAAM,KAAK,GAAG;AAClB,MAAM,KAAK,EAAE;AACb,QAAQ,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;AAC9B,QAAQ;AACR,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,SAAS,EAAE;AACnB,OAAO;AACP,MAAM,IAAI,EAAE;AACZ,KAAK;AACL,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK;AAC/B;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC;AAC7D,IAAI,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;AAC/B,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE;AACA,IAAI,OAAO,IAAI,CAAC,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;AAC1C,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC;AACxD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,8BAA8B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3E;AACA;AACA,eAAe,mBAAmB,CAAC,MAAM,EAAE,UAAU,GAAG,IAAI,EAAE;AAC9D,EAAE,IAAI;AACN,IAAI,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE;AAC5E,IAAI,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,IAAI,EAAE;AAC3C,IAAI,MAAM,WAAW,GAAG;AACxB,MAAM;AACN,KAAK;AACL,IAAI,IAAI,UAAU,EAAE;AACpB,MAAM,WAAW,CAAC,cAAc,GAAG;AACnC,WAAW,EAAE,UAAU,CAAC,KAAK;AAC7B,SAAS,EAAE,UAAU,CAAC,OAAO;AAC7B,aAAa,EAAE,UAAU,CAAC,WAAW,IAAI,EAAE;AAC3C,cAAc,EAAE,UAAU,CAAC,YAAY,IAAI,EAAE;AAC7C,CAAC;AACD;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,uBAAuB,CAAC,EAAE;AAC7E,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,OAAO,EAAE;AACf,QAAQ,cAAc,EAAE;AACxB,OAAO;AACP,MAAM,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW;AACtC,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;AACtB,MAAM,MAAM,IAAI,KAAK,CAAC,CAAC,oBAAoB,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;AACvF;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE;AACtC,IAAI,OAAO;AACX,MAAM,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,YAAY,IAAI,CAAC;AACnD,MAAM,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,YAAY,IAAI,CAAC;AACnD,MAAM,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI,CAAC;AACjD,MAAM,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,YAAY,IAAI,CAAC;AACnD,MAAM,gBAAgB,EAAE,IAAI,CAAC,QAAQ,CAAC,gBAAgB,IAAI,CAAC;AAC3D,MAAM,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC,cAAc,IAAI,EAAE;AACxD,MAAM,eAAe,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAe,IAAI,EAAE;AAC1D,MAAM,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,YAAY,IAAI,EAAE;AACpD,MAAM,kBAAkB,EAAE,IAAI,CAAC,QAAQ,CAAC,kBAAkB,IAAI,EAAE;AAChE,MAAM,sBAAsB,EAAE,IAAI,CAAC,QAAQ,CAAC,sBAAsB,IAAI;AACtE,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC;AAC7D,IAAI,OAAO;AACX,MAAM,YAAY,EAAE,EAAE;AACtB,MAAM,YAAY,EAAE,EAAE;AACtB,MAAM,WAAW,EAAE,EAAE;AACrB,MAAM,YAAY,EAAE,EAAE;AACtB,MAAM,gBAAgB,EAAE,EAAE;AAC1B,MAAM,cAAc,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,QAAQ,CAAC;AACxD,MAAM,eAAe,EAAE,CAAC,2BAA2B,EAAE,gBAAgB,CAAC;AACtE,MAAM,YAAY,EAAE,CAAC,2CAA2C,CAAC;AACjE,MAAM,kBAAkB,EAAE,CAAC,oCAAoC,CAAC;AAChE,MAAM,sBAAsB,EAAE,CAAC,+CAA+C;AAC9E,KAAK;AACL;AACA;;;;"}