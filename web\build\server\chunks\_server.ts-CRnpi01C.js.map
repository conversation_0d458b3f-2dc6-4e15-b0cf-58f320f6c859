{"version": 3, "file": "_server.ts-CRnpi01C.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/auth/check-session/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { v as verifySessionToken } from \"../../../../../chunks/auth.js\";\nconst GET = async ({ cookies }) => {\n  const token = cookies.get(\"auth_token\");\n  if (!token) {\n    return json({ authenticated: false }, { status: 401 });\n  }\n  const userData = await verifySessionToken(token);\n  if (!userData || !userData.id) {\n    return json({ authenticated: false }, { status: 401 });\n  }\n  return json({ authenticated: true, userId: userData.id });\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;;;;;;AAEK,MAAC,GAAG,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK;AACnC,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE;AACd,IAAI,OAAO,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1D;AACA,EAAE,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AAClD,EAAE,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;AACjC,IAAI,OAAO,IAAI,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1D;AACA,EAAE,OAAO,IAAI,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,EAAE,CAAC;AAC3D;;;;"}