{"version": 3, "file": "_server.ts-DZJFOm8b.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/resume/_id_/parse/_server.ts.js"], "sourcesContent": ["import { p as prisma } from \"../../../../../../chunks/prisma.js\";\nimport { j as json } from \"../../../../../../chunks/index.js\";\nimport { g as getRedisClient } from \"../../../../../../chunks/redis.js\";\nasync function getProfileId(request, resumeDocumentId) {\n  let profileId = null;\n  try {\n    const requestData = await request.json();\n    profileId = requestData.profileId ?? null;\n    console.log(\"Profile ID from request:\", profileId);\n  } catch (error) {\n    console.log(\n      \"No profile ID in request body, will try to find from database\",\n      error instanceof Error ? error.message : String(error)\n    );\n  }\n  if (!profileId) {\n    const profile = await prisma.profile.findFirst({\n      where: {\n        documents: {\n          some: {\n            id: resumeDocumentId\n          }\n        }\n      },\n      select: {\n        id: true\n      }\n    });\n    profileId = profile?.id ?? null;\n    console.log(\"Profile ID from database:\", profileId);\n  }\n  return profileId;\n}\nasync function updateWorkerProcess(profileId, resumeId, userId) {\n  try {\n    const timestamp = Date.now();\n    const random = Math.random().toString(36).substring(2, 9);\n    const jobId = `job_${timestamp}_${random}`;\n    const oldFormatId = `profile-parsing-${profileId}-${resumeId}`;\n    let existingProcess = await prisma.workerProcess.findUnique({\n      where: { id: oldFormatId }\n    });\n    if (!existingProcess) {\n      existingProcess = await prisma.workerProcess.findFirst({\n        where: {\n          type: \"resume-parsing\",\n          data: {\n            path: [\"resumeId\"],\n            equals: resumeId\n          }\n        },\n        orderBy: {\n          createdAt: \"desc\"\n        }\n      });\n    }\n    if (existingProcess) {\n      if (existingProcess.status !== \"completed\") {\n        if (existingProcess.id.startsWith(\"profile-parsing-\")) {\n          console.log(\n            `Found record with old ID format: ${existingProcess.id}, creating new record with new format`\n          );\n        } else {\n          await prisma.workerProcess.update({\n            where: { id: existingProcess.id },\n            data: {\n              status: \"pending\",\n              updatedAt: /* @__PURE__ */ new Date(),\n              data: {\n                resumeId,\n                profileId,\n                userId,\n                timestamp: (/* @__PURE__ */ new Date()).toISOString()\n              }\n            }\n          });\n          console.log(\n            `Updated existing WorkerProcess record ${existingProcess.id} for resume ${resumeId}`\n          );\n          return existingProcess.id;\n        }\n      }\n    }\n    await prisma.workerProcess.create({\n      data: {\n        id: jobId,\n        type: \"resume-parsing\",\n        status: \"pending\",\n        data: {\n          resumeId,\n          profileId,\n          userId,\n          timestamp: (/* @__PURE__ */ new Date()).toISOString()\n        },\n        createdAt: /* @__PURE__ */ new Date(),\n        updatedAt: /* @__PURE__ */ new Date()\n      }\n    });\n    console.log(\n      `Created WorkerProcess record ${jobId} for profile ${profileId} and resume ${resumeId}`\n    );\n    return jobId;\n  } catch (error) {\n    console.error(\"Error managing WorkerProcess record:\", error);\n    const fallbackJobId = `job_${Date.now()}_fallback`;\n    return fallbackJobId;\n  }\n}\nasync function addToRedisQueue(resumeId, documentId, userId, profileId, jobId) {\n  try {\n    const redis = await getRedisClient();\n    if (!redis) {\n      console.error(\"❌ Redis connection failed\");\n      return;\n    }\n    const document = await prisma.document.findUnique({\n      where: { id: documentId }\n    });\n    try {\n      await redis.xgroup(\n        \"CREATE\",\n        \"resume-parsing::stream\",\n        \"resume-parsing::group\",\n        \"$\",\n        \"MKSTREAM\"\n      );\n      console.log(\"Created resume-parsing::stream stream group\");\n    } catch (err) {\n      if (!err.message.includes(\"BUSYGROUP\")) {\n        console.error(\"Error creating stream group:\", err);\n      }\n    }\n    let filePath = document?.filePath ?? null;\n    const fileUrl = document?.fileUrl ?? null;\n    if (!filePath && fileUrl) {\n      if (fileUrl.startsWith(\"/uploads/\")) {\n        filePath = `${process.cwd()}/static${fileUrl.replace(/^\\/uploads/, \"\")}`;\n        console.log(`Constructed file path from /uploads/ format: ${filePath}`);\n      } else if (fileUrl.startsWith(\"/assets/\")) {\n        filePath = `${process.cwd()}/static${fileUrl}`;\n        console.log(`Constructed file path from /assets/ format: ${filePath}`);\n      } else if (fileUrl.startsWith(\"/\")) {\n        filePath = `${process.cwd()}/static${fileUrl}`;\n        console.log(`Constructed file path from root-relative format: ${filePath}`);\n      } else {\n        filePath = `${process.cwd()}/static/${fileUrl}`;\n        console.log(`Constructed file path from fallback format: ${filePath}`);\n      }\n    }\n    await redis.xadd(\n      \"resume-parsing::stream\",\n      \"*\",\n      \"job\",\n      JSON.stringify({\n        id: jobId,\n        // Include the job ID in the message\n        resumeId,\n        fileUrl,\n        filePath,\n        userId,\n        profileId,\n        timestamp: (/* @__PURE__ */ new Date()).toISOString()\n      })\n    );\n    await redis.publish(\n      \"resume-parsing::stream::status\",\n      JSON.stringify({\n        jobId,\n        status: \"pending\",\n        resumeId,\n        timestamp: (/* @__PURE__ */ new Date()).toISOString()\n      })\n    );\n    await redis.publish(\n      \"resume-parsing::status\",\n      JSON.stringify({\n        jobId,\n        status: \"pending\",\n        resumeId,\n        timestamp: (/* @__PURE__ */ new Date()).toISOString()\n      })\n    );\n    console.log(\n      `Resume ${resumeId} added to parsing queue with job ID ${jobId} and notifications sent`\n    );\n  } catch (redisError) {\n    console.error(\"Redis error:\", redisError);\n  }\n}\nconst POST = async ({ params, request, locals }) => {\n  try {\n    const user = locals.user;\n    if (!user) return new Response(\"Unauthorized\", { status: 401 });\n    const resumeId = params.id;\n    if (!resumeId) {\n      return json({ error: \"Resume ID is required\" }, { status: 400 });\n    }\n    console.log(\"Parsing resume with ID:\", resumeId);\n    const resume = await prisma.resume.findUnique({\n      where: { id: resumeId },\n      include: {\n        document: true\n      }\n    });\n    if (!resume) {\n      return json({ error: \"Resume not found\" }, { status: 404 });\n    }\n    if (resume.document.userId !== user.id) {\n      return json({ error: \"Unauthorized access to resume\" }, { status: 403 });\n    }\n    await prisma.resume.update({\n      where: { id: resumeId },\n      data: {\n        isParsed: false,\n        parsedAt: null\n      }\n    });\n    const profileId = await getProfileId(request, resume.document.id);\n    let jobId;\n    if (profileId) {\n      jobId = await updateWorkerProcess(profileId, resumeId, user.id);\n    } else {\n      const timestamp = Date.now();\n      const random = Math.random().toString(36).substring(2, 9);\n      jobId = `job_${timestamp}_${random}`;\n    }\n    await addToRedisQueue(resumeId, resume.documentId, user.id, profileId, jobId);\n    return json({\n      success: true,\n      message: \"Resume parsing requested successfully\",\n      resumeId: resume.id\n    });\n  } catch (error) {\n    console.error(\"Error adding resume to parsing queue:\", error);\n    return json(\n      { error: \"Failed to add resume to parsing queue\", details: String(error) },\n      { status: 500 }\n    );\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;AAGA,eAAe,YAAY,CAAC,OAAO,EAAE,gBAAgB,EAAE;AACvD,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI;AACN,IAAI,MAAM,WAAW,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AAC5C,IAAI,SAAS,GAAG,WAAW,CAAC,SAAS,IAAI,IAAI;AAC7C,IAAI,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE,SAAS,CAAC;AACtD,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,GAAG;AACf,MAAM,+DAA+D;AACrE,MAAM,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC,KAAK;AAC3D,KAAK;AACL;AACA,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,IAAI,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;AACnD,MAAM,KAAK,EAAE;AACb,QAAQ,SAAS,EAAE;AACnB,UAAU,IAAI,EAAE;AAChB,YAAY,EAAE,EAAE;AAChB;AACA;AACA,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,EAAE,EAAE;AACZ;AACA,KAAK,CAAC;AACN,IAAI,SAAS,GAAG,OAAO,EAAE,EAAE,IAAI,IAAI;AACnC,IAAI,OAAO,CAAC,GAAG,CAAC,2BAA2B,EAAE,SAAS,CAAC;AACvD;AACA,EAAE,OAAO,SAAS;AAClB;AACA,eAAe,mBAAmB,CAAC,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE;AAChE,EAAE,IAAI;AACN,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE;AAChC,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;AAC7D,IAAI,MAAM,KAAK,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAC9C,IAAI,MAAM,WAAW,GAAG,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AAClE,IAAI,IAAI,eAAe,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC;AAChE,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,WAAW;AAC9B,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,eAAe,EAAE;AAC1B,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC;AAC7D,QAAQ,KAAK,EAAE;AACf,UAAU,IAAI,EAAE,gBAAgB;AAChC,UAAU,IAAI,EAAE;AAChB,YAAY,IAAI,EAAE,CAAC,UAAU,CAAC;AAC9B,YAAY,MAAM,EAAE;AACpB;AACA,SAAS;AACT,QAAQ,OAAO,EAAE;AACjB,UAAU,SAAS,EAAE;AACrB;AACA,OAAO,CAAC;AACR;AACA,IAAI,IAAI,eAAe,EAAE;AACzB,MAAM,IAAI,eAAe,CAAC,MAAM,KAAK,WAAW,EAAE;AAClD,QAAQ,IAAI,eAAe,CAAC,EAAE,CAAC,UAAU,CAAC,kBAAkB,CAAC,EAAE;AAC/D,UAAU,OAAO,CAAC,GAAG;AACrB,YAAY,CAAC,iCAAiC,EAAE,eAAe,CAAC,EAAE,CAAC,qCAAqC;AACxG,WAAW;AACX,SAAS,MAAM;AACf,UAAU,MAAM,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;AAC5C,YAAY,KAAK,EAAE,EAAE,EAAE,EAAE,eAAe,CAAC,EAAE,EAAE;AAC7C,YAAY,IAAI,EAAE;AAClB,cAAc,MAAM,EAAE,SAAS;AAC/B,cAAc,SAAS,kBAAkB,IAAI,IAAI,EAAE;AACnD,cAAc,IAAI,EAAE;AACpB,gBAAgB,QAAQ;AACxB,gBAAgB,SAAS;AACzB,gBAAgB,MAAM;AACtB,gBAAgB,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AACnE;AACA;AACA,WAAW,CAAC;AACZ,UAAU,OAAO,CAAC,GAAG;AACrB,YAAY,CAAC,sCAAsC,EAAE,eAAe,CAAC,EAAE,CAAC,YAAY,EAAE,QAAQ,CAAC;AAC/F,WAAW;AACX,UAAU,OAAO,eAAe,CAAC,EAAE;AACnC;AACA;AACA;AACA,IAAI,MAAM,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;AACtC,MAAM,IAAI,EAAE;AACZ,QAAQ,EAAE,EAAE,KAAK;AACjB,QAAQ,IAAI,EAAE,gBAAgB;AAC9B,QAAQ,MAAM,EAAE,SAAS;AACzB,QAAQ,IAAI,EAAE;AACd,UAAU,QAAQ;AAClB,UAAU,SAAS;AACnB,UAAU,MAAM;AAChB,UAAU,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC7D,SAAS;AACT,QAAQ,SAAS,kBAAkB,IAAI,IAAI,EAAE;AAC7C,QAAQ,SAAS,kBAAkB,IAAI,IAAI;AAC3C;AACA,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,GAAG;AACf,MAAM,CAAC,6BAA6B,EAAE,KAAK,CAAC,aAAa,EAAE,SAAS,CAAC,YAAY,EAAE,QAAQ,CAAC;AAC5F,KAAK;AACL,IAAI,OAAO,KAAK;AAChB,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC;AAChE,IAAI,MAAM,aAAa,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC;AACtD,IAAI,OAAO,aAAa;AACxB;AACA;AACA,eAAe,eAAe,CAAC,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE;AAC/E,EAAE,IAAI;AACN,IAAI,MAAM,KAAK,GAAG,MAAM,cAAc,EAAE;AACxC,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,OAAO,CAAC,KAAK,CAAC,2BAA2B,CAAC;AAChD,MAAM;AACN;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;AACtD,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,UAAU;AAC7B,KAAK,CAAC;AACN,IAAI,IAAI;AACR,MAAM,MAAM,KAAK,CAAC,MAAM;AACxB,QAAQ,QAAQ;AAChB,QAAQ,wBAAwB;AAChC,QAAQ,uBAAuB;AAC/B,QAAQ,GAAG;AACX,QAAQ;AACR,OAAO;AACP,MAAM,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC;AAChE,KAAK,CAAC,OAAO,GAAG,EAAE;AAClB,MAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;AAC9C,QAAQ,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,GAAG,CAAC;AAC1D;AACA;AACA,IAAI,IAAI,QAAQ,GAAG,QAAQ,EAAE,QAAQ,IAAI,IAAI;AAC7C,IAAI,MAAM,OAAO,GAAG,QAAQ,EAAE,OAAO,IAAI,IAAI;AAC7C,IAAI,IAAI,CAAC,QAAQ,IAAI,OAAO,EAAE;AAC9B,MAAM,IAAI,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE;AAC3C,QAAQ,QAAQ,GAAG,CAAC,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC,CAAC;AAChF,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,6CAA6C,EAAE,QAAQ,CAAC,CAAC,CAAC;AAC/E,OAAO,MAAM,IAAI,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;AACjD,QAAQ,QAAQ,GAAG,CAAC,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AACtD,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,4CAA4C,EAAE,QAAQ,CAAC,CAAC,CAAC;AAC9E,OAAO,MAAM,IAAI,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;AAC1C,QAAQ,QAAQ,GAAG,CAAC,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AACtD,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,iDAAiD,EAAE,QAAQ,CAAC,CAAC,CAAC;AACnF,OAAO,MAAM;AACb,QAAQ,QAAQ,GAAG,CAAC,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AACvD,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,4CAA4C,EAAE,QAAQ,CAAC,CAAC,CAAC;AAC9E;AACA;AACA,IAAI,MAAM,KAAK,CAAC,IAAI;AACpB,MAAM,wBAAwB;AAC9B,MAAM,GAAG;AACT,MAAM,KAAK;AACX,MAAM,IAAI,CAAC,SAAS,CAAC;AACrB,QAAQ,EAAE,EAAE,KAAK;AACjB;AACA,QAAQ,QAAQ;AAChB,QAAQ,OAAO;AACf,QAAQ,QAAQ;AAChB,QAAQ,MAAM;AACd,QAAQ,SAAS;AACjB,QAAQ,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC3D,OAAO;AACP,KAAK;AACL,IAAI,MAAM,KAAK,CAAC,OAAO;AACvB,MAAM,gCAAgC;AACtC,MAAM,IAAI,CAAC,SAAS,CAAC;AACrB,QAAQ,KAAK;AACb,QAAQ,MAAM,EAAE,SAAS;AACzB,QAAQ,QAAQ;AAChB,QAAQ,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC3D,OAAO;AACP,KAAK;AACL,IAAI,MAAM,KAAK,CAAC,OAAO;AACvB,MAAM,wBAAwB;AAC9B,MAAM,IAAI,CAAC,SAAS,CAAC;AACrB,QAAQ,KAAK;AACb,QAAQ,MAAM,EAAE,SAAS;AACzB,QAAQ,QAAQ;AAChB,QAAQ,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC3D,OAAO;AACP,KAAK;AACL,IAAI,OAAO,CAAC,GAAG;AACf,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,oCAAoC,EAAE,KAAK,CAAC,uBAAuB;AAC5F,KAAK;AACL,GAAG,CAAC,OAAO,UAAU,EAAE;AACvB,IAAI,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,UAAU,CAAC;AAC7C;AACA;AACK,MAAC,IAAI,GAAG,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AACpD,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC5B,IAAI,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnE,IAAI,MAAM,QAAQ,GAAG,MAAM,CAAC,EAAE;AAC9B,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtE;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,QAAQ,CAAC;AACpD,IAAI,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;AAClD,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE;AAC7B,MAAM,OAAO,EAAE;AACf,QAAQ,QAAQ,EAAE;AAClB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,kBAAkB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE;AACA,IAAI,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,KAAK,IAAI,CAAC,EAAE,EAAE;AAC5C,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,+BAA+B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9E;AACA,IAAI,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;AAC/B,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE;AAC7B,MAAM,IAAI,EAAE;AACZ,QAAQ,QAAQ,EAAE,KAAK;AACvB,QAAQ,QAAQ,EAAE;AAClB;AACA,KAAK,CAAC;AACN,IAAI,MAAM,SAAS,GAAG,MAAM,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;AACrE,IAAI,IAAI,KAAK;AACb,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,KAAK,GAAG,MAAM,mBAAmB,CAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC;AACrE,KAAK,MAAM;AACX,MAAM,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE;AAClC,MAAM,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;AAC/D,MAAM,KAAK,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAC1C;AACA,IAAI,MAAM,eAAe,CAAC,QAAQ,EAAE,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC;AACjF,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,OAAO,EAAE,uCAAuC;AACtD,MAAM,QAAQ,EAAE,MAAM,CAAC;AACvB,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC;AACjE,IAAI,OAAO,IAAI;AACf,MAAM,EAAE,KAAK,EAAE,uCAAuC,EAAE,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;AAChF,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;;;;"}