{"version": 3, "file": "_server.ts-D03-c8fC.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/checkout/_server.ts.js"], "sourcesContent": ["import Stripe from \"stripe\";\nimport { j as json } from \"../../../../chunks/index.js\";\nimport { g as getStripePriceId } from \"../../../../chunks/stripe2.js\";\nimport { p as prisma } from \"../../../../chunks/prisma.js\";\nconst stripe = new Stripe(process.env.STRIPE_SECRET_KEY || \"sk_test_placeholder\", {\n  apiVersion: \"2025-04-30.basil\"\n});\nconst POST = async ({ request, url }) => {\n  const body = await request.json();\n  const { planId, billingCycle, seatCount = 1, userEmail } = body;\n  const plan = await prisma.plan.findUnique({\n    where: { id: planId }\n  });\n  if (!plan) return json({ error: \"Invalid plan\" }, { status: 400 });\n  const priceId = await getStripePriceId(planId, billingCycle);\n  if (!priceId) return json({ error: \"No Stripe price ID for this plan\" }, { status: 500 });\n  try {\n    const session = await stripe.checkout.sessions.create({\n      mode: \"subscription\",\n      payment_method_types: [\"card\"],\n      customer_email: userEmail,\n      line_items: [\n        {\n          price: priceId,\n          quantity: plan.section === \"teams\" ? seatCount : 1\n        }\n      ],\n      success_url: `${url.origin}/dashboard?checkout=success`,\n      cancel_url: `${url.origin}/pricing?checkout=cancelled`\n    });\n    return json({ url: session.url });\n  } catch (err) {\n    console.error(\"Stripe error:\", err);\n    return json({ error: \"Could not create checkout session\" }, { status: 500 });\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAIA,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,qBAAqB,EAAE;AAClF,EAAE,UAAU,EAAE;AACd,CAAC,CAAC;AACG,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK;AACzC,EAAE,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACnC,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,SAAS,GAAG,CAAC,EAAE,SAAS,EAAE,GAAG,IAAI;AACjE,EAAE,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAC5C,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM;AACvB,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpE,EAAE,MAAM,OAAO,GAAG,MAAM,gBAAgB,CAAC,MAAM,EAAE,YAAY,CAAC;AAC9D,EAAE,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,kCAAkC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3F,EAAE,IAAI;AACN,IAAI,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC;AAC1D,MAAM,IAAI,EAAE,cAAc;AAC1B,MAAM,oBAAoB,EAAE,CAAC,MAAM,CAAC;AACpC,MAAM,cAAc,EAAE,SAAS;AAC/B,MAAM,UAAU,EAAE;AAClB,QAAQ;AACR,UAAU,KAAK,EAAE,OAAO;AACxB,UAAU,QAAQ,EAAE,IAAI,CAAC,OAAO,KAAK,OAAO,GAAG,SAAS,GAAG;AAC3D;AACA,OAAO;AACP,MAAM,WAAW,EAAE,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,2BAA2B,CAAC;AAC7D,MAAM,UAAU,EAAE,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,2BAA2B;AAC3D,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,EAAE,GAAG,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC;AACrC,GAAG,CAAC,OAAO,GAAG,EAAE;AAChB,IAAI,OAAO,CAAC,KAAK,CAAC,eAAe,EAAE,GAAG,CAAC;AACvC,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mCAAmC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAChF;AACA;;;;"}