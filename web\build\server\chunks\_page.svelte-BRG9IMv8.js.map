{"version": 3, "file": "_page.svelte-BRG9IMv8.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/settings/admin/_page.svelte.js"], "sourcesContent": ["import \"clsx\";\nimport { O as copy_payload, P as assign_payload, y as pop, w as push } from \"../../../../../chunks/index3.js\";\nimport \"../../../../../chunks/button.js\";\nimport \"../../../../../chunks/Toaster.svelte_svelte_type_style_lang.js\";\nimport \"../../../../../chunks/client.js\";\nimport { S as SEO } from \"../../../../../chunks/SEO.js\";\nfunction _page($$payload, $$props) {\n  push();\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    SEO($$payload2, { title: \"Admin Settings - Hirli\" });\n    $$payload2.out += `<!----> `;\n    {\n      $$payload2.out += \"<!--[-->\";\n      $$payload2.out += `<div class=\"flex h-64 items-center justify-center\"><div class=\"border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent\"></div></div>`;\n    }\n    $$payload2.out += `<!--]-->`;\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;AAMA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,GAAG,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,wBAAwB,EAAE,CAAC;AACxD,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChC,IAAI;AACJ,MAAM,UAAU,CAAC,GAAG,IAAI,UAAU;AAClC,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,2JAA2J,CAAC;AACrL;AACA,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChC;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,GAAG,EAAE;AACP;;;;"}