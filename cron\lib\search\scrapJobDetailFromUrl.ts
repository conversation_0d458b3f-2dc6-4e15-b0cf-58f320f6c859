// cron/lib/scraper/scrapeJobDetailFromUrl.ts

import { chromium } from "playwright";
import { logger } from "../../utils/logger";
import fs from "fs";
import {
  launchCloudflareBypassBrowser,
  navigateWithCloudflareBypass,
  simulateHumanBehavior,
} from "../../utils/cloudflareBypass";
import {
  extractRequirements,
  extractSkills,
  extractYearsOfExperience,
  getMaxYearsOfExperience,
  ExperienceRequirement,
} from "./extractJobRequirements";
import { extractSalaryInfo, SalaryInfo } from "./extractSalaryInfo";
import {
  extractSecurityClearance,
  extractBenefits,
  extractTravelRequirements,
} from "./extractAdditionalDetails";

export type JobDetails = {
  description: string;
  employmentType?: string;
  experienceLevel?: string;
  remoteType?: string;
  postedDate?: Date;
  applyLink?: string; // Direct application link
  salary?: string; // Salary information if available
  salaryMin?: number; // Minimum salary
  salaryMax?: number; // Maximum salary
  salaryCurrency?: string; // Salary currency
  location?: string; // Specific location information
  jobId?: string; // Job ID extracted from the page
  requirements?: string[]; // Key requirements extracted from job description
  skills?: string[]; // Skills mentioned in the job description
  yearsOfExperience?: number; // Total years of experience required (maximum)
  experienceRequirements?: string; // JSON string of experience requirements with associated skills
  securityClearance?: string; // Required security clearance level
  benefits?: string[]; // Job benefits and perks
  travelRequired?: boolean; // Travel requirements
  jobExpired?: boolean; // Flag indicating if the job is expired or no longer available
  loginWallDetected?: boolean; // Flag indicating if a login wall was detected
};

export async function scrapeJobDetailFromUrl(
  url: string,
  existingPage?: any,
  limitFileStorage: boolean = false
): Promise<JobDetails> {
  // Normalize URL to fix double slashes and other issues
  const normalizedUrl = url.replace(/([^:]\/)\/+/g, "$1");

  logger.info(`🚀 Starting scrapeJobDetailFromUrl for URL: ${normalizedUrl}`);
  logger.info(`🔍 URL Analysis: ${normalizedUrl}`);

  // Parse the URL to get domain information
  try {
    const urlObj = new URL(normalizedUrl);
    logger.info(`🌐 Domain: ${urlObj.hostname}`);
    logger.info(`🛣️ Path: ${urlObj.pathname}`);
    logger.info(`🔎 Search params: ${urlObj.search}`);
  } catch (e) {
    logger.info(`⚠️ Could not parse URL: ${e.message}`);
  }

  // Variables to track if we created our own browser
  let browser;
  let context;
  let page;
  let createdOwnBrowser = false;

  // Use the existing page if provided, otherwise create a new browser
  if (existingPage) {
    logger.info(`🖥️ Using existing page`);
    page = existingPage;
  } else {
    logger.info(
      `🖥️ No existing page provided, creating new browser with Cloudflare bypass`
    );

    // Launch browser with enhanced Cloudflare bypass capabilities
    const browserResult = await launchCloudflareBypassBrowser({
      headless: false, // Always use non-headless for better Cloudflare bypass
      slowMo: 150,
    });

    browser = browserResult.browser;
    context = browserResult.context;
    page = await context.newPage();
    createdOwnBrowser = true;

    logger.info(`🖥️ Browser launched with Cloudflare bypass capabilities`);
  }

  try {
    logger.info(`🌐 Visiting job URL: ${normalizedUrl}`);

    // Use enhanced Cloudflare bypass navigation if we created our own browser
    let navigationSuccess = false;
    if (createdOwnBrowser && context) {
      navigationSuccess = await navigateWithCloudflareBypass(
        page,
        normalizedUrl,
        context
      );
    } else {
      // Fallback to regular navigation for existing pages
      logger.info(`⏱️ Starting page load with 30s timeout...`);
      const response = await page.goto(normalizedUrl, {
        waitUntil: "domcontentloaded",
        timeout: 30000,
      });

      // Log response information
      logger.info(
        `📡 Response status: ${response ? response.status() : "Unknown"}`
      );
      logger.info(`📡 Response URL: ${response ? response.url() : "Unknown"}`);
      navigationSuccess = true;
    }

    if (!navigationSuccess) {
      logger.error(
        `❌ Failed to navigate to ${normalizedUrl} due to Cloudflare challenges`
      );
      return {
        description: "Failed to bypass Cloudflare verification",
        jobExpired: true,
      };
    }

    // Simulate human behavior to avoid detection
    await simulateHumanBehavior(page);

    // Wait for the page to stabilize
    logger.info(`⏳ Waiting for page to stabilize...`);
    await page.waitForTimeout(2000);

    // Handle site-specific issues

    // Handle LinkedIn popups and login prompts
    if (normalizedUrl.includes("linkedin.com")) {
      logger.info(`🔓 LinkedIn detected, closing any popups...`);

      try {
        // Try to click any dismiss buttons without checking visibility first
        // This avoids the strict mode violation when multiple elements match
        await page.evaluate(() => {
          // Click all dismiss buttons
          document
            .querySelectorAll(
              'button[aria-label="Dismiss"], .modal__dismiss, .artdeco-modal__dismiss'
            )
            .forEach((button) => {
              (button as HTMLElement).click();
            });

          // Click all X buttons
          document
            .querySelectorAll(
              ".modal__dismiss--with-icon, .sign-in-modal__dismiss"
            )
            .forEach((button) => {
              (button as HTMLElement).click();
            });
        });

        // Also accept cookies
        await page.evaluate(() => {
          // Accept cookies if present
          document
            .querySelectorAll(
              'button[action-type="ACCEPT_COOKIES"], .cookie-consent__action'
            )
            .forEach((button) => {
              (button as HTMLElement).click();
            });

          console.log("Attempted to close all LinkedIn popups via JavaScript");
        });

        // Wait a moment for popups to close
        await page.waitForTimeout(1000);

        logger.info(`🚫 Attempted to close all LinkedIn popups`);
      } catch (e) {
        logger.warn(`⚠️ Error handling LinkedIn popups: ${e.message}`);
      }
    }

    // Note: Cloudflare verification is now handled by the enhanced bypass system

    // Skip HTML content extraction to save memory (configurable)
    const disableHtmlPreviews = process.env.DISABLE_HTML_PREVIEWS === "true";
    if (!disableHtmlPreviews) {
      const html = await page.content();
      const htmlPreview = html.substring(0, 500) + "...";
      logger.info(`📄 HTML Preview: ${htmlPreview}`);
    } else {
      logger.info(`🚫 HTML Preview disabled to save memory`);
    }

    // File storage and screenshots disabled to save disk space and memory
    logger.info(
      `🚫 File storage and screenshots disabled to save disk space and memory`
    );

    // Check if the job is no longer available by looking for common expired job messages
    const pageText = await page.evaluate(() => document.body.innerText);
    const expiredPhrases = [
      // Common expired job messages
      "The job you may have viewed online is no longer listed",
      "This job is no longer available",
      "This job posting has expired",
      "This position has been filled",
      "This job is no longer accepting applications",
      "404 Not Found",
      "Page not found",
      "Job not found",
      "Search our Job Database for similar openings",
      "Job has been removed",
      "Job has been deleted",
      "Job has been closed",
      "Position has been closed",
      "Position filled",
      "No longer accepting applications",
      "The page you're looking for can't be found",
      "Sorry, this job posting is no longer active",
      "The job posting you are looking for has been removed",
      "This job has been archived",
      "This job has been removed by the employer",

      // LinkedIn specific messages
      "This job is no longer accepting applications",
      "This job is no longer active",
      "This job is no longer available",
      "Sign in to see who the job poster selected",

      // Indeed specific messages
      "This job has expired",
      "This job is no longer available on Indeed",
      "The job posting you are looking for has expired",

      // Glassdoor specific messages
      "This job has been taken down",
      "This job is no longer active on Glassdoor",

      // ZipRecruiter specific messages
      "This job has been filled",
      "This job is no longer available on ZipRecruiter",

      // Monster specific messages
      "The job you're looking for is no longer active",
      "This job is no longer available on Monster",

      // General error messages that might indicate an expired job
      "Error",
      "Access Denied",
      "Forbidden",
      "Unauthorized",
      "Login required",
      "Sign in to continue",
    ];

    for (const phrase of expiredPhrases) {
      if (pageText.includes(phrase)) {
        logger.warn(`⚠️ Job expired message found: "${phrase}"`);
        return { description: "Job no longer available", jobExpired: true };
      }
    }

    // Check for empty or very short page content
    if (pageText.trim().length < 200) {
      logger.warn(
        `⚠️ Page content is too short (${
          pageText.trim().length
        } chars), might be an expired job`
      );
      return { description: "Job page content too short", jobExpired: true };
    }

    // Check for error page titles
    const pageTitle = await page.title();
    const errorTitles = [
      "404",
      "not found",
      "error",
      "page not found",
      "job not found",
      "no longer available",
      "access denied",
      "forbidden",
      "unauthorized",
    ];

    for (const errorTitle of errorTitles) {
      if (pageTitle.toLowerCase().includes(errorTitle.toLowerCase())) {
        logger.warn(`⚠️ Error page detected: Title contains "${errorTitle}"`);
        return { description: "Error page detected", jobExpired: true };
      }
    }

    // Note: All Cloudflare verification is now handled by the enhanced bypass system during navigation

    // Check for login walls
    const loginWallPhrases = [
      "sign in to view",
      "sign in to continue",
      "log in to view",
      "log in to continue",
      "create an account",
      "join now",
      "register to view",
      "please login",
      "please sign in",
      "sign in to apply",
      "login to apply",
      "sign in to see",
      "login required",
      "account required",
    ];

    // Check for login walls but don't stop processing
    let loginWallDetected = false;

    for (const phrase of loginWallPhrases) {
      if (pageText.toLowerCase().includes(phrase.toLowerCase())) {
        logger.warn(`⚠️ Login wall detected: "${phrase}" found on page`);
        loginWallDetected = true;
        break;
      }
    }

    // If login wall is detected, we'll still try to extract what we can
    if (loginWallDetected) {
      logger.info(
        `🔓 Login wall detected, but continuing to extract available information`
      );
      // We'll mark this in the final result, but continue processing
    }

    logger.info(`📝 Extracting job description and details...`);
    logger.info(`🔍 Analyzing page structure...`);

    // First, analyze the page structure
    const pageStructure = await page.evaluate(() => {
      const structure = {
        title: document.title,
        headings: {
          h1: Array.from(document.querySelectorAll("h1"))
            .map((h) => h.textContent?.trim())
            .filter(Boolean),
          h2: Array.from(document.querySelectorAll("h2"))
            .map((h) => h.textContent?.trim())
            .filter(Boolean),
        },
        metaTags: Array.from(
          document.querySelectorAll("meta[name], meta[property]")
        ).map((meta) => {
          const el = meta as HTMLMetaElement;
          return {
            name: el.getAttribute("name") || el.getAttribute("property"),
            content: el.getAttribute("content"),
          };
        }),
        mainContentAreas: {
          articles: document.querySelectorAll("article").length,
          sections: document.querySelectorAll("section").length,
          divs: document.querySelectorAll("div").length,
          mains: document.querySelectorAll("main").length,
        },
      };
      return structure;
    });

    logger.info(`📊 Page Structure Analysis:`);
    logger.info(`  - Page Title: ${pageStructure.title}`);
    logger.info(
      `  - H1 Headings: ${JSON.stringify(pageStructure.headings.h1)}`
    );
    logger.info(
      `  - H2 Headings: ${JSON.stringify(
        pageStructure.headings.h2.slice(0, 3)
      )}${
        pageStructure.headings.h2.length > 3
          ? ` ...and ${pageStructure.headings.h2.length - 3} more`
          : ""
      }`
    );
    logger.info(
      `  - Content Areas: Articles: ${pageStructure.mainContentAreas.articles}, Sections: ${pageStructure.mainContentAreas.sections}, Mains: ${pageStructure.mainContentAreas.mains}`
    );

    // Now extract the job description and apply links
    logger.info(`🔍 Extracting job description candidates and apply links...`);
    // Use a simpler approach to evaluate the page
    const extractionResults = await page.evaluate(() => {
      // Special handling for LinkedIn
      if (window.location.href.includes("linkedin.com")) {
        console.log("LinkedIn job page detected, using specialized selectors");

        // LinkedIn specific selectors
        const linkedInDescriptionSelectors = [
          ".description__text",
          ".show-more-less-html__markup",
          ".jobs-description__content",
          ".jobs-box__html-content",
          "[data-test-job-description]",
          "#job-details",
          ".jobs-description",
          ".jobs-unified-top-card__job-insight",
          ".jobs-unified-top-card__job-details",
        ];

        // Try to find the job title and company name even if behind login wall
        const jobTitle =
          document
            .querySelector("h1, .jobs-unified-top-card__job-title")
            ?.textContent?.trim() || "";
        const companyName =
          document
            .querySelector(
              ".jobs-unified-top-card__company-name, .jobs-unified-top-card__subtitle-primary"
            )
            ?.textContent?.trim() || "";
        const jobLocation =
          document
            .querySelector(
              ".jobs-unified-top-card__bullet, .jobs-unified-top-card__subtitle-secondary"
            )
            ?.textContent?.trim() || "";

        console.log(
          `LinkedIn job info: ${jobTitle} at ${companyName} in ${jobLocation}`
        );

        const candidates = [];
        for (const selector of linkedInDescriptionSelectors) {
          const element = document.querySelector(selector);
          if (element) {
            const text = element.textContent?.trim();
            if (text && text.length > 100) {
              console.log(
                `Found LinkedIn description with selector ${selector}, length: ${text.length}`
              );
              candidates.push(text);
            }
          }
        }

        // If we found any description candidates, return them
        if (candidates.length > 0) {
          // Try to find apply links
          // Find apply link
          let applyLink = "";
          const applyLinkElement = document.querySelector(
            "a.apply-button, a.jobs-apply-button, a[data-control-name='view_job_button']"
          );

          if (
            applyLinkElement &&
            applyLinkElement instanceof HTMLAnchorElement
          ) {
            applyLink = applyLinkElement.href || "";
          }

          // Try to find employment type
          const employmentType =
            document
              .querySelector(
                ".jobs-unified-top-card__job-insight span:first-child, .jobs-unified-top-card__workplace-type"
              )
              ?.textContent?.trim() || "";

          console.log(`LinkedIn apply link: ${applyLink}`);
          console.log(`LinkedIn employment type: ${employmentType}`);

          return {
            descriptionCandidates: candidates,
            applyLink: applyLink,
            jobTitle: jobTitle,
            companyName: companyName,
            jobLocation: jobLocation,
            employmentType: employmentType,
          };
        }

        // If we couldn't find a description but have basic job info, return that
        if (jobTitle || companyName) {
          console.log(
            "LinkedIn: No description found but returning basic job info"
          );
          return {
            descriptionCandidates: [
              `${jobTitle} at ${companyName} in ${jobLocation}`,
            ],
            jobTitle: jobTitle,
            companyName: companyName,
            jobLocation: jobLocation,
          };
        }
      }
      // Initialize candidates array first
      const candidates: string[] = [];

      // Special handling for Adzuna
      if (window.location.href.includes("adzuna.com")) {
        console.log("Adzuna job page detected, using specialized selectors");

        const adzunaDescriptionSelectors = [
          ".job-description",
          ".advert-details",
          ".job-details",
          "[data-testid='job-description']",
          ".description",
          "#job-description",
          ".job-content",
          ".advert-body",
        ];

        for (const selector of adzunaDescriptionSelectors) {
          const element = document.querySelector(selector);
          if (element) {
            const text = element.textContent?.trim();
            if (
              text &&
              text.length > 200 &&
              !text.toLowerCase().includes("popular jobs")
            ) {
              console.log(
                `Found Adzuna description with selector ${selector}, length: ${text.length}`
              );
              candidates.push(text);
            }
          }
        }

        // If we found descriptions, return them
        if (candidates.length > 0) {
          return {
            descriptionCandidates: candidates.sort(
              (a, b) => b.length - a.length
            ),
            applyLink: window.location.href, // Use current URL as apply link for Adzuna
            jobId: window.location.pathname.match(/\/(\d+)/)?.[1] || "",
          };
        }
      }

      // Special handling for BeBee
      if (window.location.href.includes("bebee.com")) {
        console.log("BeBee job page detected, using specialized selectors");

        const bebeeDescriptionSelectors = [
          ".job-description",
          ".job-details",
          ".description",
          "[class*='job-content']",
          ".content",
          "#description",
        ];

        for (const selector of bebeeDescriptionSelectors) {
          const element = document.querySelector(selector);
          if (element) {
            const text = element.textContent?.trim();
            if (
              text &&
              text.length > 200 &&
              !text.toLowerCase().includes("current jobs related") &&
              !text.toLowerCase().includes("you might also like")
            ) {
              console.log(
                `Found BeBee description with selector ${selector}, length: ${text.length}`
              );
              candidates.push(text);
            }
          }
        }

        // Look for the actual job description in the page content
        const allDivs = document.querySelectorAll("div");
        for (const div of allDivs) {
          const text = div.textContent?.trim() || "";
          if (
            text.length > 300 &&
            text.length < 3000 &&
            text.toLowerCase().includes("job title:") &&
            !text.toLowerCase().includes("current jobs related") &&
            !text.toLowerCase().includes("you might also like")
          ) {
            console.log(
              `Found BeBee job description in div, length: ${text.length}`
            );
            candidates.push(text);
          }
        }

        if (candidates.length > 0) {
          return {
            descriptionCandidates: candidates.sort(
              (a, b) => b.length - a.length
            ),
            applyLink: "", // BeBee might not have direct apply links
            jobId: window.location.pathname.match(/\/([a-f0-9]+)/)?.[1] || "",
          };
        }
      }

      console.log("Starting to extract description candidates and apply links");

      // Enhanced selectors based on HTML analysis
      const descriptionSelectors = [
        // Common job description containers
        "section, article, main",
        ".job-description",
        "#job-description",
        "[class*='description']",
        "[id*='description']",
        "[class*='job-details']",
        "[id*='job-details']",
        "[class*='requirements']",
        "[id*='requirements']",
        "[class*='responsibilities']",
        "[id*='responsibilities']",
        // Look for elements with specific text content
        "div:contains('Job Description')",
        "div:contains('Responsibilities')",
        "div:contains('Requirements')",
        "div:contains('Qualifications')",
        "div:contains('Job Details')",
        "div:contains('Job Summary')",
        "div:contains('About the Role')",
        "div:contains('About the Position')",
        "div:contains('What You'll Do')",
        "div:contains('What You Will Do')",
      ];

      // Try these specific selectors first
      for (const selector of descriptionSelectors) {
        try {
          const elements = document.querySelectorAll(selector);
          console.log(
            `Found ${elements.length} elements with selector ${selector}`
          );

          for (const element of elements) {
            const text = element.textContent?.trim();
            if (text && text.length > 200) {
              // Filter out generic navigation and page content
              const lowerText = text.toLowerCase();
              const excludePatterns = [
                "popular jobs",
                "current jobs related",
                "you might also like",
                "similar jobs",
                "recommended jobs",
                "other jobs",
                "browse jobs",
                "search jobs",
                "job alerts",
                "career advice",
                "company reviews",
                "salary information",
                "interview questions",
                "conversations @",
                "kick off the conversation",
                "see what other",
                "glassdoor estimate",
                "base pay range",
                "if an employer includes",
                "to learn more about",
              ];

              const isGenericContent = excludePatterns.some((pattern) =>
                lowerText.includes(pattern)
              );

              if (!isGenericContent) {
                console.log(
                  `Found description candidate with selector ${selector}, length: ${text.length}`
                );
                candidates.push(text);
              } else {
                console.log(
                  `Filtered out generic content with selector ${selector}, length: ${text.length}`
                );
              }
            }
          }
        } catch (e) {
          console.error(`Error with selector ${selector}: ${e.message}`);
        }
      }

      // If we didn't find anything with specific selectors, fall back to checking all divs
      if (candidates.length === 0) {
        console.log(
          "No candidates found with specific selectors, checking all divs"
        );
        const tags = document.querySelectorAll("div");
        console.log(`Found ${tags.length} potential div elements to check`);

        // Log some details about the largest elements
        const elementDetails: {
          index: number;
          tagName: string;
          className: string;
          id: string;
          textLength: number;
          textPreview: string;
        }[] = [];
        tags.forEach((el, index) => {
          try {
            const text = (el as HTMLElement).innerText?.trim();
            if (text && text.length > 200) {
              elementDetails.push({
                index,
                tagName: el.tagName,
                className: el.className,
                id: el.id,
                textLength: text.length,
                textPreview: text.substring(0, 100) + "...",
              });
            }

            if (text && text.length > 400 && text.length < 10000) {
              // Filter out generic content in fallback div checking too
              const lowerText = text.toLowerCase();
              const excludePatterns = [
                "popular jobs",
                "current jobs related",
                "you might also like",
                "similar jobs",
                "recommended jobs",
                "other jobs",
                "browse jobs",
                "search jobs",
                "job alerts",
                "career advice",
                "company reviews",
                "salary information",
                "interview questions",
                "conversations @",
                "kick off the conversation",
                "see what other",
                "glassdoor estimate",
                "base pay range",
                "if an employer includes",
                "to learn more about",
              ];

              const isGenericContent = excludePatterns.some((pattern) =>
                lowerText.includes(pattern)
              );

              if (!isGenericContent) {
                console.log(
                  `Found candidate #${candidates.length + 1} with length ${
                    text.length
                  }`
                );
                candidates.push(text);
              } else {
                console.log(
                  `Filtered out generic fallback content with length ${text.length}`
                );
              }
            }
          } catch (err) {
            console.error(`Error processing element ${index}:`, err);
          }
        });

        // Sort and log the top 5 largest elements
        elementDetails.sort((a, b) => b.textLength - a.textLength);
        console.log("Top 5 largest text elements:");
        elementDetails.slice(0, 5).forEach((el, i) => {
          console.log(
            `${i + 1}. ${el.tagName}${
              el.className ? "." + el.className.replace(/\s+/g, ".") : ""
            }${el.id ? "#" + el.id : ""} - ${el.textLength} chars`
          );
          console.log(`   Preview: ${el.textPreview}`);
        });
      }

      // Look for apply links, job IDs, and posting dates
      let applyLink = "";
      let jobId = "";
      let postedDate = "";

      // Method 1: Extract job ID from URL
      try {
        const url = window.location.href;
        // Common patterns for job IDs in URLs
        const jobIdPatterns = [
          /[\/?]job[\/-]?id[=\/](\w+[-\w]*)/i, // job-id=ABC123 or jobid=ABC123
          /[\/?]jobs?\/([\w-]+)/i, // jobs/ABC123 or job/ABC123
          /[\/?]position[=\/](\w+[-\w]*)/i, // position=ABC123
          /[\/?]posting[=\/](\w+[-\w]*)/i, // posting=ABC123
          /[\/?]req(uisition)?[=\/](\w+[-\w]*)/i, // req=ABC123 or requisition=ABC123
          /[\/?]id[=\/](\w+[-\w]*)/i, // id=ABC123
          /[\/?](\d{5,})/, // Any 5+ digit number in URL
        ];

        for (const pattern of jobIdPatterns) {
          const match = url.match(pattern);
          if (match && match[1]) {
            jobId = match[1];
            console.log(`Extracted job ID from URL: ${jobId}`);
            break;
          }
        }
      } catch (e) {
        console.error("Error extracting job ID from URL:", e);
      }

      // Method 2: Look for job ID in page content
      if (!jobId) {
        const jobIdElements = document.querySelectorAll(
          '[data-job-id], [data-jobid], [id*="job-id"], [class*="job-id"], ' +
            '[id*="jobid"], [class*="jobid"], .job-code, .job-req-id, .requisition-id'
        );

        if (jobIdElements.length > 0) {
          const idElement = jobIdElements[0];
          const idText =
            idElement.getAttribute("data-job-id") ||
            idElement.getAttribute("data-jobid") ||
            idElement.textContent?.trim();

          if (idText) {
            // Clean up the job ID - remove common prefixes like "Job ID:" or "Req:"
            jobId = idText
              .replace(
                /^(job|req|requisition|position|id|code|#)\s*[:#]?\s*/i,
                ""
              )
              .trim();
            console.log(`Extracted job ID from page element: ${jobId}`);
          }
        }
      }

      // Method 3: Look for buttons/links with "apply" text
      const applyButtons = Array.from(
        document.querySelectorAll("a, button")
      ).filter((el) => {
        const text = el.textContent?.toLowerCase() || "";
        return (
          text.includes("apply") ||
          text.includes("apply now") ||
          text.includes("apply for this job") ||
          text.includes("apply for job") ||
          text.includes("apply for position") ||
          text.includes("apply for this position") ||
          text.includes("apply online") ||
          text.includes("apply here") ||
          text.includes("submit") ||
          text.includes("submit application") ||
          text.includes("submit your application") ||
          text.includes("submit your resume") ||
          text.includes("application") ||
          text.includes("job application")
        );
      });

      // Special handling for Snagajob - construct apply link from job URL
      if (window.location.href.includes("snagajob.com")) {
        const currentUrl = window.location.href;
        const jobIdMatch = currentUrl.match(/\/jobs\/(\d+)/);
        if (jobIdMatch && jobIdMatch[1]) {
          applyLink = `https://www.snagajob.com/jobs/${jobIdMatch[1]}`;
          console.log(`Constructed Snagajob apply link: ${applyLink}`);
        }
      }

      // Special handling for Adzuna - use current URL as apply link
      if (window.location.href.includes("adzuna.com")) {
        applyLink = window.location.href;
        console.log(`Using Adzuna job URL as apply link: ${applyLink}`);
      }

      // Method 1: Look for buttons/links with "apply" text
      if (!applyLink && applyButtons.length > 0) {
        const applyButton = applyButtons[0] as HTMLElement;
        if (applyButton.tagName === "A") {
          applyLink = (applyButton as HTMLAnchorElement).href;
          console.log(`Found apply link from button text: ${applyLink}`);
        }
      }

      // Method 2: Look for links with apply-related classes or IDs
      if (!applyLink) {
        const applyLinkElements = document.querySelectorAll(
          'a[class*="apply"], a[class*="job-apply"], a[id*="apply"], a[data-automation*="apply"], ' +
            ".apply-button, .application-link, " +
            'a[href*="ashbyhq"], a[href*="jobs.ashbyhq"], ' +
            'a[href*="submit"], a[href*="job-application"]'
        );

        // Filter out generic career pages and prioritize actual job application links
        const filteredApplyLinks = Array.from(applyLinkElements).filter(
          (el) => {
            const href = (el as HTMLAnchorElement).href.toLowerCase();
            const text = el.textContent?.toLowerCase() || "";

            // Exclude generic career pages and navigation
            if (
              href.includes("/about/careers") ||
              href.includes("/careers") ||
              href.includes("/company/") ||
              href.includes("/jobs/careers") ||
              href.includes("adzuna.co.uk/jobs/careers") ||
              href.includes("adzuna.com/jobs/careers") ||
              text.includes("careers") ||
              text.includes("browse jobs") ||
              text.includes("search jobs") ||
              text.includes("job alerts")
            ) {
              return false;
            }

            // Prioritize actual application links
            return (
              href.includes("apply") ||
              href.includes("application") ||
              text.includes("apply") ||
              text.includes("application")
            );
          }
        );

        if (filteredApplyLinks.length > 0) {
          applyLink = (filteredApplyLinks[0] as HTMLAnchorElement).href;
          console.log(`Found apply link from element attributes: ${applyLink}`);
        }
      }

      // Method 3: Look for structured data
      if (!applyLink) {
        const structuredData = document.querySelector(
          'script[type="application/ld+json"]'
        );
        if (structuredData) {
          try {
            const data = JSON.parse(structuredData.textContent || "{}");
            if (data.applicationUrl) {
              applyLink = data.applicationUrl;
              console.log(
                `Found apply link from structured data: ${applyLink}`
              );
            }
          } catch (e) {
            console.error("Error parsing structured data:", e);
          }
        }
      }

      // Method 4: Look for any link with apply-related text in the URL
      if (!applyLink) {
        const allLinks = document.querySelectorAll("a[href]");
        for (const link of allLinks) {
          const href = (link as HTMLAnchorElement).href.toLowerCase();
          const text = link.textContent?.toLowerCase() || "";

          // Skip generic career pages and navigation
          if (
            href.includes("/about/careers") ||
            href.includes("/careers") ||
            href.includes("/company/") ||
            href.includes("/jobs/careers") ||
            href.includes("adzuna.co.uk/jobs/careers") ||
            href.includes("adzuna.com/jobs/careers") ||
            text.includes("careers") ||
            text.includes("browse jobs") ||
            text.includes("search jobs") ||
            text.includes("job alerts")
          ) {
            continue;
          }

          if (
            href.includes("apply") ||
            href.includes("application") ||
            href.includes("ashbyhq") ||
            href.includes("job-application") ||
            href.includes("submit")
          ) {
            applyLink = (link as HTMLAnchorElement).href;
            console.log(`Found apply link from URL text: ${applyLink}`);
            break;
          }
        }
      }

      // Method 4: Look for meta tags
      if (!applyLink) {
        const applyMeta = document.querySelector(
          'meta[property="og:apply_url"], meta[name="apply_url"]'
        );
        if (applyMeta) {
          applyLink = applyMeta.getAttribute("content") || "";
          console.log(`Found apply link from meta tag: ${applyLink}`);
        }
      }

      // Method 5: Look for any prominent CTA button
      if (!applyLink) {
        const ctaButtons = document.querySelectorAll(
          ".cta, .btn-primary, .primary-button, .main-button"
        );
        if (ctaButtons.length > 0) {
          const ctaButton = ctaButtons[0] as HTMLElement;
          if (ctaButton.tagName === "A") {
            applyLink = (ctaButton as HTMLAnchorElement).href;
            console.log(`Found apply link from CTA button: ${applyLink}`);
          }
        }
      }

      console.log(`Found ${candidates.length} description candidates`);
      if (applyLink) {
        console.log(`Found apply link: ${applyLink}`);
      } else {
        console.log("No apply link found");
      }

      // Method 1: Look for posting date in common formats
      const dateElements = document.querySelectorAll(
        '[data-posted], [class*="posted-date"], [class*="job-date"], [class*="date-posted"], ' +
          '[class*="posting-date"], [class*="post-date"], time'
      );

      if (dateElements.length > 0) {
        const dateText = dateElements[0].textContent?.trim() || "";
        if (dateText) {
          postedDate = dateText;
          console.log(`Found posted date: ${postedDate}`);
        }
      }

      // Method 2: Look for relative time patterns
      if (!postedDate) {
        const relativeTimePatterns = [
          /posted\s+(\d+)\s+day[s]?\s+ago/i,
          /posted\s+(\d+)\s+hour[s]?\s+ago/i,
          /posted\s+(\d+)\s+week[s]?\s+ago/i,
          /posted\s+(\d+)\s+month[s]?\s+ago/i,
          /(\d+)\s+day[s]?\s+ago/i,
          /(\d+)\s+hour[s]?\s+ago/i,
          /(\d+)\s+week[s]?\s+ago/i,
          /(\d+)\s+month[s]?\s+ago/i,
        ];

        // Search the entire page text
        const pageText = document.body.innerText;
        for (const pattern of relativeTimePatterns) {
          const match = pageText.match(pattern);
          if (match) {
            postedDate = match[0];
            console.log(`Found relative posted date: ${postedDate}`);
            break;
          }
        }
      }

      return {
        descriptionCandidates: candidates.sort((a, b) => b.length - a.length),
        applyLink,
        jobId,
      };
    });

    const descriptionCandidates = extractionResults.descriptionCandidates;
    const applyLink = extractionResults.applyLink;
    const extractedJobId = extractionResults.jobId;

    // Log the apply link if found
    if (applyLink) {
      logger.info(`🔗 Found apply link: ${applyLink}`);
    } else {
      logger.info(`⚠️ No apply link found`);
    }

    // Log the job ID if found
    if (extractedJobId) {
      logger.info(`📝 Found job ID: ${extractedJobId}`);
    } else {
      logger.info(`⚠️ No job ID found`);
    }

    // Deduplicate description candidates
    const uniqueCandidates = [];
    const seenPreviews = new Set();

    for (const candidate of descriptionCandidates) {
      // Create a preview to check for duplicates
      const preview = candidate.substring(0, 100).trim();

      // Skip if we've seen this preview before
      if (seenPreviews.has(preview)) {
        continue;
      }

      // Add to unique candidates and mark preview as seen
      uniqueCandidates.push(candidate);
      seenPreviews.add(preview);
    }

    // Analyze the description candidates
    logger.info(`📊 Description Candidates Analysis:`);
    logger.info(
      `  - Total candidates found: ${uniqueCandidates.length} (after deduplication)`
    );

    if (uniqueCandidates.length > 0) {
      for (let i = 0; i < Math.min(3, uniqueCandidates.length); i++) {
        const candidate = uniqueCandidates[i];
        logger.info(`  - Candidate #${i + 1}: ${candidate.length} characters`);
        logger.info(`    Preview: ${candidate.substring(0, 150)}...`);
      }
    } else {
      logger.info(`⚠️ No description candidates found!`);
    }

    // Use the first unique candidate as the description
    const description =
      uniqueCandidates.length > 0
        ? uniqueCandidates[0]
        : descriptionCandidates[0] || "";
    logger.info(
      `📄 Selected description with ${description.length} characters`
    );
    logger.info(`📄 Description preview: ${description.substring(0, 150)}...`);

    // If we found an apply link, log it again for clarity
    if (applyLink) {
      logger.info(`🔗 Will save apply link: ${applyLink}`);
    }

    // Extract requirements, skills, years of experience, and additional details
    const requirements = extractRequirements(description);
    const skills = extractSkills(description);
    const experienceRequirements = extractYearsOfExperience(description);
    const maxYearsOfExperience = getMaxYearsOfExperience(
      experienceRequirements
    );
    const securityClearance = extractSecurityClearance(description);
    const benefits = extractBenefits(description);
    const travelRequired = extractTravelRequirements(description);
    const salaryInfo = extractSalaryInfo(description);

    // Log the extracted requirements
    logger.info(`📝 Extracted ${requirements.length} requirements:`);
    requirements.forEach((req, i) => {
      logger.info(
        `  ${i + 1}. ${req.substring(0, 100)}${req.length > 100 ? "..." : ""}`
      );
    });

    // Log the extracted skills
    logger.info(`🔧 Extracted ${skills.length} skills:`);
    logger.info(`  ${skills.join(", ")}`);

    // Log the extracted years of experience
    if (experienceRequirements && experienceRequirements.length > 0) {
      logger.info(
        `⏳ Extracted ${experienceRequirements.length} experience requirements`
      );
      logger.info(`⏳ Maximum years of experience: ${maxYearsOfExperience}`);

      // Log detailed experience requirements
      experienceRequirements.forEach((req) => {
        if (req.skill) {
          logger.info(`  - ${req.years} years required for ${req.skill}`);
        } else {
          logger.info(`  - ${req.years} years of general experience`);
        }
      });
    } else {
      logger.info(`⚠️ Could not extract years of experience`);
    }

    // Log security clearance if found
    if (securityClearance) {
      logger.info(`🔑 Security clearance required: ${securityClearance}`);
    }

    // Log benefits if found
    if (benefits && benefits.length > 0) {
      logger.info(`💪 Extracted ${benefits.length} benefits:`);
      logger.info(`  ${benefits.join(", ")}`);
    }

    // Log travel requirements if found
    if (travelRequired) {
      logger.info(`✈️ Travel requirements: ${travelRequired}`);
    }

    // No final screenshot to save disk space
    logger.info(`🚫 Final screenshot disabled to save disk space and memory`);

    // Skip the wait in production mode
    if (process.env.NODE_ENV !== "production") {
      logger.info(`⏳ Pausing for 1 second in development mode...`);
      try {
        await page.waitForTimeout(1000); // Reduced from 5000ms to 1000ms
      } catch (e) {
        logger.warn(`⚠️ Error during timeout: ${(e as Error).message}`);
      }
    }

    // Extract employment details
    const employmentType = extractEmploymentType(description);
    const experienceLevel = extractExperienceLevel(description);
    const remoteType = extractRemoteType(description);

    logger.info(
      `💼 Extracted employment type: ${employmentType || "Not found"}`
    );
    logger.info(
      `🧠 Extracted experience level: ${experienceLevel || "Not found"}`
    );
    logger.info(`🏠 Extracted remote type: ${remoteType || "Not found"}`);
    logger.info(`✅ Extraction complete for URL: ${normalizedUrl}`);

    // Check if the description is too short or empty, which might indicate a problem
    if (!description || description.trim().length < 50) {
      logger.warn(
        `⚠️ Description is too short or empty (${
          description ? description.length : 0
        } chars), might be an expired job`
      );
      return {
        description: "Job description too short or empty",
        jobExpired: true,
      };
    }

    // Check if the description contains mostly HTML tags or JavaScript code
    if (
      description.includes("<html") ||
      description.includes("<!DOCTYPE") ||
      (description.includes("<") &&
        description.includes(">") &&
        description.match(/<[^>]+>/g)?.length > 10)
    ) {
      logger.warn(
        `⚠️ Description contains HTML markup, might be a parsing error`
      );
      return {
        description: "Description contains HTML markup",
        jobExpired: true,
      };
    }

    // Check for common phrases that might indicate a login wall or expired job
    const loginWallPhrasesInDescription = [
      "sign in to view",
      "sign in to continue",
      "log in to view",
      "log in to continue",
      "create an account",
      "join now",
      "register to view",
      "please login",
      "please sign in",
    ];

    for (const phrase of loginWallPhrasesInDescription) {
      if (description.toLowerCase().includes(phrase)) {
        logger.warn(`⚠️ Login wall detected in description: "${phrase}"`);
        return {
          description: "Login wall detected",
          jobExpired: true,
        };
      }
    }

    // Check for common phrases that might indicate an expired job
    const expiredJobPhrases = [
      "page not found",
      "job not found",
      "the job you may have viewed online is no longer listed",
      "this job is no longer available",
      "this job posting has expired",
      "this position has been filled",
      "this job is no longer accepting applications",
      "404 not found",
      "search our job database for similar openings",
      "no job details found",
      "job has been filled",
      "position has been closed",
      "no longer accepting applications",
    ];

    for (const phrase of expiredJobPhrases) {
      if (description.toLowerCase().includes(phrase)) {
        logger.warn(`⚠️ Expired job detected in description: "${phrase}"`);
        return {
          description: "Job posting has expired",
          jobExpired: true,
        };
      }
    }

    // Prepare the result
    const result = {
      description: description.slice(0, 5000),
      employmentType: employmentType,
      experienceLevel: experienceLevel,
      remoteType: remoteType,
      postedDate: new Date(),
      applyLink: applyLink || undefined,
      jobId: extractedJobId || undefined,
      requirements: requirements.length > 0 ? requirements : undefined,
      skills: skills.length > 0 ? skills : undefined,
      yearsOfExperience: maxYearsOfExperience || 0,
      experienceRequirements:
        experienceRequirements.length > 0
          ? JSON.stringify(experienceRequirements)
          : undefined,
      salaryMin: salaryInfo.salaryMin,
      salaryMax: salaryInfo.salaryMax,
      salaryCurrency: salaryInfo.salaryCurrency,
      salary: salaryInfo.salary,
      securityClearance: securityClearance,
      benefits: benefits && benefits.length > 0 ? benefits : undefined,
      travelRequired: travelRequired,
      // Include flags for job status
      loginWallDetected: loginWallDetected || false,
      // If login wall is detected, we'll mark the job as requiring attention but not expired
      jobExpired: false,
    };

    logger.info(
      `🔄 Returning job details with description length: ${result.description.length}`
    );
    return result;
  } catch (err) {
    logger.warn(`⚠️ Failed to scrape job detail: ${url}`, err);
    return { description: "" };
  } finally {
    // Only close the browser if we created it
    if (createdOwnBrowser) {
      try {
        // Close context first if it exists
        if (context) {
          logger.info(`🖥️ Closing browser context`);
          await context
            .close()
            .catch((e) =>
              logger.warn(`⚠️ Error closing context: ${e.message}`)
            );
        }

        // Then close the browser
        if (browser) {
          logger.info(`🖥️ Closing browser we created`);
          await browser
            .close()
            .catch((e) =>
              logger.warn(`⚠️ Error closing browser: ${e.message}`)
            );
        }

        // Set to null to help garbage collection
        context = null;
        browser = null;
        page = null;
      } catch (e) {
        logger.warn(`⚠️ Error during browser cleanup: ${e.message}`);
      }
    }
  }
}

function extractEmploymentType(text: string): string | undefined {
  // Define employment type patterns with priorities
  const employmentTypes = [
    {
      type: "Full-time",
      patterns: [/\bfull[-\s]?time\b/i, /\bft\b/i, /\bpermanent\b/i],
    },
    { type: "Part-time", patterns: [/\bpart[-\s]?time\b/i, /\bpt\b/i] },
    {
      type: "Contract",
      patterns: [
        /\bcontract\b/i,
        /\bcontractor\b/i,
        /\bconsultant\b/i,
        /\bfreelance\b/i,
      ],
    },
    { type: "Internship", patterns: [/\binternship\b/i, /\bintern\b/i] },
    {
      type: "Temporary",
      patterns: [/\btemporary\b/i, /\btemp\b/i, /\bseasonal\b/i],
    },
    { type: "Volunteer", patterns: [/\bvolunteer\b/i, /\bunpaid\b/i] },
    {
      type: "Apprenticeship",
      patterns: [/\bapprenticeship\b/i, /\bapprentice\b/i, /\btrainee\b/i],
    },
    { type: "Per Diem", patterns: [/\bper diem\b/i] },
  ];

  // Check for each employment type
  for (const { type, patterns } of employmentTypes) {
    for (const pattern of patterns) {
      if (pattern.test(text)) {
        logger.info(`💼 Detected employment type: ${type}`);
        return type;
      }
    }
  }

  // Default to Full-time if nothing else is found and the text is long enough
  // This is a reasonable assumption for most professional job postings
  if (text.length > 1000) {
    logger.info(
      `💼 No specific employment type found, defaulting to Full-time`
    );
    return "Full-time";
  }

  logger.info(`⚠️ Could not determine employment type`);
  return undefined;
}

function extractExperienceLevel(text: string): string | undefined {
  // Define experience level patterns with priorities
  const experienceLevels = [
    {
      level: "Internship",
      patterns: [/\binternship\b/i, /\bintern\b/i, /\bstudent\b/i],
    },
    {
      level: "Entry-level",
      patterns: [
        /\bentry[-\s]?level\b/i,
        /\bjunior\b/i,
        /\bjr\b/i,
        /\bgraduate\b/i,
        /\brecent graduate\b/i,
        /\b0-2 years\b/i,
        /\bno experience\b/i,
        /\blittle experience\b/i,
      ],
    },
    {
      level: "Mid-level",
      patterns: [
        /\bmid[-\s]?level\b/i,
        /\bintermediate\b/i,
        /\b2-5 years\b/i,
        /\b3-5 years\b/i,
        /\b2\+? years\b/i,
        /\b3\+? years\b/i,
      ],
    },
    {
      level: "Senior-level",
      patterns: [
        /\bsenior\b/i,
        /\bsr\b/i,
        /\bexperienced\b/i,
        /\b5-7 years\b/i,
        /\b5-10 years\b/i,
        /\b5\+? years\b/i,
        /\b7\+? years\b/i,
        /\b8\+? years\b/i,
      ],
    },
    {
      level: "Executive",
      patterns: [
        /\bexecutive\b/i,
        /\bdirector\b/i,
        /\bvp\b/i,
        /\bvice president\b/i,
        /\bcxo\b/i,
        /\bceo\b/i,
        /\bcto\b/i,
        /\bcfo\b/i,
        /\bcoo\b/i,
        /\bchief\b/i,
        /\b10\+? years\b/i,
        /\b15\+? years\b/i,
      ],
    },
  ];

  // Check for each experience level
  for (const { level, patterns } of experienceLevels) {
    for (const pattern of patterns) {
      if (pattern.test(text)) {
        logger.info(`🧠 Detected experience level: ${level}`);
        return level;
      }
    }
  }

  logger.info(`⚠️ Could not determine experience level`);
  return undefined;
}

function extractRemoteType(text: string): string | undefined {
  // Define remote work patterns with priorities
  const remoteTypes = [
    {
      type: "Remote",
      patterns: [
        /\bremote\b/i,
        /\bwork from home\b/i,
        /\bwfh\b/i,
        /\bvirtual\b/i,
        /\btelecommute\b/i,
        /\bwork anywhere\b/i,
      ],
    },
    {
      type: "Hybrid",
      patterns: [
        /\bhybrid\b/i,
        /\bflexible\b/i,
        /\bpartially remote\b/i,
        /\bremote \/? hybrid\b/i,
        /\bhybrid \/? remote\b/i,
      ],
    },
    {
      type: "On-site",
      patterns: [
        /\bon-?site\b/i,
        /\bin-?office\b/i,
        /\bon location\b/i,
        /\bin person\b/i,
        /\bphysical presence\b/i,
        /\boffice based\b/i,
      ],
    },
  ];

  // Check for each remote type
  for (const { type, patterns } of remoteTypes) {
    for (const pattern of patterns) {
      if (pattern.test(text)) {
        logger.info(`🏠 Detected remote type: ${type}`);
        return type;
      }
    }
  }

  // If we find location requirements but no explicit remote mention, assume on-site
  if (
    /\blocation\b.*\brequired\b|\bmust\b.*\brelocate\b|\bmust\b.*\blive\b.*\bin\b|\bmust\b.*\bwork\b.*\bfrom\b.*\boffice\b/i.test(
      text
    )
  ) {
    logger.info(`🏠 Location requirements found, assuming On-site`);
    return "On-site";
  }

  logger.info(`⚠️ Could not determine remote work type`);
  return undefined;
}
