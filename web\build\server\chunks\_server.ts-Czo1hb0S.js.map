{"version": 3, "file": "_server.ts-Czo1hb0S.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/health/_server.ts.js"], "sourcesContent": ["let lastLogTime = 0;\nconst LOG_INTERVAL = 864e5;\nfunction GET() {\n  const now = Date.now();\n  if (now - lastLogTime > LOG_INTERVAL) {\n    console.log(\"Health check endpoint accessed at:\", (/* @__PURE__ */ new Date()).toISOString());\n    lastLogTime = now;\n  }\n  return new Response(\"OK\", {\n    status: 200,\n    headers: {\n      \"Content-Type\": \"text/plain\",\n      // Add cache headers to reduce frequency of checks\n      \"Cache-Control\": \"public, max-age=3600\"\n      // Cache for 1 hour\n    }\n  });\n}\nexport {\n  GET\n};\n"], "names": [], "mappings": "AAAA,IAAI,WAAW,GAAG,CAAC;AACnB,MAAM,YAAY,GAAG,KAAK;AAC1B,SAAS,GAAG,GAAG;AACf,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE;AACxB,EAAE,IAAI,GAAG,GAAG,WAAW,GAAG,YAAY,EAAE;AACxC,IAAI,OAAO,CAAC,GAAG,CAAC,oCAAoC,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW,EAAE,CAAC;AACjG,IAAI,WAAW,GAAG,GAAG;AACrB;AACA,EAAE,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE;AAC5B,IAAI,MAAM,EAAE,GAAG;AACf,IAAI,OAAO,EAAE;AACb,MAAM,cAAc,EAAE,YAAY;AAClC;AACA,MAAM,eAAe,EAAE;AACvB;AACA;AACA,GAAG,CAAC;AACJ;;;;"}