{"version": 3, "file": "_page.svelte-dbmmF4NH.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/settings/admin/seed-features/_page.svelte.js"], "sourcesContent": ["import \"clsx\";\nimport { y as pop, w as push } from \"../../../../../../chunks/index3.js\";\nimport { B as Button } from \"../../../../../../chunks/button.js\";\nimport { a as toast } from \"../../../../../../chunks/Toaster.svelte_svelte_type_style_lang.js\";\nimport { g as goto } from \"../../../../../../chunks/client.js\";\nfunction _page($$payload, $$props) {\n  push();\n  let isLoading = false;\n  async function seedServiceFeatures() {\n    isLoading = true;\n    try {\n      toast.loading(\"Seeding service features...\");\n      const response = await fetch(\"/api/admin/features/seed-service\", { method: \"POST\" });\n      const result = await response.json();\n      if (result.success) {\n        toast.dismiss();\n        toast.success(\"Service features seeded successfully!\");\n        console.log(\"Seed results:\", result);\n      } else {\n        toast.dismiss();\n        toast.error(`Failed to seed service features: ${result.error}`);\n      }\n    } catch (error) {\n      console.error(\"Error seeding service features:\", error);\n      toast.dismiss();\n      toast.error(`Error seeding service features: ${error.message}`);\n    } finally {\n      isLoading = false;\n    }\n  }\n  async function seedAllFeatures() {\n    isLoading = true;\n    try {\n      toast.loading(\"Seeding all features...\");\n      const response = await fetch(\"/api/admin/features/seed-all\", { method: \"POST\" });\n      const result = await response.json();\n      if (result.success) {\n        toast.dismiss();\n        toast.success(\"All features seeded successfully!\");\n        console.log(\"Seed results:\", result);\n      } else {\n        toast.dismiss();\n        toast.error(`Failed to seed all features: ${result.error}`);\n      }\n    } catch (error) {\n      console.error(\"Error seeding all features:\", error);\n      toast.dismiss();\n      toast.error(`Error seeding all features: ${error.message}`);\n    } finally {\n      isLoading = false;\n    }\n  }\n  async function seedAnalysisFeatures() {\n    isLoading = true;\n    try {\n      toast.loading(\"Seeding analysis features...\");\n      const response = await fetch(\"/api/admin/features/seed-analysis\", { method: \"POST\" });\n      const result = await response.json();\n      if (result.success) {\n        toast.dismiss();\n        toast.success(\"Analysis features seeded successfully!\");\n        console.log(\"Seed results:\", result);\n      } else {\n        toast.dismiss();\n        toast.error(`Failed to seed analysis features: ${result.error}`);\n      }\n    } catch (error) {\n      console.error(\"Error seeding analysis features:\", error);\n      toast.dismiss();\n      toast.error(`Error seeding analysis features: ${error.message}`);\n    } finally {\n      isLoading = false;\n    }\n  }\n  $$payload.out += `<div class=\"container mx-auto p-6\"><div class=\"mb-6 flex items-center justify-between\"><h1 class=\"text-2xl font-bold\">Seed Features</h1> `;\n  Button($$payload, {\n    variant: \"outline\",\n    onclick: () => goto(),\n    children: ($$payload2) => {\n      $$payload2.out += `<!---->Back to Admin`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----></div> <div class=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3\"><div class=\"rounded-lg border p-6 shadow-sm\"><h2 class=\"mb-4 text-xl font-semibold\">Service Features</h2> <p class=\"text-muted-foreground mb-4 text-sm\">Seed service features including document storage with storage limits.</p> `;\n  Button($$payload, {\n    onclick: seedServiceFeatures,\n    disabled: isLoading,\n    class: \"w-full\",\n    children: ($$payload2) => {\n      $$payload2.out += `<!---->Seed Service Features`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----></div> <div class=\"rounded-lg border p-6 shadow-sm\"><h2 class=\"mb-4 text-xl font-semibold\">All Features</h2> <p class=\"text-muted-foreground mb-4 text-sm\">Seed all features including core, resume, job search, and application features.</p> `;\n  Button($$payload, {\n    onclick: seedAllFeatures,\n    disabled: isLoading,\n    class: \"w-full\",\n    children: ($$payload2) => {\n      $$payload2.out += `<!---->Seed All Features`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----></div> <div class=\"rounded-lg border p-6 shadow-sm\"><h2 class=\"mb-4 text-xl font-semibold\">Analysis Features</h2> <p class=\"text-muted-foreground mb-4 text-sm\">Seed analysis features for job market insights and resume analysis.</p> `;\n  Button($$payload, {\n    onclick: seedAnalysisFeatures,\n    disabled: isLoading,\n    class: \"w-full\",\n    children: ($$payload2) => {\n      $$payload2.out += `<!---->Seed Analysis Features`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----></div></div></div>`;\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAKA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,SAAS,GAAG,KAAK;AACvB,EAAE,eAAe,mBAAmB,GAAG;AACvC,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,IAAI;AACR,MAAM,KAAK,CAAC,OAAO,CAAC,6BAA6B,CAAC;AAClD,MAAM,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,kCAAkC,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;AAC1F,MAAM,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE;AAC1C,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE;AAC1B,QAAQ,KAAK,CAAC,OAAO,EAAE;AACvB,QAAQ,KAAK,CAAC,OAAO,CAAC,uCAAuC,CAAC;AAC9D,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,MAAM,CAAC;AAC5C,OAAO,MAAM;AACb,QAAQ,KAAK,CAAC,OAAO,EAAE;AACvB,QAAQ,KAAK,CAAC,KAAK,CAAC,CAAC,iCAAiC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AACvE;AACA,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC;AAC7D,MAAM,KAAK,CAAC,OAAO,EAAE;AACrB,MAAM,KAAK,CAAC,KAAK,CAAC,CAAC,gCAAgC,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;AACrE,KAAK,SAAS;AACd,MAAM,SAAS,GAAG,KAAK;AACvB;AACA;AACA,EAAE,eAAe,eAAe,GAAG;AACnC,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,IAAI;AACR,MAAM,KAAK,CAAC,OAAO,CAAC,yBAAyB,CAAC;AAC9C,MAAM,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,8BAA8B,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;AACtF,MAAM,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE;AAC1C,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE;AAC1B,QAAQ,KAAK,CAAC,OAAO,EAAE;AACvB,QAAQ,KAAK,CAAC,OAAO,CAAC,mCAAmC,CAAC;AAC1D,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,MAAM,CAAC;AAC5C,OAAO,MAAM;AACb,QAAQ,KAAK,CAAC,OAAO,EAAE;AACvB,QAAQ,KAAK,CAAC,KAAK,CAAC,CAAC,6BAA6B,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AACnE;AACA,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC;AACzD,MAAM,KAAK,CAAC,OAAO,EAAE;AACrB,MAAM,KAAK,CAAC,KAAK,CAAC,CAAC,4BAA4B,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;AACjE,KAAK,SAAS;AACd,MAAM,SAAS,GAAG,KAAK;AACvB;AACA;AACA,EAAE,eAAe,oBAAoB,GAAG;AACxC,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,IAAI;AACR,MAAM,KAAK,CAAC,OAAO,CAAC,8BAA8B,CAAC;AACnD,MAAM,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,mCAAmC,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;AAC3F,MAAM,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE;AAC1C,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE;AAC1B,QAAQ,KAAK,CAAC,OAAO,EAAE;AACvB,QAAQ,KAAK,CAAC,OAAO,CAAC,wCAAwC,CAAC;AAC/D,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,MAAM,CAAC;AAC5C,OAAO,MAAM;AACb,QAAQ,KAAK,CAAC,OAAO,EAAE;AACvB,QAAQ,KAAK,CAAC,KAAK,CAAC,CAAC,kCAAkC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AACxE;AACA,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC;AAC9D,MAAM,KAAK,CAAC,OAAO,EAAE;AACrB,MAAM,KAAK,CAAC,KAAK,CAAC,CAAC,iCAAiC,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;AACtE,KAAK,SAAS;AACd,MAAM,SAAS,GAAG,KAAK;AACvB;AACA;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,yIAAyI,CAAC;AAC9J,EAAE,MAAM,CAAC,SAAS,EAAE;AACpB,IAAI,OAAO,EAAE,SAAS;AACtB,IAAI,OAAO,EAAE,MAAM,IAAI,EAAE;AACzB,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC;AAC9C,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,sSAAsS,CAAC;AAC3T,EAAE,MAAM,CAAC,SAAS,EAAE;AACpB,IAAI,OAAO,EAAE,mBAAmB;AAChC,IAAI,QAAQ,EAAE,SAAS;AACvB,IAAI,KAAK,EAAE,QAAQ;AACnB,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,4BAA4B,CAAC;AACtD,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,sPAAsP,CAAC;AAC3Q,EAAE,MAAM,CAAC,SAAS,EAAE;AACpB,IAAI,OAAO,EAAE,eAAe;AAC5B,IAAI,QAAQ,EAAE,SAAS;AACvB,IAAI,KAAK,EAAE,QAAQ;AACnB,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,wBAAwB,CAAC;AAClD,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,+OAA+O,CAAC;AACpQ,EAAE,MAAM,CAAC,SAAS,EAAE;AACpB,IAAI,OAAO,EAAE,oBAAoB;AACjC,IAAI,QAAQ,EAAE,SAAS;AACvB,IAAI,KAAK,EAAE,QAAQ;AACnB,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,6BAA6B,CAAC;AACvD,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,yBAAyB,CAAC;AAC9C,EAAE,GAAG,EAAE;AACP;;;;"}