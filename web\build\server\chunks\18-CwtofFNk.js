const load = async ({ locals }) => {
  const session = await locals.getSession();
  return {
    session
  };
};

var _page_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 18;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-BlAShcT0.js')).default;
const server_id = "src/routes/auth/sign-up/+page.server.ts";
const imports = ["_app/immutable/nodes/18.D6kPXNgp.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/B-Xjo-Yt.js","_app/immutable/chunks/CWmzcjye.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/FN1sk3P2.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/B1K98fMG.js","_app/immutable/chunks/ncUU1dSD.js","_app/immutable/chunks/5V1tIHTN.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/DM07Bv7T.js","_app/immutable/chunks/DMTMHyMa.js","_app/immutable/chunks/CzsE_FAw.js","_app/immutable/chunks/T7uRAIbG.js","_app/immutable/chunks/BvdI7LR8.js","_app/immutable/chunks/BfX7a-t9.js","_app/immutable/chunks/BosuxZz1.js","_app/immutable/chunks/BniYvUIG.js","_app/immutable/chunks/DuoUhxYL.js","_app/immutable/chunks/Bd3zs5C6.js","_app/immutable/chunks/CIOgxH3l.js","_app/immutable/chunks/BjCTmJLi.js","_app/immutable/chunks/CnMg5bH0.js","_app/immutable/chunks/DrQfh6BY.js","_app/immutable/chunks/DxW95yuQ.js","_app/immutable/chunks/C3w0v0gR.js","_app/immutable/chunks/w80wGXGd.js","_app/immutable/chunks/DjPYYl4Z.js","_app/immutable/chunks/C6g8ubaU.js"];
const stylesheets = ["_app/immutable/assets/Toaster.DKF17Rty.css"];
const fonts = [];

export { component, fonts, imports, index, _page_server_ts as server, server_id, stylesheets };
//# sourceMappingURL=18-CwtofFNk.js.map
