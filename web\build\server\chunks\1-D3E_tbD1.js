const index = 1;
let component_cache;
const component = async () => component_cache ??= (await import('./_error.svelte-alaxNa_i.js')).default;
const imports = ["_app/immutable/nodes/1.B5upkY77.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/FN1sk3P2.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/C6g8ubaU.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/B-Xjo-Yt.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/B1K98fMG.js","_app/immutable/chunks/ncUU1dSD.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/5V1tIHTN.js","_app/immutable/chunks/DM07Bv7T.js"];
const stylesheets = [];
const fonts = [];

export { component, fonts, imports, index, stylesheets };
//# sourceMappingURL=1-D3E_tbD1.js.map
