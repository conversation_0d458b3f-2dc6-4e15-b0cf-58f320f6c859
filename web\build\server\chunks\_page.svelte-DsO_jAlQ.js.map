{"version": 3, "file": "_page.svelte-DsO_jAlQ.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/press/coverage/_page.svelte.js"], "sourcesContent": ["import { U as ensure_array_like, V as escape_html, R as attr, N as bind_props, y as pop, w as push } from \"../../../../chunks/index3.js\";\nimport { S as SEO } from \"../../../../chunks/SEO.js\";\nimport { P as PortableText } from \"../../../../chunks/PortableText.js\";\nimport { E as External_link } from \"../../../../chunks/external-link.js\";\nfunction _page($$payload, $$props) {\n  push();\n  let data = $$props[\"data\"];\n  const { newsCoveragePage, newsCoverage } = data;\n  function formatDate(dateStr) {\n    const date = new Date(dateStr);\n    return date.toLocaleDateString(\"en-US\", {\n      year: \"numeric\",\n      month: \"long\",\n      day: \"numeric\"\n    });\n  }\n  SEO($$payload, {\n    title: newsCoveragePage?.seo?.metaTitle || \"News Coverage | Hirli\",\n    description: newsCoveragePage?.seo?.metaDescription || \"Latest news coverage about Hirl<PERSON> from various media outlets.\",\n    keywords: newsCoveragePage?.seo?.keywords?.join(\", \") || \"news coverage, media coverage, press, Hirli\"\n  });\n  $$payload.out += `<!----> <div><h2 class=\"mb-8 text-3xl font-semibold\">News Coverage</h2> `;\n  if (newsCoveragePage?.content) {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<div class=\"mb-8\">`;\n    PortableText($$payload, { value: newsCoveragePage.content });\n    $$payload.out += `<!----></div>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<p class=\"text-muted-foreground mb-6\">Find the latest news coverage about Hirli from various media outlets.</p>`;\n  }\n  $$payload.out += `<!--]--> `;\n  if (newsCoverage && newsCoverage.length > 0) {\n    $$payload.out += \"<!--[-->\";\n    const each_array = ensure_array_like(newsCoverage);\n    $$payload.out += `<div class=\"space-y-6\"><!--[-->`;\n    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n      let coverage = each_array[$$index];\n      $$payload.out += `<div class=\"border-b pb-6\"><div class=\"flex flex-col md:flex-row md:items-center md:justify-between\"><div><p class=\"text-muted-foreground mb-2 text-sm\">${escape_html(formatDate(coverage.publishedAt))}</p> <h3 class=\"mb-2 text-xl font-medium\">${escape_html(coverage.publicationName)}</h3> <p class=\"text-muted-foreground mb-4\">${escape_html(coverage.title)}</p> `;\n      if (coverage.excerpt) {\n        $$payload.out += \"<!--[-->\";\n        $$payload.out += `<p class=\"mb-4 text-sm text-gray-600\">${escape_html(coverage.excerpt)}</p>`;\n      } else {\n        $$payload.out += \"<!--[!-->\";\n      }\n      $$payload.out += `<!--]--></div> <div class=\"mt-4 md:ml-4 md:mt-0\"><a${attr(\"href\", coverage.externalUrl)} target=\"_blank\" rel=\"noopener noreferrer\" class=\"text-primary inline-flex items-center text-sm font-medium hover:underline\">View Original Article `;\n      External_link($$payload, { class: \"ml-1 h-3 w-3\" });\n      $$payload.out += `<!----></a></div></div></div>`;\n    }\n    $$payload.out += `<!--]--></div>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<div class=\"py-8 text-center\"><p class=\"text-gray-500\">No news coverage available at this time.</p></div>`;\n  }\n  $$payload.out += `<!--]--></div>`;\n  bind_props($$props, { data });\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC;AAC5B,EAAE,MAAM,EAAE,gBAAgB,EAAE,YAAY,EAAE,GAAG,IAAI;AACjD,EAAE,SAAS,UAAU,CAAC,OAAO,EAAE;AAC/B,IAAI,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC;AAClC,IAAI,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE;AAC5C,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,KAAK,EAAE,MAAM;AACnB,MAAM,GAAG,EAAE;AACX,KAAK,CAAC;AACN;AACA,EAAE,GAAG,CAAC,SAAS,EAAE;AACjB,IAAI,KAAK,EAAE,gBAAgB,EAAE,GAAG,EAAE,SAAS,IAAI,uBAAuB;AACtE,IAAI,WAAW,EAAE,gBAAgB,EAAE,GAAG,EAAE,eAAe,IAAI,8DAA8D;AACzH,IAAI,QAAQ,EAAE,gBAAgB,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI;AAC7D,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,wEAAwE,CAAC;AAC7F,EAAE,IAAI,gBAAgB,EAAE,OAAO,EAAE;AACjC,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;AACzC,IAAI,YAAY,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,gBAAgB,CAAC,OAAO,EAAE,CAAC;AAChE,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACpC,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,+GAA+G,CAAC;AACtI;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AAC9B,EAAE,IAAI,YAAY,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;AAC/C,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,MAAM,UAAU,GAAG,iBAAiB,CAAC,YAAY,CAAC;AACtD,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,+BAA+B,CAAC;AACtD,IAAI,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACvF,MAAM,IAAI,QAAQ,GAAG,UAAU,CAAC,OAAO,CAAC;AACxC,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,wJAAwJ,EAAE,WAAW,CAAC,UAAU,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,0CAA0C,EAAE,WAAW,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,4CAA4C,EAAE,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;AAClY,MAAM,IAAI,QAAQ,CAAC,OAAO,EAAE;AAC5B,QAAQ,SAAS,CAAC,GAAG,IAAI,UAAU;AACnC,QAAQ,SAAS,CAAC,GAAG,IAAI,CAAC,sCAAsC,EAAE,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC;AACrG,OAAO,MAAM;AACb,QAAQ,SAAS,CAAC,GAAG,IAAI,WAAW;AACpC;AACA,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,mDAAmD,EAAE,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC,mJAAmJ,CAAC;AACpQ,MAAM,aAAa,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AACzD,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,6BAA6B,CAAC;AACtD;AACA,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACrC,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,yGAAyG,CAAC;AAChI;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACnC,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,CAAC;AAC/B,EAAE,GAAG,EAAE;AACP;;;;"}