{"version": 3, "file": "_server.ts-blp2CMce.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/skills/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../chunks/prisma.js\";\nconst GET = async ({ url }) => {\n  try {\n    if (!prisma) {\n      console.log(\"Prisma client not initialized during build\");\n      return json([]);\n    }\n    const search = url.searchParams.get(\"search\") || \"\";\n    const type = url.searchParams.get(\"type\") || void 0;\n    const limit = parseInt(url.searchParams.get(\"limit\") || \"50\");\n    const filters = {};\n    if (search) {\n      filters.name = {\n        contains: search,\n        mode: \"insensitive\"\n      };\n    }\n    if (type) {\n      filters.type = {\n        contains: type,\n        mode: \"insensitive\"\n      };\n    }\n    const skills = await prisma.skill.findMany({\n      where: filters,\n      orderBy: {\n        name: \"asc\"\n      },\n      take: limit\n    });\n    const formattedSkills = skills.map((skill) => ({\n      id: skill.id,\n      name: skill.name,\n      type: skill.type\n    }));\n    return json(formattedSkills);\n  } catch (error) {\n    console.error(\"Error fetching skills:\", error);\n    return json({ error: \"Failed to fetch skills\" }, { status: 500 });\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;AAEK,MAAC,GAAG,GAAG,OAAO,EAAE,GAAG,EAAE,KAAK;AAC/B,EAAE,IAAI;AACN,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC;AAC/D,MAAM,OAAO,IAAI,CAAC,EAAE,CAAC;AACrB;AACA,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE;AACvD,IAAI,MAAM,IAAI,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC;AACvD,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC;AACjE,IAAI,MAAM,OAAO,GAAG,EAAE;AACtB,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,OAAO,CAAC,IAAI,GAAG;AACrB,QAAQ,QAAQ,EAAE,MAAM;AACxB,QAAQ,IAAI,EAAE;AACd,OAAO;AACP;AACA,IAAI,IAAI,IAAI,EAAE;AACd,MAAM,OAAO,CAAC,IAAI,GAAG;AACrB,QAAQ,QAAQ,EAAE,IAAI;AACtB,QAAQ,IAAI,EAAE;AACd,OAAO;AACP;AACA,IAAI,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;AAC/C,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,OAAO,EAAE;AACf,QAAQ,IAAI,EAAE;AACd,OAAO;AACP,MAAM,IAAI,EAAE;AACZ,KAAK,CAAC;AACN,IAAI,MAAM,eAAe,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,MAAM;AACnD,MAAM,EAAE,EAAE,KAAK,CAAC,EAAE;AAClB,MAAM,IAAI,EAAE,KAAK,CAAC,IAAI;AACtB,MAAM,IAAI,EAAE,KAAK,CAAC;AAClB,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,IAAI,CAAC,eAAe,CAAC;AAChC,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC;AAClD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,wBAAwB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACrE;AACA;;;;"}