import { r as redirect } from './index-Ddp2AB5f.js';
import { p as prisma } from './prisma-Cit_HrSw.js';
import '@prisma/client';

const load = async ({ locals }) => {
  const user = locals.user;
  if (!user) {
    throw redirect(302, "/auth/sign-in");
  }
  const userData = await prisma.user.findUnique({
    where: { id: user.id },
    select: { isAdmin: true, role: true }
  });
  if (!userData || !userData.isAdmin && userData.role !== "admin") {
    throw redirect(302, "/dashboard/settings");
  }
  return {};
};

var _page_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 45;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-BRG9IMv8.js')).default;
const server_id = "src/routes/dashboard/settings/admin/+page.server.ts";
const imports = ["_app/immutable/nodes/45.BNtrvDn9.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/DT9WCdWY.js","_app/immutable/chunks/C3w0v0gR.js","_app/immutable/chunks/BvdI7LR8.js","_app/immutable/chunks/DuGukytH.js","_app/immutable/chunks/ncUU1dSD.js","_app/immutable/chunks/B-Xjo-Yt.js","_app/immutable/chunks/5V1tIHTN.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/Cdn-N1RY.js","_app/immutable/chunks/BkJY4La4.js","_app/immutable/chunks/GwmmX_iF.js","_app/immutable/chunks/D50jIuLr.js","_app/immutable/chunks/B1K98fMG.js","_app/immutable/chunks/DM07Bv7T.js","_app/immutable/chunks/DMTMHyMa.js","_app/immutable/chunks/CzsE_FAw.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/DjPYYl4Z.js","_app/immutable/chunks/FN1sk3P2.js","_app/immutable/chunks/C6g8ubaU.js","_app/immutable/chunks/Cs0qIT7f.js","_app/immutable/chunks/BBa424ah.js","_app/immutable/chunks/D4f2twK-.js","_app/immutable/chunks/w80wGXGd.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/tjBMsfLi.js","_app/immutable/chunks/B-l1ubNa.js","_app/immutable/chunks/yPulTJ2h.js","_app/immutable/chunks/hA0h0kTo.js","_app/immutable/chunks/BhzFx1Wy.js"];
const stylesheets = ["_app/immutable/assets/Toaster.DKF17Rty.css"];
const fonts = [];

export { component, fonts, imports, index, _page_server_ts as server, server_id, stylesheets };
//# sourceMappingURL=45-JHEV23N5.js.map
