{"version": 3, "file": "_server.ts-BTNQgkMt.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/email/worker/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { l as logger } from \"../../../../../chunks/logger.js\";\nimport { g as getPrismaClient } from \"../../../../../chunks/prisma.js\";\nimport { R as RedisConnection } from \"../../../../../chunks/redis.js\";\nconst prisma = getPrismaClient();\nasync function GET() {\n  try {\n    let workerStatus = null;\n    let workerRunning = false;\n    let lastHeartbeat = null;\n    try {\n      if (prisma && typeof prisma.workerStatus?.findUnique === \"function\") {\n        workerStatus = await prisma.workerStatus.findUnique({\n          where: { name: \"email-worker\" }\n        });\n        workerRunning = workerStatus?.status === \"running\";\n        lastHeartbeat = workerStatus?.lastHeartbeat;\n      } else {\n        logger.warn(\"WorkerStatus table may not exist yet, using default values\");\n      }\n    } catch (dbError) {\n      logger.warn(\"Error checking worker status from database:\", dbError);\n    }\n    let queueSize = 0;\n    let processingCount = 0;\n    let resultsCount = 0;\n    if (RedisConnection) {\n      try {\n        queueSize = await RedisConnection.zcard(\"email:queue\");\n        processingCount = await RedisConnection.hlen(\"email:processing\");\n        resultsCount = await RedisConnection.hlen(\"email:results\");\n        if (workerStatus === null && (queueSize > 0 || processingCount > 0)) {\n          workerRunning = true;\n        }\n      } catch (error) {\n        logger.error(\"Error checking Redis queue status:\", error);\n      }\n    }\n    return json({\n      running: workerRunning,\n      lastHeartbeat,\n      queue: {\n        size: queueSize,\n        processing: processingCount,\n        results: resultsCount\n      }\n    });\n  } catch (error) {\n    logger.error(\"Error checking worker status:\", error);\n    return json(\n      {\n        running: false,\n        error: \"Failed to check worker status\",\n        queue: {\n          size: 0,\n          processing: 0,\n          results: 0\n        }\n      },\n      { status: 500 }\n    );\n  }\n}\nasync function POST({ request }) {\n  try {\n    const body = await request.json();\n    const { action } = body;\n    switch (action) {\n      case \"start\":\n        try {\n          if (prisma && typeof prisma.workerStatus?.upsert === \"function\") {\n            await prisma.workerStatus.upsert({\n              where: { name: \"email-worker\" },\n              update: {\n                status: \"running\",\n                lastHeartbeat: /* @__PURE__ */ new Date()\n              },\n              create: {\n                name: \"email-worker\",\n                status: \"running\",\n                lastHeartbeat: /* @__PURE__ */ new Date()\n              }\n            });\n            logger.info(\"Email worker status updated to running\");\n          } else {\n            logger.warn(\"WorkerStatus table may not exist yet, skipping database update\");\n          }\n        } catch (dbError) {\n          logger.warn(\"Error updating worker status in database:\", dbError);\n        }\n        return json({\n          success: true,\n          message: \"Worker status updated to running. Note: The actual worker runs as a separate service.\"\n        });\n      case \"stop\":\n        try {\n          if (prisma && typeof prisma.workerStatus?.upsert === \"function\") {\n            await prisma.workerStatus.upsert({\n              where: { name: \"email-worker\" },\n              update: {\n                status: \"stopped\",\n                lastHeartbeat: /* @__PURE__ */ new Date()\n              },\n              create: {\n                name: \"email-worker\",\n                status: \"stopped\",\n                lastHeartbeat: /* @__PURE__ */ new Date()\n              }\n            });\n            logger.info(\"Email worker status updated to stopped\");\n          } else {\n            logger.warn(\"WorkerStatus table may not exist yet, skipping database update\");\n          }\n        } catch (dbError) {\n          logger.warn(\"Error updating worker status in database:\", dbError);\n        }\n        return json({\n          success: true,\n          message: \"Worker status updated to stopped. Note: The actual worker runs as a separate service.\"\n        });\n      case \"clear-queue\":\n        if (RedisConnection) {\n          try {\n            await RedisConnection.del(\"email:queue\");\n            await RedisConnection.del(\"email:processing\");\n            logger.info(\"Email queue cleared\");\n            return json({ success: true, message: \"Email queue cleared\" });\n          } catch (error) {\n            logger.error(\"Error clearing email queue:\", error);\n            return json({ error: \"Failed to clear email queue\" }, { status: 500 });\n          }\n        } else {\n          return json({ error: \"Redis not available\" }, { status: 500 });\n        }\n      default:\n        return json({ error: \"Invalid action\" }, { status: 400 });\n    }\n  } catch (error) {\n    logger.error(\"Error managing email worker:\", error);\n    return json({ error: \"Failed to manage worker\" }, { status: 500 });\n  }\n}\nexport {\n  GET,\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;AAIA,MAAM,MAAM,GAAG,eAAe,EAAE;AAChC,eAAe,GAAG,GAAG;AACrB,EAAE,IAAI;AACN,IAAI,IAAI,YAAY,GAAG,IAAI;AAC3B,IAAI,IAAI,aAAa,GAAG,KAAK;AAC7B,IAAI,IAAI,aAAa,GAAG,IAAI;AAC5B,IAAI,IAAI;AACR,MAAM,IAAI,MAAM,IAAI,OAAO,MAAM,CAAC,YAAY,EAAE,UAAU,KAAK,UAAU,EAAE;AAC3E,QAAQ,YAAY,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC;AAC5D,UAAU,KAAK,EAAE,EAAE,IAAI,EAAE,cAAc;AACvC,SAAS,CAAC;AACV,QAAQ,aAAa,GAAG,YAAY,EAAE,MAAM,KAAK,SAAS;AAC1D,QAAQ,aAAa,GAAG,YAAY,EAAE,aAAa;AACnD,OAAO,MAAM;AACb,QAAQ,MAAM,CAAC,IAAI,CAAC,4DAA4D,CAAC;AACjF;AACA,KAAK,CAAC,OAAO,OAAO,EAAE;AACtB,MAAM,MAAM,CAAC,IAAI,CAAC,6CAA6C,EAAE,OAAO,CAAC;AACzE;AACA,IAAI,IAAI,SAAS,GAAG,CAAC;AACrB,IAAI,IAAI,eAAe,GAAG,CAAC;AAC3B,IAAI,IAAI,YAAY,GAAG,CAAC;AACxB,IAAI,IAAI,eAAe,EAAE;AACzB,MAAM,IAAI;AACV,QAAQ,SAAS,GAAG,MAAM,eAAe,CAAC,KAAK,CAAC,aAAa,CAAC;AAC9D,QAAQ,eAAe,GAAG,MAAM,eAAe,CAAC,IAAI,CAAC,kBAAkB,CAAC;AACxE,QAAQ,YAAY,GAAG,MAAM,eAAe,CAAC,IAAI,CAAC,eAAe,CAAC;AAClE,QAAQ,IAAI,YAAY,KAAK,IAAI,KAAK,SAAS,GAAG,CAAC,IAAI,eAAe,GAAG,CAAC,CAAC,EAAE;AAC7E,UAAU,aAAa,GAAG,IAAI;AAC9B;AACA,OAAO,CAAC,OAAO,KAAK,EAAE;AACtB,QAAQ,MAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC;AACjE;AACA;AACA,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,aAAa;AACnB,MAAM,KAAK,EAAE;AACb,QAAQ,IAAI,EAAE,SAAS;AACvB,QAAQ,UAAU,EAAE,eAAe;AACnC,QAAQ,OAAO,EAAE;AACjB;AACA,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC;AACxD,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,KAAK,EAAE,+BAA+B;AAC9C,QAAQ,KAAK,EAAE;AACf,UAAU,IAAI,EAAE,CAAC;AACjB,UAAU,UAAU,EAAE,CAAC;AACvB,UAAU,OAAO,EAAE;AACnB;AACA,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;AACA,eAAe,IAAI,CAAC,EAAE,OAAO,EAAE,EAAE;AACjC,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI;AAC3B,IAAI,QAAQ,MAAM;AAClB,MAAM,KAAK,OAAO;AAClB,QAAQ,IAAI;AACZ,UAAU,IAAI,MAAM,IAAI,OAAO,MAAM,CAAC,YAAY,EAAE,MAAM,KAAK,UAAU,EAAE;AAC3E,YAAY,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AAC7C,cAAc,KAAK,EAAE,EAAE,IAAI,EAAE,cAAc,EAAE;AAC7C,cAAc,MAAM,EAAE;AACtB,gBAAgB,MAAM,EAAE,SAAS;AACjC,gBAAgB,aAAa,kBAAkB,IAAI,IAAI;AACvD,eAAe;AACf,cAAc,MAAM,EAAE;AACtB,gBAAgB,IAAI,EAAE,cAAc;AACpC,gBAAgB,MAAM,EAAE,SAAS;AACjC,gBAAgB,aAAa,kBAAkB,IAAI,IAAI;AACvD;AACA,aAAa,CAAC;AACd,YAAY,MAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC;AACjE,WAAW,MAAM;AACjB,YAAY,MAAM,CAAC,IAAI,CAAC,gEAAgE,CAAC;AACzF;AACA,SAAS,CAAC,OAAO,OAAO,EAAE;AAC1B,UAAU,MAAM,CAAC,IAAI,CAAC,2CAA2C,EAAE,OAAO,CAAC;AAC3E;AACA,QAAQ,OAAO,IAAI,CAAC;AACpB,UAAU,OAAO,EAAE,IAAI;AACvB,UAAU,OAAO,EAAE;AACnB,SAAS,CAAC;AACV,MAAM,KAAK,MAAM;AACjB,QAAQ,IAAI;AACZ,UAAU,IAAI,MAAM,IAAI,OAAO,MAAM,CAAC,YAAY,EAAE,MAAM,KAAK,UAAU,EAAE;AAC3E,YAAY,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AAC7C,cAAc,KAAK,EAAE,EAAE,IAAI,EAAE,cAAc,EAAE;AAC7C,cAAc,MAAM,EAAE;AACtB,gBAAgB,MAAM,EAAE,SAAS;AACjC,gBAAgB,aAAa,kBAAkB,IAAI,IAAI;AACvD,eAAe;AACf,cAAc,MAAM,EAAE;AACtB,gBAAgB,IAAI,EAAE,cAAc;AACpC,gBAAgB,MAAM,EAAE,SAAS;AACjC,gBAAgB,aAAa,kBAAkB,IAAI,IAAI;AACvD;AACA,aAAa,CAAC;AACd,YAAY,MAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC;AACjE,WAAW,MAAM;AACjB,YAAY,MAAM,CAAC,IAAI,CAAC,gEAAgE,CAAC;AACzF;AACA,SAAS,CAAC,OAAO,OAAO,EAAE;AAC1B,UAAU,MAAM,CAAC,IAAI,CAAC,2CAA2C,EAAE,OAAO,CAAC;AAC3E;AACA,QAAQ,OAAO,IAAI,CAAC;AACpB,UAAU,OAAO,EAAE,IAAI;AACvB,UAAU,OAAO,EAAE;AACnB,SAAS,CAAC;AACV,MAAM,KAAK,aAAa;AACxB,QAAQ,IAAI,eAAe,EAAE;AAC7B,UAAU,IAAI;AACd,YAAY,MAAM,eAAe,CAAC,GAAG,CAAC,aAAa,CAAC;AACpD,YAAY,MAAM,eAAe,CAAC,GAAG,CAAC,kBAAkB,CAAC;AACzD,YAAY,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC;AAC9C,YAAY,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,qBAAqB,EAAE,CAAC;AAC1E,WAAW,CAAC,OAAO,KAAK,EAAE;AAC1B,YAAY,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC;AAC9D,YAAY,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,6BAA6B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClF;AACA,SAAS,MAAM;AACf,UAAU,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,qBAAqB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA,MAAM;AACN,QAAQ,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,gBAAgB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC;AACvD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtE;AACA;;;;"}