import { r as redirect, f as fail } from './index-Ddp2AB5f.js';
import { p as prisma } from './prisma-Cit_HrSw.js';
import { g as getUserFromToken } from './auth-BPad-IlN.js';
import { s as superValidate, z as zod } from './zod-DfpldWlD.js';
import { o as objectType, s as stringType } from './types-D78SXuvm.js';
import '@prisma/client';
import 'jsonwebtoken';
import 'ua-parser-js';
import './index4-HpJcNJHQ.js';
import './false-CRHihH2U.js';
import './constants-BaiUsPxc.js';
import './_commonjsHelpers-BFTU3MAI.js';

const profileSchema = objectType({
  name: stringType().min(1, "Profile name is required"),
  jobType: stringType().min(1, "Job type is required"),
  industry: stringType().optional(),
  resumeId: stringType().optional()
});
const load = async ({ locals, url }) => {
  const user = locals.user;
  if (!user || !user.email) {
    throw redirect(302, "/auth/sign-in");
  }
  const userData = await prisma.user.findUnique({
    where: { email: user.email },
    include: {
      TeamMember: {
        include: {
          team: true
        }
      }
    }
  });
  if (!userData) {
    throw redirect(302, "/auth/sign-in");
  }
  locals.user = userData;
  const page = parseInt(url.searchParams.get("page") || "1");
  const limit = parseInt(url.searchParams.get("limit") || "12");
  const search = url.searchParams.get("search") || "";
  const jobType = url.searchParams.get("jobType") || "";
  const industry = url.searchParams.get("industry") || "";
  const owner = url.searchParams.get("owner") || "all";
  const offset = (page - 1) * limit;
  const userTeams = userData.TeamMember.map((tm) => tm.teamId);
  const hasTeamAccess = userTeams.length > 0;
  const whereClause = {
    OR: [
      { userId: userData.id },
      ...hasTeamAccess ? [
        {
          teamId: {
            in: userTeams
          }
        }
      ] : []
    ]
  };
  if (search) {
    whereClause.name = {
      contains: search,
      mode: "insensitive"
    };
  }
  if (owner === "user") {
    whereClause.OR = [{ userId: userData.id }];
  } else if (owner === "team" && hasTeamAccess) {
    whereClause.OR = [
      {
        teamId: {
          in: userTeams
        }
      }
    ];
  }
  const profiles = await prisma.profile.findMany({
    where: whereClause,
    orderBy: { updatedAt: "desc" },
    take: limit,
    skip: offset,
    include: {
      defaultDocument: true,
      data: true,
      team: {
        select: {
          id: true,
          name: true
        }
      },
      user: {
        select: {
          id: true,
          name: true,
          email: true
        }
      }
    }
  });
  const totalProfiles = await prisma.profile.count({
    where: whereClause
  });
  const documents = await prisma.document.findMany({
    where: {
      userId: userData.id,
      resume: { isNot: null }
    },
    include: {
      resume: true
    },
    orderBy: { updatedAt: "desc" }
  });
  const formattedDocuments = documents.map((doc) => ({
    id: doc.id,
    label: doc.label || doc.fileName,
    resume: doc.resume
  }));
  const allProfiles = await prisma.profile.findMany({
    where: {
      OR: [
        { userId: userData.id },
        ...hasTeamAccess ? [
          {
            teamId: {
              in: userTeams
            }
          }
        ] : []
      ]
    },
    include: {
      data: true
    }
  });
  const jobTypes = /* @__PURE__ */ new Set();
  const industries = /* @__PURE__ */ new Set();
  allProfiles.forEach((profile) => {
    if (profile.data?.data) {
      try {
        const dataStr = typeof profile.data.data === "string" ? profile.data.data : JSON.stringify(profile.data.data);
        const data = JSON.parse(dataStr);
        if (data.jobType && typeof data.jobType === "string") jobTypes.add(data.jobType);
        if (data.industry && typeof data.industry === "string") industries.add(data.industry);
      } catch (e) {
        console.warn("Failed to parse profile data:", e);
      }
    }
  });
  const form = await superValidate(
    {
      name: "",
      jobType: "",
      industry: "",
      resumeId: ""
    },
    zod(profileSchema)
  );
  const totalPages = Math.ceil(totalProfiles / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;
  return {
    user: userData,
    profiles,
    documents: formattedDocuments,
    form,
    pagination: {
      page,
      limit,
      totalProfiles,
      totalPages,
      hasNextPage,
      hasPrevPage
    },
    filters: {
      search,
      jobType,
      industry,
      owner,
      jobTypes: Array.from(jobTypes).sort((a, b) => a.localeCompare(b)),
      industries: Array.from(industries).sort((a, b) => a.localeCompare(b))
    },
    hasTeamAccess
  };
};
const actions = {
  default: async ({ request, cookies }) => {
    const tokenData = await getUserFromToken(cookies);
    if (!tokenData?.email) {
      throw redirect(302, "/auth/sign-in");
    }
    const userData = await prisma.user.findUnique({
      where: { email: tokenData.email }
    });
    if (!userData) {
      throw redirect(302, "/auth/sign-in");
    }
    const form = await superValidate(request, zod(profileSchema));
    if (!form.valid) {
      return fail(400, { form });
    }
    try {
      const newProfile = await prisma.profile.create({
        data: {
          name: form.data.name,
          userId: userData.id,
          ...form.data.resumeId && form.data.resumeId !== "" && { defaultDocumentId: form.data.resumeId },
          data: {
            create: {
              data: JSON.stringify({
                jobType: form.data.jobType,
                industry: form.data.industry || null
              })
            }
          }
        }
      });
      return { form, success: true, profile: newProfile };
    } catch (error) {
      console.error("Error updating profile:", error);
      return fail(500, { form, error: "Failed to update profile" });
    }
  }
};

var _page_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  actions: actions,
  load: load
});

const index = 65;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-CLhcHgxT.js')).default;
const server_id = "src/routes/dashboard/settings/profile/+page.server.ts";
const imports = ["_app/immutable/nodes/65.eiN_bfv-.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/C3w0v0gR.js","_app/immutable/chunks/BvdI7LR8.js","_app/immutable/chunks/B-Xjo-Yt.js","_app/immutable/chunks/DuGukytH.js","_app/immutable/chunks/ncUU1dSD.js","_app/immutable/chunks/5V1tIHTN.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/Cdn-N1RY.js","_app/immutable/chunks/DETxXRrJ.js","_app/immutable/chunks/GwmmX_iF.js","_app/immutable/chunks/B1K98fMG.js","_app/immutable/chunks/DM07Bv7T.js","_app/immutable/chunks/DaBofrVv.js","_app/immutable/chunks/w80wGXGd.js","_app/immutable/chunks/DMTMHyMa.js","_app/immutable/chunks/CzsE_FAw.js","_app/immutable/chunks/DrGkVJ95.js","_app/immutable/chunks/BfX7a-t9.js","_app/immutable/chunks/BosuxZz1.js","_app/immutable/chunks/CnMg5bH0.js","_app/immutable/chunks/BnikQ10_.js","_app/immutable/chunks/DMoa_yM9.js","_app/immutable/chunks/DuoUhxYL.js","_app/immutable/chunks/Bd3zs5C6.js","_app/immutable/chunks/CIOgxH3l.js","_app/immutable/chunks/XESq6qWN.js","_app/immutable/chunks/OOsIR5sE.js","_app/immutable/chunks/BaVT73bJ.js","_app/immutable/chunks/DT9WCdWY.js","_app/immutable/chunks/Bpi49Nrf.js","_app/immutable/chunks/Cb-3cdbh.js","_app/immutable/chunks/DX6rZLP_.js","_app/immutable/chunks/BJIrNhIJ.js","_app/immutable/chunks/BKLOCbjP.js","_app/immutable/chunks/C6g8ubaU.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/FN1sk3P2.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/DjPYYl4Z.js","_app/immutable/chunks/Dqigtbi1.js","_app/immutable/chunks/yW0TxTga.js","_app/immutable/chunks/BBa424ah.js","_app/immutable/chunks/D4f2twK-.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/BhzFx1Wy.js","_app/immutable/chunks/DR5zc253.js","_app/immutable/chunks/-SpbofVw.js","_app/immutable/chunks/CYoZicO9.js","_app/immutable/chunks/DumgozFE.js","_app/immutable/chunks/C33xR25f.js","_app/immutable/chunks/BSHZ37s_.js","_app/immutable/chunks/B_6ivTD3.js","_app/immutable/chunks/CDnvByek.js","_app/immutable/chunks/ChqRiddM.js","_app/immutable/chunks/CwgkX8t9.js","_app/immutable/chunks/BBNNmnYR.js","_app/immutable/chunks/DkmCSZhC.js"];
const stylesheets = ["_app/immutable/assets/Toaster.DKF17Rty.css"];
const fonts = [];

export { component, fonts, imports, index, _page_server_ts as server, server_id, stylesheets };
//# sourceMappingURL=65-BQo7MHqF.js.map
