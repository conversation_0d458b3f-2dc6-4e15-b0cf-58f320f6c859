{"version": 3, "file": "_server.ts-5D3698ZI.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/resume/templates/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { l as logger } from \"../../../../../chunks/logger.js\";\nconst GET = async () => {\n  try {\n    const templates = [\n      { id: \"modern\", name: \"Modern\", thumbnail: \"/templates/modern.png\" },\n      { id: \"professional\", name: \"Professional\", thumbnail: \"/templates/professional.png\" },\n      { id: \"creative\", name: \"<PERSON>\", thumbnail: \"/templates/creative.png\" },\n      { id: \"simple\", name: \"Simple\", thumbnail: \"/templates/simple.png\" }\n    ];\n    return json({\n      templates,\n      count: templates.length,\n      timestamp: (/* @__PURE__ */ new Date()).toISOString()\n    });\n  } catch (error) {\n    logger.error(\"Error fetching resume templates:\", error);\n    return json(\n      {\n        error: \"Failed to fetch resume templates\",\n        timestamp: (/* @__PURE__ */ new Date()).toISOString()\n      },\n      { status: 500 }\n    );\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;AAEK,MAAC,GAAG,GAAG,YAAY;AACxB,EAAE,IAAI;AACN,IAAI,MAAM,SAAS,GAAG;AACtB,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,uBAAuB,EAAE;AAC1E,MAAM,EAAE,EAAE,EAAE,cAAc,EAAE,IAAI,EAAE,cAAc,EAAE,SAAS,EAAE,6BAA6B,EAAE;AAC5F,MAAM,EAAE,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,yBAAyB,EAAE;AAChF,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,uBAAuB;AACxE,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,SAAS;AACf,MAAM,KAAK,EAAE,SAAS,CAAC,MAAM;AAC7B,MAAM,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AACzD,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC;AAC3D,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,KAAK,EAAE,kCAAkC;AACjD,QAAQ,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC3D,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;;;;"}