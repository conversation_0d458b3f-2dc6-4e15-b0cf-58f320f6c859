{"version": 3, "file": "_server.ts-B3v5rG5H.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/admin/features/seed-service/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../../chunks/prisma.js\";\nimport { S as SERVICE_FEATURES } from \"../../../../../../chunks/service-features.js\";\nimport { v as verifySessionToken } from \"../../../../../../chunks/auth.js\";\nconst POST = async ({ cookies }) => {\n  const token = cookies.get(\"auth_token\");\n  if (!token) return new Response(\"Unauthorized\", { status: 401 });\n  const userData = await verifySessionToken(token);\n  if (!userData?.id) return new Response(\"Unauthorized\", { status: 401 });\n  const user = await prisma.user.findUnique({\n    where: { id: userData.id },\n    select: { isAdmin: true, role: true }\n  });\n  if (!user || !user.isAdmin && user.role !== \"admin\") {\n    return new Response(\"Unauthorized - Admin access required\", { status: 403 });\n  }\n  try {\n    const results = {\n      features: 0,\n      updated: 0,\n      limits: 0,\n      updatedLimits: 0,\n      errors: 0\n    };\n    console.log(`Starting to seed ${SERVICE_FEATURES.length} service features...`);\n    for (const feature of SERVICE_FEATURES) {\n      try {\n        console.log(`Processing feature: ${feature.id} - ${feature.name}`);\n        const existingFeature = await prisma.feature.findUnique({\n          where: { id: feature.id }\n        });\n        const featureData = {\n          name: feature.name,\n          description: feature.description ?? \"\",\n          category: feature.category ?? \"general\",\n          icon: feature.icon ?? null,\n          beta: feature.beta ?? false,\n          updatedAt: /* @__PURE__ */ new Date()\n        };\n        if (!existingFeature) {\n          await prisma.feature.create({\n            data: {\n              id: feature.id,\n              ...featureData\n            }\n          });\n          console.log(`Created new feature: ${feature.name}`);\n          results.features++;\n        } else {\n          await prisma.feature.update({\n            where: { id: feature.id },\n            data: featureData\n          });\n          console.log(`Updated existing feature: ${feature.name}`);\n          results.updated++;\n        }\n        if (feature.limits && Array.isArray(feature.limits) && feature.limits.length > 0) {\n          console.log(`Processing ${feature.limits.length} limits for feature ${feature.id}`);\n          for (const limit of feature.limits) {\n            if (!limit?.id) {\n              console.warn(`Skipping invalid limit for feature ${feature.id}`);\n              continue;\n            }\n            const existingLimit = await prisma.featureLimit.findUnique({\n              where: { id: limit.id }\n            });\n            const limitData = {\n              name: limit.name,\n              description: limit.description ?? \"\",\n              defaultValue: (limit.defaultValue ?? 10).toString(),\n              type: limit.type,\n              unit: limit.unit ?? null,\n              resetDay: limit.resetDay ?? null\n            };\n            if (!existingLimit) {\n              await prisma.featureLimit.create({\n                data: {\n                  id: limit.id,\n                  featureId: feature.id,\n                  ...limitData\n                }\n              });\n              console.log(`Created new limit: ${limit.name} for feature ${feature.name}`);\n              results.limits++;\n            } else {\n              await prisma.featureLimit.update({\n                where: { id: limit.id },\n                data: limitData\n              });\n              console.log(`Updated existing limit: ${limit.name} for feature ${feature.name}`);\n              results.updatedLimits++;\n            }\n          }\n        } else {\n          console.log(`No limits defined for feature ${feature.id}`);\n        }\n      } catch (error) {\n        console.error(`Error processing feature ${feature.name}:`, error);\n        if (error instanceof Error) {\n          console.error(\"Error details:\", error.message);\n          console.error(\"Stack trace:\", error.stack);\n        }\n        results.errors++;\n      }\n    }\n    console.log(\"Service features seeding completed successfully\");\n    return json({\n      success: true,\n      message: `Seeded ${results.features} new service features, updated ${results.updated} existing features, added ${results.limits} limits, updated ${results.updatedLimits} limits`,\n      results\n    });\n  } catch (error) {\n    console.error(\"Error seeding service features:\", error);\n    if (error instanceof Error) {\n      console.error(\"Error details:\", error.message);\n      console.error(\"Stack trace:\", error.stack);\n    }\n    return json(\n      {\n        success: false,\n        error: \"Failed to seed service features\",\n        message: error instanceof Error ? error.message : String(error)\n      },\n      { status: 500 }\n    );\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAIK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK;AACpC,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE,EAAE,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AAClD,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE,EAAE,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAC5C,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE;AAC9B,IAAI,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AACvC,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;AACvD,IAAI,OAAO,IAAI,QAAQ,CAAC,sCAAsC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAChF;AACA,EAAE,IAAI;AACN,IAAI,MAAM,OAAO,GAAG;AACpB,MAAM,QAAQ,EAAE,CAAC;AACjB,MAAM,OAAO,EAAE,CAAC;AAChB,MAAM,MAAM,EAAE,CAAC;AACf,MAAM,aAAa,EAAE,CAAC;AACtB,MAAM,MAAM,EAAE;AACd,KAAK;AACL,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,gBAAgB,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;AAClF,IAAI,KAAK,MAAM,OAAO,IAAI,gBAAgB,EAAE;AAC5C,MAAM,IAAI;AACV,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;AAC1E,QAAQ,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;AAChE,UAAU,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE;AACjC,SAAS,CAAC;AACV,QAAQ,MAAM,WAAW,GAAG;AAC5B,UAAU,IAAI,EAAE,OAAO,CAAC,IAAI;AAC5B,UAAU,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,EAAE;AAChD,UAAU,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,SAAS;AACjD,UAAU,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,IAAI;AACpC,UAAU,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,KAAK;AACrC,UAAU,SAAS,kBAAkB,IAAI,IAAI;AAC7C,SAAS;AACT,QAAQ,IAAI,CAAC,eAAe,EAAE;AAC9B,UAAU,MAAM,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;AACtC,YAAY,IAAI,EAAE;AAClB,cAAc,EAAE,EAAE,OAAO,CAAC,EAAE;AAC5B,cAAc,GAAG;AACjB;AACA,WAAW,CAAC;AACZ,UAAU,OAAO,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;AAC7D,UAAU,OAAO,CAAC,QAAQ,EAAE;AAC5B,SAAS,MAAM;AACf,UAAU,MAAM,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;AACtC,YAAY,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE;AACrC,YAAY,IAAI,EAAE;AAClB,WAAW,CAAC;AACZ,UAAU,OAAO,CAAC,GAAG,CAAC,CAAC,0BAA0B,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;AAClE,UAAU,OAAO,CAAC,OAAO,EAAE;AAC3B;AACA,QAAQ,IAAI,OAAO,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;AAC1F,UAAU,OAAO,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,oBAAoB,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;AAC7F,UAAU,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,MAAM,EAAE;AAC9C,YAAY,IAAI,CAAC,KAAK,EAAE,EAAE,EAAE;AAC5B,cAAc,OAAO,CAAC,IAAI,CAAC,CAAC,mCAAmC,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;AAC9E,cAAc;AACd;AACA,YAAY,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC;AACvE,cAAc,KAAK,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,EAAE;AACnC,aAAa,CAAC;AACd,YAAY,MAAM,SAAS,GAAG;AAC9B,cAAc,IAAI,EAAE,KAAK,CAAC,IAAI;AAC9B,cAAc,WAAW,EAAE,KAAK,CAAC,WAAW,IAAI,EAAE;AAClD,cAAc,YAAY,EAAE,CAAC,KAAK,CAAC,YAAY,IAAI,EAAE,EAAE,QAAQ,EAAE;AACjE,cAAc,IAAI,EAAE,KAAK,CAAC,IAAI;AAC9B,cAAc,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,IAAI;AACtC,cAAc,QAAQ,EAAE,KAAK,CAAC,QAAQ,IAAI;AAC1C,aAAa;AACb,YAAY,IAAI,CAAC,aAAa,EAAE;AAChC,cAAc,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AAC/C,gBAAgB,IAAI,EAAE;AACtB,kBAAkB,EAAE,EAAE,KAAK,CAAC,EAAE;AAC9B,kBAAkB,SAAS,EAAE,OAAO,CAAC,EAAE;AACvC,kBAAkB,GAAG;AACrB;AACA,eAAe,CAAC;AAChB,cAAc,OAAO,CAAC,GAAG,CAAC,CAAC,mBAAmB,EAAE,KAAK,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;AACzF,cAAc,OAAO,CAAC,MAAM,EAAE;AAC9B,aAAa,MAAM;AACnB,cAAc,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AAC/C,gBAAgB,KAAK,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,EAAE,EAAE;AACvC,gBAAgB,IAAI,EAAE;AACtB,eAAe,CAAC;AAChB,cAAc,OAAO,CAAC,GAAG,CAAC,CAAC,wBAAwB,EAAE,KAAK,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;AAC9F,cAAc,OAAO,CAAC,aAAa,EAAE;AACrC;AACA;AACA,SAAS,MAAM;AACf,UAAU,OAAO,CAAC,GAAG,CAAC,CAAC,8BAA8B,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;AACpE;AACA,OAAO,CAAC,OAAO,KAAK,EAAE;AACtB,QAAQ,OAAO,CAAC,KAAK,CAAC,CAAC,yBAAyB,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;AACzE,QAAQ,IAAI,KAAK,YAAY,KAAK,EAAE;AACpC,UAAU,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,OAAO,CAAC;AACxD,UAAU,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,KAAK,CAAC;AACpD;AACA,QAAQ,OAAO,CAAC,MAAM,EAAE;AACxB;AACA;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC;AAClE,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,QAAQ,CAAC,+BAA+B,EAAE,OAAO,CAAC,OAAO,CAAC,0BAA0B,EAAE,OAAO,CAAC,MAAM,CAAC,iBAAiB,EAAE,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC;AACvL,MAAM;AACN,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC;AAC3D,IAAI,IAAI,KAAK,YAAY,KAAK,EAAE;AAChC,MAAM,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,OAAO,CAAC;AACpD,MAAM,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,KAAK,CAAC;AAChD;AACA,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,KAAK,EAAE,iCAAiC;AAChD,QAAQ,OAAO,EAAE,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC,KAAK;AACtE,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;;;;"}