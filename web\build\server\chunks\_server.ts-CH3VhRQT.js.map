{"version": 3, "file": "_server.ts-CH3VhRQT.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/system/memory/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { l as logger } from \"../../../../../chunks/logger.js\";\nimport os from \"os\";\nconst GET = async ({ locals }) => {\n  try {\n    const user = locals.user;\n    const isAdmin = user?.isAdmin === true;\n    const totalMemory = os.totalmem();\n    const freeMemory = os.freemem();\n    const usedMemory = totalMemory - freeMemory;\n    const memoryUsagePercent = Math.round(usedMemory / totalMemory * 100);\n    const memoryInfo = {\n      total: formatBytes(totalMemory),\n      used: formatBytes(usedMemory),\n      free: formatBytes(freeMemory),\n      usagePercent: memoryUsagePercent\n    };\n    if (isAdmin) {\n      const detailedInfo = {\n        ...memoryInfo,\n        totalBytes: totalMemory,\n        usedBytes: usedMemory,\n        freeBytes: freeMemory,\n        // Add process memory info\n        process: {\n          rss: formatBytes(process.memoryUsage().rss),\n          heapTotal: formatBytes(process.memoryUsage().heapTotal),\n          heapUsed: formatBytes(process.memoryUsage().heapUsed),\n          external: formatBytes(process.memoryUsage().external),\n          arrayBuffers: formatBytes(process.memoryUsage().arrayBuffers || 0)\n        },\n        // Add system info\n        system: {\n          platform: os.platform(),\n          arch: os.arch(),\n          cpus: os.cpus().length,\n          uptime: formatUptime(os.uptime())\n        }\n      };\n      return json(detailedInfo);\n    }\n    return json(memoryInfo);\n  } catch (error) {\n    logger.error(\"Error fetching memory usage:\", error);\n    return json({ error: \"Failed to fetch memory usage\" }, { status: 500 });\n  }\n};\nfunction formatBytes(bytes) {\n  if (bytes === 0) return \"0 Bytes\";\n  const k = 1024;\n  const sizes = [\"Bytes\", \"KB\", \"MB\", \"GB\", \"TB\"];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n}\nfunction formatUptime(seconds) {\n  const days = Math.floor(seconds / (3600 * 24));\n  const hours = Math.floor(seconds % (3600 * 24) / 3600);\n  const minutes = Math.floor(seconds % 3600 / 60);\n  const secs = Math.floor(seconds % 60);\n  const parts = [];\n  if (days > 0) parts.push(`${days}d`);\n  if (hours > 0) parts.push(`${hours}h`);\n  if (minutes > 0) parts.push(`${minutes}m`);\n  if (secs > 0 || parts.length === 0) parts.push(`${secs}s`);\n  return parts.join(\" \");\n}\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;AAGK,MAAC,GAAG,GAAG,OAAO,EAAE,MAAM,EAAE,KAAK;AAClC,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC5B,IAAI,MAAM,OAAO,GAAG,IAAI,EAAE,OAAO,KAAK,IAAI;AAC1C,IAAI,MAAM,WAAW,GAAG,EAAE,CAAC,QAAQ,EAAE;AACrC,IAAI,MAAM,UAAU,GAAG,EAAE,CAAC,OAAO,EAAE;AACnC,IAAI,MAAM,UAAU,GAAG,WAAW,GAAG,UAAU;AAC/C,IAAI,MAAM,kBAAkB,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,WAAW,GAAG,GAAG,CAAC;AACzE,IAAI,MAAM,UAAU,GAAG;AACvB,MAAM,KAAK,EAAE,WAAW,CAAC,WAAW,CAAC;AACrC,MAAM,IAAI,EAAE,WAAW,CAAC,UAAU,CAAC;AACnC,MAAM,IAAI,EAAE,WAAW,CAAC,UAAU,CAAC;AACnC,MAAM,YAAY,EAAE;AACpB,KAAK;AACL,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,MAAM,YAAY,GAAG;AAC3B,QAAQ,GAAG,UAAU;AACrB,QAAQ,UAAU,EAAE,WAAW;AAC/B,QAAQ,SAAS,EAAE,UAAU;AAC7B,QAAQ,SAAS,EAAE,UAAU;AAC7B;AACA,QAAQ,OAAO,EAAE;AACjB,UAAU,GAAG,EAAE,WAAW,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,GAAG,CAAC;AACrD,UAAU,SAAS,EAAE,WAAW,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC;AACjE,UAAU,QAAQ,EAAE,WAAW,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC;AAC/D,UAAU,QAAQ,EAAE,WAAW,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC;AAC/D,UAAU,YAAY,EAAE,WAAW,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,YAAY,IAAI,CAAC;AAC3E,SAAS;AACT;AACA,QAAQ,MAAM,EAAE;AAChB,UAAU,QAAQ,EAAE,EAAE,CAAC,QAAQ,EAAE;AACjC,UAAU,IAAI,EAAE,EAAE,CAAC,IAAI,EAAE;AACzB,UAAU,IAAI,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM;AAChC,UAAU,MAAM,EAAE,YAAY,CAAC,EAAE,CAAC,MAAM,EAAE;AAC1C;AACA,OAAO;AACP,MAAM,OAAO,IAAI,CAAC,YAAY,CAAC;AAC/B;AACA,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC;AAC3B,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC;AACvD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,8BAA8B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3E;AACA;AACA,SAAS,WAAW,CAAC,KAAK,EAAE;AAC5B,EAAE,IAAI,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;AACnC,EAAE,MAAM,CAAC,GAAG,IAAI;AAChB,EAAE,MAAM,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;AACjD,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACrD,EAAE,OAAO,UAAU,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC;AACzE;AACA,SAAS,YAAY,CAAC,OAAO,EAAE;AAC/B,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;AAChD,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,IAAI,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC;AACxD,EAAE,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;AACjD,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,CAAC;AACvC,EAAE,MAAM,KAAK,GAAG,EAAE;AAClB,EAAE,IAAI,IAAI,GAAG,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AACtC,EAAE,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,GAAG,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;AAC5C,EAAE,IAAI,IAAI,GAAG,CAAC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC5D,EAAE,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC;AACxB;;;;"}