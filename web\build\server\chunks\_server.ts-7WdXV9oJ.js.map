{"version": 3, "file": "_server.ts-7WdXV9oJ.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/worker-process/_id_/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { l as logger } from \"../../../../../chunks/logger.js\";\nconst GET = async ({ params, locals }) => {\n  try {\n    const user = locals.user;\n    if (!user) {\n      return new Response(\"Unauthorized\", { status: 401 });\n    }\n    const id = params.id;\n    if (!id) {\n      return json({ error: \"Worker process ID is required\" }, { status: 400 });\n    }\n    const workerProcess = await prisma.workerProcess.findUnique({\n      where: { id }\n    });\n    if (!workerProcess) {\n      return json({ error: \"Worker process not found\" }, { status: 404 });\n    }\n    let parsedData = workerProcess.data;\n    if (typeof parsedData === \"string\") {\n      try {\n        parsedData = JSON.parse(parsedData);\n      } catch (parseError) {\n        logger.warn(`Failed to parse worker process data for ${id}:`, parseError);\n      }\n    }\n    return json({\n      success: true,\n      workerProcess: {\n        ...workerProcess,\n        data: parsedData\n      }\n    });\n  } catch (error) {\n    logger.error(\"Error retrieving worker process:\", error);\n    return json(\n      {\n        success: false,\n        error: \"Failed to retrieve worker process\",\n        message: error instanceof Error ? error.message : String(error)\n      },\n      { status: 500 }\n    );\n  }\n};\nconst PATCH = async ({ params, request, locals }) => {\n  try {\n    const user = locals.user;\n    if (!user) {\n      return new Response(\"Unauthorized\", { status: 401 });\n    }\n    const id = params.id;\n    if (!id) {\n      return json({ error: \"Worker process ID is required\" }, { status: 400 });\n    }\n    const body = await request.json();\n    const updateData = {};\n    if (body.status) {\n      updateData.status = body.status;\n      if (body.status === \"PROCESSING\" && !body.startedAt) {\n        updateData.startedAt = /* @__PURE__ */ new Date();\n      } else if (body.status === \"COMPLETED\" || body.status === \"FAILED\") {\n        updateData.completedAt = /* @__PURE__ */ new Date();\n      }\n    }\n    if (body.data !== void 0) {\n      updateData.data = typeof body.data === \"string\" ? body.data : JSON.stringify(body.data || {});\n    }\n    if (body.error !== void 0) {\n      updateData.error = body.error;\n    }\n    const workerProcess = await prisma.workerProcess.update({\n      where: { id },\n      data: updateData\n    });\n    return json({\n      success: true,\n      workerProcess,\n      message: `Worker process ${id} updated`\n    });\n  } catch (error) {\n    logger.error(\"Error updating worker process:\", error);\n    return json(\n      {\n        success: false,\n        error: \"Failed to update worker process\",\n        message: error instanceof Error ? error.message : String(error)\n      },\n      { status: 500 }\n    );\n  }\n};\nconst DELETE = async ({ params, locals }) => {\n  try {\n    const user = locals.user;\n    if (!user) {\n      return new Response(\"Unauthorized\", { status: 401 });\n    }\n    const id = params.id;\n    if (!id) {\n      return json({ error: \"Worker process ID is required\" }, { status: 400 });\n    }\n    await prisma.workerProcess.delete({\n      where: { id }\n    });\n    return json({\n      success: true,\n      message: `Worker process ${id} deleted`\n    });\n  } catch (error) {\n    logger.error(\"Error deleting worker process:\", error);\n    return json(\n      {\n        success: false,\n        error: \"Failed to delete worker process\",\n        message: error instanceof Error ? error.message : String(error)\n      },\n      { status: 500 }\n    );\n  }\n};\nexport {\n  DELETE,\n  GET,\n  PATCH\n};\n"], "names": [], "mappings": ";;;;;AAGK,MAAC,GAAG,GAAG,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK;AAC1C,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC5B,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1D;AACA,IAAI,MAAM,EAAE,GAAG,MAAM,CAAC,EAAE;AACxB,IAAI,IAAI,CAAC,EAAE,EAAE;AACb,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,+BAA+B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9E;AACA,IAAI,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC;AAChE,MAAM,KAAK,EAAE,EAAE,EAAE;AACjB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,aAAa,EAAE;AACxB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,0BAA0B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE;AACA,IAAI,IAAI,UAAU,GAAG,aAAa,CAAC,IAAI;AACvC,IAAI,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;AACxC,MAAM,IAAI;AACV,QAAQ,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;AAC3C,OAAO,CAAC,OAAO,UAAU,EAAE;AAC3B,QAAQ,MAAM,CAAC,IAAI,CAAC,CAAC,wCAAwC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC;AACjF;AACA;AACA,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,aAAa,EAAE;AACrB,QAAQ,GAAG,aAAa;AACxB,QAAQ,IAAI,EAAE;AACd;AACA,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC;AAC3D,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,KAAK,EAAE,mCAAmC;AAClD,QAAQ,OAAO,EAAE,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC,KAAK;AACtE,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;AACK,MAAC,KAAK,GAAG,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AACrD,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC5B,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1D;AACA,IAAI,MAAM,EAAE,GAAG,MAAM,CAAC,EAAE;AACxB,IAAI,IAAI,CAAC,EAAE,EAAE;AACb,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,+BAA+B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9E;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,MAAM,UAAU,GAAG,EAAE;AACzB,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;AACrB,MAAM,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM;AACrC,MAAM,IAAI,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AAC3D,QAAQ,UAAU,CAAC,SAAS,mBAAmB,IAAI,IAAI,EAAE;AACzD,OAAO,MAAM,IAAI,IAAI,CAAC,MAAM,KAAK,WAAW,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE;AAC1E,QAAQ,UAAU,CAAC,WAAW,mBAAmB,IAAI,IAAI,EAAE;AAC3D;AACA;AACA,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,EAAE;AAC9B,MAAM,UAAU,CAAC,IAAI,GAAG,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;AACnG;AACA,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,EAAE;AAC/B,MAAM,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK;AACnC;AACA,IAAI,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;AAC5D,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE;AACnB,MAAM,IAAI,EAAE;AACZ,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,aAAa;AACnB,MAAM,OAAO,EAAE,CAAC,eAAe,EAAE,EAAE,CAAC,QAAQ;AAC5C,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC;AACzD,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,KAAK,EAAE,iCAAiC;AAChD,QAAQ,OAAO,EAAE,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC,KAAK;AACtE,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;AACK,MAAC,MAAM,GAAG,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK;AAC7C,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC5B,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1D;AACA,IAAI,MAAM,EAAE,GAAG,MAAM,CAAC,EAAE;AACxB,IAAI,IAAI,CAAC,EAAE,EAAE;AACb,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,+BAA+B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9E;AACA,IAAI,MAAM,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;AACtC,MAAM,KAAK,EAAE,EAAE,EAAE;AACjB,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,OAAO,EAAE,CAAC,eAAe,EAAE,EAAE,CAAC,QAAQ;AAC5C,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC;AACzD,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,KAAK,EAAE,iCAAiC;AAChD,QAAQ,OAAO,EAAE,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC,KAAK;AACtE,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;;;;"}