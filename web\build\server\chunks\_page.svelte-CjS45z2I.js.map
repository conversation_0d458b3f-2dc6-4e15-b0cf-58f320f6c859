{"version": 3, "file": "_page.svelte-CjS45z2I.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/pricing/_page.svelte.js"], "sourcesContent": ["import { w as push, Y as fallback, U as ensure_array_like, S as attr_class, V as escape_html, N as bind_props, y as pop, W as stringify, _ as store_get, O as copy_payload, P as assign_payload, a1 as unsubscribe_stores, R as attr } from \"../../../chunks/index3.js\";\nimport { S as SEO } from \"../../../chunks/SEO.js\";\nimport { B as Button } from \"../../../chunks/button.js\";\nimport { S as Separator } from \"../../../chunks/separator.js\";\nimport { L as Loader_circle } from \"../../../chunks/loader-circle.js\";\nimport { C as Check } from \"../../../chunks/check.js\";\nimport { I as Info } from \"../../../chunks/info.js\";\nimport { X } from \"../../../chunks/x.js\";\nimport { S as Switch } from \"../../../chunks/switch.js\";\nimport { w as writable } from \"../../../chunks/index2.js\";\nimport { R as Root, P as Portal, d as Dialog_overlay, D as Dialog_content } from \"../../../chunks/index7.js\";\nimport { S as SignIn } from \"../../../chunks/SignIn.js\";\nimport { D as Dialog_trigger } from \"../../../chunks/dialog-trigger2.js\";\nimport { D as Dialog_header, a as Dialog_title, b as Dialog_description, c as Dialog_footer } from \"../../../chunks/dialog-description.js\";\nfunction PricingCard($$payload, $$props) {\n  push();\n  let title = $$props[\"title\"];\n  let price = $$props[\"price\"];\n  let description = $$props[\"description\"];\n  let isPopular = fallback($$props[\"isPopular\"], false);\n  let ctaText = $$props[\"ctaText\"];\n  let billingCycle = $$props[\"billingCycle\"];\n  let limits = fallback($$props[\"limits\"], () => ({}), true);\n  let features = fallback($$props[\"features\"], () => [], true);\n  let onCtaClick = $$props[\"onCtaClick\"];\n  let disabled = fallback($$props[\"disabled\"], false);\n  let activePlan = fallback($$props[\"activePlan\"], false);\n  let loading = fallback($$props[\"loading\"], false);\n  const each_array = ensure_array_like(features);\n  $$payload.out += `<div${attr_class(`border-border rounded-lg border shadow-sm ${stringify(isPopular ? \"border-primary shadow-primary/20 relative\" : \"\")}`)}>`;\n  if (isPopular) {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<div class=\"absolute inset-x-0 top-0 -translate-y-1/2 transform\"><div class=\"bg-primary text-primary-foreground inline-block rounded-full px-4 py-1 text-xs font-semibold uppercase tracking-wider\">Most Popular</div></div>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--> <div class=\"p-7\"><h3 class=\"text-foreground text-3xl font-semibold\">${escape_html(title)}</h3> <p class=\"text-muted-foreground mt-4 h-12\">${escape_html(description)}</p></div> `;\n  Separator($$payload, { class: \"bg-border my-4\" });\n  $$payload.out += `<!----> <div class=\"align-center mt-4 flex min-h-20 flex-col justify-center text-center\"><div><span class=\"font-semi-bold text-5xl\">`;\n  if (title.toLowerCase() === \"free\") {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `Free`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<span class=\"mr-1 align-super text-sm\">$</span>${escape_html(price)} <span class=\"text-lg\">/ month</span>`;\n  }\n  $$payload.out += `<!--]--></span> `;\n  if (title.toLowerCase() === \"custom\") {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<span>/ seat</span>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--></div> `;\n  if (title.toLowerCase() !== \"free\") {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<span class=\"text-muted-foreground mt-2 text-sm\">Billed ${escape_html(billingCycle)}</span>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--> <div class=\"mt-6 px-8\">`;\n  Button($$payload, {\n    class: \"w-full\",\n    variant: isPopular ? \"default\" : \"outline\",\n    disabled: disabled || loading,\n    onclick: onCtaClick,\n    children: ($$payload2) => {\n      if (loading) {\n        $$payload2.out += \"<!--[-->\";\n        Loader_circle($$payload2, { class: \"mr-2 h-4 w-4 animate-spin\" });\n        $$payload2.out += `<!----> Processing...`;\n      } else if (activePlan) {\n        $$payload2.out += \"<!--[1-->\";\n        $$payload2.out += `Current Plan`;\n      } else {\n        $$payload2.out += \"<!--[!-->\";\n        $$payload2.out += `${escape_html(ctaText)}`;\n      }\n      $$payload2.out += `<!--]-->`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----></div></div> `;\n  Separator($$payload, { class: \"bg-border mt-4\" });\n  $$payload.out += `<!----> <div class=\"mt-6 px-8\"><div class=\"min-h-[300px]\"><div class=\"mb-4\"><h4 class=\"text-muted-foreground mb-2 text-xs font-medium uppercase\">Plan Limits</h4> <div class=\"grid grid-cols-1 gap-2 sm:grid-cols-3\"><div class=\"rounded-md border p-3 text-center\"><p class=\"text-muted-foreground mb-1 text-xs\">Resume Scans</p> <p class=\"flex items-center justify-center text-lg font-semibold\">`;\n  if (title.toLowerCase() === \"free\") {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `10<span class=\"ml-1 text-xs font-normal\">/mo</span>`;\n  } else if (limits?.resumesPerMonth !== void 0 && limits.resumesPerMonth !== null && limits.resumesPerMonth !== \"U\") {\n    $$payload.out += \"<!--[1-->\";\n    $$payload.out += `${escape_html(limits.resumesPerMonth)} <span class=\"ml-1 text-xs font-normal\">/mo</span>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<span class=\"text-lg\">∞</span>`;\n  }\n  $$payload.out += `<!--]--></p></div> <div class=\"rounded-md border p-3 text-center\"><p class=\"text-muted-foreground mb-1 text-xs\">Job Profiles</p> <p class=\"flex items-center justify-center text-lg font-semibold\">`;\n  if (title.toLowerCase() === \"free\") {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `1`;\n  } else if (limits?.profiles !== void 0 && limits.profiles !== null && limits.profiles !== \"U\") {\n    $$payload.out += \"<!--[1-->\";\n    $$payload.out += `${escape_html(limits.profiles)}`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<span class=\"text-lg\">∞</span>`;\n  }\n  $$payload.out += `<!--]--></p></div> <div class=\"rounded-md border p-3 text-center\"><p class=\"text-muted-foreground mb-1 text-xs\">`;\n  if (title.toLowerCase() === \"free\") {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `AI Credits`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `Team Seats`;\n  }\n  $$payload.out += `<!--]--></p> <p class=\"flex items-center justify-center text-lg font-semibold\">`;\n  if (title.toLowerCase() === \"free\") {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `5<span class=\"ml-1 text-xs font-normal\">/mo</span>`;\n  } else if (limits?.seats !== void 0 && limits.seats !== null && limits.seats !== \"U\") {\n    $$payload.out += \"<!--[1-->\";\n    $$payload.out += `${escape_html(limits.seats)}`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<span class=\"text-lg\">∞</span>`;\n  }\n  $$payload.out += `<!--]--></p></div></div></div> <div><h4 class=\"text-muted-foreground mb-2 text-xs font-medium uppercase\">Key Features</h4> <table class=\"w-full table-auto border-collapse text-sm\"><tbody><!--[-->`;\n  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n    let feature = each_array[$$index];\n    $$payload.out += `<tr class=\"border-border border-t\"><td class=\"text-foreground py-3 pr-4\">`;\n    if (feature.featureId === \"resume_scanner\") {\n      $$payload.out += \"<!--[-->\";\n      $$payload.out += `Resume Scanner`;\n    } else if (feature.featureId === \"resume_builder\") {\n      $$payload.out += \"<!--[1-->\";\n      $$payload.out += `Resume Builder`;\n    } else if (feature.featureId === \"resume_ai\") {\n      $$payload.out += \"<!--[2-->\";\n      $$payload.out += `Resume AI`;\n    } else if (feature.featureId === \"job_search_profiles\") {\n      $$payload.out += \"<!--[3-->\";\n      $$payload.out += `Job Search Profiles`;\n    } else if (feature.featureId === \"job_save\") {\n      $$payload.out += \"<!--[4-->\";\n      $$payload.out += `Save Jobs`;\n    } else if (feature.featureId === \"job_alerts\") {\n      $$payload.out += \"<!--[5-->\";\n      $$payload.out += `Job Alerts`;\n    } else if (feature.featureId === \"tracker\" || feature.featureId === \"application_tracker\") {\n      $$payload.out += \"<!--[6-->\";\n      $$payload.out += `Application Tracker`;\n    } else if (feature.featureId === \"cover_letter_generator\") {\n      $$payload.out += \"<!--[7-->\";\n      $$payload.out += `Cover Letter Generator`;\n    } else if (feature.featureId === \"dashboard\") {\n      $$payload.out += \"<!--[8-->\";\n      $$payload.out += `Dashboard Access`;\n    } else if (feature.featureId === \"profile\") {\n      $$payload.out += \"<!--[9-->\";\n      $$payload.out += `User Profile`;\n    } else if (feature.featureId === \"documents\") {\n      $$payload.out += \"<!--[10-->\";\n      $$payload.out += `Document Storage`;\n    } else {\n      $$payload.out += \"<!--[!-->\";\n      $$payload.out += `${escape_html(feature.featureId.split(\"_\").map((word) => word.charAt(0).toUpperCase() + word.slice(1)).join(\" \"))}`;\n    }\n    $$payload.out += `<!--]--></td><td class=\"py-3 pl-4 text-right\">`;\n    if (feature.accessLevel === \"included\") {\n      $$payload.out += \"<!--[-->\";\n      Check($$payload, { class: \"text-success ml-auto h-4 w-4\" });\n    } else if (feature.accessLevel === \"limited\") {\n      $$payload.out += \"<!--[1-->\";\n      $$payload.out += `<div class=\"flex items-center\"><span class=\"mr-1 text-xs text-amber-500\">Limited</span> <div class=\"tooltip-wrapper svelte-12270ft\">`;\n      Info($$payload, {\n        class: \"text-muted-foreground h-3 w-3 cursor-help\"\n      });\n      $$payload.out += `<!----> <div class=\"tooltip svelte-12270ft\"><p class=\"text-xs\">This feature has usage limits based on your plan</p></div></div></div>`;\n    } else {\n      $$payload.out += \"<!--[!-->\";\n      X($$payload, { class: \"text-destructive ml-auto h-4 w-4\" });\n    }\n    $$payload.out += `<!--]--></td></tr>`;\n  }\n  $$payload.out += `<!--]--></tbody></table></div></div></div> `;\n  Separator($$payload, { class: \"bg-border mt-4\" });\n  $$payload.out += `<!----></div>`;\n  bind_props($$props, {\n    title,\n    price,\n    description,\n    isPopular,\n    ctaText,\n    billingCycle,\n    limits,\n    features,\n    onCtaClick,\n    disabled,\n    activePlan,\n    loading\n  });\n  pop();\n}\nfunction _page($$payload, $$props) {\n  push();\n  var $$store_subs;\n  let billingCycle;\n  let data = $$props[\"data\"];\n  let selectedPlanId = data.preselectedPlanId || null;\n  const urlParams = typeof window !== \"undefined\" ? new URLSearchParams(window.location.search) : null;\n  const sectionParam = urlParams?.get(\"section\");\n  let activeTab = sectionParam === \"teams\" ? \"teams\" : data.preselectedSection === \"teams\" ? \"teams\" : \"pro\";\n  let billingCycleStore = writable(data.preselectedBillingCycle === \"annual\" ? \"annual\" : \"monthly\");\n  let isAnnual = store_get($$store_subs ??= {}, \"$billingCycleStore\", billingCycleStore) === \"annual\";\n  let pendingCheckout = null;\n  const user = data.user;\n  let plans = data.plans;\n  console.log(\"All plans:\", plans);\n  console.log(\"Individual plans:\", plans.filter((p) => p.section === \"pro\"));\n  console.log(\"Team plans:\", plans.filter((p) => p.section === \"teams\"));\n  console.log(\"Plans from server:\", plans);\n  console.log(\"Individual plans:\", plans.filter((p) => p.section === \"pro\").map((p) => p.id));\n  console.log(\"Team plans:\", plans.filter((p) => p.section === \"teams\").map((p) => p.id));\n  let isDialogOpen = false;\n  let isLoading = false;\n  function formatPrice(cents) {\n    return `${(cents / 100).toFixed(0)}`;\n  }\n  async function openStripeCheckout(planId, billingCycle2) {\n    isLoading = true;\n    try {\n      console.log(\"Creating checkout session\", { planId, billingCycle: billingCycle2 });\n      const res = await fetch(\"/api/billing/create-checkout-session\", {\n        method: \"POST\",\n        headers: { \"Content-Type\": \"application/json\" },\n        body: JSON.stringify({ planId, billingCycle: billingCycle2 })\n      });\n      if (!res.ok) {\n        const errorText = await res.text();\n        console.error(\"Failed to create Stripe session\", {\n          status: res.status,\n          statusText: res.statusText,\n          errorText\n        });\n        alert(`Error: ${errorText || \"Failed to create checkout session\"}`);\n        isLoading = false;\n        return;\n      }\n      const data2 = await res.json();\n      if (!data2.url) {\n        console.error(\"No URL returned from checkout session\", data2);\n        alert(\"Error: No checkout URL returned\");\n        isLoading = false;\n        return;\n      }\n      console.log(\"Redirecting to Stripe\", { url: data2.url });\n      window.location.href = data2.url;\n    } catch (error) {\n      console.error(\"Failed to create Stripe session\", error);\n      alert(`Error: ${error.message || \"Failed to create checkout session\"}`);\n      isLoading = false;\n    }\n  }\n  async function loginWithEmail(email, password) {\n    const res = await fetch(\"/api/auth/login\", {\n      method: \"POST\",\n      headers: { \"Content-Type\": \"application/json\" },\n      body: JSON.stringify({ email, password })\n    });\n    if (res.ok) {\n      isLoading = false;\n      if (pendingCheckout) {\n        const { planId, billingCycle: billingCycle2 } = pendingCheckout;\n        pendingCheckout = null;\n        selectedPlanId = planId;\n        openStripeCheckout(planId, billingCycle2);\n      } else {\n        window.location.href = \"/dashboard\";\n      }\n    }\n  }\n  function openLoginDialog() {\n    isDialogOpen = true;\n  }\n  function closeLoginDialog() {\n    isDialogOpen = false;\n  }\n  billingCycle = store_get($$store_subs ??= {}, \"$billingCycleStore\", billingCycleStore);\n  {\n    billingCycleStore.set(isAnnual ? \"annual\" : \"monthly\");\n  }\n  if (selectedPlanId) {\n    setTimeout(\n      () => {\n        const planElement = document.getElementById(`plan-${selectedPlanId}`);\n        if (planElement) {\n          planElement.scrollIntoView({ behavior: \"smooth\", block: \"center\" });\n        }\n      },\n      500\n    );\n  }\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    const each_array = ensure_array_like(plans.filter((p) => p.section === activeTab).sort((a, b) => {\n      if (a.id === \"free\") return -1;\n      if (b.id === \"free\") return 1;\n      return a.monthlyPrice - b.monthlyPrice;\n    }));\n    SEO($$payload2, {\n      title: \"Pricing | Hirli\",\n      description: \"Simple, transparent pricing plans for all your job application automation needs. Choose the plan that fits your career goals.\",\n      keywords: \"pricing, subscription plans, job application tools, career services, job search automation, resume optimization\"\n    });\n    $$payload2.out += `<!----> <section><div class=\"container mx-auto\"><div class=\"py-14 text-center\"><h2 class=\"mb-4 text-3xl font-semibold\">Simple, Transparent Pricing</h2> <p class=\"text-muted-foreground font-lg mx-auto mt-2 max-w-2xl\">Start with a free account to speed up your job hunt or boost your entire team to scale\n        hiring process with resume automation.</p> <div class=\"mt-8 flex flex-col items-center justify-between gap-4 px-4 md:flex-row\"><div class=\"flex justify-start space-x-4 md:justify-center\">`;\n    Button($$payload2, {\n      onclick: () => activeTab = \"pro\",\n      class: `border-border rounded-full border px-4 py-2 text-sm font-medium transition ${stringify(activeTab === \"pro\" ? \"border-primary bg-primary text-primary-foreground\" : \"bg-background text-foreground hover:bg-muted\")}`,\n      children: ($$payload3) => {\n        $$payload3.out += `<!---->Individual`;\n      },\n      $$slots: { default: true }\n    });\n    $$payload2.out += `<!----> `;\n    Button($$payload2, {\n      onclick: () => activeTab = \"teams\",\n      class: `border-border rounded-full border px-4 py-2 text-sm font-medium transition ${stringify(activeTab === \"teams\" ? \"border-primary bg-primary text-primary-foreground\" : \"bg-background text-foreground hover:bg-muted\")}`,\n      children: ($$payload3) => {\n        $$payload3.out += `<!---->Teams`;\n      },\n      $$slots: { default: true }\n    });\n    $$payload2.out += `<!----></div> <div class=\"text-muted-foreground flex items-center gap-3 text-sm\">`;\n    Switch($$payload2, {\n      get checked() {\n        return isAnnual;\n      },\n      set checked($$value) {\n        isAnnual = $$value;\n        $$settled = false;\n      }\n    });\n    $$payload2.out += `<!----> <span>Annual Billing</span></div></div></div> <div class=\"mt-10 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3\"><!--[-->`;\n    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n      let plan = each_array[$$index];\n      $$payload2.out += `<div${attr(\"id\", `plan-${stringify(plan.id)}`)}${attr_class(`relative ${stringify(selectedPlanId === plan.id ? \"ring-primary ring-2 ring-offset-2\" : \"\")}`)}>`;\n      PricingCard($$payload2, {\n        title: plan.name,\n        price: formatPrice(billingCycle === \"monthly\" ? plan.monthlyPrice : plan.annualPrice / 12),\n        description: plan.description,\n        limits: plan.limits,\n        features: plan.features,\n        billingCycle,\n        isPopular: plan.id === \"pro\" || plan.id === \"startup\" || plan.popular || selectedPlanId === plan.id,\n        activePlan: user && user.role === plan.id,\n        loading: selectedPlanId === plan.id && isLoading,\n        ctaText: user ? user.role === plan.id ? \"Current Plan\" : \"Upgrade\" : plan.id === \"free\" ? \"Start Free\" : \"Choose Plan\",\n        disabled: isLoading || user && user.role === plan.id,\n        onCtaClick: () => {\n          if (plan.id === \"free\") {\n            window.location.href = \"/auth/sign-up\";\n            return;\n          }\n          if (!user) {\n            pendingCheckout = { planId: plan.id, billingCycle };\n            openLoginDialog();\n            return;\n          }\n          selectedPlanId = plan.id;\n          openStripeCheckout(plan.id, billingCycle);\n        }\n      });\n      $$payload2.out += `<!----></div>`;\n    }\n    $$payload2.out += `<!--]--></div> <div class=\"border-border bg-card text-card-foreground mt-16 rounded-lg border p-6 text-center shadow-sm\"><h3 class=\"mb-2 text-xl font-semibold\">Need a custom solution?</h3> <p class=\"text-muted-foreground mb-4\">Get in touch for enterprise or partner solutions tailored to your needs.</p> <a href=\"/contact\" class=\"text-primary font-medium hover:underline\">Talk to Sales →</a></div></div></section> `;\n    Root($$payload2, {\n      open: isDialogOpen,\n      onOpenChange: closeLoginDialog,\n      children: ($$payload3) => {\n        Dialog_trigger($$payload3, {});\n        $$payload3.out += `<!----> `;\n        Portal($$payload3, {\n          children: ($$payload4) => {\n            Dialog_overlay($$payload4, {});\n            $$payload4.out += `<!----> `;\n            Dialog_content($$payload4, {\n              class: `md:w-[375px]`,\n              children: ($$payload5) => {\n                Dialog_header($$payload5, {\n                  children: ($$payload6) => {\n                    Dialog_title($$payload6, {\n                      class: \"mb-2 text-2xl\",\n                      children: ($$payload7) => {\n                        $$payload7.out += `<!---->Sign In`;\n                      },\n                      $$slots: { default: true }\n                    });\n                    $$payload6.out += `<!----> `;\n                    Dialog_description($$payload6, {\n                      children: ($$payload7) => {\n                        $$payload7.out += `<p class=\"text-muted-foreground text-md mb-6\">You need to sign in to update your account plan.</p> `;\n                        SignIn($$payload7, {\n                          isLoading,\n                          onEmailPasswordLogin: loginWithEmail\n                        });\n                        $$payload7.out += `<!---->`;\n                      },\n                      $$slots: { default: true }\n                    });\n                    $$payload6.out += `<!---->`;\n                  },\n                  $$slots: { default: true }\n                });\n                $$payload5.out += `<!----> `;\n                Dialog_footer($$payload5, {});\n                $$payload5.out += `<!---->`;\n              },\n              $$slots: { default: true }\n            });\n            $$payload4.out += `<!---->`;\n          }\n        });\n        $$payload3.out += `<!---->`;\n      },\n      $$slots: { default: true }\n    });\n    $$payload2.out += `<!---->`;\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  if ($$store_subs) unsubscribe_stores($$store_subs);\n  bind_props($$props, { data });\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcA,SAAS,WAAW,CAAC,SAAS,EAAE,OAAO,EAAE;AACzC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC;AAC9B,EAAE,IAAI,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC;AAC9B,EAAE,IAAI,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC;AAC1C,EAAE,IAAI,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,KAAK,CAAC;AACvD,EAAE,IAAI,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC;AAClC,EAAE,IAAI,YAAY,GAAG,OAAO,CAAC,cAAc,CAAC;AAC5C,EAAE,IAAI,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,IAAI,CAAC;AAC5D,EAAE,IAAI,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC;AAC9D,EAAE,IAAI,UAAU,GAAG,OAAO,CAAC,YAAY,CAAC;AACxC,EAAE,IAAI,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,KAAK,CAAC;AACrD,EAAE,IAAI,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,KAAK,CAAC;AACzD,EAAE,IAAI,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,KAAK,CAAC;AACnD,EAAE,MAAM,UAAU,GAAG,iBAAiB,CAAC,QAAQ,CAAC;AAChD,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,0CAA0C,EAAE,SAAS,CAAC,SAAS,GAAG,2CAA2C,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/J,EAAE,IAAI,SAAS,EAAE;AACjB,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,4NAA4N,CAAC;AACnP,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,6EAA6E,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,iDAAiD,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC,WAAW,CAAC;AAC9M,EAAE,SAAS,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,gBAAgB,EAAE,CAAC;AACnD,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,oIAAoI,CAAC;AACzJ,EAAE,IAAI,KAAK,CAAC,WAAW,EAAE,KAAK,MAAM,EAAE;AACtC,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;AAC3B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,+CAA+C,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,qCAAqC,CAAC;AAChI;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC;AACrC,EAAE,IAAI,KAAK,CAAC,WAAW,EAAE,KAAK,QAAQ,EAAE;AACxC,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC;AAC1C,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;AACpC,EAAE,IAAI,KAAK,CAAC,WAAW,EAAE,KAAK,MAAM,EAAE;AACtC,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,wDAAwD,EAAE,WAAW,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC;AAClH,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,gCAAgC,CAAC;AACrD,EAAE,MAAM,CAAC,SAAS,EAAE;AACpB,IAAI,KAAK,EAAE,QAAQ;AACnB,IAAI,OAAO,EAAE,SAAS,GAAG,SAAS,GAAG,SAAS;AAC9C,IAAI,QAAQ,EAAE,QAAQ,IAAI,OAAO;AACjC,IAAI,OAAO,EAAE,UAAU;AACvB,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,IAAI,OAAO,EAAE;AACnB,QAAQ,UAAU,CAAC,GAAG,IAAI,UAAU;AACpC,QAAQ,aAAa,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,2BAA2B,EAAE,CAAC;AACzE,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC;AACjD,OAAO,MAAM,IAAI,UAAU,EAAE;AAC7B,QAAQ,UAAU,CAAC,GAAG,IAAI,WAAW;AACrC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;AACxC,OAAO,MAAM;AACb,QAAQ,UAAU,CAAC,GAAG,IAAI,WAAW;AACrC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;AACnD;AACA,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAClC,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC;AACzC,EAAE,SAAS,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,gBAAgB,EAAE,CAAC;AACnD,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,qYAAqY,CAAC;AAC1Z,EAAE,IAAI,KAAK,CAAC,WAAW,EAAE,KAAK,MAAM,EAAE;AACtC,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,mDAAmD,CAAC;AAC1E,GAAG,MAAM,IAAI,MAAM,EAAE,eAAe,KAAK,MAAM,IAAI,MAAM,CAAC,eAAe,KAAK,IAAI,IAAI,MAAM,CAAC,eAAe,KAAK,GAAG,EAAE;AACtH,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,EAAE,WAAW,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,kDAAkD,CAAC;AAC/G,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,8BAA8B,CAAC;AACrD;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,mMAAmM,CAAC;AACxN,EAAE,IAAI,KAAK,CAAC,WAAW,EAAE,KAAK,MAAM,EAAE;AACtC,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AACxB,GAAG,MAAM,IAAI,MAAM,EAAE,QAAQ,KAAK,MAAM,IAAI,MAAM,CAAC,QAAQ,KAAK,IAAI,IAAI,MAAM,CAAC,QAAQ,KAAK,GAAG,EAAE;AACjG,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,EAAE,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;AACtD,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,8BAA8B,CAAC;AACrD;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,gHAAgH,CAAC;AACrI,EAAE,IAAI,KAAK,CAAC,WAAW,EAAE,KAAK,MAAM,EAAE;AACtC,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC;AACjC,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC;AACjC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,+EAA+E,CAAC;AACpG,EAAE,IAAI,KAAK,CAAC,WAAW,EAAE,KAAK,MAAM,EAAE;AACtC,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,kDAAkD,CAAC;AACzE,GAAG,MAAM,IAAI,MAAM,EAAE,KAAK,KAAK,MAAM,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,IAAI,MAAM,CAAC,KAAK,KAAK,GAAG,EAAE;AACxF,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,EAAE,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AACnD,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,8BAA8B,CAAC;AACrD;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,mMAAmM,CAAC;AACxN,EAAE,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACrF,IAAI,IAAI,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;AACrC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,yEAAyE,CAAC;AAChG,IAAI,IAAI,OAAO,CAAC,SAAS,KAAK,gBAAgB,EAAE;AAChD,MAAM,SAAS,CAAC,GAAG,IAAI,UAAU;AACjC,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACvC,KAAK,MAAM,IAAI,OAAO,CAAC,SAAS,KAAK,gBAAgB,EAAE;AACvD,MAAM,SAAS,CAAC,GAAG,IAAI,WAAW;AAClC,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACvC,KAAK,MAAM,IAAI,OAAO,CAAC,SAAS,KAAK,WAAW,EAAE;AAClD,MAAM,SAAS,CAAC,GAAG,IAAI,WAAW;AAClC,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AAClC,KAAK,MAAM,IAAI,OAAO,CAAC,SAAS,KAAK,qBAAqB,EAAE;AAC5D,MAAM,SAAS,CAAC,GAAG,IAAI,WAAW;AAClC,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC;AAC5C,KAAK,MAAM,IAAI,OAAO,CAAC,SAAS,KAAK,UAAU,EAAE;AACjD,MAAM,SAAS,CAAC,GAAG,IAAI,WAAW;AAClC,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AAClC,KAAK,MAAM,IAAI,OAAO,CAAC,SAAS,KAAK,YAAY,EAAE;AACnD,MAAM,SAAS,CAAC,GAAG,IAAI,WAAW;AAClC,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC;AACnC,KAAK,MAAM,IAAI,OAAO,CAAC,SAAS,KAAK,SAAS,IAAI,OAAO,CAAC,SAAS,KAAK,qBAAqB,EAAE;AAC/F,MAAM,SAAS,CAAC,GAAG,IAAI,WAAW;AAClC,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC;AAC5C,KAAK,MAAM,IAAI,OAAO,CAAC,SAAS,KAAK,wBAAwB,EAAE;AAC/D,MAAM,SAAS,CAAC,GAAG,IAAI,WAAW;AAClC,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC;AAC/C,KAAK,MAAM,IAAI,OAAO,CAAC,SAAS,KAAK,WAAW,EAAE;AAClD,MAAM,SAAS,CAAC,GAAG,IAAI,WAAW;AAClC,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC;AACzC,KAAK,MAAM,IAAI,OAAO,CAAC,SAAS,KAAK,SAAS,EAAE;AAChD,MAAM,SAAS,CAAC,GAAG,IAAI,WAAW;AAClC,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;AACrC,KAAK,MAAM,IAAI,OAAO,CAAC,SAAS,KAAK,WAAW,EAAE;AAClD,MAAM,SAAS,CAAC,GAAG,IAAI,YAAY;AACnC,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC;AACzC,KAAK,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,IAAI,WAAW;AAClC,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,EAAE,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3I;AACA,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,8CAA8C,CAAC;AACrE,IAAI,IAAI,OAAO,CAAC,WAAW,KAAK,UAAU,EAAE;AAC5C,MAAM,SAAS,CAAC,GAAG,IAAI,UAAU;AACjC,MAAM,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,8BAA8B,EAAE,CAAC;AACjE,KAAK,MAAM,IAAI,OAAO,CAAC,WAAW,KAAK,SAAS,EAAE;AAClD,MAAM,SAAS,CAAC,GAAG,IAAI,WAAW;AAClC,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,oIAAoI,CAAC;AAC7J,MAAM,IAAI,CAAC,SAAS,EAAE;AACtB,QAAQ,KAAK,EAAE;AACf,OAAO,CAAC;AACR,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,qIAAqI,CAAC;AAC9J,KAAK,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,IAAI,WAAW;AAClC,MAAM,CAAC,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,kCAAkC,EAAE,CAAC;AACjE;AACA,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;AACzC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,2CAA2C,CAAC;AAChE,EAAE,SAAS,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,gBAAgB,EAAE,CAAC;AACnD,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AAClC,EAAE,UAAU,CAAC,OAAO,EAAE;AACtB,IAAI,KAAK;AACT,IAAI,KAAK;AACT,IAAI,WAAW;AACf,IAAI,SAAS;AACb,IAAI,OAAO;AACX,IAAI,YAAY;AAChB,IAAI,MAAM;AACV,IAAI,QAAQ;AACZ,IAAI,UAAU;AACd,IAAI,QAAQ;AACZ,IAAI,UAAU;AACd,IAAI;AACJ,GAAG,CAAC;AACJ,EAAE,GAAG,EAAE;AACP;AACA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,YAAY;AAClB,EAAE,IAAI,YAAY;AAClB,EAAE,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC;AAC5B,EAAE,IAAI,cAAc,GAAG,IAAI,CAAC,iBAAiB,IAAI,IAAI;AACrD,EAAE,MAAM,SAAS,GAAG,OAAO,MAAM,KAAK,WAAW,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,IAAI;AACtG,EAAE,MAAM,YAAY,GAAG,SAAS,EAAE,GAAG,CAAC,SAAS,CAAC;AAChD,EAAE,IAAI,SAAS,GAAG,YAAY,KAAK,OAAO,GAAG,OAAO,GAAG,IAAI,CAAC,kBAAkB,KAAK,OAAO,GAAG,OAAO,GAAG,KAAK;AAC5G,EAAE,IAAI,iBAAiB,GAAG,QAAQ,CAAC,IAAI,CAAC,uBAAuB,KAAK,QAAQ,GAAG,QAAQ,GAAG,SAAS,CAAC;AACpG,EAAE,IAAI,QAAQ,GAAG,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,oBAAoB,EAAE,iBAAiB,CAAC,KAAK,QAAQ;AACrG,EAAE,IAAI,eAAe,GAAG,IAAI;AAC5B,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI;AACxB,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK;AACxB,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,KAAK,CAAC;AAClC,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,KAAK,KAAK,CAAC,CAAC;AAC5E,EAAE,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,CAAC;AACxE,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,KAAK,CAAC;AAC1C,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,KAAK,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;AAC7F,EAAE,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;AACzF,EAAE,IAAI,YAAY,GAAG,KAAK;AAC1B,EAAE,IAAI,SAAS,GAAG,KAAK;AACvB,EAAE,SAAS,WAAW,CAAC,KAAK,EAAE;AAC9B,IAAI,OAAO,CAAC,EAAE,CAAC,KAAK,GAAG,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AACxC;AACA,EAAE,eAAe,kBAAkB,CAAC,MAAM,EAAE,aAAa,EAAE;AAC3D,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,IAAI;AACR,MAAM,OAAO,CAAC,GAAG,CAAC,2BAA2B,EAAE,EAAE,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,CAAC;AACvF,MAAM,MAAM,GAAG,GAAG,MAAM,KAAK,CAAC,sCAAsC,EAAE;AACtE,QAAQ,MAAM,EAAE,MAAM;AACtB,QAAQ,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;AACvD,QAAQ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE;AACpE,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE;AACnB,QAAQ,MAAM,SAAS,GAAG,MAAM,GAAG,CAAC,IAAI,EAAE;AAC1C,QAAQ,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE;AACzD,UAAU,MAAM,EAAE,GAAG,CAAC,MAAM;AAC5B,UAAU,UAAU,EAAE,GAAG,CAAC,UAAU;AACpC,UAAU;AACV,SAAS,CAAC;AACV,QAAQ,KAAK,CAAC,CAAC,OAAO,EAAE,SAAS,IAAI,mCAAmC,CAAC,CAAC,CAAC;AAC3E,QAAQ,SAAS,GAAG,KAAK;AACzB,QAAQ;AACR;AACA,MAAM,MAAM,KAAK,GAAG,MAAM,GAAG,CAAC,IAAI,EAAE;AACpC,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE;AACtB,QAAQ,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC;AACrE,QAAQ,KAAK,CAAC,iCAAiC,CAAC;AAChD,QAAQ,SAAS,GAAG,KAAK;AACzB,QAAQ;AACR;AACA,MAAM,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;AAC9D,MAAM,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG;AACtC,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC;AAC7D,MAAM,KAAK,CAAC,CAAC,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,mCAAmC,CAAC,CAAC,CAAC;AAC7E,MAAM,SAAS,GAAG,KAAK;AACvB;AACA;AACA,EAAE,eAAe,cAAc,CAAC,KAAK,EAAE,QAAQ,EAAE;AACjD,IAAI,MAAM,GAAG,GAAG,MAAM,KAAK,CAAC,iBAAiB,EAAE;AAC/C,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;AACrD,MAAM,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE;AAC9C,KAAK,CAAC;AACN,IAAI,IAAI,GAAG,CAAC,EAAE,EAAE;AAChB,MAAM,SAAS,GAAG,KAAK;AACvB,MAAM,IAAI,eAAe,EAAE;AAC3B,QAAQ,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,GAAG,eAAe;AACvE,QAAQ,eAAe,GAAG,IAAI;AAC9B,QAAQ,cAAc,GAAG,MAAM;AAC/B,QAAQ,kBAAkB,CAAC,MAAM,EAAE,aAAa,CAAC;AACjD,OAAO,MAAM;AACb,QAAQ,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,YAAY;AAC3C;AACA;AACA;AACA,EAAE,SAAS,eAAe,GAAG;AAC7B,IAAI,YAAY,GAAG,IAAI;AACvB;AACA,EAAE,SAAS,gBAAgB,GAAG;AAC9B,IAAI,YAAY,GAAG,KAAK;AACxB;AACA,EAAE,YAAY,GAAG,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,oBAAoB,EAAE,iBAAiB,CAAC;AACxF,EAAE;AACF,IAAI,iBAAiB,CAAC,GAAG,CAAC,QAAQ,GAAG,QAAQ,GAAG,SAAS,CAAC;AAC1D;AACA,EAAE,IAAI,cAAc,EAAE;AACtB,IAAI,UAAU;AACd,MAAM,MAAM;AACZ,QAAQ,MAAM,WAAW,GAAG,QAAQ,CAAC,cAAc,CAAC,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC,CAAC;AAC7E,QAAQ,IAAI,WAAW,EAAE;AACzB,UAAU,WAAW,CAAC,cAAc,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC;AAC7E;AACA,OAAO;AACP,MAAM;AACN,KAAK;AACL;AACA,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,MAAM,UAAU,GAAG,iBAAiB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;AACrG,MAAM,IAAI,CAAC,CAAC,EAAE,KAAK,MAAM,EAAE,OAAO,EAAE;AACpC,MAAM,IAAI,CAAC,CAAC,EAAE,KAAK,MAAM,EAAE,OAAO,CAAC;AACnC,MAAM,OAAO,CAAC,CAAC,YAAY,GAAG,CAAC,CAAC,YAAY;AAC5C,KAAK,CAAC,CAAC;AACP,IAAI,GAAG,CAAC,UAAU,EAAE;AACpB,MAAM,KAAK,EAAE,iBAAiB;AAC9B,MAAM,WAAW,EAAE,+HAA+H;AAClJ,MAAM,QAAQ,EAAE;AAChB,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC;AACvB,mMAAmM,CAAC;AACpM,IAAI,MAAM,CAAC,UAAU,EAAE;AACvB,MAAM,OAAO,EAAE,MAAM,SAAS,GAAG,KAAK;AACtC,MAAM,KAAK,EAAE,CAAC,2EAA2E,EAAE,SAAS,CAAC,SAAS,KAAK,KAAK,GAAG,mDAAmD,GAAG,8CAA8C,CAAC,CAAC,CAAC;AAClO,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC;AAC7C,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChC,IAAI,MAAM,CAAC,UAAU,EAAE;AACvB,MAAM,OAAO,EAAE,MAAM,SAAS,GAAG,OAAO;AACxC,MAAM,KAAK,EAAE,CAAC,2EAA2E,EAAE,SAAS,CAAC,SAAS,KAAK,OAAO,GAAG,mDAAmD,GAAG,8CAA8C,CAAC,CAAC,CAAC;AACpO,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;AACxC,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,iFAAiF,CAAC;AACzG,IAAI,MAAM,CAAC,UAAU,EAAE;AACvB,MAAM,IAAI,OAAO,GAAG;AACpB,QAAQ,OAAO,QAAQ;AACvB,OAAO;AACP,MAAM,IAAI,OAAO,CAAC,OAAO,EAAE;AAC3B,QAAQ,QAAQ,GAAG,OAAO;AAC1B,QAAQ,SAAS,GAAG,KAAK;AACzB;AACA,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,sIAAsI,CAAC;AAC9J,IAAI,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACvF,MAAM,IAAI,IAAI,GAAG,UAAU,CAAC,OAAO,CAAC;AACpC,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,SAAS,EAAE,SAAS,CAAC,cAAc,KAAK,IAAI,CAAC,EAAE,GAAG,mCAAmC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvL,MAAM,WAAW,CAAC,UAAU,EAAE;AAC9B,QAAQ,KAAK,EAAE,IAAI,CAAC,IAAI;AACxB,QAAQ,KAAK,EAAE,WAAW,CAAC,YAAY,KAAK,SAAS,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AAClG,QAAQ,WAAW,EAAE,IAAI,CAAC,WAAW;AACrC,QAAQ,MAAM,EAAE,IAAI,CAAC,MAAM;AAC3B,QAAQ,QAAQ,EAAE,IAAI,CAAC,QAAQ;AAC/B,QAAQ,YAAY;AACpB,QAAQ,SAAS,EAAE,IAAI,CAAC,EAAE,KAAK,KAAK,IAAI,IAAI,CAAC,EAAE,KAAK,SAAS,IAAI,IAAI,CAAC,OAAO,IAAI,cAAc,KAAK,IAAI,CAAC,EAAE;AAC3G,QAAQ,UAAU,EAAE,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE;AACjD,QAAQ,OAAO,EAAE,cAAc,KAAK,IAAI,CAAC,EAAE,IAAI,SAAS;AACxD,QAAQ,OAAO,EAAE,IAAI,GAAG,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,GAAG,cAAc,GAAG,SAAS,GAAG,IAAI,CAAC,EAAE,KAAK,MAAM,GAAG,YAAY,GAAG,aAAa;AAC9H,QAAQ,QAAQ,EAAE,SAAS,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE;AAC5D,QAAQ,UAAU,EAAE,MAAM;AAC1B,UAAU,IAAI,IAAI,CAAC,EAAE,KAAK,MAAM,EAAE;AAClC,YAAY,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,eAAe;AAClD,YAAY;AACZ;AACA,UAAU,IAAI,CAAC,IAAI,EAAE;AACrB,YAAY,eAAe,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,YAAY,EAAE;AAC/D,YAAY,eAAe,EAAE;AAC7B,YAAY;AACZ;AACA,UAAU,cAAc,GAAG,IAAI,CAAC,EAAE;AAClC,UAAU,kBAAkB,CAAC,IAAI,CAAC,EAAE,EAAE,YAAY,CAAC;AACnD;AACA,OAAO,CAAC;AACR,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACvC;AACA,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,8ZAA8Z,CAAC;AACtb,IAAI,IAAI,CAAC,UAAU,EAAE;AACrB,MAAM,IAAI,EAAE,YAAY;AACxB,MAAM,YAAY,EAAE,gBAAgB;AACpC,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,cAAc,CAAC,UAAU,EAAE,EAAE,CAAC;AACtC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACpC,QAAQ,MAAM,CAAC,UAAU,EAAE;AAC3B,UAAU,QAAQ,EAAE,CAAC,UAAU,KAAK;AACpC,YAAY,cAAc,CAAC,UAAU,EAAE,EAAE,CAAC;AAC1C,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACxC,YAAY,cAAc,CAAC,UAAU,EAAE;AACvC,cAAc,KAAK,EAAE,CAAC,YAAY,CAAC;AACnC,cAAc,QAAQ,EAAE,CAAC,UAAU,KAAK;AACxC,gBAAgB,aAAa,CAAC,UAAU,EAAE;AAC1C,kBAAkB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC5C,oBAAoB,YAAY,CAAC,UAAU,EAAE;AAC7C,sBAAsB,KAAK,EAAE,eAAe;AAC5C,sBAAsB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChD,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AAC1D,uBAAuB;AACvB,sBAAsB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9C,qBAAqB,CAAC;AACtB,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChD,oBAAoB,kBAAkB,CAAC,UAAU,EAAE;AACnD,sBAAsB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChD,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,mGAAmG,CAAC;AAC/I,wBAAwB,MAAM,CAAC,UAAU,EAAE;AAC3C,0BAA0B,SAAS;AACnC,0BAA0B,oBAAoB,EAAE;AAChD,yBAAyB,CAAC;AAC1B,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnD,uBAAuB;AACvB,sBAAsB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9C,qBAAqB,CAAC;AACtB,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/C,mBAAmB;AACnB,kBAAkB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC1C,iBAAiB,CAAC;AAClB,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC5C,gBAAgB,aAAa,CAAC,UAAU,EAAE,EAAE,CAAC;AAC7C,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC3C,eAAe;AACf,cAAc,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACtC,aAAa,CAAC;AACd,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACvC;AACA,SAAS,CAAC;AACV,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,IAAI,YAAY,EAAE,kBAAkB,CAAC,YAAY,CAAC;AACpD,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,CAAC;AAC/B,EAAE,GAAG,EAAE;AACP;;;;"}