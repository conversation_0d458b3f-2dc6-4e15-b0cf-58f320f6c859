{"version": 3, "file": "_server.ts-C5jVFWaC.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/admin/features/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../../../chunks/auth.js\";\nimport { i as initializeFeatureData } from \"../../../../../chunks/initialize-features.js\";\nimport { F as FeatureAccessLevel } from \"../../../../../chunks/features.js\";\nasync function syncFeatureWithPlans(featureId) {\n  try {\n    const plans = await prisma.plan.findMany({\n      include: {\n        features: {\n          include: {\n            limits: true\n          }\n        }\n      }\n    });\n    if (plans.length === 0) {\n      console.log(\"No plans found to synchronize with.\");\n      return;\n    }\n    console.log(`Syncing feature ${featureId} with ${plans.length} plans...`);\n    const getDefaultAccessLevel = (planName, featureId2) => {\n      const planNameLower = planName.toLowerCase();\n      if (featureId2.startsWith(\"career_\") || featureId2 === \"privacy_settings\" || featureId2 === \"notification_preferences\") {\n        return FeatureAccessLevel.Included;\n      }\n      if (planNameLower.includes(\"free\")) {\n        if (featureId2 === \"job_alerts\" || featureId2 === \"resume_builder\" || featureId2 === \"application_tracking\") {\n          return FeatureAccessLevel.Limited;\n        }\n        return FeatureAccessLevel.NotIncluded;\n      }\n      if (planNameLower.includes(\"basic\") || planNameLower.includes(\"starter\")) {\n        if (featureId2.includes(\"advanced_\") || featureId2.includes(\"team_\") || featureId2.includes(\"integration_\")) {\n          return FeatureAccessLevel.NotIncluded;\n        }\n        return FeatureAccessLevel.Limited;\n      }\n      if (planNameLower.includes(\"pro\") || planNameLower.includes(\"premium\")) {\n        if (featureId2.includes(\"team_\") || featureId2 === \"custom_automation_workflows\") {\n          return FeatureAccessLevel.Limited;\n        }\n        return FeatureAccessLevel.Unlimited;\n      }\n      if (planNameLower.includes(\"enterprise\") || planNameLower.includes(\"business\")) {\n        return FeatureAccessLevel.Unlimited;\n      }\n      return FeatureAccessLevel.Limited;\n    };\n    let syncCount = 0;\n    let errorCount = 0;\n    for (const plan of plans) {\n      try {\n        const existingFeature = plan.features.find((f) => f.featureId === featureId);\n        if (existingFeature) {\n          console.log(`Plan ${plan.name} already has feature ${featureId}`);\n          continue;\n        }\n        const accessLevel = getDefaultAccessLevel(plan.name, featureId);\n        await prisma.planFeature.create({\n          data: {\n            planId: plan.id,\n            featureId,\n            accessLevel\n          }\n        });\n        console.log(\n          `Added feature ${featureId} to plan ${plan.name} with access level: ${accessLevel}`\n        );\n        syncCount++;\n      } catch (error) {\n        console.error(`Error adding feature ${featureId} to plan ${plan.name}:`, error);\n        errorCount++;\n      }\n    }\n    console.log(\n      `Feature synchronization complete! Synchronized with ${syncCount} plans. Errors: ${errorCount}`\n    );\n  } catch (error) {\n    console.error(\"Error synchronizing feature with plans:\", error);\n  }\n}\nconst GET = async ({ cookies }) => {\n  const token = cookies.get(\"auth_token\");\n  if (!token) return new Response(\"Unauthorized\", { status: 401 });\n  const userData = await verifySessionToken(token);\n  if (!userData?.id) return new Response(\"Unauthorized\", { status: 401 });\n  const user = await prisma.user.findUnique({\n    where: { id: userData.id },\n    select: { isAdmin: true, role: true }\n  });\n  if (!user || !user.isAdmin && user.role !== \"admin\") {\n    return new Response(\"Unauthorized\", { status: 401 });\n  }\n  try {\n    try {\n      await initializeFeatureData();\n    } catch (initError) {\n      console.warn(\"Error initializing features:\", initError);\n    }\n    const features = await prisma.feature.findMany({\n      include: {\n        limits: true\n      },\n      orderBy: {\n        category: \"asc\"\n      }\n    });\n    return json({ features });\n  } catch (error) {\n    console.error(\"Error loading features:\", error);\n    return new Response(`Failed to load features: ${error.message}`, { status: 500 });\n  }\n};\nconst POST = async ({ cookies, request }) => {\n  const token = cookies.get(\"auth_token\");\n  if (!token) return new Response(\"Unauthorized\", { status: 401 });\n  const userData = await verifySessionToken(token);\n  if (!userData?.id) return new Response(\"Unauthorized\", { status: 401 });\n  const user = await prisma.user.findUnique({\n    where: { id: userData.id },\n    select: { isAdmin: true, role: true }\n  });\n  if (!user || !user.isAdmin && user.role !== \"admin\") {\n    return new Response(\"Unauthorized\", { status: 401 });\n  }\n  try {\n    const requestData = await request.json();\n    const { action } = requestData;\n    if (action === \"add_feature\") {\n      const { feature } = requestData;\n      if (!feature || !feature.id || !feature.name) {\n        return new Response(\"Invalid feature data\", { status: 400 });\n      }\n      const existingFeature = await prisma.feature.findUnique({\n        where: { id: feature.id }\n      });\n      if (existingFeature) {\n        return new Response(`Feature with ID ${feature.id} already exists`, { status: 400 });\n      }\n      const newFeature = await prisma.$transaction(async (tx) => {\n        const createdFeature = await tx.feature.create({\n          data: {\n            id: feature.id,\n            name: feature.name,\n            description: feature.description || \"\",\n            category: feature.category || \"general\",\n            icon: feature.icon || null,\n            beta: feature.beta || false,\n            updatedAt: /* @__PURE__ */ new Date()\n          }\n        });\n        if (feature.limits && Array.isArray(feature.limits) && feature.limits.length > 0) {\n          console.log(`Creating ${feature.limits.length} limits for feature ${feature.id}`);\n          for (const limit of feature.limits) {\n            await tx.featureLimit.create({\n              data: {\n                featureId: feature.id,\n                id: limit.id,\n                name: limit.name,\n                description: limit.description || \"\",\n                defaultValue: limit.defaultValue?.toString() || \"10\",\n                type: limit.type || \"monthly\",\n                unit: limit.unit || null,\n                resetDay: limit.resetDay || null\n              }\n            });\n          }\n        }\n        return createdFeature;\n      });\n      await syncFeatureWithPlans(feature.id);\n      return json({\n        success: true,\n        feature: newFeature,\n        message: `Feature ${feature.name} created successfully`\n      });\n    }\n    if (action === \"remove_feature\") {\n      const { featureId } = requestData;\n      if (!featureId) {\n        return new Response(\"Invalid feature ID\", { status: 400 });\n      }\n      const existingFeature = await prisma.feature.findUnique({\n        where: { id: featureId }\n      });\n      if (!existingFeature) {\n        return new Response(`Feature with ID ${featureId} not found`, { status: 404 });\n      }\n      await prisma.$transaction(async (tx) => {\n        const planFeatures = await tx.planFeature.findMany({\n          where: { featureId },\n          select: { id: true }\n        });\n        if (planFeatures.length > 0) {\n          const planFeatureIds = planFeatures.map((pf) => pf.id);\n          await tx.planFeatureLimit.deleteMany({\n            where: { planFeatureId: { in: planFeatureIds } }\n          });\n        }\n        await tx.planFeature.deleteMany({\n          where: { featureId }\n        });\n        await tx.featureLimit.deleteMany({\n          where: { featureId }\n        });\n        await tx.feature.delete({\n          where: { id: featureId }\n        });\n      });\n      return json({\n        success: true,\n        message: `Feature ${featureId} removed successfully`\n      });\n    }\n    if (action === \"update_feature\") {\n      const { feature } = requestData;\n      if (!feature || !feature.id) {\n        return new Response(\"Invalid feature data\", { status: 400 });\n      }\n      const existingFeature = await prisma.feature.findUnique({\n        where: { id: feature.id }\n      });\n      if (!existingFeature) {\n        return new Response(`Feature with ID ${feature.id} not found`, { status: 404 });\n      }\n      const updatedFeature = await prisma.$transaction(async (tx) => {\n        const updated = await tx.feature.update({\n          where: { id: feature.id },\n          data: {\n            name: feature.name,\n            description: feature.description || \"\",\n            category: feature.category || \"general\",\n            icon: feature.icon || null,\n            beta: feature.beta || false,\n            updatedAt: /* @__PURE__ */ new Date()\n          },\n          include: {\n            limits: true\n          }\n        });\n        if (feature.limits && Array.isArray(feature.limits)) {\n          console.log(`Updating limits for feature ${feature.id}`);\n          const existingLimits = await tx.featureLimit.findMany({\n            where: { featureId: feature.id }\n          });\n          const existingLimitIds = existingLimits.map((limit) => limit.id);\n          const newLimitIds = feature.limits.map((limit) => limit.id);\n          const limitsToDelete = existingLimitIds.filter((id) => !newLimitIds.includes(id));\n          if (limitsToDelete.length > 0) {\n            await tx.featureLimit.deleteMany({\n              where: {\n                id: { in: limitsToDelete },\n                featureId: feature.id\n              }\n            });\n          }\n          for (const limit of feature.limits) {\n            if (existingLimitIds.includes(limit.id)) {\n              await tx.featureLimit.update({\n                where: {\n                  id: limit.id\n                },\n                data: {\n                  name: limit.name,\n                  description: limit.description || \"\",\n                  defaultValue: limit.defaultValue?.toString() || \"10\",\n                  type: limit.type || \"monthly\",\n                  unit: limit.unit || null,\n                  resetDay: limit.resetDay || null\n                }\n              });\n            } else {\n              await tx.featureLimit.create({\n                data: {\n                  id: limit.id,\n                  featureId: feature.id,\n                  name: limit.name,\n                  description: limit.description || \"\",\n                  defaultValue: limit.defaultValue?.toString() || \"10\",\n                  type: limit.type || \"monthly\",\n                  unit: limit.unit || null,\n                  resetDay: limit.resetDay || null\n                }\n              });\n            }\n          }\n        }\n        return await tx.feature.findUnique({\n          where: { id: feature.id },\n          include: {\n            limits: true\n          }\n        });\n      });\n      return json({\n        success: true,\n        feature: updatedFeature,\n        message: `Feature ${feature.name} updated successfully`\n      });\n    }\n    if (action === \"sync_features\") {\n      try {\n        await initializeFeatureData();\n        const features = await prisma.feature.findMany();\n        const plans = await prisma.plan.findMany({\n          include: {\n            features: {\n              include: {\n                limits: true\n              }\n            }\n          }\n        });\n        const changes = {\n          total: 0,\n          byPlan: {}\n        };\n        for (const plan of plans) {\n          changes.byPlan[plan.id] = 0;\n          const existingFeatureIds = plan.features.map((pf) => pf.featureId);\n          const missingFeatures = features.filter(\n            (feature) => !existingFeatureIds.includes(feature.id)\n          );\n          if (missingFeatures.length > 0) {\n            let defaultAccessLevel = \"included\";\n            if (plan.id === \"free\" || plan.name.toLowerCase() === \"free\") {\n              defaultAccessLevel = \"not_included\";\n              const includedCategories = [\"core\"];\n              const limitedCategories = [\"resume\", \"job_search\", \"applications\"];\n              const includedFeatures = [\"application_tracker\", \"application_submit\"];\n              const limitedFeatures = [\n                \"cover_letter_generator\",\n                \"application_tracking\",\n                \"resume_scanner\",\n                \"resume_builder\"\n              ];\n              const featureAssignments = missingFeatures.map((feature) => {\n                let accessLevel = defaultAccessLevel;\n                if (includedFeatures.includes(feature.id)) {\n                  accessLevel = \"included\";\n                } else if (limitedFeatures.includes(feature.id)) {\n                  accessLevel = \"limited\";\n                } else if (includedCategories.includes(feature.category)) {\n                  accessLevel = \"included\";\n                } else if (limitedCategories.includes(feature.category)) {\n                  accessLevel = \"limited\";\n                }\n                return {\n                  planId: plan.id,\n                  featureId: feature.id,\n                  accessLevel\n                };\n              });\n              await prisma.planFeature.createMany({\n                data: featureAssignments\n              });\n              changes.total += featureAssignments.length;\n              changes.byPlan[plan.id] = featureAssignments.length;\n            } else {\n              const featureAssignments = missingFeatures.map((feature) => ({\n                planId: plan.id,\n                featureId: feature.id,\n                accessLevel: defaultAccessLevel\n              }));\n              await prisma.planFeature.createMany({\n                data: featureAssignments\n              });\n              changes.total += featureAssignments.length;\n              changes.byPlan[plan.id] = featureAssignments.length;\n            }\n          }\n        }\n        return json({\n          success: true,\n          message: `Synced features across all plans. Added ${changes.total} missing feature assignments.`,\n          changes\n        });\n      } catch (error) {\n        console.error(\"Error syncing features:\", error);\n        return json(\n          {\n            success: false,\n            error: \"Failed to sync features\",\n            details: error.message\n          },\n          { status: 500 }\n        );\n      }\n    }\n    return new Response(\"Invalid action\", { status: 400 });\n  } catch (error) {\n    console.error(\"Error managing features:\", error);\n    return new Response(`Failed to manage features: ${error.message}`, { status: 500 });\n  }\n};\nexport {\n  GET,\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAKA,eAAe,oBAAoB,CAAC,SAAS,EAAE;AAC/C,EAAE,IAAI;AACN,IAAI,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC7C,MAAM,OAAO,EAAE;AACf,QAAQ,QAAQ,EAAE;AAClB,UAAU,OAAO,EAAE;AACnB,YAAY,MAAM,EAAE;AACpB;AACA;AACA;AACA,KAAK,CAAC;AACN,IAAI,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;AAC5B,MAAM,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC;AACxD,MAAM;AACN;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,gBAAgB,EAAE,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AAC7E,IAAI,MAAM,qBAAqB,GAAG,CAAC,QAAQ,EAAE,UAAU,KAAK;AAC5D,MAAM,MAAM,aAAa,GAAG,QAAQ,CAAC,WAAW,EAAE;AAClD,MAAM,IAAI,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,UAAU,KAAK,kBAAkB,IAAI,UAAU,KAAK,0BAA0B,EAAE;AAC9H,QAAQ,OAAO,kBAAkB,CAAC,QAAQ;AAC1C;AACA,MAAM,IAAI,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;AAC1C,QAAQ,IAAI,UAAU,KAAK,YAAY,IAAI,UAAU,KAAK,gBAAgB,IAAI,UAAU,KAAK,sBAAsB,EAAE;AACrH,UAAU,OAAO,kBAAkB,CAAC,OAAO;AAC3C;AACA,QAAQ,OAAO,kBAAkB,CAAC,WAAW;AAC7C;AACA,MAAM,IAAI,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,aAAa,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;AAChF,QAAQ,IAAI,UAAU,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE;AACrH,UAAU,OAAO,kBAAkB,CAAC,WAAW;AAC/C;AACA,QAAQ,OAAO,kBAAkB,CAAC,OAAO;AACzC;AACA,MAAM,IAAI,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,aAAa,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;AAC9E,QAAQ,IAAI,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,UAAU,KAAK,6BAA6B,EAAE;AAC1F,UAAU,OAAO,kBAAkB,CAAC,OAAO;AAC3C;AACA,QAAQ,OAAO,kBAAkB,CAAC,SAAS;AAC3C;AACA,MAAM,IAAI,aAAa,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,aAAa,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;AACtF,QAAQ,OAAO,kBAAkB,CAAC,SAAS;AAC3C;AACA,MAAM,OAAO,kBAAkB,CAAC,OAAO;AACvC,KAAK;AACL,IAAI,IAAI,SAAS,GAAG,CAAC;AACrB,IAAI,IAAI,UAAU,GAAG,CAAC;AACtB,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;AAC9B,MAAM,IAAI;AACV,QAAQ,MAAM,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,KAAK,SAAS,CAAC;AACpF,QAAQ,IAAI,eAAe,EAAE;AAC7B,UAAU,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,SAAS,CAAC,CAAC,CAAC;AAC3E,UAAU;AACV;AACA,QAAQ,MAAM,WAAW,GAAG,qBAAqB,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC;AACvE,QAAQ,MAAM,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;AACxC,UAAU,IAAI,EAAE;AAChB,YAAY,MAAM,EAAE,IAAI,CAAC,EAAE;AAC3B,YAAY,SAAS;AACrB,YAAY;AACZ;AACA,SAAS,CAAC;AACV,QAAQ,OAAO,CAAC,GAAG;AACnB,UAAU,CAAC,cAAc,EAAE,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,WAAW,CAAC;AAC5F,SAAS;AACT,QAAQ,SAAS,EAAE;AACnB,OAAO,CAAC,OAAO,KAAK,EAAE;AACtB,QAAQ,OAAO,CAAC,KAAK,CAAC,CAAC,qBAAqB,EAAE,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;AACvF,QAAQ,UAAU,EAAE;AACpB;AACA;AACA,IAAI,OAAO,CAAC,GAAG;AACf,MAAM,CAAC,oDAAoD,EAAE,SAAS,CAAC,gBAAgB,EAAE,UAAU,CAAC;AACpG,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC;AACnE;AACA;AACK,MAAC,GAAG,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK;AACnC,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE,EAAE,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AAClD,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE,EAAE,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAC5C,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE;AAC9B,IAAI,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AACvC,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;AACvD,IAAI,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxD;AACA,EAAE,IAAI;AACN,IAAI,IAAI;AACR,MAAM,MAAM,qBAAqB,EAAE;AACnC,KAAK,CAAC,OAAO,SAAS,EAAE;AACxB,MAAM,OAAO,CAAC,IAAI,CAAC,8BAA8B,EAAE,SAAS,CAAC;AAC7D;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;AACnD,MAAM,OAAO,EAAE;AACf,QAAQ,MAAM,EAAE;AAChB,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,QAAQ,EAAE;AAClB;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,EAAE,QAAQ,EAAE,CAAC;AAC7B,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC;AACnD,IAAI,OAAO,IAAI,QAAQ,CAAC,CAAC,yBAAyB,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACrF;AACA;AACK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AAC7C,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE,EAAE,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AAClD,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE,EAAE,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAC5C,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE;AAC9B,IAAI,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AACvC,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;AACvD,IAAI,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxD;AACA,EAAE,IAAI;AACN,IAAI,MAAM,WAAW,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AAC5C,IAAI,MAAM,EAAE,MAAM,EAAE,GAAG,WAAW;AAClC,IAAI,IAAI,MAAM,KAAK,aAAa,EAAE;AAClC,MAAM,MAAM,EAAE,OAAO,EAAE,GAAG,WAAW;AACrC,MAAM,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;AACpD,QAAQ,OAAO,IAAI,QAAQ,CAAC,sBAAsB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpE;AACA,MAAM,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;AAC9D,QAAQ,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE;AAC/B,OAAO,CAAC;AACR,MAAM,IAAI,eAAe,EAAE;AAC3B,QAAQ,OAAO,IAAI,QAAQ,CAAC,CAAC,gBAAgB,EAAE,OAAO,CAAC,EAAE,CAAC,eAAe,CAAC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC5F;AACA,MAAM,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK;AACjE,QAAQ,MAAM,cAAc,GAAG,MAAM,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC;AACvD,UAAU,IAAI,EAAE;AAChB,YAAY,EAAE,EAAE,OAAO,CAAC,EAAE;AAC1B,YAAY,IAAI,EAAE,OAAO,CAAC,IAAI;AAC9B,YAAY,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,EAAE;AAClD,YAAY,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,SAAS;AACnD,YAAY,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,IAAI;AACtC,YAAY,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,KAAK;AACvC,YAAY,SAAS,kBAAkB,IAAI,IAAI;AAC/C;AACA,SAAS,CAAC;AACV,QAAQ,IAAI,OAAO,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;AAC1F,UAAU,OAAO,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,oBAAoB,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3F,UAAU,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,MAAM,EAAE;AAC9C,YAAY,MAAM,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC;AACzC,cAAc,IAAI,EAAE;AACpB,gBAAgB,SAAS,EAAE,OAAO,CAAC,EAAE;AACrC,gBAAgB,EAAE,EAAE,KAAK,CAAC,EAAE;AAC5B,gBAAgB,IAAI,EAAE,KAAK,CAAC,IAAI;AAChC,gBAAgB,WAAW,EAAE,KAAK,CAAC,WAAW,IAAI,EAAE;AACpD,gBAAgB,YAAY,EAAE,KAAK,CAAC,YAAY,EAAE,QAAQ,EAAE,IAAI,IAAI;AACpE,gBAAgB,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,SAAS;AAC7C,gBAAgB,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,IAAI;AACxC,gBAAgB,QAAQ,EAAE,KAAK,CAAC,QAAQ,IAAI;AAC5C;AACA,aAAa,CAAC;AACd;AACA;AACA,QAAQ,OAAO,cAAc;AAC7B,OAAO,CAAC;AACR,MAAM,MAAM,oBAAoB,CAAC,OAAO,CAAC,EAAE,CAAC;AAC5C,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,OAAO,EAAE,UAAU;AAC3B,QAAQ,OAAO,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,CAAC,qBAAqB;AAC9D,OAAO,CAAC;AACR;AACA,IAAI,IAAI,MAAM,KAAK,gBAAgB,EAAE;AACrC,MAAM,MAAM,EAAE,SAAS,EAAE,GAAG,WAAW;AACvC,MAAM,IAAI,CAAC,SAAS,EAAE;AACtB,QAAQ,OAAO,IAAI,QAAQ,CAAC,oBAAoB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE;AACA,MAAM,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;AAC9D,QAAQ,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS;AAC9B,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,eAAe,EAAE;AAC5B,QAAQ,OAAO,IAAI,QAAQ,CAAC,CAAC,gBAAgB,EAAE,SAAS,CAAC,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtF;AACA,MAAM,MAAM,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK;AAC9C,QAAQ,MAAM,YAAY,GAAG,MAAM,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC;AAC3D,UAAU,KAAK,EAAE,EAAE,SAAS,EAAE;AAC9B,UAAU,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI;AAC5B,SAAS,CAAC;AACV,QAAQ,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;AACrC,UAAU,MAAM,cAAc,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;AAChE,UAAU,MAAM,EAAE,CAAC,gBAAgB,CAAC,UAAU,CAAC;AAC/C,YAAY,KAAK,EAAE,EAAE,aAAa,EAAE,EAAE,EAAE,EAAE,cAAc,EAAE;AAC1D,WAAW,CAAC;AACZ;AACA,QAAQ,MAAM,EAAE,CAAC,WAAW,CAAC,UAAU,CAAC;AACxC,UAAU,KAAK,EAAE,EAAE,SAAS;AAC5B,SAAS,CAAC;AACV,QAAQ,MAAM,EAAE,CAAC,YAAY,CAAC,UAAU,CAAC;AACzC,UAAU,KAAK,EAAE,EAAE,SAAS;AAC5B,SAAS,CAAC;AACV,QAAQ,MAAM,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC;AAChC,UAAU,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS;AAChC,SAAS,CAAC;AACV,OAAO,CAAC;AACR,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,OAAO,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC,qBAAqB;AAC3D,OAAO,CAAC;AACR;AACA,IAAI,IAAI,MAAM,KAAK,gBAAgB,EAAE;AACrC,MAAM,MAAM,EAAE,OAAO,EAAE,GAAG,WAAW;AACrC,MAAM,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE;AACnC,QAAQ,OAAO,IAAI,QAAQ,CAAC,sBAAsB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpE;AACA,MAAM,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;AAC9D,QAAQ,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE;AAC/B,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,eAAe,EAAE;AAC5B,QAAQ,OAAO,IAAI,QAAQ,CAAC,CAAC,gBAAgB,EAAE,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACvF;AACA,MAAM,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK;AACrE,QAAQ,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC;AAChD,UAAU,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE;AACnC,UAAU,IAAI,EAAE;AAChB,YAAY,IAAI,EAAE,OAAO,CAAC,IAAI;AAC9B,YAAY,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,EAAE;AAClD,YAAY,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,SAAS;AACnD,YAAY,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,IAAI;AACtC,YAAY,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,KAAK;AACvC,YAAY,SAAS,kBAAkB,IAAI,IAAI;AAC/C,WAAW;AACX,UAAU,OAAO,EAAE;AACnB,YAAY,MAAM,EAAE;AACpB;AACA,SAAS,CAAC;AACV,QAAQ,IAAI,OAAO,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;AAC7D,UAAU,OAAO,CAAC,GAAG,CAAC,CAAC,4BAA4B,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;AAClE,UAAU,MAAM,cAAc,GAAG,MAAM,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC;AAChE,YAAY,KAAK,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE;AAC1C,WAAW,CAAC;AACZ,UAAU,MAAM,gBAAgB,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC;AAC1E,UAAU,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC;AACrE,UAAU,MAAM,cAAc,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;AAC3F,UAAU,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE;AACzC,YAAY,MAAM,EAAE,CAAC,YAAY,CAAC,UAAU,CAAC;AAC7C,cAAc,KAAK,EAAE;AACrB,gBAAgB,EAAE,EAAE,EAAE,EAAE,EAAE,cAAc,EAAE;AAC1C,gBAAgB,SAAS,EAAE,OAAO,CAAC;AACnC;AACA,aAAa,CAAC;AACd;AACA,UAAU,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,MAAM,EAAE;AAC9C,YAAY,IAAI,gBAAgB,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE;AACrD,cAAc,MAAM,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC;AAC3C,gBAAgB,KAAK,EAAE;AACvB,kBAAkB,EAAE,EAAE,KAAK,CAAC;AAC5B,iBAAiB;AACjB,gBAAgB,IAAI,EAAE;AACtB,kBAAkB,IAAI,EAAE,KAAK,CAAC,IAAI;AAClC,kBAAkB,WAAW,EAAE,KAAK,CAAC,WAAW,IAAI,EAAE;AACtD,kBAAkB,YAAY,EAAE,KAAK,CAAC,YAAY,EAAE,QAAQ,EAAE,IAAI,IAAI;AACtE,kBAAkB,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,SAAS;AAC/C,kBAAkB,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,IAAI;AAC1C,kBAAkB,QAAQ,EAAE,KAAK,CAAC,QAAQ,IAAI;AAC9C;AACA,eAAe,CAAC;AAChB,aAAa,MAAM;AACnB,cAAc,MAAM,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC;AAC3C,gBAAgB,IAAI,EAAE;AACtB,kBAAkB,EAAE,EAAE,KAAK,CAAC,EAAE;AAC9B,kBAAkB,SAAS,EAAE,OAAO,CAAC,EAAE;AACvC,kBAAkB,IAAI,EAAE,KAAK,CAAC,IAAI;AAClC,kBAAkB,WAAW,EAAE,KAAK,CAAC,WAAW,IAAI,EAAE;AACtD,kBAAkB,YAAY,EAAE,KAAK,CAAC,YAAY,EAAE,QAAQ,EAAE,IAAI,IAAI;AACtE,kBAAkB,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,SAAS;AAC/C,kBAAkB,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,IAAI;AAC1C,kBAAkB,QAAQ,EAAE,KAAK,CAAC,QAAQ,IAAI;AAC9C;AACA,eAAe,CAAC;AAChB;AACA;AACA;AACA,QAAQ,OAAO,MAAM,EAAE,CAAC,OAAO,CAAC,UAAU,CAAC;AAC3C,UAAU,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE;AACnC,UAAU,OAAO,EAAE;AACnB,YAAY,MAAM,EAAE;AACpB;AACA,SAAS,CAAC;AACV,OAAO,CAAC;AACR,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,OAAO,EAAE,cAAc;AAC/B,QAAQ,OAAO,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,CAAC,qBAAqB;AAC9D,OAAO,CAAC;AACR;AACA,IAAI,IAAI,MAAM,KAAK,eAAe,EAAE;AACpC,MAAM,IAAI;AACV,QAAQ,MAAM,qBAAqB,EAAE;AACrC,QAAQ,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE;AACxD,QAAQ,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;AACjD,UAAU,OAAO,EAAE;AACnB,YAAY,QAAQ,EAAE;AACtB,cAAc,OAAO,EAAE;AACvB,gBAAgB,MAAM,EAAE;AACxB;AACA;AACA;AACA,SAAS,CAAC;AACV,QAAQ,MAAM,OAAO,GAAG;AACxB,UAAU,KAAK,EAAE,CAAC;AAClB,UAAU,MAAM,EAAE;AAClB,SAAS;AACT,QAAQ,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;AAClC,UAAU,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC;AACrC,UAAU,MAAM,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,SAAS,CAAC;AAC5E,UAAU,MAAM,eAAe,GAAG,QAAQ,CAAC,MAAM;AACjD,YAAY,CAAC,OAAO,KAAK,CAAC,kBAAkB,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;AAChE,WAAW;AACX,UAAU,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;AAC1C,YAAY,IAAI,kBAAkB,GAAG,UAAU;AAC/C,YAAY,IAAI,IAAI,CAAC,EAAE,KAAK,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,MAAM,EAAE;AAC1E,cAAc,kBAAkB,GAAG,cAAc;AACjD,cAAc,MAAM,kBAAkB,GAAG,CAAC,MAAM,CAAC;AACjD,cAAc,MAAM,iBAAiB,GAAG,CAAC,QAAQ,EAAE,YAAY,EAAE,cAAc,CAAC;AAChF,cAAc,MAAM,gBAAgB,GAAG,CAAC,qBAAqB,EAAE,oBAAoB,CAAC;AACpF,cAAc,MAAM,eAAe,GAAG;AACtC,gBAAgB,wBAAwB;AACxC,gBAAgB,sBAAsB;AACtC,gBAAgB,gBAAgB;AAChC,gBAAgB;AAChB,eAAe;AACf,cAAc,MAAM,kBAAkB,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK;AAC1E,gBAAgB,IAAI,WAAW,GAAG,kBAAkB;AACpD,gBAAgB,IAAI,gBAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;AAC3D,kBAAkB,WAAW,GAAG,UAAU;AAC1C,iBAAiB,MAAM,IAAI,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;AACjE,kBAAkB,WAAW,GAAG,SAAS;AACzC,iBAAiB,MAAM,IAAI,kBAAkB,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AAC1E,kBAAkB,WAAW,GAAG,UAAU;AAC1C,iBAAiB,MAAM,IAAI,iBAAiB,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AACzE,kBAAkB,WAAW,GAAG,SAAS;AACzC;AACA,gBAAgB,OAAO;AACvB,kBAAkB,MAAM,EAAE,IAAI,CAAC,EAAE;AACjC,kBAAkB,SAAS,EAAE,OAAO,CAAC,EAAE;AACvC,kBAAkB;AAClB,iBAAiB;AACjB,eAAe,CAAC;AAChB,cAAc,MAAM,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;AAClD,gBAAgB,IAAI,EAAE;AACtB,eAAe,CAAC;AAChB,cAAc,OAAO,CAAC,KAAK,IAAI,kBAAkB,CAAC,MAAM;AACxD,cAAc,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,kBAAkB,CAAC,MAAM;AACjE,aAAa,MAAM;AACnB,cAAc,MAAM,kBAAkB,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,OAAO,MAAM;AAC3E,gBAAgB,MAAM,EAAE,IAAI,CAAC,EAAE;AAC/B,gBAAgB,SAAS,EAAE,OAAO,CAAC,EAAE;AACrC,gBAAgB,WAAW,EAAE;AAC7B,eAAe,CAAC,CAAC;AACjB,cAAc,MAAM,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;AAClD,gBAAgB,IAAI,EAAE;AACtB,eAAe,CAAC;AAChB,cAAc,OAAO,CAAC,KAAK,IAAI,kBAAkB,CAAC,MAAM;AACxD,cAAc,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,kBAAkB,CAAC,MAAM;AACjE;AACA;AACA;AACA,QAAQ,OAAO,IAAI,CAAC;AACpB,UAAU,OAAO,EAAE,IAAI;AACvB,UAAU,OAAO,EAAE,CAAC,wCAAwC,EAAE,OAAO,CAAC,KAAK,CAAC,6BAA6B,CAAC;AAC1G,UAAU;AACV,SAAS,CAAC;AACV,OAAO,CAAC,OAAO,KAAK,EAAE;AACtB,QAAQ,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC;AACvD,QAAQ,OAAO,IAAI;AACnB,UAAU;AACV,YAAY,OAAO,EAAE,KAAK;AAC1B,YAAY,KAAK,EAAE,yBAAyB;AAC5C,YAAY,OAAO,EAAE,KAAK,CAAC;AAC3B,WAAW;AACX,UAAU,EAAE,MAAM,EAAE,GAAG;AACvB,SAAS;AACT;AACA;AACA,IAAI,OAAO,IAAI,QAAQ,CAAC,gBAAgB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1D,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC;AACpD,IAAI,OAAO,IAAI,QAAQ,CAAC,CAAC,2BAA2B,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACvF;AACA;;;;"}