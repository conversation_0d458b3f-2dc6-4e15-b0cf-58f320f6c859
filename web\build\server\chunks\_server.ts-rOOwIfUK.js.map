{"version": 3, "file": "_server.ts-rOOwIfUK.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/admin/users/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../../../chunks/auth.js\";\nconst GET = async ({ cookies }) => {\n  const token = cookies.get(\"auth_token\");\n  if (!token) return new Response(\"Unauthorized\", { status: 401 });\n  const userData = await verifySessionToken(token);\n  if (!userData?.id) return new Response(\"Unauthorized\", { status: 401 });\n  const user = await prisma.user.findUnique({\n    where: { id: userData.id },\n    select: { isAdmin: true, role: true }\n  });\n  if (!user || !user.isAdmin && user.role !== \"admin\") {\n    return new Response(\"Unauthorized\", { status: 401 });\n  }\n  try {\n    const users = await prisma.user.findMany({\n      select: {\n        id: true,\n        email: true,\n        name: true,\n        role: true,\n        isAdmin: true,\n        createdAt: true,\n        updatedAt: true,\n        stripeCustomerId: true,\n        subscriptions: {\n          orderBy: { createdAt: \"desc\" },\n          take: 1\n        }\n      },\n      orderBy: { createdAt: \"desc\" }\n    });\n    const formattedUsers = users.map((user2) => {\n      const subscription = user2.subscriptions[0];\n      return {\n        id: user2.id,\n        email: user2.email,\n        name: user2.name,\n        role: user2.role,\n        isAdmin: user2.isAdmin,\n        createdAt: user2.createdAt,\n        updatedAt: user2.updatedAt,\n        stripeCustomerId: user2.stripeCustomerId,\n        subscription: subscription ? {\n          id: subscription.id,\n          status: subscription.status,\n          currentPeriodStart: subscription.currentPeriodStart,\n          currentPeriodEnd: subscription.currentPeriodEnd,\n          cancelAtPeriodEnd: subscription.cancelAtPeriodEnd\n        } : null\n      };\n    });\n    return json(formattedUsers);\n  } catch (error) {\n    console.error(\"Error fetching users:\", error);\n    return new Response(`Failed to fetch users: ${error.message}`, { status: 500 });\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;;;;;;AAGK,MAAC,GAAG,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK;AACnC,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE,EAAE,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AAClD,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE,EAAE,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAC5C,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE;AAC9B,IAAI,MAAM,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AACvC,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;AACvD,IAAI,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxD;AACA,EAAE,IAAI;AACN,IAAI,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC7C,MAAM,MAAM,EAAE;AACd,QAAQ,EAAE,EAAE,IAAI;AAChB,QAAQ,KAAK,EAAE,IAAI;AACnB,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,SAAS,EAAE,IAAI;AACvB,QAAQ,SAAS,EAAE,IAAI;AACvB,QAAQ,gBAAgB,EAAE,IAAI;AAC9B,QAAQ,aAAa,EAAE;AACvB,UAAU,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;AACxC,UAAU,IAAI,EAAE;AAChB;AACA,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM;AAClC,KAAK,CAAC;AACN,IAAI,MAAM,cAAc,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK;AAChD,MAAM,MAAM,YAAY,GAAG,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC;AACjD,MAAM,OAAO;AACb,QAAQ,EAAE,EAAE,KAAK,CAAC,EAAE;AACpB,QAAQ,KAAK,EAAE,KAAK,CAAC,KAAK;AAC1B,QAAQ,IAAI,EAAE,KAAK,CAAC,IAAI;AACxB,QAAQ,IAAI,EAAE,KAAK,CAAC,IAAI;AACxB,QAAQ,OAAO,EAAE,KAAK,CAAC,OAAO;AAC9B,QAAQ,SAAS,EAAE,KAAK,CAAC,SAAS;AAClC,QAAQ,SAAS,EAAE,KAAK,CAAC,SAAS;AAClC,QAAQ,gBAAgB,EAAE,KAAK,CAAC,gBAAgB;AAChD,QAAQ,YAAY,EAAE,YAAY,GAAG;AACrC,UAAU,EAAE,EAAE,YAAY,CAAC,EAAE;AAC7B,UAAU,MAAM,EAAE,YAAY,CAAC,MAAM;AACrC,UAAU,kBAAkB,EAAE,YAAY,CAAC,kBAAkB;AAC7D,UAAU,gBAAgB,EAAE,YAAY,CAAC,gBAAgB;AACzD,UAAU,iBAAiB,EAAE,YAAY,CAAC;AAC1C,SAAS,GAAG;AACZ,OAAO;AACP,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,cAAc,CAAC;AAC/B,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC;AACjD,IAAI,OAAO,IAAI,QAAQ,CAAC,CAAC,uBAAuB,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnF;AACA;;;;"}