import { p as prisma } from './prisma-Cit_HrSw.js';
import '@prisma/client';

const load = async () => {
  try {
    const jobCollections = await prisma.$queryRaw`
      SELECT * FROM cron.job_collections ORDER BY name ASC
    `;
    return {
      jobCollections
    };
  } catch (error) {
    console.error("Error fetching job collections:", error);
    return {
      jobCollections: []
    };
  }
};

var _page_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 9;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-B2VcjWZV.js')).default;
const server_id = "src/routes/+page.server.ts";
const imports = ["_app/immutable/nodes/9.3i8Aadfq.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/C6g8ubaU.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/B-Xjo-Yt.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/C3w0v0gR.js","_app/immutable/chunks/BvdI7LR8.js","_app/immutable/chunks/B1K98fMG.js","_app/immutable/chunks/ncUU1dSD.js","_app/immutable/chunks/5V1tIHTN.js","_app/immutable/chunks/DM07Bv7T.js","_app/immutable/chunks/DuGukytH.js","_app/immutable/chunks/Cdn-N1RY.js","_app/immutable/chunks/DETxXRrJ.js","_app/immutable/chunks/GwmmX_iF.js","_app/immutable/chunks/BPvdPoic.js","_app/immutable/chunks/iTqMWrIH.js","_app/immutable/chunks/BBa424ah.js","_app/immutable/chunks/D4f2twK-.js","_app/immutable/chunks/w80wGXGd.js","_app/immutable/chunks/DW7T7T22.js","_app/immutable/chunks/BV675lZR.js","_app/immutable/chunks/DvO_AOqy.js","_app/immutable/chunks/yW0TxTga.js","_app/immutable/chunks/D871oxnv.js","_app/immutable/chunks/1zwBog76.js","_app/immutable/chunks/CZ8wIJN8.js","_app/immutable/chunks/-SpbofVw.js","_app/immutable/chunks/CXUk17vb.js","_app/immutable/chunks/rNI1Perp.js","_app/immutable/chunks/D1zde6Ej.js","_app/immutable/chunks/QtAhPN2H.js","_app/immutable/chunks/B_tyjpYb.js","_app/immutable/chunks/CYoZicO9.js","_app/immutable/chunks/C6FI6jUA.js","_app/immutable/chunks/DDUgF6Ik.js","_app/immutable/chunks/Cs0qIT7f.js","_app/immutable/chunks/FAbXdqfL.js","_app/immutable/chunks/CfcZq63z.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/Cf6rS4LV.js","_app/immutable/chunks/DjPYYl4Z.js","_app/immutable/chunks/9r-6KH_O.js"];
const stylesheets = ["_app/immutable/assets/Toaster.DKF17Rty.css","_app/immutable/assets/9.BM-gZ187.css"];
const fonts = [];

export { component, fonts, imports, index, _page_server_ts as server, server_id, stylesheets };
//# sourceMappingURL=9-a7KPyRof.js.map
