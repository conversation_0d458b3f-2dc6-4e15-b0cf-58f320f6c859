{"version": 3, "file": "_server.ts-k9kHjeQ-.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/billing/create-checkout-session/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../../../chunks/auth.js\";\nimport { g as getStripePriceId } from \"../../../../../chunks/stripe2.js\";\nasync function POST({ request, cookies, url }) {\n  const { planId, billingCycle } = await request.json();\n  const token = cookies.get(\"auth_token\");\n  const isProd = process.env.NODE_ENV === \"production\";\n  const priceId = await getStripePriceId(planId, billingCycle);\n  if (!token) return new Response(\"Unauthorized\", { status: 401 });\n  const userData = await verifySessionToken(token);\n  if (!userData?.id) return new Response(\"Unauthorized\", { status: 401 });\n  const stripeSecret = isProd ? process.env.STRIPE_SECRET_KEY_LIVE || \"sk_live_placeholder\" : process.env.STRIPE_SECRET_KEY_TEST || \"sk_test_placeholder\";\n  const Stripe = (await import(\"stripe\")).default;\n  const stripe = new Stripe(stripeSecret, {\n    apiVersion: \"2025-04-30.basil\"\n  });\n  if (!priceId) {\n    const availablePlans = await prisma.plan.findMany({\n      select: { id: true, name: true, stripePriceMonthlyId: true, stripePriceYearlyId: true }\n    });\n    console.error(\"Invalid plan or billing cycle\", {\n      planId,\n      billingCycle,\n      availablePlans: availablePlans.map((p) => p.id),\n      planDetails: availablePlans.find((p) => p.id === planId)\n    });\n    return new Response(\"Invalid plan or billing cycle\", { status: 400 });\n  }\n  let user = await prisma.user.findUnique({ where: { id: userData.id } });\n  if (!user) return new Response(\"User not found\", { status: 404 });\n  if (!user.stripeCustomerId) {\n    const customer = await stripe.customers.create({\n      email: user.email,\n      name: user.name ?? void 0\n    });\n    user = await prisma.user.update({\n      where: { id: user.id },\n      data: { stripeCustomerId: customer.id }\n    });\n  }\n  const YOUR_DOMAIN = `${url.protocol}//${url.host}`;\n  try {\n    console.log(\"Creating Stripe checkout session\", {\n      planId,\n      billingCycle,\n      priceId,\n      customerId: user.stripeCustomerId\n    });\n    const session = await stripe.checkout.sessions.create({\n      mode: \"subscription\",\n      customer: user.stripeCustomerId,\n      payment_method_types: [\"card\"],\n      line_items: [{ price: priceId, quantity: 1 }],\n      success_url: `${YOUR_DOMAIN}/dashboard/settings/billing?checkout=success`,\n      cancel_url: `${YOUR_DOMAIN}/dashboard/settings/billing?checkout=cancel`\n    });\n    if (!session.url) {\n      console.error(\"Stripe session created but no URL returned\", { session });\n      return new Response(\"Failed to create checkout session\", { status: 500 });\n    }\n    return json({ url: session.url });\n  } catch (error) {\n    console.error(\"Error creating Stripe checkout session\", {\n      error,\n      planId,\n      billingCycle,\n      priceId\n    });\n    return new Response(`Failed to create checkout session: ${error.message}`, { status: 500 });\n  }\n}\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAIA,eAAe,IAAI,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,EAAE;AAC/C,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACvD,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;AACtD,EAAE,MAAM,OAAO,GAAG,MAAM,gBAAgB,CAAC,MAAM,EAAE,YAAY,CAAC;AAC9D,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE,EAAE,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AAClD,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE,EAAE,MAAM,YAAY,GAAG,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,qBAAqB,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,qBAAqB;AACzJ,EAAE,MAAM,MAAM,GAAG,CAAC,MAAM,OAAO,+BAAQ,CAAC,EAAE,OAAO;AACjD,EAAE,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,YAAY,EAAE;AAC1C,IAAI,UAAU,EAAE;AAChB,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;AACtD,MAAM,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,oBAAoB,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI;AAC3F,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE;AACnD,MAAM,MAAM;AACZ,MAAM,YAAY;AAClB,MAAM,cAAc,EAAE,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;AACrD,MAAM,WAAW,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,MAAM;AAC7D,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,QAAQ,CAAC,+BAA+B,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE;AACA,EAAE,IAAI,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE,EAAE,CAAC;AACzE,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,QAAQ,CAAC,gBAAgB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnE,EAAE,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;AAC9B,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;AACnD,MAAM,KAAK,EAAE,IAAI,CAAC,KAAK;AACvB,MAAM,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI;AACzB,KAAK,CAAC;AACN,IAAI,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AACpC,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;AAC5B,MAAM,IAAI,EAAE,EAAE,gBAAgB,EAAE,QAAQ,CAAC,EAAE;AAC3C,KAAK,CAAC;AACN;AACA,EAAE,MAAM,WAAW,GAAG,CAAC,EAAE,GAAG,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;AACpD,EAAE,IAAI;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,kCAAkC,EAAE;AACpD,MAAM,MAAM;AACZ,MAAM,YAAY;AAClB,MAAM,OAAO;AACb,MAAM,UAAU,EAAE,IAAI,CAAC;AACvB,KAAK,CAAC;AACN,IAAI,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC;AAC1D,MAAM,IAAI,EAAE,cAAc;AAC1B,MAAM,QAAQ,EAAE,IAAI,CAAC,gBAAgB;AACrC,MAAM,oBAAoB,EAAE,CAAC,MAAM,CAAC;AACpC,MAAM,UAAU,EAAE,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;AACnD,MAAM,WAAW,EAAE,CAAC,EAAE,WAAW,CAAC,4CAA4C,CAAC;AAC/E,MAAM,UAAU,EAAE,CAAC,EAAE,WAAW,CAAC,2CAA2C;AAC5E,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;AACtB,MAAM,OAAO,CAAC,KAAK,CAAC,4CAA4C,EAAE,EAAE,OAAO,EAAE,CAAC;AAC9E,MAAM,OAAO,IAAI,QAAQ,CAAC,mCAAmC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC/E;AACA,IAAI,OAAO,IAAI,CAAC,EAAE,GAAG,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC;AACrC,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE;AAC5D,MAAM,KAAK;AACX,MAAM,MAAM;AACZ,MAAM,YAAY;AAClB,MAAM;AACN,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,QAAQ,CAAC,CAAC,mCAAmC,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC/F;AACA;;;;"}