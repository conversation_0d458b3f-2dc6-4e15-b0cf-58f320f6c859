{"version": 3, "file": "_server.ts-C4Ay56uj.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/referrals/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../chunks/prisma.js\";\nfunction generateReferralCode(name, email) {\n  const prefix = name ? name.replace(/[^a-zA-Z]/g, \"\").substring(0, 3).toUpperCase() : email?.substring(0, 3).toUpperCase() || \"REF\";\n  const randomSuffix = Math.random().toString(36).substring(2, 8).toUpperCase();\n  return `${prefix}${randomSuffix}`;\n}\nconst GET = async ({ locals }) => {\n  const user = locals.user;\n  if (!user?.email) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  try {\n    const userData = await prisma.user.findUnique({\n      where: { email: user.email },\n      include: {\n        referralsMade: {\n          include: {\n            referred: {\n              select: {\n                id: true,\n                name: true,\n                email: true,\n                createdAt: true\n              }\n            }\n          },\n          orderBy: { createdAt: \"desc\" }\n        },\n        referrals: {\n          select: {\n            id: true,\n            name: true,\n            email: true,\n            createdAt: true\n          }\n        },\n        referredBy: {\n          select: {\n            id: true,\n            name: true,\n            email: true,\n            referralCode: true\n          }\n        }\n      }\n    });\n    if (!userData) {\n      return json({ error: \"User not found\" }, { status: 404 });\n    }\n    let referralCode = userData.referralCode;\n    if (!referralCode) {\n      referralCode = generateReferralCode(userData.name, userData.email);\n      let attempts = 0;\n      while (attempts < 5) {\n        const existing = await prisma.user.findUnique({\n          where: { referralCode }\n        });\n        if (!existing) break;\n        referralCode = generateReferralCode(userData.name, userData.email);\n        attempts++;\n      }\n      await prisma.user.update({\n        where: { id: userData.id },\n        data: { referralCode }\n      });\n    }\n    const baseUrl = process.env.PUBLIC_BASE_URL || \"http://localhost:5173\";\n    const referralLink = `${baseUrl}/auth/sign-up?ref=${referralCode}`;\n    return json({\n      referralCode,\n      referralLink,\n      referralCount: userData.referralCount ?? 0,\n      referralRewards: userData.referralRewards ?? 0,\n      referrals: userData.referralsMade ?? [],\n      referredBy: userData.referredBy\n    });\n  } catch (error) {\n    console.error(\"Error getting referral data:\", error);\n    return json({ error: \"Failed to get referral data\" }, { status: 500 });\n  }\n};\nconst POST = async ({ request, locals }) => {\n  const user = locals.user;\n  if (!user?.email) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  try {\n    const userData = await prisma.user.findUnique({\n      where: { email: user.email }\n    });\n    if (!userData) {\n      return json({ error: \"User not found\" }, { status: 404 });\n    }\n    const { action, customCode } = await request.json();\n    if (action === \"regenerate\" || action === \"create\") {\n      const user2 = await prisma.user.findUnique({\n        where: { id: userData.id }\n      });\n      if (!user2) {\n        return json({ error: \"User not found\" }, { status: 404 });\n      }\n      let newReferralCode;\n      if (customCode && customCode.length >= 4 && customCode.length <= 12) {\n        if (!/^[A-Za-z0-9]+$/.test(customCode)) {\n          return json(\n            { error: \"Referral code can only contain letters and numbers\" },\n            { status: 400 }\n          );\n        }\n        const existing = await prisma.user.findUnique({\n          where: { referralCode: customCode.toUpperCase() }\n        });\n        if (existing && existing.id !== userData.id) {\n          return json({ error: \"This referral code is already taken\" }, { status: 400 });\n        }\n        newReferralCode = customCode.toUpperCase();\n      } else {\n        newReferralCode = generateReferralCode(user2.name, user2.email);\n        let attempts = 0;\n        while (attempts < 5) {\n          const existing = await prisma.user.findUnique({\n            where: { referralCode: newReferralCode }\n          });\n          if (!existing) break;\n          newReferralCode = generateReferralCode(user2.name, user2.email);\n          attempts++;\n        }\n      }\n      await prisma.$transaction(async (tx) => {\n        let shouldPreserveOldCode = false;\n        if (userData.referralCode) {\n          const existingReferrals = await tx.referral.count({\n            where: {\n              referrerId: userData.id,\n              referralCode: userData.referralCode\n            }\n          });\n          shouldPreserveOldCode = existingReferrals > 0;\n        }\n        if (shouldPreserveOldCode && userData.referralCode) {\n          const existingHistory = await tx.referralCodeHistory.findFirst({\n            where: {\n              userId: userData.id,\n              referralCode: userData.referralCode\n            }\n          });\n          if (!existingHistory) {\n            await tx.referralCodeHistory.create({\n              data: {\n                userId: userData.id,\n                referralCode: userData.referralCode,\n                isActive: false,\n                deactivatedAt: /* @__PURE__ */ new Date(),\n                reason: \"preserved_with_data\",\n                metadata: {\n                  referralCount: await tx.referral.count({\n                    where: {\n                      referrerId: userData.id,\n                      referralCode: userData.referralCode\n                    }\n                  }),\n                  replacedBy: newReferralCode\n                }\n              }\n            });\n          } else {\n            await tx.referralCodeHistory.update({\n              where: { id: existingHistory.id },\n              data: {\n                isActive: false,\n                deactivatedAt: /* @__PURE__ */ new Date(),\n                metadata: {\n                  ...existingHistory.metadata,\n                  replacedBy: newReferralCode\n                }\n              }\n            });\n          }\n        }\n        await tx.user.update({\n          where: { id: userData.id },\n          data: { referralCode: newReferralCode }\n        });\n      });\n      const baseUrl = process.env.PUBLIC_BASE_URL || \"http://localhost:5173\";\n      const referralLink = `${baseUrl}/auth/sign-up?ref=${newReferralCode}`;\n      return json({\n        referralCode: newReferralCode,\n        referralLink,\n        message: \"Referral code updated successfully\"\n      });\n    }\n    return json({ error: \"Invalid action\" }, { status: 400 });\n  } catch (error) {\n    console.error(\"Error updating referral code:\", error);\n    return json({ error: \"Failed to update referral code\" }, { status: 500 });\n  }\n};\nexport {\n  GET,\n  POST\n};\n"], "names": [], "mappings": ";;;;AAEA,SAAS,oBAAoB,CAAC,IAAI,EAAE,KAAK,EAAE;AAC3C,EAAE,MAAM,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,KAAK,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,IAAI,KAAK;AACpI,EAAE,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE;AAC/E,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,EAAE,YAAY,CAAC,CAAC;AACnC;AACK,MAAC,GAAG,GAAG,OAAO,EAAE,MAAM,EAAE,KAAK;AAClC,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE;AACpB,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAClD,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE;AAClC,MAAM,OAAO,EAAE;AACf,QAAQ,aAAa,EAAE;AACvB,UAAU,OAAO,EAAE;AACnB,YAAY,QAAQ,EAAE;AACtB,cAAc,MAAM,EAAE;AACtB,gBAAgB,EAAE,EAAE,IAAI;AACxB,gBAAgB,IAAI,EAAE,IAAI;AAC1B,gBAAgB,KAAK,EAAE,IAAI;AAC3B,gBAAgB,SAAS,EAAE;AAC3B;AACA;AACA,WAAW;AACX,UAAU,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM;AACtC,SAAS;AACT,QAAQ,SAAS,EAAE;AACnB,UAAU,MAAM,EAAE;AAClB,YAAY,EAAE,EAAE,IAAI;AACpB,YAAY,IAAI,EAAE,IAAI;AACtB,YAAY,KAAK,EAAE,IAAI;AACvB,YAAY,SAAS,EAAE;AACvB;AACA,SAAS;AACT,QAAQ,UAAU,EAAE;AACpB,UAAU,MAAM,EAAE;AAClB,YAAY,EAAE,EAAE,IAAI;AACpB,YAAY,IAAI,EAAE,IAAI;AACtB,YAAY,KAAK,EAAE,IAAI;AACvB,YAAY,YAAY,EAAE;AAC1B;AACA;AACA;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,gBAAgB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC/D;AACA,IAAI,IAAI,YAAY,GAAG,QAAQ,CAAC,YAAY;AAC5C,IAAI,IAAI,CAAC,YAAY,EAAE;AACvB,MAAM,YAAY,GAAG,oBAAoB,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC;AACxE,MAAM,IAAI,QAAQ,GAAG,CAAC;AACtB,MAAM,OAAO,QAAQ,GAAG,CAAC,EAAE;AAC3B,QAAQ,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AACtD,UAAU,KAAK,EAAE,EAAE,YAAY;AAC/B,SAAS,CAAC;AACV,QAAQ,IAAI,CAAC,QAAQ,EAAE;AACvB,QAAQ,YAAY,GAAG,oBAAoB,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC;AAC1E,QAAQ,QAAQ,EAAE;AAClB;AACA,MAAM,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AAC/B,QAAQ,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE;AAClC,QAAQ,IAAI,EAAE,EAAE,YAAY;AAC5B,OAAO,CAAC;AACR;AACA,IAAI,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,uBAAuB;AAC1E,IAAI,MAAM,YAAY,GAAG,CAAC,EAAE,OAAO,CAAC,kBAAkB,EAAE,YAAY,CAAC,CAAC;AACtE,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,YAAY;AAClB,MAAM,YAAY;AAClB,MAAM,aAAa,EAAE,QAAQ,CAAC,aAAa,IAAI,CAAC;AAChD,MAAM,eAAe,EAAE,QAAQ,CAAC,eAAe,IAAI,CAAC;AACpD,MAAM,SAAS,EAAE,QAAQ,CAAC,aAAa,IAAI,EAAE;AAC7C,MAAM,UAAU,EAAE,QAAQ,CAAC;AAC3B,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC;AACxD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,6BAA6B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1E;AACA;AACK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AAC5C,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE;AACpB,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAClD,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK;AAChC,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,gBAAgB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC/D;AACA,IAAI,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACvD,IAAI,IAAI,MAAM,KAAK,YAAY,IAAI,MAAM,KAAK,QAAQ,EAAE;AACxD,MAAM,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AACjD,QAAQ,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE;AAChC,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,KAAK,EAAE;AAClB,QAAQ,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,gBAAgB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE;AACA,MAAM,IAAI,eAAe;AACzB,MAAM,IAAI,UAAU,IAAI,UAAU,CAAC,MAAM,IAAI,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,EAAE,EAAE;AAC3E,QAAQ,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AAChD,UAAU,OAAO,IAAI;AACrB,YAAY,EAAE,KAAK,EAAE,oDAAoD,EAAE;AAC3E,YAAY,EAAE,MAAM,EAAE,GAAG;AACzB,WAAW;AACX;AACA,QAAQ,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AACtD,UAAU,KAAK,EAAE,EAAE,YAAY,EAAE,UAAU,CAAC,WAAW,EAAE;AACzD,SAAS,CAAC;AACV,QAAQ,IAAI,QAAQ,IAAI,QAAQ,CAAC,EAAE,KAAK,QAAQ,CAAC,EAAE,EAAE;AACrD,UAAU,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,qCAAqC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxF;AACA,QAAQ,eAAe,GAAG,UAAU,CAAC,WAAW,EAAE;AAClD,OAAO,MAAM;AACb,QAAQ,eAAe,GAAG,oBAAoB,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC;AACvE,QAAQ,IAAI,QAAQ,GAAG,CAAC;AACxB,QAAQ,OAAO,QAAQ,GAAG,CAAC,EAAE;AAC7B,UAAU,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AACxD,YAAY,KAAK,EAAE,EAAE,YAAY,EAAE,eAAe;AAClD,WAAW,CAAC;AACZ,UAAU,IAAI,CAAC,QAAQ,EAAE;AACzB,UAAU,eAAe,GAAG,oBAAoB,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC;AACzE,UAAU,QAAQ,EAAE;AACpB;AACA;AACA,MAAM,MAAM,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK;AAC9C,QAAQ,IAAI,qBAAqB,GAAG,KAAK;AACzC,QAAQ,IAAI,QAAQ,CAAC,YAAY,EAAE;AACnC,UAAU,MAAM,iBAAiB,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC;AAC5D,YAAY,KAAK,EAAE;AACnB,cAAc,UAAU,EAAE,QAAQ,CAAC,EAAE;AACrC,cAAc,YAAY,EAAE,QAAQ,CAAC;AACrC;AACA,WAAW,CAAC;AACZ,UAAU,qBAAqB,GAAG,iBAAiB,GAAG,CAAC;AACvD;AACA,QAAQ,IAAI,qBAAqB,IAAI,QAAQ,CAAC,YAAY,EAAE;AAC5D,UAAU,MAAM,eAAe,GAAG,MAAM,EAAE,CAAC,mBAAmB,CAAC,SAAS,CAAC;AACzE,YAAY,KAAK,EAAE;AACnB,cAAc,MAAM,EAAE,QAAQ,CAAC,EAAE;AACjC,cAAc,YAAY,EAAE,QAAQ,CAAC;AACrC;AACA,WAAW,CAAC;AACZ,UAAU,IAAI,CAAC,eAAe,EAAE;AAChC,YAAY,MAAM,EAAE,CAAC,mBAAmB,CAAC,MAAM,CAAC;AAChD,cAAc,IAAI,EAAE;AACpB,gBAAgB,MAAM,EAAE,QAAQ,CAAC,EAAE;AACnC,gBAAgB,YAAY,EAAE,QAAQ,CAAC,YAAY;AACnD,gBAAgB,QAAQ,EAAE,KAAK;AAC/B,gBAAgB,aAAa,kBAAkB,IAAI,IAAI,EAAE;AACzD,gBAAgB,MAAM,EAAE,qBAAqB;AAC7C,gBAAgB,QAAQ,EAAE;AAC1B,kBAAkB,aAAa,EAAE,MAAM,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC;AACzD,oBAAoB,KAAK,EAAE;AAC3B,sBAAsB,UAAU,EAAE,QAAQ,CAAC,EAAE;AAC7C,sBAAsB,YAAY,EAAE,QAAQ,CAAC;AAC7C;AACA,mBAAmB,CAAC;AACpB,kBAAkB,UAAU,EAAE;AAC9B;AACA;AACA,aAAa,CAAC;AACd,WAAW,MAAM;AACjB,YAAY,MAAM,EAAE,CAAC,mBAAmB,CAAC,MAAM,CAAC;AAChD,cAAc,KAAK,EAAE,EAAE,EAAE,EAAE,eAAe,CAAC,EAAE,EAAE;AAC/C,cAAc,IAAI,EAAE;AACpB,gBAAgB,QAAQ,EAAE,KAAK;AAC/B,gBAAgB,aAAa,kBAAkB,IAAI,IAAI,EAAE;AACzD,gBAAgB,QAAQ,EAAE;AAC1B,kBAAkB,GAAG,eAAe,CAAC,QAAQ;AAC7C,kBAAkB,UAAU,EAAE;AAC9B;AACA;AACA,aAAa,CAAC;AACd;AACA;AACA,QAAQ,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC;AAC7B,UAAU,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE;AACpC,UAAU,IAAI,EAAE,EAAE,YAAY,EAAE,eAAe;AAC/C,SAAS,CAAC;AACV,OAAO,CAAC;AACR,MAAM,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,uBAAuB;AAC5E,MAAM,MAAM,YAAY,GAAG,CAAC,EAAE,OAAO,CAAC,kBAAkB,EAAE,eAAe,CAAC,CAAC;AAC3E,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,YAAY,EAAE,eAAe;AACrC,QAAQ,YAAY;AACpB,QAAQ,OAAO,EAAE;AACjB,OAAO,CAAC;AACR;AACA,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,gBAAgB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7D,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC;AACzD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,gCAAgC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7E;AACA;;;;"}