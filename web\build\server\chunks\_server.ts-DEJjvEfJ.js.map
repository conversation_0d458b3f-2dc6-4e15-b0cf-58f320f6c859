{"version": 3, "file": "_server.ts-DEJjvEfJ.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/resume/manual-parse/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { g as getRedisClient } from \"../../../../../chunks/redis.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nconst POST = async ({ request, locals }) => {\n  try {\n    const user = locals.user;\n    if (!user) return new Response(\"Unauthorized\", { status: 401 });\n    const { resumeId } = await request.json();\n    if (!resumeId) {\n      return json({ error: \"Resume ID is required\" }, { status: 400 });\n    }\n    console.log(`Manually parsing resume ${resumeId}`);\n    const resume = await prisma.resume.findUnique({\n      where: { id: resumeId },\n      include: {\n        document: true\n      }\n    });\n    if (!resume) {\n      return json({ error: \"Resume not found\" }, { status: 404 });\n    }\n    if (resume.document.userId !== user.id) {\n      return json({ error: \"Unauthorized access to resume\" }, { status: 403 });\n    }\n    const profileId = resume.document.profileId;\n    await prisma.resume.update({\n      where: { id: resumeId },\n      data: {\n        isParsed: false,\n        parsedAt: null\n      }\n    });\n    const workerProcessId = `resume-parsing-${resumeId}-${Date.now()}`;\n    try {\n      await prisma.workerProcess.create({\n        data: {\n          id: workerProcessId,\n          type: \"resume-parsing\",\n          status: \"pending\",\n          createdAt: /* @__PURE__ */ new Date(),\n          updatedAt: /* @__PURE__ */ new Date(),\n          data: {\n            resumeId,\n            profileId: profileId || null,\n            userId: user.id,\n            documentId: resume.documentId,\n            timestamp: (/* @__PURE__ */ new Date()).toISOString(),\n            fileUrl: resume.document?.fileUrl ?? null,\n            filePath: resume.document?.filePath ?? null\n          }\n        }\n      });\n      console.log(`Created WorkerProcess record ${workerProcessId}`);\n    } catch (workerProcessError) {\n      console.error(\"Error creating WorkerProcess record:\", workerProcessError);\n      return json({\n        error: \"Failed to create worker process record\",\n        details: String(workerProcessError)\n      }, { status: 500 });\n    }\n    try {\n      const redis = await getRedisClient();\n      if (!redis) {\n        throw new Error(\"Redis client not available\");\n      }\n      const streamName = \"resume-parsing::stream\";\n      try {\n        await redis.xgroup(\"CREATE\", streamName, \"resume-parsing::group\", \"$\", \"MKSTREAM\");\n        console.log(`Created ${streamName} stream group`);\n      } catch (err) {\n        if (!err.message.includes(\"BUSYGROUP\")) {\n          console.error(`Error creating stream group for ${streamName}:`, err);\n        }\n      }\n      const messageId = await redis.xadd(\n        streamName,\n        \"*\",\n        \"job\",\n        JSON.stringify({\n          jobId: workerProcessId,\n          resumeId,\n          fileUrl: resume.document?.fileUrl ?? null,\n          filePath: resume.document?.filePath ?? null,\n          userId: user.id,\n          profileId: profileId || null,\n          timestamp: (/* @__PURE__ */ new Date()).toISOString()\n        })\n      );\n      console.log(`Added job to ${streamName} with message ID ${messageId}`);\n      redis.disconnect();\n      return json({\n        success: true,\n        message: \"Resume parsing job added to the stream\",\n        resumeId,\n        profileId: profileId || null,\n        streamName,\n        messageId,\n        workerProcessId\n      });\n    } catch (redisError) {\n      console.error(\"Redis error:\", redisError);\n      try {\n        await prisma.workerProcess.update({\n          where: { id: workerProcessId },\n          data: {\n            status: \"failed\",\n            error: String(redisError),\n            updatedAt: /* @__PURE__ */ new Date()\n          }\n        });\n      } catch (updateError) {\n        console.error(\"Error updating WorkerProcess record:\", updateError);\n      }\n      return json(\n        {\n          error: \"Failed to add job to Redis\",\n          details: String(redisError),\n          workerProcessId\n        },\n        { status: 500 }\n      );\n    }\n  } catch (error) {\n    console.error(\"Error manually parsing resume:\", error);\n    return json(\n      { error: \"Failed to manually parse resume\", details: String(error) },\n      { status: 500 }\n    );\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;AAGK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AAC5C,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC5B,IAAI,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnE,IAAI,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AAC7C,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtE;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,wBAAwB,EAAE,QAAQ,CAAC,CAAC,CAAC;AACtD,IAAI,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;AAClD,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE;AAC7B,MAAM,OAAO,EAAE;AACf,QAAQ,QAAQ,EAAE;AAClB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,kBAAkB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE;AACA,IAAI,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,KAAK,IAAI,CAAC,EAAE,EAAE;AAC5C,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,+BAA+B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9E;AACA,IAAI,MAAM,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,SAAS;AAC/C,IAAI,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;AAC/B,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE;AAC7B,MAAM,IAAI,EAAE;AACZ,QAAQ,QAAQ,EAAE,KAAK;AACvB,QAAQ,QAAQ,EAAE;AAClB;AACA,KAAK,CAAC;AACN,IAAI,MAAM,eAAe,GAAG,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;AACtE,IAAI,IAAI;AACR,MAAM,MAAM,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;AACxC,QAAQ,IAAI,EAAE;AACd,UAAU,EAAE,EAAE,eAAe;AAC7B,UAAU,IAAI,EAAE,gBAAgB;AAChC,UAAU,MAAM,EAAE,SAAS;AAC3B,UAAU,SAAS,kBAAkB,IAAI,IAAI,EAAE;AAC/C,UAAU,SAAS,kBAAkB,IAAI,IAAI,EAAE;AAC/C,UAAU,IAAI,EAAE;AAChB,YAAY,QAAQ;AACpB,YAAY,SAAS,EAAE,SAAS,IAAI,IAAI;AACxC,YAAY,MAAM,EAAE,IAAI,CAAC,EAAE;AAC3B,YAAY,UAAU,EAAE,MAAM,CAAC,UAAU;AACzC,YAAY,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW,EAAE;AACjE,YAAY,OAAO,EAAE,MAAM,CAAC,QAAQ,EAAE,OAAO,IAAI,IAAI;AACrD,YAAY,QAAQ,EAAE,MAAM,CAAC,QAAQ,EAAE,QAAQ,IAAI;AACnD;AACA;AACA,OAAO,CAAC;AACR,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,eAAe,CAAC,CAAC,CAAC;AACpE,KAAK,CAAC,OAAO,kBAAkB,EAAE;AACjC,MAAM,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,kBAAkB,CAAC;AAC/E,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,KAAK,EAAE,wCAAwC;AACvD,QAAQ,OAAO,EAAE,MAAM,CAAC,kBAAkB;AAC1C,OAAO,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzB;AACA,IAAI,IAAI;AACR,MAAM,MAAM,KAAK,GAAG,MAAM,cAAc,EAAE;AAC1C,MAAM,IAAI,CAAC,KAAK,EAAE;AAClB,QAAQ,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC;AACrD;AACA,MAAM,MAAM,UAAU,GAAG,wBAAwB;AACjD,MAAM,IAAI;AACV,QAAQ,MAAM,KAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,UAAU,EAAE,uBAAuB,EAAE,GAAG,EAAE,UAAU,CAAC;AAC1F,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC,aAAa,CAAC,CAAC;AACzD,OAAO,CAAC,OAAO,GAAG,EAAE;AACpB,QAAQ,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;AAChD,UAAU,OAAO,CAAC,KAAK,CAAC,CAAC,gCAAgC,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC;AAC9E;AACA;AACA,MAAM,MAAM,SAAS,GAAG,MAAM,KAAK,CAAC,IAAI;AACxC,QAAQ,UAAU;AAClB,QAAQ,GAAG;AACX,QAAQ,KAAK;AACb,QAAQ,IAAI,CAAC,SAAS,CAAC;AACvB,UAAU,KAAK,EAAE,eAAe;AAChC,UAAU,QAAQ;AAClB,UAAU,OAAO,EAAE,MAAM,CAAC,QAAQ,EAAE,OAAO,IAAI,IAAI;AACnD,UAAU,QAAQ,EAAE,MAAM,CAAC,QAAQ,EAAE,QAAQ,IAAI,IAAI;AACrD,UAAU,MAAM,EAAE,IAAI,CAAC,EAAE;AACzB,UAAU,SAAS,EAAE,SAAS,IAAI,IAAI;AACtC,UAAU,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC7D,SAAS;AACT,OAAO;AACP,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,UAAU,CAAC,iBAAiB,EAAE,SAAS,CAAC,CAAC,CAAC;AAC5E,MAAM,KAAK,CAAC,UAAU,EAAE;AACxB,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,OAAO,EAAE,wCAAwC;AACzD,QAAQ,QAAQ;AAChB,QAAQ,SAAS,EAAE,SAAS,IAAI,IAAI;AACpC,QAAQ,UAAU;AAClB,QAAQ,SAAS;AACjB,QAAQ;AACR,OAAO,CAAC;AACR,KAAK,CAAC,OAAO,UAAU,EAAE;AACzB,MAAM,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,UAAU,CAAC;AAC/C,MAAM,IAAI;AACV,QAAQ,MAAM,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;AAC1C,UAAU,KAAK,EAAE,EAAE,EAAE,EAAE,eAAe,EAAE;AACxC,UAAU,IAAI,EAAE;AAChB,YAAY,MAAM,EAAE,QAAQ;AAC5B,YAAY,KAAK,EAAE,MAAM,CAAC,UAAU,CAAC;AACrC,YAAY,SAAS,kBAAkB,IAAI,IAAI;AAC/C;AACA,SAAS,CAAC;AACV,OAAO,CAAC,OAAO,WAAW,EAAE;AAC5B,QAAQ,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,WAAW,CAAC;AAC1E;AACA,MAAM,OAAO,IAAI;AACjB,QAAQ;AACR,UAAU,KAAK,EAAE,4BAA4B;AAC7C,UAAU,OAAO,EAAE,MAAM,CAAC,UAAU,CAAC;AACrC,UAAU;AACV,SAAS;AACT,QAAQ,EAAE,MAAM,EAAE,GAAG;AACrB,OAAO;AACP;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC;AAC1D,IAAI,OAAO,IAAI;AACf,MAAM,EAAE,KAAK,EAAE,iCAAiC,EAAE,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;AAC1E,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;;;;"}