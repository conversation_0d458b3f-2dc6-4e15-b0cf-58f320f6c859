{"version": 3, "file": "_server.ts-DivX2TAZ.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/languages/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../chunks/prisma.js\";\nconst GET = async ({ url }) => {\n  try {\n    if (!prisma) {\n      console.log(\"Prisma client not initialized during build\");\n      return json([]);\n    }\n    const search = url.searchParams.get(\"search\") || \"\";\n    const limit = parseInt(url.searchParams.get(\"limit\") || \"100\");\n    const filters = {};\n    if (search) {\n      filters.name = {\n        contains: search,\n        mode: \"insensitive\"\n      };\n    }\n    const languages = await prisma.language.findMany({\n      where: filters,\n      orderBy: {\n        name: \"asc\"\n      },\n      take: limit\n    });\n    const formattedLanguages = languages.map((language) => ({\n      id: language.id,\n      name: language.name,\n      code: language.code\n    }));\n    return json(formattedLanguages);\n  } catch (error) {\n    console.error(\"Error fetching languages:\", error);\n    return json({ error: \"Failed to fetch languages\" }, { status: 500 });\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;AAEK,MAAC,GAAG,GAAG,OAAO,EAAE,GAAG,EAAE,KAAK;AAC/B,EAAE,IAAI;AACN,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC;AAC/D,MAAM,OAAO,IAAI,CAAC,EAAE,CAAC;AACrB;AACA,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE;AACvD,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC;AAClE,IAAI,MAAM,OAAO,GAAG,EAAE;AACtB,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,OAAO,CAAC,IAAI,GAAG;AACrB,QAAQ,QAAQ,EAAE,MAAM;AACxB,QAAQ,IAAI,EAAE;AACd,OAAO;AACP;AACA,IAAI,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;AACrD,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,OAAO,EAAE;AACf,QAAQ,IAAI,EAAE;AACd,OAAO;AACP,MAAM,IAAI,EAAE;AACZ,KAAK,CAAC;AACN,IAAI,MAAM,kBAAkB,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,MAAM;AAC5D,MAAM,EAAE,EAAE,QAAQ,CAAC,EAAE;AACrB,MAAM,IAAI,EAAE,QAAQ,CAAC,IAAI;AACzB,MAAM,IAAI,EAAE,QAAQ,CAAC;AACrB,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,IAAI,CAAC,kBAAkB,CAAC;AACnC,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC;AACrD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,2BAA2B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA;;;;"}