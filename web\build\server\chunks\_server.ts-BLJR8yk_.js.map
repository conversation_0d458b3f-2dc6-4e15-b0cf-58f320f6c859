{"version": 3, "file": "_server.ts-BLJR8yk_.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/jobs/_id_/apply/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../../../../chunks/auth.js\";\nasync function POST({ request, params, cookies }) {\n  try {\n    const token = cookies.get(\"auth_token\");\n    const user = token && verifySessionToken(token);\n    if (!user) {\n      return json({ error: \"Unauthorized\" }, { status: 401 });\n    }\n    const { id } = params;\n    const job = await prisma.job_listing.findUnique({\n      where: { id }\n    });\n    if (!job) {\n      return json({ error: \"Job not found\" }, { status: 404 });\n    }\n    const existingApplication = await prisma.application.findFirst({\n      where: {\n        userId: user.id,\n        url: job.url\n      }\n    });\n    if (existingApplication) {\n      return json({\n        message: \"Already applied to this job\",\n        application: existingApplication\n      });\n    }\n    const application = await prisma.application.create({\n      data: {\n        userId: user.id,\n        company: job.company || \"Unknown Company\",\n        position: job.title,\n        location: job.location,\n        appliedDate: /* @__PURE__ */ new Date(),\n        status: \"Applied\",\n        url: job.url,\n        jobType: job.employmentType || \"Full-time\",\n        resumeUploaded: false\n      }\n    });\n    if (job.id) {\n      try {\n        await prisma.job_match_result.updateMany({\n          where: {\n            userId: user.id,\n            jobId: job.id\n          },\n          data: {\n            applied: true\n          }\n        });\n      } catch (error) {\n        console.error(\"Error updating job match result:\", error);\n      }\n    }\n    return json({\n      success: true,\n      message: \"Application created successfully\",\n      application\n    });\n  } catch (error) {\n    console.error(\"Error applying to job:\", error);\n    return json({ error: \"Failed to apply to job\" }, { status: 500 });\n  }\n}\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;AAGA,eAAe,IAAI,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE;AAClD,EAAE,IAAI;AACN,IAAI,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AAC3C,IAAI,MAAM,IAAI,GAAG,KAAK,IAAI,kBAAkB,CAAC,KAAK,CAAC;AACnD,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7D;AACA,IAAI,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;AACzB,IAAI,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;AACpD,MAAM,KAAK,EAAE,EAAE,EAAE;AACjB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,GAAG,EAAE;AACd,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9D;AACA,IAAI,MAAM,mBAAmB,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC;AACnE,MAAM,KAAK,EAAE;AACb,QAAQ,MAAM,EAAE,IAAI,CAAC,EAAE;AACvB,QAAQ,GAAG,EAAE,GAAG,CAAC;AACjB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,mBAAmB,EAAE;AAC7B,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,OAAO,EAAE,6BAA6B;AAC9C,QAAQ,WAAW,EAAE;AACrB,OAAO,CAAC;AACR;AACA,IAAI,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;AACxD,MAAM,IAAI,EAAE;AACZ,QAAQ,MAAM,EAAE,IAAI,CAAC,EAAE;AACvB,QAAQ,OAAO,EAAE,GAAG,CAAC,OAAO,IAAI,iBAAiB;AACjD,QAAQ,QAAQ,EAAE,GAAG,CAAC,KAAK;AAC3B,QAAQ,QAAQ,EAAE,GAAG,CAAC,QAAQ;AAC9B,QAAQ,WAAW,kBAAkB,IAAI,IAAI,EAAE;AAC/C,QAAQ,MAAM,EAAE,SAAS;AACzB,QAAQ,GAAG,EAAE,GAAG,CAAC,GAAG;AACpB,QAAQ,OAAO,EAAE,GAAG,CAAC,cAAc,IAAI,WAAW;AAClD,QAAQ,cAAc,EAAE;AACxB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,GAAG,CAAC,EAAE,EAAE;AAChB,MAAM,IAAI;AACV,QAAQ,MAAM,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC;AACjD,UAAU,KAAK,EAAE;AACjB,YAAY,MAAM,EAAE,IAAI,CAAC,EAAE;AAC3B,YAAY,KAAK,EAAE,GAAG,CAAC;AACvB,WAAW;AACX,UAAU,IAAI,EAAE;AAChB,YAAY,OAAO,EAAE;AACrB;AACA,SAAS,CAAC;AACV,OAAO,CAAC,OAAO,KAAK,EAAE;AACtB,QAAQ,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC;AAChE;AACA;AACA,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,OAAO,EAAE,kCAAkC;AACjD,MAAM;AACN,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC;AAClD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,wBAAwB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACrE;AACA;;;;"}