{"version": 3, "file": "_server.ts-CKTtqTKF.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/admin/seed-features/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { F as FEATURES } from \"../../../../../chunks/dynamic-registry.js\";\nimport { v as verifySessionToken } from \"../../../../../chunks/auth.js\";\nconst POST = async ({ cookies }) => {\n  const token = cookies.get(\"auth_token\");\n  if (!token) return new Response(\"Unauthorized\", { status: 401 });\n  const userData = await verifySessionToken(token);\n  if (!userData?.id) return new Response(\"Unauthorized\", { status: 401 });\n  const user = await prisma.user.findUnique({\n    where: { id: userData.id }\n  });\n  if (!user?.isAdmin) {\n    return new Response(\"Unauthorized - Admin access required\", { status: 403 });\n  }\n  try {\n    const results = {\n      features: 0,\n      limits: 0,\n      plans: 0,\n      planFeatures: 0,\n      planLimits: 0\n    };\n    for (const feature of FEATURES) {\n      try {\n        const existingFeature = await prisma.feature.findUnique({\n          where: { id: feature.id }\n        });\n        if (!existingFeature) {\n          await prisma.feature.create({\n            data: {\n              id: feature.id,\n              name: feature.name,\n              description: feature.description || \"\",\n              category: feature.category || \"general\",\n              icon: feature.icon || null,\n              beta: feature.beta || false,\n              updatedAt: /* @__PURE__ */ new Date()\n            }\n          });\n          results.features++;\n        }\n        if (feature.limits) {\n          for (const limit of feature.limits) {\n            if (!limit || !limit.id) continue;\n            const existingLimit = await prisma.featureLimit.findUnique({\n              where: { id: limit.id }\n            });\n            if (!existingLimit) {\n              await prisma.featureLimit.create({\n                data: {\n                  id: limit.id,\n                  featureId: feature.id,\n                  name: limit.name,\n                  description: limit.description || \"\",\n                  defaultValue: (limit.defaultValue || 10).toString(),\n                  type: limit.type,\n                  unit: limit.unit || null,\n                  resetDay: limit.resetDay || null\n                }\n              });\n              results.limits++;\n            }\n          }\n        }\n      } catch (error) {\n        console.error(`Error creating feature ${feature.name}:`, error);\n      }\n    }\n    try {\n      const now = /* @__PURE__ */ new Date();\n      const period = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, \"0\")}`;\n      const existingUsage = await prisma.featureUsage.findFirst({\n        where: {\n          userId: userData.id,\n          featureId: \"resume_scanner\",\n          limitId: \"resume_scans_per_month\",\n          period\n        }\n      });\n      if (!existingUsage) {\n        await prisma.featureUsage.create({\n          data: {\n            userId: userData.id,\n            featureId: \"resume_scanner\",\n            limitId: \"resume_scans_per_month\",\n            used: 5,\n            period\n          }\n        });\n      } else {\n        await prisma.featureUsage.update({\n          where: { id: existingUsage.id },\n          data: {\n            used: 5,\n            updatedAt: /* @__PURE__ */ new Date()\n          }\n        });\n      }\n    } catch (error) {\n      console.error(\"Error creating test usage record:\", error);\n    }\n    return json({ success: true, results });\n  } catch (error) {\n    console.error(\"Error seeding feature data:\", error);\n    return json({ success: false, error: error.message }, { status: 500 });\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAIK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK;AACpC,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE,EAAE,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC;AAClD,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE,EAAE,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAC5C,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE;AAC5B,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE;AACtB,IAAI,OAAO,IAAI,QAAQ,CAAC,sCAAsC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAChF;AACA,EAAE,IAAI;AACN,IAAI,MAAM,OAAO,GAAG;AACpB,MAAM,QAAQ,EAAE,CAAC;AACjB,MAAM,MAAM,EAAE,CAAC;AACf,MAAM,KAAK,EAAE,CAAC;AACd,MAAM,YAAY,EAAE,CAAC;AACrB,MAAM,UAAU,EAAE;AAClB,KAAK;AACL,IAAI,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;AACpC,MAAM,IAAI;AACV,QAAQ,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;AAChE,UAAU,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE;AACjC,SAAS,CAAC;AACV,QAAQ,IAAI,CAAC,eAAe,EAAE;AAC9B,UAAU,MAAM,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;AACtC,YAAY,IAAI,EAAE;AAClB,cAAc,EAAE,EAAE,OAAO,CAAC,EAAE;AAC5B,cAAc,IAAI,EAAE,OAAO,CAAC,IAAI;AAChC,cAAc,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,EAAE;AACpD,cAAc,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,SAAS;AACrD,cAAc,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,IAAI;AACxC,cAAc,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,KAAK;AACzC,cAAc,SAAS,kBAAkB,IAAI,IAAI;AACjD;AACA,WAAW,CAAC;AACZ,UAAU,OAAO,CAAC,QAAQ,EAAE;AAC5B;AACA,QAAQ,IAAI,OAAO,CAAC,MAAM,EAAE;AAC5B,UAAU,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,MAAM,EAAE;AAC9C,YAAY,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE;AACrC,YAAY,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC;AACvE,cAAc,KAAK,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,EAAE;AACnC,aAAa,CAAC;AACd,YAAY,IAAI,CAAC,aAAa,EAAE;AAChC,cAAc,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AAC/C,gBAAgB,IAAI,EAAE;AACtB,kBAAkB,EAAE,EAAE,KAAK,CAAC,EAAE;AAC9B,kBAAkB,SAAS,EAAE,OAAO,CAAC,EAAE;AACvC,kBAAkB,IAAI,EAAE,KAAK,CAAC,IAAI;AAClC,kBAAkB,WAAW,EAAE,KAAK,CAAC,WAAW,IAAI,EAAE;AACtD,kBAAkB,YAAY,EAAE,CAAC,KAAK,CAAC,YAAY,IAAI,EAAE,EAAE,QAAQ,EAAE;AACrE,kBAAkB,IAAI,EAAE,KAAK,CAAC,IAAI;AAClC,kBAAkB,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,IAAI;AAC1C,kBAAkB,QAAQ,EAAE,KAAK,CAAC,QAAQ,IAAI;AAC9C;AACA,eAAe,CAAC;AAChB,cAAc,OAAO,CAAC,MAAM,EAAE;AAC9B;AACA;AACA;AACA,OAAO,CAAC,OAAO,KAAK,EAAE;AACtB,QAAQ,OAAO,CAAC,KAAK,CAAC,CAAC,uBAAuB,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;AACvE;AACA;AACA,IAAI,IAAI;AACR,MAAM,MAAM,GAAG,mBAAmB,IAAI,IAAI,EAAE;AAC5C,MAAM,MAAM,MAAM,GAAG,CAAC,EAAE,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAC1F,MAAM,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC;AAChE,QAAQ,KAAK,EAAE;AACf,UAAU,MAAM,EAAE,QAAQ,CAAC,EAAE;AAC7B,UAAU,SAAS,EAAE,gBAAgB;AACrC,UAAU,OAAO,EAAE,wBAAwB;AAC3C,UAAU;AACV;AACA,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,aAAa,EAAE;AAC1B,QAAQ,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AACzC,UAAU,IAAI,EAAE;AAChB,YAAY,MAAM,EAAE,QAAQ,CAAC,EAAE;AAC/B,YAAY,SAAS,EAAE,gBAAgB;AACvC,YAAY,OAAO,EAAE,wBAAwB;AAC7C,YAAY,IAAI,EAAE,CAAC;AACnB,YAAY;AACZ;AACA,SAAS,CAAC;AACV,OAAO,MAAM;AACb,QAAQ,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AACzC,UAAU,KAAK,EAAE,EAAE,EAAE,EAAE,aAAa,CAAC,EAAE,EAAE;AACzC,UAAU,IAAI,EAAE;AAChB,YAAY,IAAI,EAAE,CAAC;AACnB,YAAY,SAAS,kBAAkB,IAAI,IAAI;AAC/C;AACA,SAAS,CAAC;AACV;AACA,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC;AAC/D;AACA,IAAI,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;AAC3C,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC;AACvD,IAAI,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1E;AACA;;;;"}