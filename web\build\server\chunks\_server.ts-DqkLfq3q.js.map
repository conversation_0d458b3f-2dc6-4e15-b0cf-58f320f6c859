{"version": 3, "file": "_server.ts-DqkLfq3q.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/passkeys/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../chunks/prisma.js\";\nimport { g as getUserFromToken } from \"../../../../chunks/auth.js\";\nimport { g as generatePasskeyRegistrationOptions, v as verifyPasskeyRegistration } from \"../../../../chunks/webauthn.js\";\nconst POST = async ({ request, cookies, url }) => {\n  console.log(\"API: getRegistrationOptions called\");\n  try {\n    const tokenData = await getUserFromToken(cookies);\n    console.log(\"Token data:\", tokenData);\n    if (!tokenData || !tokenData.email) {\n      console.log(\"Unauthorized: No token data or email\");\n      return json({ error: \"Unauthorized\" }, { status: 401 });\n    }\n    console.log(\"Getting user data for email:\", tokenData.email);\n    const userData = await prisma.user.findUnique({\n      where: { email: tokenData.email }\n    });\n    console.log(\"User data found:\", !!userData);\n    if (!userData) {\n      console.log(\"Unauthorized: No user data found\");\n      return json({ error: \"Unauthorized\" }, { status: 401 });\n    }\n    const action = url.searchParams.get(\"action\");\n    console.log(\"Action:\", action);\n    if (action === \"getRegistrationOptions\") {\n      try {\n        console.log(\"Parsing JSON request body\");\n        const body = await request.json();\n        const name = body.name;\n        console.log(\"Passkey name:\", name);\n        if (!name) {\n          console.log(\"Passkey name is required\");\n          return json({ error: \"Passkey name is required\" }, { status: 400 });\n        }\n        console.log(\"Getting existing passkeys\");\n        const preferences = userData.preferences || {};\n        const securityPrefs = preferences.security || {};\n        const existingPasskeys = securityPrefs.passkeys || [];\n        console.log(\"Existing passkeys count:\", existingPasskeys.length);\n        console.log(\"Generating registration options\");\n        const mappedPasskeys = existingPasskeys.map((p) => {\n          const credId = p.credentialID || p.id;\n          const pubKey = p.credentialPublicKey || p.publicKey;\n          console.log(\"Mapping passkey:\", {\n            name: p.name,\n            id: p.id,\n            credentialID: credId,\n            publicKey: pubKey ? pubKey.substring(0, 20) + \"...\" : \"undefined\"\n          });\n          return {\n            id: credId,\n            publicKey: pubKey,\n            transports: p.transports || []\n          };\n        });\n        const options = await generatePasskeyRegistrationOptions(\n          userData.id,\n          userData.email,\n          mappedPasskeys\n        );\n        console.log(\"Registration options generated\");\n        console.log(\"Storing challenge in database\");\n        console.log(\"Challenge to store:\", options.challenge);\n        const updatedPreferences = {\n          ...preferences,\n          security: {\n            ...securityPrefs,\n            currentChallenge: options.challenge\n          }\n        };\n        await prisma.user.update({\n          where: { id: userData.id },\n          data: {\n            preferences: updatedPreferences\n          }\n        });\n        console.log(\"Challenge stored in database\");\n        console.log(\"Returning options\");\n        return json(options);\n      } catch (innerError) {\n        console.error(\"Inner error in getRegistrationOptions:\", innerError);\n        console.error(\"Inner error stack:\", innerError.stack);\n        return json({ error: \"Failed to process registration options\" }, { status: 500 });\n      }\n    } else if (action === \"verifyRegistration\") {\n      console.log(\"verifyRegistration called\");\n      try {\n        console.log(\"Parsing JSON request body\");\n        const body = await request.json();\n        const { name, registrationResponse } = body;\n        console.log(\"Data from JSON:\", { name, hasResponse: !!registrationResponse });\n        if (!name || !registrationResponse) {\n          console.log(\"Invalid request data\");\n          return json({ error: \"Invalid request data\" }, { status: 400 });\n        }\n        const preferences = userData.preferences || {};\n        const securityPrefs = preferences.security || {};\n        const challenge = securityPrefs.currentChallenge;\n        console.log(\"Stored challenge:\", challenge);\n        if (!challenge) {\n          console.log(\"No challenge found in user preferences\");\n          return json({ error: \"No challenge found\" }, { status: 400 });\n        }\n        console.log(\"Calling verifyPasskeyRegistration with challenge:\", challenge);\n        const verification = await verifyPasskeyRegistration(registrationResponse, challenge);\n        console.log(\"Verification result:\", verification);\n        if (!verification.verified || !verification.registrationInfo) {\n          console.log(\"Verification failed:\", verification.error || \"Unknown error\");\n          return json(\n            {\n              error: verification.error || \"Passkey verification failed\",\n              details: verification\n            },\n            { status: 400 }\n          );\n        }\n        const existingPasskeys = securityPrefs.passkeys || [];\n        const credentialIDBase64Url = Buffer.from(\n          verification.registrationInfo.credentialID\n        ).toString(\"base64url\");\n        const newPasskey = {\n          id: credentialIDBase64Url,\n          // Use the same base64url format for both id and credentialID\n          name,\n          credentialID: credentialIDBase64Url,\n          credentialPublicKey: Buffer.from(\n            verification.registrationInfo.credentialPublicKey\n          ).toString(\"base64url\"),\n          counter: verification.registrationInfo.counter,\n          transports: registrationResponse.response.transports || [],\n          createdAt: (/* @__PURE__ */ new Date()).toISOString(),\n          lastUsed: (/* @__PURE__ */ new Date()).toISOString()\n        };\n        const updatedPreferences = {\n          ...preferences,\n          security: {\n            ...securityPrefs,\n            passkeys: [...existingPasskeys, newPasskey],\n            currentChallenge: null\n            // Clear the challenge\n          }\n        };\n        await prisma.user.update({\n          where: { id: userData.id },\n          data: {\n            preferences: updatedPreferences\n          }\n        });\n        return json({\n          success: true,\n          passkeys: [...existingPasskeys, newPasskey]\n        });\n      } catch (error) {\n        console.error(\"Error verifying passkey registration:\", error);\n        return json({ error: \"Failed to verify passkey registration\" }, { status: 500 });\n      }\n    } else if (action === \"removePasskey\") {\n      try {\n        console.log(\"removePasskey called\");\n        const body = await request.json();\n        const { passkeyId } = body;\n        console.log(\"Passkey ID to remove:\", passkeyId);\n        if (!passkeyId) {\n          return json({ error: \"Passkey ID is required\" }, { status: 400 });\n        }\n        const preferences = userData.preferences || {};\n        const securityPrefs = preferences.security || {};\n        const existingPasskeys = securityPrefs.passkeys || [];\n        console.log(\"Existing passkeys count:\", existingPasskeys.length);\n        console.log(\n          \"Existing passkeys before filtering:\",\n          JSON.stringify(existingPasskeys, null, 2)\n        );\n        console.log(\"Passkey ID to filter out:\", passkeyId);\n        console.log(\n          \"Available passkey IDs:\",\n          existingPasskeys.map((p) => ({\n            id: p.id,\n            credentialID: p.credentialID,\n            matches: p.id === passkeyId || p.credentialID === passkeyId\n          }))\n        );\n        const updatedPasskeys = existingPasskeys.filter((passkey) => {\n          const idMatches = passkey.id === passkeyId;\n          const credentialIDMatches = passkey.credentialID === passkeyId;\n          console.log(\n            `Passkey ${passkey.name || \"unnamed\"}: id match=${idMatches}, credentialID match=${credentialIDMatches}`\n          );\n          return !(idMatches || credentialIDMatches);\n        });\n        console.log(\"Updated passkeys count:\", updatedPasskeys.length);\n        console.log(\"Passkeys after filtering:\", JSON.stringify(updatedPasskeys, null, 2));\n        if (updatedPasskeys.length === existingPasskeys.length) {\n          console.log(\"Passkey not found with ID:\", passkeyId);\n          console.log(\n            \"Available passkey IDs:\",\n            existingPasskeys.map((p) => ({ id: p.id, credentialID: p.credentialID }))\n          );\n          return json({ error: \"Passkey not found\" }, { status: 404 });\n        }\n        const updatedPreferences = {\n          ...preferences,\n          security: {\n            ...securityPrefs,\n            passkeys: updatedPasskeys\n          }\n        };\n        await prisma.user.update({\n          where: { id: userData.id },\n          data: {\n            preferences: updatedPreferences\n          }\n        });\n        return json({\n          success: true,\n          passkeys: updatedPasskeys\n        });\n      } catch (error) {\n        console.error(\"Error removing passkey:\", error);\n        return json({ error: \"Failed to remove passkey\" }, { status: 500 });\n      }\n    } else {\n      return json({ error: \"Invalid action\" }, { status: 400 });\n    }\n  } catch (error) {\n    console.error(\"Error in passkeys API:\", error);\n    console.error(\"Error stack:\", error.stack);\n    return json({ error: \"Failed to process passkey request\" }, { status: 500 });\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAIK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK;AAClD,EAAE,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC;AACnD,EAAE,IAAI;AACN,IAAI,MAAM,SAAS,GAAG,MAAM,gBAAgB,CAAC,OAAO,CAAC;AACrD,IAAI,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,SAAS,CAAC;AACzC,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;AACxC,MAAM,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC;AACzD,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7D;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,SAAS,CAAC,KAAK,CAAC;AAChE,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAClD,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,SAAS,CAAC,KAAK;AACrC,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,CAAC,CAAC,QAAQ,CAAC;AAC/C,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC;AACrD,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7D;AACA,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC;AACjD,IAAI,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC;AAClC,IAAI,IAAI,MAAM,KAAK,wBAAwB,EAAE;AAC7C,MAAM,IAAI;AACV,QAAQ,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC;AAChD,QAAQ,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACzC,QAAQ,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI;AAC9B,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC;AAC1C,QAAQ,IAAI,CAAC,IAAI,EAAE;AACnB,UAAU,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC;AACjD,UAAU,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,0BAA0B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7E;AACA,QAAQ,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC;AAChD,QAAQ,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,IAAI,EAAE;AACtD,QAAQ,MAAM,aAAa,GAAG,WAAW,CAAC,QAAQ,IAAI,EAAE;AACxD,QAAQ,MAAM,gBAAgB,GAAG,aAAa,CAAC,QAAQ,IAAI,EAAE;AAC7D,QAAQ,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE,gBAAgB,CAAC,MAAM,CAAC;AACxE,QAAQ,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC;AACtD,QAAQ,MAAM,cAAc,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK;AAC3D,UAAU,MAAM,MAAM,GAAG,CAAC,CAAC,YAAY,IAAI,CAAC,CAAC,EAAE;AAC/C,UAAU,MAAM,MAAM,GAAG,CAAC,CAAC,mBAAmB,IAAI,CAAC,CAAC,SAAS;AAC7D,UAAU,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE;AAC1C,YAAY,IAAI,EAAE,CAAC,CAAC,IAAI;AACxB,YAAY,EAAE,EAAE,CAAC,CAAC,EAAE;AACpB,YAAY,YAAY,EAAE,MAAM;AAChC,YAAY,SAAS,EAAE,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAG;AAClE,WAAW,CAAC;AACZ,UAAU,OAAO;AACjB,YAAY,EAAE,EAAE,MAAM;AACtB,YAAY,SAAS,EAAE,MAAM;AAC7B,YAAY,UAAU,EAAE,CAAC,CAAC,UAAU,IAAI;AACxC,WAAW;AACX,SAAS,CAAC;AACV,QAAQ,MAAM,OAAO,GAAG,MAAM,kCAAkC;AAChE,UAAU,QAAQ,CAAC,EAAE;AACrB,UAAU,QAAQ,CAAC,KAAK;AACxB,UAAU;AACV,SAAS;AACT,QAAQ,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC;AACrD,QAAQ,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC;AACpD,QAAQ,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,OAAO,CAAC,SAAS,CAAC;AAC7D,QAAQ,MAAM,kBAAkB,GAAG;AACnC,UAAU,GAAG,WAAW;AACxB,UAAU,QAAQ,EAAE;AACpB,YAAY,GAAG,aAAa;AAC5B,YAAY,gBAAgB,EAAE,OAAO,CAAC;AACtC;AACA,SAAS;AACT,QAAQ,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AACjC,UAAU,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE;AACpC,UAAU,IAAI,EAAE;AAChB,YAAY,WAAW,EAAE;AACzB;AACA,SAAS,CAAC;AACV,QAAQ,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC;AACnD,QAAQ,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC;AACxC,QAAQ,OAAO,IAAI,CAAC,OAAO,CAAC;AAC5B,OAAO,CAAC,OAAO,UAAU,EAAE;AAC3B,QAAQ,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,UAAU,CAAC;AAC3E,QAAQ,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,UAAU,CAAC,KAAK,CAAC;AAC7D,QAAQ,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,wCAAwC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzF;AACA,KAAK,MAAM,IAAI,MAAM,KAAK,oBAAoB,EAAE;AAChD,MAAM,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC;AAC9C,MAAM,IAAI;AACV,QAAQ,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC;AAChD,QAAQ,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACzC,QAAQ,MAAM,EAAE,IAAI,EAAE,oBAAoB,EAAE,GAAG,IAAI;AACnD,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC,oBAAoB,EAAE,CAAC;AACrF,QAAQ,IAAI,CAAC,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC5C,UAAU,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC;AAC7C,UAAU,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,sBAAsB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE;AACA,QAAQ,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,IAAI,EAAE;AACtD,QAAQ,MAAM,aAAa,GAAG,WAAW,CAAC,QAAQ,IAAI,EAAE;AACxD,QAAQ,MAAM,SAAS,GAAG,aAAa,CAAC,gBAAgB;AACxD,QAAQ,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,SAAS,CAAC;AACnD,QAAQ,IAAI,CAAC,SAAS,EAAE;AACxB,UAAU,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC;AAC/D,UAAU,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oBAAoB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACvE;AACA,QAAQ,OAAO,CAAC,GAAG,CAAC,mDAAmD,EAAE,SAAS,CAAC;AACnF,QAAQ,MAAM,YAAY,GAAG,MAAM,yBAAyB,CAAC,oBAAoB,EAAE,SAAS,CAAC;AAC7F,QAAQ,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,YAAY,CAAC;AACzD,QAAQ,IAAI,CAAC,YAAY,CAAC,QAAQ,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE;AACtE,UAAU,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,YAAY,CAAC,KAAK,IAAI,eAAe,CAAC;AACpF,UAAU,OAAO,IAAI;AACrB,YAAY;AACZ,cAAc,KAAK,EAAE,YAAY,CAAC,KAAK,IAAI,6BAA6B;AACxE,cAAc,OAAO,EAAE;AACvB,aAAa;AACb,YAAY,EAAE,MAAM,EAAE,GAAG;AACzB,WAAW;AACX;AACA,QAAQ,MAAM,gBAAgB,GAAG,aAAa,CAAC,QAAQ,IAAI,EAAE;AAC7D,QAAQ,MAAM,qBAAqB,GAAG,MAAM,CAAC,IAAI;AACjD,UAAU,YAAY,CAAC,gBAAgB,CAAC;AACxC,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC;AAC/B,QAAQ,MAAM,UAAU,GAAG;AAC3B,UAAU,EAAE,EAAE,qBAAqB;AACnC;AACA,UAAU,IAAI;AACd,UAAU,YAAY,EAAE,qBAAqB;AAC7C,UAAU,mBAAmB,EAAE,MAAM,CAAC,IAAI;AAC1C,YAAY,YAAY,CAAC,gBAAgB,CAAC;AAC1C,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC;AACjC,UAAU,OAAO,EAAE,YAAY,CAAC,gBAAgB,CAAC,OAAO;AACxD,UAAU,UAAU,EAAE,oBAAoB,CAAC,QAAQ,CAAC,UAAU,IAAI,EAAE;AACpE,UAAU,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW,EAAE;AAC/D,UAAU,QAAQ,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC5D,SAAS;AACT,QAAQ,MAAM,kBAAkB,GAAG;AACnC,UAAU,GAAG,WAAW;AACxB,UAAU,QAAQ,EAAE;AACpB,YAAY,GAAG,aAAa;AAC5B,YAAY,QAAQ,EAAE,CAAC,GAAG,gBAAgB,EAAE,UAAU,CAAC;AACvD,YAAY,gBAAgB,EAAE;AAC9B;AACA;AACA,SAAS;AACT,QAAQ,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AACjC,UAAU,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE;AACpC,UAAU,IAAI,EAAE;AAChB,YAAY,WAAW,EAAE;AACzB;AACA,SAAS,CAAC;AACV,QAAQ,OAAO,IAAI,CAAC;AACpB,UAAU,OAAO,EAAE,IAAI;AACvB,UAAU,QAAQ,EAAE,CAAC,GAAG,gBAAgB,EAAE,UAAU;AACpD,SAAS,CAAC;AACV,OAAO,CAAC,OAAO,KAAK,EAAE;AACtB,QAAQ,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC;AACrE,QAAQ,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uCAAuC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxF;AACA,KAAK,MAAM,IAAI,MAAM,KAAK,eAAe,EAAE;AAC3C,MAAM,IAAI;AACV,QAAQ,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC;AAC3C,QAAQ,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACzC,QAAQ,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI;AAClC,QAAQ,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,SAAS,CAAC;AACvD,QAAQ,IAAI,CAAC,SAAS,EAAE;AACxB,UAAU,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,wBAAwB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3E;AACA,QAAQ,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,IAAI,EAAE;AACtD,QAAQ,MAAM,aAAa,GAAG,WAAW,CAAC,QAAQ,IAAI,EAAE;AACxD,QAAQ,MAAM,gBAAgB,GAAG,aAAa,CAAC,QAAQ,IAAI,EAAE;AAC7D,QAAQ,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE,gBAAgB,CAAC,MAAM,CAAC;AACxE,QAAQ,OAAO,CAAC,GAAG;AACnB,UAAU,qCAAqC;AAC/C,UAAU,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE,IAAI,EAAE,CAAC;AAClD,SAAS;AACT,QAAQ,OAAO,CAAC,GAAG,CAAC,2BAA2B,EAAE,SAAS,CAAC;AAC3D,QAAQ,OAAO,CAAC,GAAG;AACnB,UAAU,wBAAwB;AAClC,UAAU,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM;AACvC,YAAY,EAAE,EAAE,CAAC,CAAC,EAAE;AACpB,YAAY,YAAY,EAAE,CAAC,CAAC,YAAY;AACxC,YAAY,OAAO,EAAE,CAAC,CAAC,EAAE,KAAK,SAAS,IAAI,CAAC,CAAC,YAAY,KAAK;AAC9D,WAAW,CAAC;AACZ,SAAS;AACT,QAAQ,MAAM,eAAe,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,OAAO,KAAK;AACrE,UAAU,MAAM,SAAS,GAAG,OAAO,CAAC,EAAE,KAAK,SAAS;AACpD,UAAU,MAAM,mBAAmB,GAAG,OAAO,CAAC,YAAY,KAAK,SAAS;AACxE,UAAU,OAAO,CAAC,GAAG;AACrB,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,IAAI,SAAS,CAAC,WAAW,EAAE,SAAS,CAAC,qBAAqB,EAAE,mBAAmB,CAAC;AACnH,WAAW;AACX,UAAU,OAAO,EAAE,SAAS,IAAI,mBAAmB,CAAC;AACpD,SAAS,CAAC;AACV,QAAQ,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,eAAe,CAAC,MAAM,CAAC;AACtE,QAAQ,OAAO,CAAC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AAC1F,QAAQ,IAAI,eAAe,CAAC,MAAM,KAAK,gBAAgB,CAAC,MAAM,EAAE;AAChE,UAAU,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE,SAAS,CAAC;AAC9D,UAAU,OAAO,CAAC,GAAG;AACrB,YAAY,wBAAwB;AACpC,YAAY,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,YAAY,EAAE,CAAC,CAAC,YAAY,EAAE,CAAC;AACpF,WAAW;AACX,UAAU,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtE;AACA,QAAQ,MAAM,kBAAkB,GAAG;AACnC,UAAU,GAAG,WAAW;AACxB,UAAU,QAAQ,EAAE;AACpB,YAAY,GAAG,aAAa;AAC5B,YAAY,QAAQ,EAAE;AACtB;AACA,SAAS;AACT,QAAQ,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AACjC,UAAU,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE;AACpC,UAAU,IAAI,EAAE;AAChB,YAAY,WAAW,EAAE;AACzB;AACA,SAAS,CAAC;AACV,QAAQ,OAAO,IAAI,CAAC;AACpB,UAAU,OAAO,EAAE,IAAI;AACvB,UAAU,QAAQ,EAAE;AACpB,SAAS,CAAC;AACV,OAAO,CAAC,OAAO,KAAK,EAAE;AACtB,QAAQ,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC;AACvD,QAAQ,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,0BAA0B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3E;AACA,KAAK,MAAM;AACX,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,gBAAgB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC/D;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC;AAClD,IAAI,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,KAAK,CAAC;AAC9C,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mCAAmC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAChF;AACA;;;;"}