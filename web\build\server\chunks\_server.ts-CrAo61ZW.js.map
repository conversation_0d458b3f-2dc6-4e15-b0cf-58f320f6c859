{"version": 3, "file": "_server.ts-CrAo61ZW.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/feature-access/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../chunks/index.js\";\nimport { g as getFeatureById, a as getFeatureLimitById } from \"../../../../chunks/registry.js\";\nimport { hasReachedLimit } from \"../../../../chunks/feature-usage.js\";\nasync function GET({ url, locals }) {\n  const session = locals.session;\n  if (!session?.user) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  const featureId = url.searchParams.get(\"featureId\");\n  if (!featureId) {\n    return json({ error: \"Feature ID is required\" }, { status: 400 });\n  }\n  const feature = getFeatureById(featureId);\n  if (!feature) {\n    return json({ error: \"Feature not found\" }, { status: 404 });\n  }\n  try {\n    if (!feature.limits || feature.limits.length === 0) {\n      return json({ hasAccess: true });\n    }\n    for (const limit of feature.limits) {\n      const limitReached = await hasReachedLimit(session.user.id, featureId, limit.id);\n      if (limitReached) {\n        return json({ hasAccess: false, limitReached: true, limitId: limit.id });\n      }\n    }\n    return json({ hasAccess: true });\n  } catch (error) {\n    console.error(\"Error checking feature access:\", error);\n    return json({ error: \"Failed to check feature access\" }, { status: 500 });\n  }\n}\nasync function POST({ request, locals }) {\n  const session = locals.session;\n  if (!session?.user) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  const body = await request.json();\n  const { featureId, limitId } = body;\n  if (!featureId) {\n    return json({ error: \"Feature ID is required\" }, { status: 400 });\n  }\n  if (!limitId) {\n    return json({ error: \"Limit ID is required\" }, { status: 400 });\n  }\n  const feature = getFeatureById(featureId);\n  const limit = getFeatureLimitById(limitId);\n  if (!feature) {\n    return json({ error: \"Feature not found\" }, { status: 404 });\n  }\n  if (!limit) {\n    return json({ error: \"Limit not found\" }, { status: 404 });\n  }\n  try {\n    const limitReached = await hasReachedLimit(session.user.id, featureId, limitId);\n    return json({\n      hasAccess: !limitReached,\n      limitReached,\n      featureId,\n      limitId,\n      featureName: feature.name,\n      limitName: limit.name\n    });\n  } catch (error) {\n    console.error(\"Error checking feature access:\", error);\n    return json({ error: \"Failed to check feature access\" }, { status: 500 });\n  }\n}\nexport {\n  GET,\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAGA,eAAe,GAAG,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE;AACpC,EAAE,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO;AAChC,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE;AACtB,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,MAAM,SAAS,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC;AACrD,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,wBAAwB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACrE;AACA,EAAE,MAAM,OAAO,GAAG,cAAc,CAAC,SAAS,CAAC;AAC3C,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAChE;AACA,EAAE,IAAI;AACN,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;AACxD,MAAM,OAAO,IAAI,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;AACtC;AACA,IAAI,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,MAAM,EAAE;AACxC,MAAM,MAAM,YAAY,GAAG,MAAM,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,EAAE,CAAC;AACtF,MAAM,IAAI,YAAY,EAAE;AACxB,QAAQ,OAAO,IAAI,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,YAAY,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC;AAChF;AACA;AACA,IAAI,OAAO,IAAI,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;AACpC,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC;AAC1D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,gCAAgC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7E;AACA;AACA,eAAe,IAAI,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE;AACzC,EAAE,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO;AAChC,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE;AACtB,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACnC,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,IAAI;AACrC,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,wBAAwB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACrE;AACA,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,sBAAsB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnE;AACA,EAAE,MAAM,OAAO,GAAG,cAAc,CAAC,SAAS,CAAC;AAC3C,EAAE,MAAM,KAAK,GAAG,mBAAmB,CAAC,OAAO,CAAC;AAC5C,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAChE;AACA,EAAE,IAAI,CAAC,KAAK,EAAE;AACd,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,iBAAiB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,YAAY,GAAG,MAAM,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC;AACnF,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,SAAS,EAAE,CAAC,YAAY;AAC9B,MAAM,YAAY;AAClB,MAAM,SAAS;AACf,MAAM,OAAO;AACb,MAAM,WAAW,EAAE,OAAO,CAAC,IAAI;AAC/B,MAAM,SAAS,EAAE,KAAK,CAAC;AACvB,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC;AAC1D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,gCAAgC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7E;AACA;;;;"}