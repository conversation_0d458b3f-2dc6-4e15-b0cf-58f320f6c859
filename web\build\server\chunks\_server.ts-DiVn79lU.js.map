{"version": 3, "file": "_server.ts-DiVn79lU.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/jobs/search/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { v as verifySessionToken } from \"../../../../../chunks/auth.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { canPerformJobSearch, trackJobSearch } from \"../../../../../chunks/job-usage.js\";\nimport { d as dev } from \"../../../../../chunks/index4.js\";\nconst POST = async ({ request, cookies }) => {\n  const token = cookies.get(\"auth_token\");\n  const user = token ? await verifySessionToken(token) : null;\n  const { title, location, locationType, experience, category, education, salary, saveSearch } = await request.json();\n  if (!title) {\n    return json({ error: \"Job title is required\" }, { status: 400 });\n  }\n  try {\n    if (user) {\n      if (!dev) {\n        const canSearch = await canPerformJobSearch(user.id);\n        if (!canSearch) {\n          return json(\n            {\n              error: \"You have reached your limit of job searches for this month. Please upgrade your plan to perform more searches.\",\n              limitReached: true\n            },\n            { status: 403 }\n          );\n        }\n        await trackJobSearch(user.id);\n      }\n      let jobSearch = null;\n      if (saveSearch) {\n        jobSearch = await prisma.jobSearch.create({\n          data: {\n            userId: user.id,\n            // No longer using profileId\n            query: title,\n            location: location || null\n            // We'll handle metadata separately with a raw query if needed\n          }\n        });\n        console.log(\"Job search created with ID:\", jobSearch.id, \"and parameters:\", {\n          title,\n          location,\n          locationType,\n          experience,\n          category,\n          education,\n          salary,\n          saveSearch\n        });\n      } else {\n        console.log(\"Search not saved (saveSearch=false):\", {\n          title,\n          location,\n          locationType,\n          experience,\n          category,\n          education,\n          salary,\n          saveSearch\n        });\n      }\n      const searchResults = await prisma.job_listing.findMany({\n        where: {\n          title: {\n            contains: title,\n            mode: \"insensitive\"\n          },\n          ...location ? {\n            location: {\n              contains: location,\n              mode: \"insensitive\"\n            }\n          } : {}\n        },\n        take: 50,\n        orderBy: {\n          postedDate: \"desc\"\n        }\n      });\n      return json({\n        searchId: jobSearch?.id || null,\n        results: searchResults,\n        saved: !!jobSearch\n      });\n    } else {\n      const searchResults = await prisma.job_listing.findMany({\n        where: {\n          title: {\n            contains: title,\n            mode: \"insensitive\"\n          },\n          ...location ? {\n            location: {\n              contains: location,\n              mode: \"insensitive\"\n            }\n          } : {}\n        },\n        take: 20,\n        // Limit results for unauthenticated users\n        orderBy: {\n          postedDate: \"desc\"\n        }\n      });\n      return json({\n        results: searchResults\n      });\n    }\n  } catch (error) {\n    console.error(\"Job search error:\", error);\n    return json({ error: \"Failed to perform job search\" }, { status: 500 });\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAKK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AAC7C,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,MAAM,IAAI,GAAG,KAAK,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC,GAAG,IAAI;AAC7D,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,YAAY,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrH,EAAE,IAAI,CAAC,KAAK,EAAE;AACd,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpE;AACA,EAAE,IAAI;AACN,IAAI,IAAI,IAAI,EAAE;AACd,MAAM,IAAI,CAAC,GAAG,EAAE;AAChB,QAAQ,MAAM,SAAS,GAAG,MAAM,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC;AAC5D,QAAQ,IAAI,CAAC,SAAS,EAAE;AACxB,UAAU,OAAO,IAAI;AACrB,YAAY;AACZ,cAAc,KAAK,EAAE,gHAAgH;AACrI,cAAc,YAAY,EAAE;AAC5B,aAAa;AACb,YAAY,EAAE,MAAM,EAAE,GAAG;AACzB,WAAW;AACX;AACA,QAAQ,MAAM,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;AACrC;AACA,MAAM,IAAI,SAAS,GAAG,IAAI;AAC1B,MAAM,IAAI,UAAU,EAAE;AACtB,QAAQ,SAAS,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;AAClD,UAAU,IAAI,EAAE;AAChB,YAAY,MAAM,EAAE,IAAI,CAAC,EAAE;AAC3B;AACA,YAAY,KAAK,EAAE,KAAK;AACxB,YAAY,QAAQ,EAAE,QAAQ,IAAI;AAClC;AACA;AACA,SAAS,CAAC;AACV,QAAQ,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,SAAS,CAAC,EAAE,EAAE,iBAAiB,EAAE;AACpF,UAAU,KAAK;AACf,UAAU,QAAQ;AAClB,UAAU,YAAY;AACtB,UAAU,UAAU;AACpB,UAAU,QAAQ;AAClB,UAAU,SAAS;AACnB,UAAU,MAAM;AAChB,UAAU;AACV,SAAS,CAAC;AACV,OAAO,MAAM;AACb,QAAQ,OAAO,CAAC,GAAG,CAAC,sCAAsC,EAAE;AAC5D,UAAU,KAAK;AACf,UAAU,QAAQ;AAClB,UAAU,YAAY;AACtB,UAAU,UAAU;AACpB,UAAU,QAAQ;AAClB,UAAU,SAAS;AACnB,UAAU,MAAM;AAChB,UAAU;AACV,SAAS,CAAC;AACV;AACA,MAAM,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;AAC9D,QAAQ,KAAK,EAAE;AACf,UAAU,KAAK,EAAE;AACjB,YAAY,QAAQ,EAAE,KAAK;AAC3B,YAAY,IAAI,EAAE;AAClB,WAAW;AACX,UAAU,GAAG,QAAQ,GAAG;AACxB,YAAY,QAAQ,EAAE;AACtB,cAAc,QAAQ,EAAE,QAAQ;AAChC,cAAc,IAAI,EAAE;AACpB;AACA,WAAW,GAAG;AACd,SAAS;AACT,QAAQ,IAAI,EAAE,EAAE;AAChB,QAAQ,OAAO,EAAE;AACjB,UAAU,UAAU,EAAE;AACtB;AACA,OAAO,CAAC;AACR,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,QAAQ,EAAE,SAAS,EAAE,EAAE,IAAI,IAAI;AACvC,QAAQ,OAAO,EAAE,aAAa;AAC9B,QAAQ,KAAK,EAAE,CAAC,CAAC;AACjB,OAAO,CAAC;AACR,KAAK,MAAM;AACX,MAAM,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;AAC9D,QAAQ,KAAK,EAAE;AACf,UAAU,KAAK,EAAE;AACjB,YAAY,QAAQ,EAAE,KAAK;AAC3B,YAAY,IAAI,EAAE;AAClB,WAAW;AACX,UAAU,GAAG,QAAQ,GAAG;AACxB,YAAY,QAAQ,EAAE;AACtB,cAAc,QAAQ,EAAE,QAAQ;AAChC,cAAc,IAAI,EAAE;AACpB;AACA,WAAW,GAAG;AACd,SAAS;AACT,QAAQ,IAAI,EAAE,EAAE;AAChB;AACA,QAAQ,OAAO,EAAE;AACjB,UAAU,UAAU,EAAE;AACtB;AACA,OAAO,CAAC;AACR,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,OAAO,EAAE;AACjB,OAAO,CAAC;AACR;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC;AAC7C,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,8BAA8B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3E;AACA;;;;"}