{"version": 3, "file": "_server.ts-PDPdnKr9.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/email/retry-failed/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { l as logger } from \"../../../../../chunks/logger.js\";\nimport { g as getRedisClient } from \"../../../../../chunks/redis.js\";\nasync function POST({ request }) {\n  try {\n    const body = await request.json();\n    const { id } = body;\n    logger.info(`Retrying failed email: ${id}`);\n    const redis = await getRedisClient();\n    if (!redis) {\n      return json({ error: \"Redis client not available\" }, { status: 500 });\n    }\n    const isFailed = await redis.sismember(\"email:failed\", id);\n    if (!isFailed) {\n      return json({ error: \"Email not found in failed jobs\" }, { status: 404 });\n    }\n    const emailData = await redis.get(`email:${id}`);\n    if (!emailData) {\n      return json({ error: \"Email data not found\" }, { status: 404 });\n    }\n    const email = JSON.parse(emailData);\n    await redis.srem(\"email:failed\", id);\n    await redis.lpush(\"email:queue\", JSON.stringify({\n      ...email,\n      retries: (email.retries || 0) + 1,\n      status: \"waiting\",\n      retriedAt: (/* @__PURE__ */ new Date()).toISOString()\n    }));\n    await redis.publish(\"email:process\", \"process_one\");\n    return json({\n      status: \"success\",\n      message: `Email ${id} has been requeued`\n    });\n  } catch (error) {\n    logger.error(\"Error retrying failed email:\", error);\n    return json({ error: \"Failed to retry email\" }, { status: 500 });\n  }\n}\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;AAGA,eAAe,IAAI,CAAC,EAAE,OAAO,EAAE,EAAE;AACjC,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,MAAM,EAAE,EAAE,EAAE,GAAG,IAAI;AACvB,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,uBAAuB,EAAE,EAAE,CAAC,CAAC,CAAC;AAC/C,IAAI,MAAM,KAAK,GAAG,MAAM,cAAc,EAAE;AACxC,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,4BAA4B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3E;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,SAAS,CAAC,cAAc,EAAE,EAAE,CAAC;AAC9D,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,gCAAgC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC/E;AACA,IAAI,MAAM,SAAS,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC;AACpD,IAAI,IAAI,CAAC,SAAS,EAAE;AACpB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,sBAAsB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACrE;AACA,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;AACvC,IAAI,MAAM,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC;AACxC,IAAI,MAAM,KAAK,CAAC,KAAK,CAAC,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC;AACpD,MAAM,GAAG,KAAK;AACd,MAAM,OAAO,EAAE,CAAC,KAAK,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC;AACvC,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AACzD,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,KAAK,CAAC,OAAO,CAAC,eAAe,EAAE,aAAa,CAAC;AACvD,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,OAAO,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,kBAAkB;AAC7C,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC;AACvD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpE;AACA;;;;"}