{"version": 3, "file": "_server.ts-BacqD7xR.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/user/me/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { l as logger } from \"../../../../../chunks/logger.js\";\nasync function GET({ locals }) {\n  try {\n    const userId = locals.user?.id;\n    if (!userId) {\n      return json({ error: \"Not authenticated\" }, { status: 401 });\n    }\n    const user = await prisma.user.findUnique({\n      where: { id: userId },\n      select: {\n        id: true,\n        email: true,\n        name: true,\n        image: true,\n        role: true,\n        isAdmin: true,\n        preferences: true,\n        createdAt: true,\n        updatedAt: true\n      }\n    });\n    if (!user) {\n      return json({ error: \"User not found\" }, { status: 404 });\n    }\n    return json(user);\n  } catch (error) {\n    logger.error(\"Error getting user:\", error);\n    return json({ error: \"Failed to get user\" }, { status: 500 });\n  }\n}\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;;AAGA,eAAe,GAAG,CAAC,EAAE,MAAM,EAAE,EAAE;AAC/B,EAAE,IAAI;AACN,IAAI,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,EAAE,EAAE;AAClC,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAC9C,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;AAC3B,MAAM,MAAM,EAAE;AACd,QAAQ,EAAE,EAAE,IAAI;AAChB,QAAQ,KAAK,EAAE,IAAI;AACnB,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,KAAK,EAAE,IAAI;AACnB,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,WAAW,EAAE,IAAI;AACzB,QAAQ,SAAS,EAAE,IAAI;AACvB,QAAQ,SAAS,EAAE;AACnB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,gBAAgB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC/D;AACA,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC;AACrB,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC;AAC9C,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oBAAoB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE;AACA;;;;"}