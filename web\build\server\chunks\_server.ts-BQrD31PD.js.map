{"version": 3, "file": "_server.ts-BQrD31PD.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/push/vapid-key/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nconst GET = async () => {\n  const publicKey = process.env.VAPID_PUBLIC_KEY;\n  if (!publicKey) {\n    return json({ error: \"VAPID public key not configured\" }, { status: 500 });\n  }\n  return json({ publicKey });\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;AACK,MAAC,GAAG,GAAG,YAAY;AACxB,EAAE,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,gBAAgB;AAChD,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,iCAAiC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9E;AACA,EAAE,OAAO,IAAI,CAAC,EAAE,SAAS,EAAE,CAAC;AAC5B;;;;"}