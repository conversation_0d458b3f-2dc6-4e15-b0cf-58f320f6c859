{"version": 3, "file": "_server.ts-BJCu2mKJ.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/profile/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../chunks/prisma.js\";\nimport { Roles } from \"../../../../chunks/roles.js\";\nconst GET = async ({ locals }) => {\n  const user = locals.user;\n  if (!user) return new Response(\"Unauthorized\", { status: 401 });\n  const profiles = await prisma.profile.findMany({\n    where: { userId: user.id }\n  });\n  return json(profiles);\n};\nconst POST = async ({ request, locals }) => {\n  const user = locals.user;\n  if (!user) return new Response(\"Unauthorized\", { status: 401 });\n  const { name } = await request.json();\n  const roleConfig = Roles[user.role] || Roles[\"free\"];\n  const profileLimit = roleConfig.limits.profiles;\n  const currentCount = await prisma.profile.count({\n    where: { userId: user.id }\n  });\n  if (profileLimit !== null && currentCount >= profileLimit) {\n    return json(\n      {\n        error: `You’ve reached your profile limit of ${profileLimit}.`,\n        limitReached: true\n      },\n      { status: 403 }\n    );\n  }\n  const profile = await prisma.profile.create({\n    data: {\n      name,\n      userId: user.id,\n      updatedAt: /* @__PURE__ */ new Date()\n    }\n  });\n  return json({ profileId: profile.id });\n};\nexport {\n  GET,\n  POST\n};\n"], "names": [], "mappings": ";;;;;AAGK,MAAC,GAAG,GAAG,OAAO,EAAE,MAAM,EAAE,KAAK;AAClC,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE,EAAE,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;AACjD,IAAI,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE;AAC5B,GAAG,CAAC;AACJ,EAAE,OAAO,IAAI,CAAC,QAAQ,CAAC;AACvB;AACK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AAC5C,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACvC,EAAE,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC;AACtD,EAAE,MAAM,YAAY,GAAG,UAAU,CAAC,MAAM,CAAC,QAAQ;AACjD,EAAE,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;AAClD,IAAI,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE;AAC5B,GAAG,CAAC;AACJ,EAAE,IAAI,YAAY,KAAK,IAAI,IAAI,YAAY,IAAI,YAAY,EAAE;AAC7D,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,KAAK,EAAE,CAAC,qCAAqC,EAAE,YAAY,CAAC,CAAC,CAAC;AACtE,QAAQ,YAAY,EAAE;AACtB,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA,EAAE,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;AAC9C,IAAI,IAAI,EAAE;AACV,MAAM,IAAI;AACV,MAAM,MAAM,EAAE,IAAI,CAAC,EAAE;AACrB,MAAM,SAAS,kBAAkB,IAAI,IAAI;AACzC;AACA,GAAG,CAAC;AACJ,EAAE,OAAO,IAAI,CAAC,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC;AACxC;;;;"}