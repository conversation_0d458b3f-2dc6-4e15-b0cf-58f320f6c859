{"version": 3, "file": "_page.svelte-D2asU3Qy.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/help/quick-start/_page.svelte.js"], "sourcesContent": ["import \"clsx\";\nimport { S as SEO } from \"../../../../chunks/SEO.js\";\nimport \"../../../../chunks/button.js\";\nimport { C as Card } from \"../../../../chunks/card.js\";\nimport { C as Card_content } from \"../../../../chunks/card-content.js\";\nimport { C as Circle_check_big } from \"../../../../chunks/circle-check-big.js\";\nimport { A as Arrow_left } from \"../../../../chunks/arrow-left.js\";\nimport { A as Arrow_right } from \"../../../../chunks/arrow-right.js\";\nfunction _page($$payload) {\n  SEO($$payload, {\n    title: \"Quick Start Guide | Hirli Help Center\",\n    description: \"Get started with <PERSON><PERSON><PERSON> in minutes. Learn how to set up your account, create your resume, and start applying for jobs.\",\n    keywords: \"quick start, getting started, Hirli tutorial, job application guide\"\n  });\n  $$payload.out += `<!----> <div class=\"container mx-auto px-4 py-16\"><div class=\"mx-auto max-w-4xl\"><div class=\"mb-8\"><div class=\"text-muted-foreground flex items-center text-sm\"><a href=\"/help\" class=\"hover:text-foreground\">Help Center</a> <span class=\"mx-2\">/</span> <span>Quick Start Guide</span></div></div> <div class=\"mb-12\"><h1 class=\"mb-4 text-4xl font-bold\">Quick Start Guide</h1> <p class=\"text-muted-foreground text-lg\">Welcome to Hirli! This guide will help you get started and make the most of our platform in\n        just a few minutes.</p></div> <div class=\"space-y-12\"><div class=\"flex flex-col gap-8 md:flex-row\"><div class=\"md:w-1/3\"><div class=\"sticky top-8\"><div class=\"bg-primary text-primary-foreground mb-4 flex h-12 w-12 items-center justify-center rounded-full text-xl font-bold\">1</div> <h2 class=\"mb-2 text-2xl font-semibold\">Create Your Account</h2> <p class=\"text-muted-foreground\">Sign up and set up your personal profile to get started.</p></div></div> <div class=\"md:w-2/3\">`;\n  Card($$payload, {\n    children: ($$payload2) => {\n      Card_content($$payload2, {\n        class: \"pt-6\",\n        children: ($$payload3) => {\n          $$payload3.out += `<div class=\"space-y-4\"><div class=\"space-y-2\"><h3 class=\"text-lg font-medium\">Sign up for Hirli</h3> <p>Visit our <a href=\"/sign-up\" class=\"text-primary hover:underline\">sign-up page</a> and create your account using your email or sign in with Google\n                    or LinkedIn.</p></div> <div class=\"space-y-2\"><h3 class=\"text-lg font-medium\">Complete your profile</h3> <p>Fill in your basic information, including your name, location, and contact\n                    details. This information will be used to personalize your job search\n                    experience.</p> <ul class=\"mt-2 space-y-1\"><li class=\"flex items-start\">`;\n          Circle_check_big($$payload3, {\n            class: \"mr-2 mt-0.5 h-5 w-5 flex-shrink-0 text-green-500\"\n          });\n          $$payload3.out += `<!----> <span>Add your professional title and industry</span></li> <li class=\"flex items-start\">`;\n          Circle_check_big($$payload3, {\n            class: \"mr-2 mt-0.5 h-5 w-5 flex-shrink-0 text-green-500\"\n          });\n          $$payload3.out += `<!----> <span>Set your location preferences</span></li> <li class=\"flex items-start\">`;\n          Circle_check_big($$payload3, {\n            class: \"mr-2 mt-0.5 h-5 w-5 flex-shrink-0 text-green-500\"\n          });\n          $$payload3.out += `<!----> <span>Configure your notification settings</span></li></ul></div> <div class=\"bg-muted rounded-lg p-4\"><p class=\"text-sm font-medium\">Pro Tip</p> <p class=\"text-muted-foreground text-sm\">Adding your LinkedIn profile during sign-up allows us to automatically import\n                    your work history and skills, saving you time during the resume creation\n                    process.</p></div></div>`;\n        },\n        $$slots: { default: true }\n      });\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----></div></div> <div class=\"flex flex-col gap-8 md:flex-row\"><div class=\"md:w-1/3\"><div class=\"sticky top-8\"><div class=\"bg-primary text-primary-foreground mb-4 flex h-12 w-12 items-center justify-center rounded-full text-xl font-bold\">2</div> <h2 class=\"mb-2 text-2xl font-semibold\">Create Your Resume</h2> <p class=\"text-muted-foreground\">Build a professional resume that stands out to employers and ATS systems.</p></div></div> <div class=\"md:w-2/3\">`;\n  Card($$payload, {\n    children: ($$payload2) => {\n      Card_content($$payload2, {\n        class: \"pt-6\",\n        children: ($$payload3) => {\n          $$payload3.out += `<div class=\"space-y-4\"><div class=\"space-y-2\"><h3 class=\"text-lg font-medium\">Choose a resume template</h3> <p>Select from our collection of professionally designed, ATS-friendly resume\n                    templates that match your style and industry.</p></div> <div class=\"space-y-2\"><h3 class=\"text-lg font-medium\">Add your work experience</h3> <p>Enter your work history, including job titles, companies, dates, and key\n                    responsibilities. Be specific about your achievements and use action verbs.</p></div> <div class=\"space-y-2\"><h3 class=\"text-lg font-medium\">List your skills and education</h3> <p>Add your educational background and highlight both technical and soft skills\n                    relevant to your target positions.</p></div> <div class=\"space-y-2\"><h3 class=\"text-lg font-medium\">Write a compelling summary</h3> <p>Craft a professional summary that highlights your expertise, experience, and\n                    what makes you unique as a candidate.</p></div> <div class=\"bg-muted rounded-lg p-4\"><p class=\"text-sm font-medium\">Pro Tip</p> <p class=\"text-muted-foreground text-sm\">Use our AI Resume Analyzer to get personalized suggestions for improving your\n                    resume's effectiveness and ATS compatibility.</p></div></div>`;\n        },\n        $$slots: { default: true }\n      });\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----></div></div> <div class=\"flex flex-col gap-8 md:flex-row\"><div class=\"md:w-1/3\"><div class=\"sticky top-8\"><div class=\"bg-primary text-primary-foreground mb-4 flex h-12 w-12 items-center justify-center rounded-full text-xl font-bold\">3</div> <h2 class=\"mb-2 text-2xl font-semibold\">Set Job Preferences</h2> <p class=\"text-muted-foreground\">Define your job search criteria to find the most relevant opportunities.</p></div></div> <div class=\"md:w-2/3\">`;\n  Card($$payload, {\n    children: ($$payload2) => {\n      Card_content($$payload2, {\n        class: \"pt-6\",\n        children: ($$payload3) => {\n          $$payload3.out += `<div class=\"space-y-4\"><div class=\"space-y-2\"><h3 class=\"text-lg font-medium\">Specify job titles and keywords</h3> <p>Enter the job titles you're interested in and relevant keywords that match your\n                    skills and experience.</p></div> <div class=\"space-y-2\"><h3 class=\"text-lg font-medium\">Set location preferences</h3> <p>Choose your preferred work locations or indicate if you're open to remote\n                    positions.</p></div> <div class=\"space-y-2\"><h3 class=\"text-lg font-medium\">Define salary expectations</h3> <p>Set your desired salary range to ensure you're matched with jobs that meet your\n                    financial requirements.</p></div> <div class=\"space-y-2\"><h3 class=\"text-lg font-medium\">Select additional filters</h3> <p>Refine your search with filters for company size, industry, experience level,\n                    and more.</p></div> <div class=\"bg-muted rounded-lg p-4\"><p class=\"text-sm font-medium\">Pro Tip</p> <p class=\"text-muted-foreground text-sm\">Being specific with your job preferences helps our AI find better matches, but\n                    don't be too restrictive. Consider setting a wider geographic area or including\n                    related job titles to increase your opportunities.</p></div></div>`;\n        },\n        $$slots: { default: true }\n      });\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----></div></div> <div class=\"flex flex-col gap-8 md:flex-row\"><div class=\"md:w-1/3\"><div class=\"sticky top-8\"><div class=\"bg-primary text-primary-foreground mb-4 flex h-12 w-12 items-center justify-center rounded-full text-xl font-bold\">4</div> <h2 class=\"mb-2 text-2xl font-semibold\">Start Auto Apply</h2> <p class=\"text-muted-foreground\">Let our AI system apply to jobs on your behalf and track your applications.</p></div></div> <div class=\"md:w-2/3\">`;\n  Card($$payload, {\n    children: ($$payload2) => {\n      Card_content($$payload2, {\n        class: \"pt-6\",\n        children: ($$payload3) => {\n          $$payload3.out += `<div class=\"space-y-4\"><div class=\"space-y-2\"><h3 class=\"text-lg font-medium\">Configure Auto Apply settings</h3> <p>Set your application preferences, including how many jobs to apply to per day\n                    and which types of positions to prioritize.</p></div> <div class=\"space-y-2\"><h3 class=\"text-lg font-medium\">Review and approve job matches</h3> <p>Our AI will find jobs that match your criteria. Review these matches and approve\n                    the ones you want to apply to.</p></div> <div class=\"space-y-2\"><h3 class=\"text-lg font-medium\">Monitor your applications</h3> <p>Track the status of your applications in your dashboard and receive\n                    notifications for updates and responses.</p></div> <div class=\"space-y-2\"><h3 class=\"text-lg font-medium\">Prepare for interviews</h3> <p>When you start receiving interview requests, use our AI Interview Coach to\n                    practice and prepare.</p></div> <div class=\"bg-muted rounded-lg p-4\"><p class=\"text-sm font-medium\">Pro Tip</p> <p class=\"text-muted-foreground text-sm\">For best results, start with a smaller number of applications per day and review\n                    the results. You can always increase the volume as you become more comfortable\n                    with the system.</p></div></div>`;\n        },\n        $$slots: { default: true }\n      });\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----></div></div></div> <div class=\"bg-muted/50 mt-16 rounded-lg border p-8\"><h2 class=\"mb-4 text-2xl font-semibold\">Next Steps</h2> <p class=\"text-muted-foreground mb-6\">Now that you've set up the basics, here are some additional resources to help you make the\n        most of Hirli:</p> <div class=\"grid gap-4 sm:grid-cols-2\"><a href=\"/help/create-resume\" class=\"bg-card hover:bg-accent rounded-lg border p-4 transition-colors\"><h3 class=\"mb-1 font-medium\">Advanced Resume Tips</h3> <p class=\"text-muted-foreground text-sm\">Learn how to optimize your resume for specific industries and roles.</p></a> <a href=\"/help/job-preferences\" class=\"bg-card hover:bg-accent rounded-lg border p-4 transition-colors\"><h3 class=\"mb-1 font-medium\">Optimizing Job Preferences</h3> <p class=\"text-muted-foreground text-sm\">Fine-tune your job search criteria for better matches.</p></a> <a href=\"/help/auto-apply-explained\" class=\"bg-card hover:bg-accent rounded-lg border p-4 transition-colors\"><h3 class=\"mb-1 font-medium\">Understanding Auto Apply</h3> <p class=\"text-muted-foreground text-sm\">Dive deeper into how our automated application system works.</p></a> <a href=\"/help/dashboard-guide\" class=\"bg-card hover:bg-accent rounded-lg border p-4 transition-colors\"><h3 class=\"mb-1 font-medium\">Dashboard Guide</h3> <p class=\"text-muted-foreground text-sm\">Learn how to navigate and use your dashboard effectively.</p></a></div></div> <div class=\"mt-12 flex items-center justify-between\"><a href=\"/help\" class=\"text-primary inline-flex items-center text-sm font-medium hover:underline\">`;\n  Arrow_left($$payload, { class: \"mr-1 h-4 w-4\" });\n  $$payload.out += `<!----> Back to Help Center</a> <a href=\"/help/create-resume\" class=\"text-primary inline-flex items-center text-sm font-medium hover:underline\">Next: Creating Your Resume `;\n  Arrow_right($$payload, { class: \"ml-1 h-4 w-4\" });\n  $$payload.out += `<!----></a></div></div></div>`;\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAQA,SAAS,KAAK,CAAC,SAAS,EAAE;AAC1B,EAAE,GAAG,CAAC,SAAS,EAAE;AACjB,IAAI,KAAK,EAAE,uCAAuC;AAClD,IAAI,WAAW,EAAE,uHAAuH;AACxI,IAAI,QAAQ,EAAE;AACd,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC;AACpB,meAAme,CAAC;AACpe,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,YAAY,CAAC,UAAU,EAAE;AAC/B,QAAQ,KAAK,EAAE,MAAM;AACrB,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC;AAC7B;AACA;AACA,4FAA4F,CAAC;AAC7F,UAAU,gBAAgB,CAAC,UAAU,EAAE;AACvC,YAAY,KAAK,EAAE;AACnB,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,gGAAgG,CAAC;AAC9H,UAAU,gBAAgB,CAAC,UAAU,EAAE;AACvC,YAAY,KAAK,EAAE;AACnB,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,qFAAqF,CAAC;AACnH,UAAU,gBAAgB,CAAC,UAAU,EAAE;AACvC,YAAY,KAAK,EAAE;AACnB,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC;AAC7B;AACA,4CAA4C,CAAC;AAC7C,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC,OAAO,CAAC;AACR,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,ycAAyc,CAAC;AAC9d,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,YAAY,CAAC,UAAU,EAAE;AAC/B,QAAQ,KAAK,EAAE,MAAM;AACrB,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC;AAC7B;AACA;AACA;AACA;AACA,iFAAiF,CAAC;AAClF,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC,OAAO,CAAC;AACR,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,ycAAyc,CAAC;AAC9d,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,YAAY,CAAC,UAAU,EAAE;AAC/B,QAAQ,KAAK,EAAE,MAAM;AACrB,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA,sFAAsF,CAAC;AACvF,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC,OAAO,CAAC;AACR,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,ycAAyc,CAAC;AAC9d,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,YAAY,CAAC,UAAU,EAAE;AAC/B,QAAQ,KAAK,EAAE,MAAM;AACrB,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA,oDAAoD,CAAC;AACrD,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC,OAAO,CAAC;AACR,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC;AACpB,gyCAAgyC,CAAC;AACjyC,EAAE,UAAU,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AAClD,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,2KAA2K,CAAC;AAChM,EAAE,WAAW,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AACnD,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,6BAA6B,CAAC;AAClD;;;;"}