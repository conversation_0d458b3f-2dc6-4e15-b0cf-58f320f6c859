{"version": 3, "file": "_server.ts-DxB4lFa3.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/applications/_applicationId_/interviews/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../../chunks/prisma.js\";\nasync function GET({ params, locals }) {\n  console.log(\"GET interview stages for application:\", params.applicationId);\n  console.log(\"User in locals:\", locals.user ? `ID: ${locals.user.id}` : \"Not authenticated\");\n  const isDev = process.env.NODE_ENV === \"development\";\n  if (!locals.user && !isDev) {\n    console.log(\"Unauthorized access attempt - no user in locals\");\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  const { applicationId } = params;\n  try {\n    if (!isDev && locals.user) {\n      const application = await prisma.application.findUnique({\n        where: {\n          id: applicationId,\n          userId: locals.user.id\n        }\n      });\n      if (!application) {\n        console.log(\"Application not found or does not belong to user\");\n        return json({ error: \"Application not found\" }, { status: 404 });\n      }\n    }\n    const interviewStages = await prisma.interviewStage.findMany({\n      where: {\n        applicationId\n      },\n      include: {\n        questions: true\n      },\n      orderBy: {\n        stageDate: \"desc\"\n      }\n    });\n    console.log(`Found ${interviewStages.length} interview stages`);\n    return json({ interviewStages });\n  } catch (error) {\n    console.error(\"Error fetching interview stages:\", error);\n    return json({ error: \"Failed to fetch interview stages\" }, { status: 500 });\n  }\n}\nasync function POST({ request, params, locals }) {\n  console.log(\"POST new interview stage for application:\", params.applicationId);\n  console.log(\"User in locals:\", locals.user ? `ID: ${locals.user.id}` : \"Not authenticated\");\n  const isDev = process.env.NODE_ENV === \"development\";\n  if (!locals.user && !isDev) {\n    console.log(\"Unauthorized access attempt - no user in locals\");\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  const { applicationId } = params;\n  try {\n    if (!isDev && locals.user) {\n      const application2 = await prisma.application.findUnique({\n        where: {\n          id: applicationId,\n          userId: locals.user.id\n        }\n      });\n      if (!application2) {\n        console.log(\"Application not found or does not belong to user\");\n        return json({ error: \"Application not found\" }, { status: 404 });\n      }\n    }\n    const body = await request.json();\n    const { stageName, stageDate, outcome, feedback, interviewers, duration, notes } = body;\n    console.log(\"Creating interview stage with data:\", {\n      stageName,\n      stageDate,\n      outcome,\n      feedback,\n      interviewers,\n      duration\n    });\n    if (!stageName || !stageDate) {\n      return json({ error: \"Stage name and date are required\" }, { status: 400 });\n    }\n    let application = await prisma.application.findUnique({\n      where: {\n        id: applicationId\n      }\n    });\n    if (!application && isDev) {\n      console.log(\"Creating mock application for development mode\");\n      try {\n        const mockApplication = await prisma.application.create({\n          data: {\n            id: applicationId,\n            company: \"Mock Company\",\n            position: \"Mock Position\",\n            status: \"Applied\",\n            appliedDate: /* @__PURE__ */ new Date(),\n            resumeUploaded: false,\n            userId: locals.user?.id || \"dev-user-123\"\n          }\n        });\n        console.log(\"Created mock application:\", mockApplication);\n        application = mockApplication;\n      } catch (error) {\n        console.error(\"Error creating mock application:\", error);\n        if (isDev) {\n          return json(\n            {\n              error: \"Failed to create mock application\",\n              details: error.message,\n              code: error.code\n            },\n            { status: 500 }\n          );\n        } else {\n          return json({ error: \"Failed to create mock application\" }, { status: 500 });\n        }\n      }\n    } else if (!application) {\n      console.error(\"Application not found:\", applicationId);\n      return json({ error: \"Application not found\" }, { status: 404 });\n    }\n    const interviewStage = await prisma.interviewStage.create({\n      data: {\n        applicationId,\n        stageName,\n        stageDate: new Date(stageDate),\n        outcome,\n        feedback,\n        interviewers,\n        duration,\n        notes\n      }\n    });\n    console.log(\"Interview stage created successfully:\", interviewStage.id);\n    return json({ interviewStage }, { status: 201 });\n  } catch (error) {\n    console.error(\"Error creating interview stage:\", error);\n    if (isDev) {\n      return json(\n        {\n          error: \"Failed to create interview stage\",\n          details: error.message,\n          code: error.code\n        },\n        { status: 500 }\n      );\n    } else {\n      return json({ error: \"Failed to create interview stage\" }, { status: 500 });\n    }\n  }\n}\nexport {\n  GET,\n  POST\n};\n"], "names": [], "mappings": ";;;;AAEA,eAAe,GAAG,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;AACvC,EAAE,OAAO,CAAC,GAAG,CAAC,uCAAuC,EAAE,MAAM,CAAC,aAAa,CAAC;AAC5E,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,MAAM,CAAC,IAAI,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,mBAAmB,CAAC;AAC7F,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa;AACtD,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE;AAC9B,IAAI,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC;AAClE,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,MAAM;AAClC,EAAE,IAAI;AACN,IAAI,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,EAAE;AAC/B,MAAM,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;AAC9D,QAAQ,KAAK,EAAE;AACf,UAAU,EAAE,EAAE,aAAa;AAC3B,UAAU,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC;AAC9B;AACA,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,WAAW,EAAE;AACxB,QAAQ,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC;AACvE,QAAQ,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA;AACA,IAAI,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC;AACjE,MAAM,KAAK,EAAE;AACb,QAAQ;AACR,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,SAAS,EAAE;AACnB,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,SAAS,EAAE;AACnB;AACA,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,eAAe,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;AACnE,IAAI,OAAO,IAAI,CAAC,EAAE,eAAe,EAAE,CAAC;AACpC,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC;AAC5D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,kCAAkC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC/E;AACA;AACA,eAAe,IAAI,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;AACjD,EAAE,OAAO,CAAC,GAAG,CAAC,2CAA2C,EAAE,MAAM,CAAC,aAAa,CAAC;AAChF,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,MAAM,CAAC,IAAI,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,mBAAmB,CAAC;AAC7F,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa;AACtD,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE;AAC9B,IAAI,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC;AAClE,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,MAAM;AAClC,EAAE,IAAI;AACN,IAAI,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,EAAE;AAC/B,MAAM,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;AAC/D,QAAQ,KAAK,EAAE;AACf,UAAU,EAAE,EAAE,aAAa;AAC3B,UAAU,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC;AAC9B;AACA,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,YAAY,EAAE;AACzB,QAAQ,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC;AACvE,QAAQ,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,IAAI;AAC3F,IAAI,OAAO,CAAC,GAAG,CAAC,qCAAqC,EAAE;AACvD,MAAM,SAAS;AACf,MAAM,SAAS;AACf,MAAM,OAAO;AACb,MAAM,QAAQ;AACd,MAAM,YAAY;AAClB,MAAM;AACN,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,EAAE;AAClC,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,kCAAkC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjF;AACA,IAAI,IAAI,WAAW,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;AAC1D,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE;AACZ;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,WAAW,IAAI,KAAK,EAAE;AAC/B,MAAM,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC;AACnE,MAAM,IAAI;AACV,QAAQ,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;AAChE,UAAU,IAAI,EAAE;AAChB,YAAY,EAAE,EAAE,aAAa;AAC7B,YAAY,OAAO,EAAE,cAAc;AACnC,YAAY,QAAQ,EAAE,eAAe;AACrC,YAAY,MAAM,EAAE,SAAS;AAC7B,YAAY,WAAW,kBAAkB,IAAI,IAAI,EAAE;AACnD,YAAY,cAAc,EAAE,KAAK;AACjC,YAAY,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,IAAI;AACvC;AACA,SAAS,CAAC;AACV,QAAQ,OAAO,CAAC,GAAG,CAAC,2BAA2B,EAAE,eAAe,CAAC;AACjE,QAAQ,WAAW,GAAG,eAAe;AACrC,OAAO,CAAC,OAAO,KAAK,EAAE;AACtB,QAAQ,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC;AAChE,QAAQ,IAAI,KAAK,EAAE;AACnB,UAAU,OAAO,IAAI;AACrB,YAAY;AACZ,cAAc,KAAK,EAAE,mCAAmC;AACxD,cAAc,OAAO,EAAE,KAAK,CAAC,OAAO;AACpC,cAAc,IAAI,EAAE,KAAK,CAAC;AAC1B,aAAa;AACb,YAAY,EAAE,MAAM,EAAE,GAAG;AACzB,WAAW;AACX,SAAS,MAAM;AACf,UAAU,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mCAAmC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtF;AACA;AACA,KAAK,MAAM,IAAI,CAAC,WAAW,EAAE;AAC7B,MAAM,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,aAAa,CAAC;AAC5D,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtE;AACA,IAAI,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC;AAC9D,MAAM,IAAI,EAAE;AACZ,QAAQ,aAAa;AACrB,QAAQ,SAAS;AACjB,QAAQ,SAAS,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC;AACtC,QAAQ,OAAO;AACf,QAAQ,QAAQ;AAChB,QAAQ,YAAY;AACpB,QAAQ,QAAQ;AAChB,QAAQ;AACR;AACA,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,uCAAuC,EAAE,cAAc,CAAC,EAAE,CAAC;AAC3E,IAAI,OAAO,IAAI,CAAC,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpD,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC;AAC3D,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,OAAO,IAAI;AACjB,QAAQ;AACR,UAAU,KAAK,EAAE,kCAAkC;AACnD,UAAU,OAAO,EAAE,KAAK,CAAC,OAAO;AAChC,UAAU,IAAI,EAAE,KAAK,CAAC;AACtB,SAAS;AACT,QAAQ,EAAE,MAAM,EAAE,GAAG;AACrB,OAAO;AACP,KAAK,MAAM;AACX,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,kCAAkC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjF;AACA;AACA;;;;"}