{"version": 3, "file": "_server.ts-B1x7vL7y.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/health/services/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { l as logger } from \"../../../../../chunks/logger.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nconst GET = async ({ url }) => {\n  try {\n    const service = url.searchParams.get(\"service\");\n    const includeHistory = url.searchParams.get(\"history\") === \"true\";\n    if (service) {\n      const serviceStatus = await prisma.serviceStatus.findUnique({\n        where: { name: service },\n        include: includeHistory ? {\n          statusHistory: {\n            orderBy: { recordedAt: \"desc\" },\n            take: 50\n            // Last 50 status changes\n          }\n        } : void 0\n      });\n      if (!serviceStatus) {\n        return json({ error: `Service '${service}' not found` }, { status: 404 });\n      }\n      const response = {\n        service: serviceStatus.name,\n        status: serviceStatus.status,\n        description: serviceStatus.description,\n        lastCheckedAt: serviceStatus.lastCheckedAt.toISOString(),\n        timestamp: (/* @__PURE__ */ new Date()).toISOString()\n      };\n      if (includeHistory && serviceStatus.statusHistory) {\n        response.history = serviceStatus.statusHistory.map((h) => ({\n          status: h.status,\n          recordedAt: h.recordedAt.toISOString()\n        }));\n      }\n      return json(response);\n    } else {\n      const services = await prisma.serviceStatus.findMany({\n        orderBy: { name: \"asc\" },\n        include: includeHistory ? {\n          statusHistory: {\n            orderBy: { recordedAt: \"desc\" },\n            take: 10\n            // Last 10 status changes per service\n          }\n        } : void 0\n      });\n      let overallStatus = \"operational\";\n      if (services.some((s) => s.status === \"outage\")) {\n        overallStatus = \"outage\";\n      } else if (services.some((s) => s.status === \"degraded\")) {\n        overallStatus = \"degraded\";\n      } else if (services.some((s) => s.status === \"maintenance\")) {\n        overallStatus = \"maintenance\";\n      }\n      const statusCounts = services.reduce(\n        (counts, service2) => {\n          counts[service2.status] = (counts[service2.status] || 0) + 1;\n          return counts;\n        },\n        {}\n      );\n      const response = {\n        overallStatus,\n        totalServices: services.length,\n        statusCounts,\n        services: services.map((s) => {\n          const serviceData = {\n            name: s.name,\n            status: s.status,\n            description: s.description,\n            lastCheckedAt: s.lastCheckedAt.toISOString()\n          };\n          if (includeHistory && s.statusHistory) {\n            serviceData.history = s.statusHistory.map((h) => ({\n              status: h.status,\n              recordedAt: h.recordedAt.toISOString()\n            }));\n          }\n          return serviceData;\n        }),\n        timestamp: (/* @__PURE__ */ new Date()).toISOString()\n      };\n      return json(response);\n    }\n  } catch (error) {\n    logger.error(\"Error retrieving service health from database:\", error);\n    return json(\n      {\n        error: \"Failed to retrieve service health\",\n        message: error instanceof Error ? error.message : \"Unknown error\",\n        timestamp: (/* @__PURE__ */ new Date()).toISOString()\n      },\n      { status: 500 }\n    );\n  }\n};\nconst POST = async ({ request }) => {\n  try {\n    const { action, service } = await request.json();\n    if (action === \"refresh\" && service) {\n      const updatedService = await prisma.serviceStatus.update({\n        where: { name: service },\n        data: {\n          lastCheckedAt: /* @__PURE__ */ new Date(0)\n          // Force refresh by setting old timestamp\n        }\n      });\n      logger.info(`Triggered refresh for service: ${service}`);\n      return json({\n        success: true,\n        message: `Refresh triggered for ${service}`,\n        service: {\n          name: updatedService.name,\n          status: updatedService.status,\n          lastCheckedAt: updatedService.lastCheckedAt.toISOString()\n        },\n        timestamp: (/* @__PURE__ */ new Date()).toISOString()\n      });\n    } else if (action === \"refresh-all\") {\n      await prisma.serviceStatus.updateMany({\n        data: {\n          lastCheckedAt: /* @__PURE__ */ new Date(0)\n          // Force refresh by setting old timestamp\n        }\n      });\n      logger.info(\"Triggered refresh for all services\");\n      return json({\n        success: true,\n        message: \"Refresh triggered for all services\",\n        timestamp: (/* @__PURE__ */ new Date()).toISOString()\n      });\n    } else {\n      return json(\n        {\n          error: \"Invalid action or missing service parameter\",\n          validActions: [\"refresh\", \"refresh-all\"],\n          timestamp: (/* @__PURE__ */ new Date()).toISOString()\n        },\n        { status: 400 }\n      );\n    }\n  } catch (error) {\n    logger.error(\"Error processing service health action:\", error);\n    return json(\n      {\n        error: \"Failed to process service health action\",\n        message: error instanceof Error ? error.message : \"Unknown error\",\n        timestamp: (/* @__PURE__ */ new Date()).toISOString()\n      },\n      { status: 500 }\n    );\n  }\n};\nconst OPTIONS = async () => {\n  return new Response(null, {\n    status: 200,\n    headers: {\n      \"Access-Control-Allow-Origin\": \"*\",\n      \"Access-Control-Allow-Methods\": \"GET, POST, OPTIONS\",\n      \"Access-Control-Allow-Headers\": \"Content-Type, Authorization\"\n    }\n  });\n};\nexport {\n  GET,\n  OPTIONS,\n  POST\n};\n"], "names": [], "mappings": ";;;;;AAGK,MAAC,GAAG,GAAG,OAAO,EAAE,GAAG,EAAE,KAAK;AAC/B,EAAE,IAAI;AACN,IAAI,MAAM,OAAO,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC;AACnD,IAAI,MAAM,cAAc,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,MAAM;AACrE,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC;AAClE,QAAQ,KAAK,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE;AAChC,QAAQ,OAAO,EAAE,cAAc,GAAG;AAClC,UAAU,aAAa,EAAE;AACzB,YAAY,OAAO,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;AAC3C,YAAY,IAAI,EAAE;AAClB;AACA;AACA,SAAS,GAAG,KAAK;AACjB,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,aAAa,EAAE;AAC1B,QAAQ,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,WAAW,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjF;AACA,MAAM,MAAM,QAAQ,GAAG;AACvB,QAAQ,OAAO,EAAE,aAAa,CAAC,IAAI;AACnC,QAAQ,MAAM,EAAE,aAAa,CAAC,MAAM;AACpC,QAAQ,WAAW,EAAE,aAAa,CAAC,WAAW;AAC9C,QAAQ,aAAa,EAAE,aAAa,CAAC,aAAa,CAAC,WAAW,EAAE;AAChE,QAAQ,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC3D,OAAO;AACP,MAAM,IAAI,cAAc,IAAI,aAAa,CAAC,aAAa,EAAE;AACzD,QAAQ,QAAQ,CAAC,OAAO,GAAG,aAAa,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM;AACnE,UAAU,MAAM,EAAE,CAAC,CAAC,MAAM;AAC1B,UAAU,UAAU,EAAE,CAAC,CAAC,UAAU,CAAC,WAAW;AAC9C,SAAS,CAAC,CAAC;AACX;AACA,MAAM,OAAO,IAAI,CAAC,QAAQ,CAAC;AAC3B,KAAK,MAAM;AACX,MAAM,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;AAC3D,QAAQ,OAAO,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;AAChC,QAAQ,OAAO,EAAE,cAAc,GAAG;AAClC,UAAU,aAAa,EAAE;AACzB,YAAY,OAAO,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;AAC3C,YAAY,IAAI,EAAE;AAClB;AACA;AACA,SAAS,GAAG,KAAK;AACjB,OAAO,CAAC;AACR,MAAM,IAAI,aAAa,GAAG,aAAa;AACvC,MAAM,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,QAAQ,CAAC,EAAE;AACvD,QAAQ,aAAa,GAAG,QAAQ;AAChC,OAAO,MAAM,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,UAAU,CAAC,EAAE;AAChE,QAAQ,aAAa,GAAG,UAAU;AAClC,OAAO,MAAM,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,aAAa,CAAC,EAAE;AACnE,QAAQ,aAAa,GAAG,aAAa;AACrC;AACA,MAAM,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM;AAC1C,QAAQ,CAAC,MAAM,EAAE,QAAQ,KAAK;AAC9B,UAAU,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;AACtE,UAAU,OAAO,MAAM;AACvB,SAAS;AACT,QAAQ;AACR,OAAO;AACP,MAAM,MAAM,QAAQ,GAAG;AACvB,QAAQ,aAAa;AACrB,QAAQ,aAAa,EAAE,QAAQ,CAAC,MAAM;AACtC,QAAQ,YAAY;AACpB,QAAQ,QAAQ,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK;AACtC,UAAU,MAAM,WAAW,GAAG;AAC9B,YAAY,IAAI,EAAE,CAAC,CAAC,IAAI;AACxB,YAAY,MAAM,EAAE,CAAC,CAAC,MAAM;AAC5B,YAAY,WAAW,EAAE,CAAC,CAAC,WAAW;AACtC,YAAY,aAAa,EAAE,CAAC,CAAC,aAAa,CAAC,WAAW;AACtD,WAAW;AACX,UAAU,IAAI,cAAc,IAAI,CAAC,CAAC,aAAa,EAAE;AACjD,YAAY,WAAW,CAAC,OAAO,GAAG,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM;AAC9D,cAAc,MAAM,EAAE,CAAC,CAAC,MAAM;AAC9B,cAAc,UAAU,EAAE,CAAC,CAAC,UAAU,CAAC,WAAW;AAClD,aAAa,CAAC,CAAC;AACf;AACA,UAAU,OAAO,WAAW;AAC5B,SAAS,CAAC;AACV,QAAQ,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC3D,OAAO;AACP,MAAM,OAAO,IAAI,CAAC,QAAQ,CAAC;AAC3B;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,gDAAgD,EAAE,KAAK,CAAC;AACzE,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,KAAK,EAAE,mCAAmC;AAClD,QAAQ,OAAO,EAAE,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,eAAe;AACzE,QAAQ,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC3D,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;AACK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK;AACpC,EAAE,IAAI;AACN,IAAI,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACpD,IAAI,IAAI,MAAM,KAAK,SAAS,IAAI,OAAO,EAAE;AACzC,MAAM,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;AAC/D,QAAQ,KAAK,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE;AAChC,QAAQ,IAAI,EAAE;AACd,UAAU,aAAa,kBAAkB,IAAI,IAAI,CAAC,CAAC;AACnD;AACA;AACA,OAAO,CAAC;AACR,MAAM,MAAM,CAAC,IAAI,CAAC,CAAC,+BAA+B,EAAE,OAAO,CAAC,CAAC,CAAC;AAC9D,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,OAAO,EAAE,CAAC,sBAAsB,EAAE,OAAO,CAAC,CAAC;AACnD,QAAQ,OAAO,EAAE;AACjB,UAAU,IAAI,EAAE,cAAc,CAAC,IAAI;AACnC,UAAU,MAAM,EAAE,cAAc,CAAC,MAAM;AACvC,UAAU,aAAa,EAAE,cAAc,CAAC,aAAa,CAAC,WAAW;AACjE,SAAS;AACT,QAAQ,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC3D,OAAO,CAAC;AACR,KAAK,MAAM,IAAI,MAAM,KAAK,aAAa,EAAE;AACzC,MAAM,MAAM,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC;AAC5C,QAAQ,IAAI,EAAE;AACd,UAAU,aAAa,kBAAkB,IAAI,IAAI,CAAC,CAAC;AACnD;AACA;AACA,OAAO,CAAC;AACR,MAAM,MAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC;AACvD,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,OAAO,EAAE,oCAAoC;AACrD,QAAQ,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC3D,OAAO,CAAC;AACR,KAAK,MAAM;AACX,MAAM,OAAO,IAAI;AACjB,QAAQ;AACR,UAAU,KAAK,EAAE,6CAA6C;AAC9D,UAAU,YAAY,EAAE,CAAC,SAAS,EAAE,aAAa,CAAC;AAClD,UAAU,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC7D,SAAS;AACT,QAAQ,EAAE,MAAM,EAAE,GAAG;AACrB,OAAO;AACP;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC;AAClE,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,KAAK,EAAE,yCAAyC;AACxD,QAAQ,OAAO,EAAE,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,eAAe;AACzE,QAAQ,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC3D,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;AACK,MAAC,OAAO,GAAG,YAAY;AAC5B,EAAE,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE;AAC5B,IAAI,MAAM,EAAE,GAAG;AACf,IAAI,OAAO,EAAE;AACb,MAAM,6BAA6B,EAAE,GAAG;AACxC,MAAM,8BAA8B,EAAE,oBAAoB;AAC1D,MAAM,8BAA8B,EAAE;AACtC;AACA,GAAG,CAAC;AACJ;;;;"}