{"version": 3, "file": "_page.svelte-DxsAo16P.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/auth/sign-in/_page.svelte.js"], "sourcesContent": ["import \"clsx\";\nimport { y as pop, w as push } from \"../../../../chunks/index3.js\";\nimport { S as SEO } from \"../../../../chunks/SEO.js\";\nimport { g as goto } from \"../../../../chunks/client.js\";\nimport { S as SignIn } from \"../../../../chunks/SignIn.js\";\nimport { a as toast } from \"../../../../chunks/Toaster.svelte_svelte_type_style_lang.js\";\nfunction _page($$payload, $$props) {\n  push();\n  let isLoading = false;\n  async function loginWithEmail(email, password) {\n    isLoading = true;\n    const res = await fetch(\"/api/auth/login\", {\n      method: \"POST\",\n      headers: { \"Content-Type\": \"application/json\" },\n      body: JSON.stringify({ email, password })\n    });\n    isLoading = false;\n    if (res.ok) {\n      console.log(\"Login successful\");\n      toast.success(\"Login Successful\", {\n        description: \"Welcome back! You are now logged in.\"\n      });\n      goto();\n    } else {\n      const errorData = await res.json();\n      console.error(\"Login failed\", errorData);\n      if (errorData.error === \"Email not verified\") {\n        toast.error(\"Email Not Verified\", {\n          description: errorData.message || \"Please verify your email address before signing in.\",\n          action: {\n            label: \"Resend\",\n            onClick: () => resendVerificationEmail(email)\n          }\n        });\n      } else {\n        toast.error(\"Login Error\", {\n          description: errorData?.message || \"Please check your credentials and try again.\"\n        });\n      }\n    }\n  }\n  async function resendVerificationEmail(userEmail) {\n    try {\n      const res = await fetch(\"/api/auth/resend-verification\", {\n        method: \"POST\",\n        headers: { \"Content-Type\": \"application/json\" },\n        body: JSON.stringify({ email: userEmail })\n      });\n      if (res.ok) {\n        toast.success(\"Verification Email Sent\", {\n          description: \"Please check your inbox for the verification email.\"\n        });\n      } else {\n        const errorData = await res.json();\n        toast.error(\"Error\", {\n          description: errorData?.message || \"Failed to resend verification email.\"\n        });\n      }\n    } catch (error) {\n      console.error(\"Error resending verification email:\", error);\n      toast.error(\"Error\", {\n        description: \"An error occurred while resending the verification email.\"\n      });\n    }\n  }\n  SEO($$payload, {\n    title: \"Sign In | Hirli\",\n    description: \"Sign in to your Hirli account to access your job applications, resume, and personalized job recommendations.\",\n    keywords: \"sign in, login, job application, career, automation, Hirli account\"\n  });\n  $$payload.out += `<!----> <div class=\"mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]\"><div class=\"mb-8 flex flex-col space-y-2 text-left\"><h1 class=\"text-3xl font-semibold tracking-tight\">Sign in</h1> <p class=\"text-muted-foreground text-md font-light\">Don't have an account? <a class=\"underline\" href=\"/auth/sign-up\">Create one</a></p></div> `;\n  SignIn($$payload, {\n    isLoading,\n    onEmailPasswordLogin: loginWithEmail\n  });\n  $$payload.out += `<!----></div>`;\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAMA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,SAAS,GAAG,KAAK;AACvB,EAAE,eAAe,cAAc,CAAC,KAAK,EAAE,QAAQ,EAAE;AACjD,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,MAAM,GAAG,GAAG,MAAM,KAAK,CAAC,iBAAiB,EAAE;AAC/C,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;AACrD,MAAM,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE;AAC9C,KAAK,CAAC;AACN,IAAI,SAAS,GAAG,KAAK;AACrB,IAAI,IAAI,GAAG,CAAC,EAAE,EAAE;AAChB,MAAM,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC;AACrC,MAAM,KAAK,CAAC,OAAO,CAAC,kBAAkB,EAAE;AACxC,QAAQ,WAAW,EAAE;AACrB,OAAO,CAAC;AACR,MAAM,IAAI,EAAE;AACZ,KAAK,MAAM;AACX,MAAM,MAAM,SAAS,GAAG,MAAM,GAAG,CAAC,IAAI,EAAE;AACxC,MAAM,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,SAAS,CAAC;AAC9C,MAAM,IAAI,SAAS,CAAC,KAAK,KAAK,oBAAoB,EAAE;AACpD,QAAQ,KAAK,CAAC,KAAK,CAAC,oBAAoB,EAAE;AAC1C,UAAU,WAAW,EAAE,SAAS,CAAC,OAAO,IAAI,qDAAqD;AACjG,UAAU,MAAM,EAAE;AAClB,YAAY,KAAK,EAAE,QAAQ;AAC3B,YAAY,OAAO,EAAE,MAAM,uBAAuB,CAAC,KAAK;AACxD;AACA,SAAS,CAAC;AACV,OAAO,MAAM;AACb,QAAQ,KAAK,CAAC,KAAK,CAAC,aAAa,EAAE;AACnC,UAAU,WAAW,EAAE,SAAS,EAAE,OAAO,IAAI;AAC7C,SAAS,CAAC;AACV;AACA;AACA;AACA,EAAE,eAAe,uBAAuB,CAAC,SAAS,EAAE;AACpD,IAAI,IAAI;AACR,MAAM,MAAM,GAAG,GAAG,MAAM,KAAK,CAAC,+BAA+B,EAAE;AAC/D,QAAQ,MAAM,EAAE,MAAM;AACtB,QAAQ,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;AACvD,QAAQ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE;AACjD,OAAO,CAAC;AACR,MAAM,IAAI,GAAG,CAAC,EAAE,EAAE;AAClB,QAAQ,KAAK,CAAC,OAAO,CAAC,yBAAyB,EAAE;AACjD,UAAU,WAAW,EAAE;AACvB,SAAS,CAAC;AACV,OAAO,MAAM;AACb,QAAQ,MAAM,SAAS,GAAG,MAAM,GAAG,CAAC,IAAI,EAAE;AAC1C,QAAQ,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE;AAC7B,UAAU,WAAW,EAAE,SAAS,EAAE,OAAO,IAAI;AAC7C,SAAS,CAAC;AACV;AACA,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC;AACjE,MAAM,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE;AAC3B,QAAQ,WAAW,EAAE;AACrB,OAAO,CAAC;AACR;AACA;AACA,EAAE,GAAG,CAAC,SAAS,EAAE;AACjB,IAAI,KAAK,EAAE,iBAAiB;AAC5B,IAAI,WAAW,EAAE,8GAA8G;AAC/H,IAAI,QAAQ,EAAE;AACd,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,yVAAyV,CAAC;AAC9W,EAAE,MAAM,CAAC,SAAS,EAAE;AACpB,IAAI,SAAS;AACb,IAAI,oBAAoB,EAAE;AAC1B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AAClC,EAAE,GAAG,EAAE;AACP;;;;"}