{"version": 3, "file": "_server.ts-BiJFM7SY.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/email/analytics/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { l as logger } from \"../../../../../chunks/logger.js\";\nasync function GET() {\n  try {\n    logger.info(\"Fetching email analytics\");\n    const analytics = {\n      summary: {\n        sent: 1250,\n        delivered: 1200,\n        opened: 850,\n        clicked: 320,\n        bounced: 30,\n        complained: 5,\n        unsubscribed: 15\n      },\n      deliveryRate: 96,\n      // percentage\n      openRate: 70.8,\n      // percentage\n      clickRate: 37.6,\n      // percentage\n      topTemplates: [\n        { name: \"welcome\", label: \"Welcome Email\", sent: 450, opened: 380, clicked: 210 },\n        { name: \"verification\", label: \"Email Verification\", sent: 320, opened: 290, clicked: 250 },\n        { name: \"password-reset\", label: \"Password Reset\", sent: 180, opened: 170, clicked: 160 },\n        { name: \"job-match\", label: \"Job Match\", sent: 150, opened: 120, clicked: 80 },\n        { name: \"weekly-summary\", label: \"Weekly Summary\", sent: 150, opened: 90, clicked: 40 }\n      ],\n      recentEmails: [\n        { id: \"email_1\", to: \"<EMAIL>\", template: \"welcome\", status: \"delivered\", sentAt: \"2023-06-01T10:30:00Z\" },\n        { id: \"email_2\", to: \"<EMAIL>\", template: \"verification\", status: \"opened\", sentAt: \"2023-06-01T11:15:00Z\" },\n        { id: \"email_3\", to: \"<EMAIL>\", template: \"password-reset\", status: \"clicked\", sentAt: \"2023-06-01T12:00:00Z\" },\n        { id: \"email_4\", to: \"<EMAIL>\", template: \"job-match\", status: \"delivered\", sentAt: \"2023-06-01T13:30:00Z\" },\n        { id: \"email_5\", to: \"<EMAIL>\", template: \"weekly-summary\", status: \"bounced\", sentAt: \"2023-06-01T14:45:00Z\" }\n      ],\n      dailyStats: [\n        { date: \"2023-05-26\", sent: 120, delivered: 115, opened: 80, clicked: 30 },\n        { date: \"2023-05-27\", sent: 150, delivered: 145, opened: 100, clicked: 40 },\n        { date: \"2023-05-28\", sent: 100, delivered: 95, opened: 70, clicked: 25 },\n        { date: \"2023-05-29\", sent: 200, delivered: 190, opened: 130, clicked: 50 },\n        { date: \"2023-05-30\", sent: 180, delivered: 175, opened: 120, clicked: 45 },\n        { date: \"2023-05-31\", sent: 220, delivered: 210, opened: 150, clicked: 60 },\n        { date: \"2023-06-01\", sent: 280, delivered: 270, opened: 200, clicked: 70 }\n      ]\n    };\n    return json(analytics);\n  } catch (error) {\n    logger.error(\"Error fetching email analytics:\", error);\n    return json({ error: \"Failed to fetch email analytics\" }, { status: 500 });\n  }\n}\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;AAEA,eAAe,GAAG,GAAG;AACrB,EAAE,IAAI;AACN,IAAI,MAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC;AAC3C,IAAI,MAAM,SAAS,GAAG;AACtB,MAAM,OAAO,EAAE;AACf,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,SAAS,EAAE,IAAI;AACvB,QAAQ,MAAM,EAAE,GAAG;AACnB,QAAQ,OAAO,EAAE,GAAG;AACpB,QAAQ,OAAO,EAAE,EAAE;AACnB,QAAQ,UAAU,EAAE,CAAC;AACrB,QAAQ,YAAY,EAAE;AACtB,OAAO;AACP,MAAM,YAAY,EAAE,EAAE;AACtB;AACA,MAAM,QAAQ,EAAE,IAAI;AACpB;AACA,MAAM,SAAS,EAAE,IAAI;AACrB;AACA,MAAM,YAAY,EAAE;AACpB,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,eAAe,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE;AACzF,QAAQ,EAAE,IAAI,EAAE,cAAc,EAAE,KAAK,EAAE,oBAAoB,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE;AACnG,QAAQ,EAAE,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,gBAAgB,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE;AACjG,QAAQ,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,EAAE,EAAE;AACtF,QAAQ,EAAE,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,gBAAgB,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE;AAC7F,OAAO;AACP,MAAM,YAAY,EAAE;AACpB,QAAQ,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,mBAAmB,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,sBAAsB,EAAE;AAC5H,QAAQ,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,mBAAmB,EAAE,QAAQ,EAAE,cAAc,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,sBAAsB,EAAE;AAC9H,QAAQ,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,mBAAmB,EAAE,QAAQ,EAAE,gBAAgB,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,sBAAsB,EAAE;AACjI,QAAQ,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,mBAAmB,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,sBAAsB,EAAE;AAC9H,QAAQ,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,mBAAmB,EAAE,QAAQ,EAAE,gBAAgB,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,sBAAsB;AAC/H,OAAO;AACP,MAAM,UAAU,EAAE;AAClB,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE;AAClF,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,EAAE,EAAE;AACnF,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,EAAE,SAAS,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE;AACjF,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,EAAE,EAAE;AACnF,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,EAAE,EAAE;AACnF,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,EAAE,EAAE;AACnF,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,EAAE;AACjF;AACA,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC;AAC1B,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC;AAC1D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,iCAAiC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9E;AACA;;;;"}