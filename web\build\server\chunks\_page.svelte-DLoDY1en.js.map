{"version": 3, "file": "_page.svelte-DLoDY1en.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/resources/_slug_/_page.svelte.js"], "sourcesContent": ["import { W as stringify, V as escape_html, R as attr, U as ensure_array_like, N as bind_props, y as pop, w as push } from \"../../../../chunks/index3.js\";\nimport { S as SEO } from \"../../../../chunks/SEO.js\";\nimport { C as Card } from \"../../../../chunks/card.js\";\nimport { C as Card_content } from \"../../../../chunks/card-content.js\";\nimport { C as Card_header } from \"../../../../chunks/card-header.js\";\nimport { C as Card_title } from \"../../../../chunks/card-title.js\";\nimport { u as urlFor } from \"../../../../chunks/client2.js\";\nimport { P as PortableText } from \"../../../../chunks/PortableText.js\";\nimport { A as Arrow_left } from \"../../../../chunks/arrow-left.js\";\nimport { A as Arrow_right } from \"../../../../chunks/arrow-right.js\";\nfunction _page($$payload, $$props) {\n  push();\n  let data = $$props[\"data\"];\n  const resource = data.resource;\n  const relatedResources = resource.relatedResources || [];\n  SEO($$payload, {\n    title: `${stringify(resource.title)} | Hirli Resources`,\n    description: resource.description,\n    keywords: resource.tags ? `career resources, job search, ${resource.tags.join(\", \")}` : `career resources, job search, ${resource.title.toLowerCase()}, ${resource.resourceType || resource.type || \"resource\"}`\n  });\n  $$payload.out += `<!----> <div class=\"container mx-auto px-4 py-12\"><div class=\"mb-8\"><a href=\"/resources\" class=\"text-primary inline-flex items-center text-sm hover:underline\">`;\n  Arrow_left($$payload, { class: \"mr-1 h-4 w-4\" });\n  $$payload.out += `<!----> Back to Resources</a></div> <div class=\"grid gap-8 lg:grid-cols-3\"><div class=\"lg:col-span-2\"><div class=\"mb-8\"><h1 class=\"mb-4 text-3xl font-bold\">${escape_html(resource.title)}</h1> <p class=\"text-lg text-gray-600\">${escape_html(resource.description)}</p></div> `;\n  if (resource.mainImage) {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<div class=\"mb-8 overflow-hidden rounded-lg\"><img${attr(\"src\", urlFor(resource.mainImage, { width: 800 }))}${attr(\"alt\", resource.mainImage.alt || resource.title)} class=\"w-full\"/></div>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--> `;\n  if (resource.content) {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<div class=\"prose prose-lg max-w-none\">`;\n    PortableText($$payload, { value: resource.content });\n    $$payload.out += `<!----></div>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--> `;\n  if (resource.downloadUrl) {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<div class=\"mt-8\"><a${attr(\"href\", resource.downloadUrl)} target=\"_blank\" rel=\"noopener noreferrer\" class=\"bg-primary hover:bg-primary/90 inline-flex items-center rounded-md px-4 py-2 text-white\"><svg xmlns=\"http://www.w3.org/2000/svg\" class=\"mr-2 h-5 w-5\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\"></path><polyline points=\"7 10 12 15 17 10\"></polyline><line x1=\"12\" y1=\"15\" x2=\"12\" y2=\"3\"></line></svg> Download Resource</a></div>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--></div> <div><div class=\"space-y-6\">`;\n  Card($$payload, {\n    children: ($$payload2) => {\n      Card_header($$payload2, {\n        children: ($$payload3) => {\n          Card_title($$payload3, {\n            children: ($$payload4) => {\n              $$payload4.out += `<!---->Resource Details`;\n            },\n            $$slots: { default: true }\n          });\n        },\n        $$slots: { default: true }\n      });\n      $$payload2.out += `<!----> `;\n      Card_content($$payload2, {\n        children: ($$payload3) => {\n          $$payload3.out += `<dl class=\"space-y-4\"><div><dt class=\"text-sm font-medium text-gray-500\">Category</dt> <dd class=\"text-gray-700\">${escape_html(resource.category)}</dd></div> <div><dt class=\"text-sm font-medium text-gray-500\">Type</dt> <dd class=\"capitalize text-gray-700\">${escape_html(resource.resourceType || resource.type || \"Resource\")}</dd></div> `;\n          if (resource.tags && resource.tags.length > 0) {\n            $$payload3.out += \"<!--[-->\";\n            const each_array = ensure_array_like(resource.tags);\n            $$payload3.out += `<div><dt class=\"text-sm font-medium text-gray-500\">Tags</dt> <dd class=\"text-gray-700\"><div class=\"mt-1 flex flex-wrap gap-2\"><!--[-->`;\n            for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n              let tag = each_array[$$index];\n              $$payload3.out += `<span class=\"bg-primary/10 text-primary rounded-full px-3 py-1 text-xs\">${escape_html(tag)}</span>`;\n            }\n            $$payload3.out += `<!--]--></div></dd></div>`;\n          } else {\n            $$payload3.out += \"<!--[!-->\";\n          }\n          $$payload3.out += `<!--]--> `;\n          if (resource.publishedAt) {\n            $$payload3.out += \"<!--[-->\";\n            $$payload3.out += `<div><dt class=\"text-sm font-medium text-gray-500\">Published</dt> <dd class=\"text-gray-700\">${escape_html(new Date(resource.publishedAt).toLocaleDateString(\"en-US\", {\n              year: \"numeric\",\n              month: \"long\",\n              day: \"numeric\"\n            }))}</dd></div>`;\n          } else {\n            $$payload3.out += \"<!--[!-->\";\n          }\n          $$payload3.out += `<!--]--></dl>`;\n        },\n        $$slots: { default: true }\n      });\n      $$payload2.out += `<!---->`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----> `;\n  if (relatedResources.length > 0) {\n    $$payload.out += \"<!--[-->\";\n    Card($$payload, {\n      children: ($$payload2) => {\n        Card_header($$payload2, {\n          children: ($$payload3) => {\n            Card_title($$payload3, {\n              children: ($$payload4) => {\n                $$payload4.out += `<!---->Related Resources`;\n              },\n              $$slots: { default: true }\n            });\n          },\n          $$slots: { default: true }\n        });\n        $$payload2.out += `<!----> `;\n        Card_content($$payload2, {\n          children: ($$payload3) => {\n            const each_array_1 = ensure_array_like(relatedResources);\n            $$payload3.out += `<div class=\"space-y-4\"><!--[-->`;\n            for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {\n              let relatedResource = each_array_1[$$index_1];\n              $$payload3.out += `<div class=\"rounded-lg border p-4\"><h3 class=\"mb-1 font-medium\">${escape_html(relatedResource.title)}</h3> <p class=\"mb-2 text-sm text-gray-600\">${escape_html(relatedResource.description.substring(0, 100))}...</p> <a${attr(\"href\", `/resources/${relatedResource.slug.current}`)} class=\"text-primary inline-flex items-center text-sm font-medium hover:underline\">View Resource `;\n              Arrow_right($$payload3, { class: \"ml-1 h-3 w-3\" });\n              $$payload3.out += `<!----></a></div>`;\n            }\n            $$payload3.out += `<!--]--></div>`;\n          },\n          $$slots: { default: true }\n        });\n        $$payload2.out += `<!---->`;\n      },\n      $$slots: { default: true }\n    });\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--></div></div></div></div>`;\n  bind_props($$props, { data });\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAUA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC;AAC5B,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ;AAChC,EAAE,MAAM,gBAAgB,GAAG,QAAQ,CAAC,gBAAgB,IAAI,EAAE;AAC1D,EAAE,GAAG,CAAC,SAAS,EAAE;AACjB,IAAI,KAAK,EAAE,CAAC,EAAE,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,kBAAkB,CAAC;AAC3D,IAAI,WAAW,EAAE,QAAQ,CAAC,WAAW;AACrC,IAAI,QAAQ,EAAE,QAAQ,CAAC,IAAI,GAAG,CAAC,8BAA8B,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,8BAA8B,EAAE,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,EAAE,EAAE,QAAQ,CAAC,YAAY,IAAI,QAAQ,CAAC,IAAI,IAAI,UAAU,CAAC;AACnN,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,+JAA+J,CAAC;AACpL,EAAE,UAAU,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AAClD,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,4JAA4J,EAAE,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,uCAAuC,EAAE,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,WAAW,CAAC;AACrS,EAAE,IAAI,QAAQ,CAAC,SAAS,EAAE;AAC1B,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,iDAAiD,EAAE,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC,GAAG,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,uBAAuB,CAAC;AACjN,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AAC9B,EAAE,IAAI,QAAQ,CAAC,OAAO,EAAE;AACxB,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,uCAAuC,CAAC;AAC9D,IAAI,YAAY,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,OAAO,EAAE,CAAC;AACxD,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACpC,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AAC9B,EAAE,IAAI,QAAQ,CAAC,WAAW,EAAE;AAC5B,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC,sfAAsf,CAAC;AACtkB,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,2CAA2C,CAAC;AAChE,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,WAAW,CAAC,UAAU,EAAE;AAC9B,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,UAAU,CAAC,UAAU,EAAE;AACjC,YAAY,QAAQ,EAAE,CAAC,UAAU,KAAK;AACtC,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC;AACzD,aAAa;AACb,YAAY,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACpC,WAAW,CAAC;AACZ,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC,OAAO,CAAC;AACR,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAClC,MAAM,YAAY,CAAC,UAAU,EAAE;AAC/B,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,iHAAiH,EAAE,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,8GAA8G,EAAE,WAAW,CAAC,QAAQ,CAAC,YAAY,IAAI,QAAQ,CAAC,IAAI,IAAI,UAAU,CAAC,CAAC,YAAY,CAAC;AAC9W,UAAU,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;AACzD,YAAY,UAAU,CAAC,GAAG,IAAI,UAAU;AACxC,YAAY,MAAM,UAAU,GAAG,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC;AAC/D,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,sIAAsI,CAAC;AACtK,YAAY,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AAC/F,cAAc,IAAI,GAAG,GAAG,UAAU,CAAC,OAAO,CAAC;AAC3C,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,wEAAwE,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC;AACpI;AACA,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,yBAAyB,CAAC;AACzD,WAAW,MAAM;AACjB,YAAY,UAAU,CAAC,GAAG,IAAI,WAAW;AACzC;AACA,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AACvC,UAAU,IAAI,QAAQ,CAAC,WAAW,EAAE;AACpC,YAAY,UAAU,CAAC,GAAG,IAAI,UAAU;AACxC,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,4FAA4F,EAAE,WAAW,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,kBAAkB,CAAC,OAAO,EAAE;AACpM,cAAc,IAAI,EAAE,SAAS;AAC7B,cAAc,KAAK,EAAE,MAAM;AAC3B,cAAc,GAAG,EAAE;AACnB,aAAa,CAAC,CAAC,CAAC,WAAW,CAAC;AAC5B,WAAW,MAAM;AACjB,YAAY,UAAU,CAAC,GAAG,IAAI,WAAW;AACzC;AACA,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AAC3C,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC,OAAO,CAAC;AACR,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACjC,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE;AACnC,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,IAAI,CAAC,SAAS,EAAE;AACpB,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,WAAW,CAAC,UAAU,EAAE;AAChC,UAAU,QAAQ,EAAE,CAAC,UAAU,KAAK;AACpC,YAAY,UAAU,CAAC,UAAU,EAAE;AACnC,cAAc,QAAQ,EAAE,CAAC,UAAU,KAAK;AACxC,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,wBAAwB,CAAC;AAC5D,eAAe;AACf,cAAc,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACtC,aAAa,CAAC;AACd,WAAW;AACX,UAAU,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAClC,SAAS,CAAC;AACV,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACpC,QAAQ,YAAY,CAAC,UAAU,EAAE;AACjC,UAAU,QAAQ,EAAE,CAAC,UAAU,KAAK;AACpC,YAAY,MAAM,YAAY,GAAG,iBAAiB,CAAC,gBAAgB,CAAC;AACpE,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,+BAA+B,CAAC;AAC/D,YAAY,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,QAAQ,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,QAAQ,EAAE,SAAS,EAAE,EAAE;AACvG,cAAc,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC3D,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,gEAAgE,EAAE,WAAW,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,4CAA4C,EAAE,WAAW,CAAC,eAAe,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,WAAW,EAAE,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,iGAAiG,CAAC;AACxZ,cAAc,WAAW,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AAChE,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC;AACnD;AACA,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AAC9C,WAAW;AACX,UAAU,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAClC,SAAS,CAAC;AACV,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B,KAAK,CAAC;AACN,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,gCAAgC,CAAC;AACrD,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,CAAC;AAC/B,EAAE,GAAG,EAAE;AACP;;;;"}