{"version": 3, "file": "_server.ts-DV40Yetk.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/resume/generate/status/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { l as logger } from \"../../../../../../chunks/logger.js\";\nconst GET = async () => {\n  try {\n    const status = {\n      operational: true,\n      queueSize: 3,\n      averageGenerationTime: 2.5,\n      // seconds\n      dailyGenerations: 120,\n      successRate: 98.5\n      // percentage\n    };\n    return json({\n      ...status,\n      timestamp: (/* @__PURE__ */ new Date()).toISOString()\n    });\n  } catch (error) {\n    logger.error(\"Error checking resume generation status:\", error);\n    return json(\n      {\n        error: \"Failed to check resume generation status\",\n        timestamp: (/* @__PURE__ */ new Date()).toISOString()\n      },\n      { status: 500 }\n    );\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;AAEK,MAAC,GAAG,GAAG,YAAY;AACxB,EAAE,IAAI;AACN,IAAI,MAAM,MAAM,GAAG;AACnB,MAAM,WAAW,EAAE,IAAI;AACvB,MAAM,SAAS,EAAE,CAAC;AAClB,MAAM,qBAAqB,EAAE,GAAG;AAChC;AACA,MAAM,gBAAgB,EAAE,GAAG;AAC3B,MAAM,WAAW,EAAE;AACnB;AACA,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,GAAG,MAAM;AACf,MAAM,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AACzD,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC;AACnE,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,KAAK,EAAE,0CAA0C;AACzD,QAAQ,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC3D,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;;;;"}