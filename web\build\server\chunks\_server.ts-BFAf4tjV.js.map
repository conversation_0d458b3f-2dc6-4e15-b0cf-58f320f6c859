{"version": 3, "file": "_server.ts-BFAf4tjV.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/resumes/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../chunks/prisma.js\";\nimport { e as ensureUniqueDocumentName } from \"../../../../chunks/documentNameUniqueness.js\";\nimport { d as determineDocumentSource } from \"../../../../chunks/documentSource.js\";\nimport { c as canCreateResume, a as trackResumeCreation } from \"../../../../chunks/resume-usage.js\";\nconst GET = async ({ locals }) => {\n  if (!locals.user) {\n    return new Response(\"Unauthorized\", { status: 401 });\n  }\n  const data = await prisma.resume.findMany({\n    where: {\n      document: {\n        userId: locals.user.id\n      }\n    },\n    orderBy: { createdAt: \"desc\" }\n  });\n  await prisma.$disconnect();\n  return json(data);\n};\nconst POST = async ({ request, locals }) => {\n  try {\n    const user = locals.user;\n    if (!user) {\n      return new Response(\"Unauthorized\", { status: 401 });\n    }\n    const userId = user.id;\n    console.log(\"Creating new resume for user:\", userId);\n    const canCreate = await canCreateResume(userId);\n    if (!canCreate) {\n      return json(\n        {\n          error: \"You have reached your limit of resume versions. Please upgrade your plan to create more resumes.\",\n          limitReached: true\n        },\n        { status: 403 }\n      );\n    }\n    const data = await request.json();\n    let label = data.name || \"New Resume\";\n    const profileId = data.profileId && data.profileId.trim() ? data.profileId : null;\n    label = await ensureUniqueDocumentName(label, userId, \"resume\");\n    const document = await prisma.document.create({\n      data: {\n        label,\n        fileUrl: \"/placeholder.pdf\",\n        // Use a placeholder URL since fileUrl is required\n        filePath: null,\n        // Will be generated later\n        fileName: null,\n        // Will be set when file is generated\n        type: \"resume\",\n        contentType: \"application/pdf\",\n        storageType: \"local\",\n        isDefault: false,\n        // Connect to the user instead of just providing the userId\n        user: {\n          connect: { id: userId }\n        },\n        // Only connect to profile if profileId is provided\n        ...profileId ? { profile: { connect: { id: profileId } } } : {}\n        // We don't need to set team to null, it's optional by default\n      }\n    });\n    const resume = await prisma.resume.create({\n      data: {\n        documentId: document.id,\n        isParsed: false,\n        parsedData: {\n          header: {\n            name: \"\",\n            email: \"\",\n            phone: \"\"\n          },\n          summary: {\n            content: \"\"\n          },\n          experience: [],\n          education: [],\n          skills: []\n        },\n        rawText: \"\"\n      }\n    });\n    console.log(\"Resume created successfully:\", resume.id);\n    console.log(\"Created resume:\", JSON.stringify(resume));\n    console.log(\"Created document:\", JSON.stringify(document));\n    await trackResumeCreation(userId);\n    const source = determineDocumentSource(document);\n    return json({\n      success: true,\n      id: resume.id,\n      documentId: document.id,\n      label,\n      source,\n      // Include the determined source in the response\n      message: \"Resume created successfully\"\n    });\n  } catch (error) {\n    console.error(\"Error creating resume:\", error);\n    return json({ error: \"Failed to create resume\", details: String(error) }, { status: 500 });\n  }\n};\nexport {\n  GET,\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAKK,MAAC,GAAG,GAAG,OAAO,EAAE,MAAM,EAAE,KAAK;AAClC,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;AACpB,IAAI,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxD;AACA,EAAE,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;AAC5C,IAAI,KAAK,EAAE;AACX,MAAM,QAAQ,EAAE;AAChB,QAAQ,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC;AAC5B;AACA,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM;AAChC,GAAG,CAAC;AACJ,EAAE,MAAM,MAAM,CAAC,WAAW,EAAE;AAC5B,EAAE,OAAO,IAAI,CAAC,IAAI,CAAC;AACnB;AACK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AAC5C,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC5B,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1D;AACA,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,EAAE;AAC1B,IAAI,OAAO,CAAC,GAAG,CAAC,+BAA+B,EAAE,MAAM,CAAC;AACxD,IAAI,MAAM,SAAS,GAAG,MAAM,eAAe,CAAC,MAAM,CAAC;AACnD,IAAI,IAAI,CAAC,SAAS,EAAE;AACpB,MAAM,OAAO,IAAI;AACjB,QAAQ;AACR,UAAU,KAAK,EAAE,kGAAkG;AACnH,UAAU,YAAY,EAAE;AACxB,SAAS;AACT,QAAQ,EAAE,MAAM,EAAE,GAAG;AACrB,OAAO;AACP;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,IAAI,YAAY;AACzC,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI;AACrF,IAAI,KAAK,GAAG,MAAM,wBAAwB,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC;AACnE,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;AAClD,MAAM,IAAI,EAAE;AACZ,QAAQ,KAAK;AACb,QAAQ,OAAO,EAAE,kBAAkB;AACnC;AACA,QAAQ,QAAQ,EAAE,IAAI;AACtB;AACA,QAAQ,QAAQ,EAAE,IAAI;AACtB;AACA,QAAQ,IAAI,EAAE,QAAQ;AACtB,QAAQ,WAAW,EAAE,iBAAiB;AACtC,QAAQ,WAAW,EAAE,OAAO;AAC5B,QAAQ,SAAS,EAAE,KAAK;AACxB;AACA,QAAQ,IAAI,EAAE;AACd,UAAU,OAAO,EAAE,EAAE,EAAE,EAAE,MAAM;AAC/B,SAAS;AACT;AACA,QAAQ,GAAG,SAAS,GAAG,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,GAAG;AACrE;AACA;AACA,KAAK,CAAC;AACN,IAAI,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;AAC9C,MAAM,IAAI,EAAE;AACZ,QAAQ,UAAU,EAAE,QAAQ,CAAC,EAAE;AAC/B,QAAQ,QAAQ,EAAE,KAAK;AACvB,QAAQ,UAAU,EAAE;AACpB,UAAU,MAAM,EAAE;AAClB,YAAY,IAAI,EAAE,EAAE;AACpB,YAAY,KAAK,EAAE,EAAE;AACrB,YAAY,KAAK,EAAE;AACnB,WAAW;AACX,UAAU,OAAO,EAAE;AACnB,YAAY,OAAO,EAAE;AACrB,WAAW;AACX,UAAU,UAAU,EAAE,EAAE;AACxB,UAAU,SAAS,EAAE,EAAE;AACvB,UAAU,MAAM,EAAE;AAClB,SAAS;AACT,QAAQ,OAAO,EAAE;AACjB;AACA,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,MAAM,CAAC,EAAE,CAAC;AAC1D,IAAI,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AAC1D,IAAI,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;AAC9D,IAAI,MAAM,mBAAmB,CAAC,MAAM,CAAC;AACrC,IAAI,MAAM,MAAM,GAAG,uBAAuB,CAAC,QAAQ,CAAC;AACpD,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,EAAE,EAAE,MAAM,CAAC,EAAE;AACnB,MAAM,UAAU,EAAE,QAAQ,CAAC,EAAE;AAC7B,MAAM,KAAK;AACX,MAAM,MAAM;AACZ;AACA,MAAM,OAAO,EAAE;AACf,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC;AAClD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9F;AACA;;;;"}