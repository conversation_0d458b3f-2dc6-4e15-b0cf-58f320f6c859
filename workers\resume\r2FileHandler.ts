// workers/resume/r2FileHandler.ts
// Utility to handle file operations for R2 storage in resume parsing workers

import fs from "fs/promises";
import path from "path";
import { downloadFile, BucketType } from "../../utils/r2FileUpload.js";

export interface FileLocation {
  isR2: boolean;
  r2Key: string;
  bucketType: BucketType;
}

/**
 * Analyze file path - now assumes all files are in R2
 * Legacy local file support removed since we're all R2 now
 */
export function analyzeFilePath(filePath: string): FileLocation {
  // All files are now stored in R2
  // Determine bucket type based on file path patterns
  let bucketType = BucketType.RESUMES; // Default to resumes

  // Check if it's a user document (cover letters, etc.)
  if (
    filePath.includes("documents/") ||
    filePath.includes("cover-letter") ||
    filePath.includes("portfolio") ||
    filePath.includes("transcript") ||
    filePath.includes("certificate")
  ) {
    bucketType = BucketType.USER;
  }
  // Check if it's a company asset
  else if (filePath.includes("logos/") || filePath.includes("company-")) {
    bucketType = BucketType.COMPANY;
  }
  // Check if it's a job asset
  else if (filePath.includes("screenshots/") || filePath.includes("assets/")) {
    bucketType = BucketType.JOBS;
  }

  return {
    isR2: true,
    r2Key: filePath,
    bucketType,
  };
}

/**
 * Get file buffer from R2 storage
 * All files are now in R2, so this is simplified
 */
export async function getFileBuffer(
  filePath: string
): Promise<{ success: boolean; buffer?: Buffer; error?: string }> {
  const location = analyzeFilePath(filePath);
  return await getR2FileBuffer(location.r2Key, location.bucketType);
}

// Local file handling removed - all files are now in R2

/**
 * Get file buffer from R2 storage
 */
async function getR2FileBuffer(
  r2Key: string,
  bucketType: BucketType
): Promise<{ success: boolean; buffer?: Buffer; error?: string }> {
  console.log(
    `[R2FileHandler] Attempting to download from R2: ${r2Key} (bucket: ${bucketType})`
  );

  try {
    const result = await downloadFile(r2Key, bucketType);

    if (result.success && result.buffer) {
      console.log(
        `[R2FileHandler] Successfully downloaded from R2: ${r2Key} (${result.buffer.length} bytes)`
      );
      return { success: true, buffer: result.buffer };
    } else {
      console.log(
        `[R2FileHandler] Failed to download from R2: ${result.error}`
      );
      return { success: false, error: result.error };
    }
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";
    console.log(`[R2FileHandler] Error downloading from R2: ${errorMessage}`);
    return { success: false, error: errorMessage };
  }
}

/**
 * Create a temporary local file from R2 content for legacy parsers
 */
export async function createTempFileFromR2(
  r2Key: string,
  bucketType: BucketType
): Promise<{
  success: boolean;
  tempPath?: string;
  error?: string;
  cleanup?: () => Promise<void>;
}> {
  try {
    console.log(`[R2FileHandler] Creating temporary file for R2 key: ${r2Key}`);

    // Download from R2
    const downloadResult = await getR2FileBuffer(r2Key, bucketType);
    if (!downloadResult.success || !downloadResult.buffer) {
      return { success: false, error: downloadResult.error };
    }

    // Create temporary file
    const tempDir = path.join(process.cwd(), "temp");
    await fs.mkdir(tempDir, { recursive: true });

    const tempFileName = `temp-${Date.now()}-${path.basename(r2Key)}`;
    const tempPath = path.join(tempDir, tempFileName);

    await fs.writeFile(tempPath, downloadResult.buffer);

    console.log(`[R2FileHandler] Created temporary file: ${tempPath}`);

    // Return cleanup function
    const cleanup = async () => {
      try {
        await fs.unlink(tempPath);
        console.log(`[R2FileHandler] Cleaned up temporary file: ${tempPath}`);
      } catch (error) {
        console.log(
          `[R2FileHandler] Failed to cleanup temporary file: ${tempPath}`,
          error
        );
      }
    };

    return { success: true, tempPath, cleanup };
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";
    console.log(
      `[R2FileHandler] Error creating temporary file: ${errorMessage}`
    );
    return { success: false, error: errorMessage };
  }
}

/**
 * File resolver for parsing - downloads from R2 and creates temporary local file
 * All files are now in R2, so this always creates a temp file for parsers
 */
export async function resolveFileForParsing(filePath: string): Promise<{
  success: boolean;
  localPath?: string;
  error?: string;
  cleanup?: () => Promise<void>;
}> {
  const location = analyzeFilePath(filePath);

  // All files are in R2, so create a temporary local file for parsing
  return await createTempFileFromR2(location.r2Key, location.bucketType);
}

export default {
  analyzeFilePath,
  getFileBuffer,
  createTempFileFromR2,
  resolveFileForParsing,
};
