#!/usr/bin/env node

/**
 * Simple test for proxy error patterns
 * Tests the regex patterns without requiring TypeScript compilation
 */

console.log("🧪 Testing Proxy Error Pattern Recognition\n");

// Quota exhaustion patterns (from proxyQuotaDetector.ts)
const QUOTA_PATTERNS = [
  /traffic.*limit.*exceeded/i,
  /quota.*exceeded/i,
  /quota.*has.*been.*reached/i,
  /bandwidth.*limit.*reached/i,
  /bandwidth.*limit.*exceeded/i,
  /monthly.*limit.*reached/i,
  /usage.*limit.*exceeded/i,
  /account.*suspended/i,
  /insufficient.*traffic/i,
  /traffic.*depleted/i,
  /data.*allowance.*exceeded/i,
  /402.*payment.*required/i,
  /429.*too.*many.*requests/i,
  /503.*service.*unavailable/i,
  /limit.*reached/i,
  /quota.*depleted/i,
  /allowance.*exceeded/i,
  /subscription.*expired/i,
  /plan.*limit.*exceeded/i,
];

// Connectivity patterns
const CONNECTIVITY_PATTERNS = [
  /net::ERR_EMPTY_RESPONSE/i,
  /net::ERR_CONNECTION_REFUSED/i,
  /net::ERR_TIMED_OUT/i,
  /ECONNREFUSED/i,
  /ENOTFOUND/i,
  /timeout/i,
];

function isQuotaError(errorMessage) {
  return QUOTA_PATTERNS.some((pattern) => pattern.test(errorMessage));
}

function isConnectivityError(errorMessage) {
  return CONNECTIVITY_PATTERNS.some((pattern) => pattern.test(errorMessage));
}

// Test cases
const testCases = [
  // Quota errors
  { error: "Traffic limit exceeded for your account", expected: "quota" },
  { error: "Monthly quota has been reached", expected: "quota" },
  { error: "Bandwidth limit exceeded", expected: "quota" },
  { error: "Account suspended due to usage limits", expected: "quota" },
  { error: "Insufficient traffic remaining", expected: "quota" },
  { error: "HTTP 402 Payment Required", expected: "quota" },
  { error: "Subscription expired", expected: "quota" },

  // Connectivity errors
  { error: "net::ERR_EMPTY_RESPONSE", expected: "connectivity" },
  { error: "net::ERR_CONNECTION_REFUSED", expected: "connectivity" },
  { error: "ECONNREFUSED", expected: "connectivity" },
  { error: "Connection timeout", expected: "connectivity" },

  // Unknown errors
  { error: "Invalid credentials", expected: "unknown" },
  { error: "Server error", expected: "unknown" },
  { error: "Internal server error", expected: "unknown" },
];

console.log("📋 Testing Error Pattern Recognition:");
console.log("====================================");

let correctQuota = 0;
let correctConnectivity = 0;
let correctUnknown = 0;
let totalQuota = 0;
let totalConnectivity = 0;
let totalUnknown = 0;

testCases.forEach((testCase, index) => {
  const isQuota = isQuotaError(testCase.error);
  const isConnectivity = isConnectivityError(testCase.error);

  let detected = "unknown";
  if (isQuota && !isConnectivity) {
    detected = "quota";
  } else if (isConnectivity && !isQuota) {
    detected = "connectivity";
  }

  const isCorrect = detected === testCase.expected;
  const status = isCorrect ? "✅" : "❌";

  console.log(`${index + 1}. ${status} "${testCase.error}"`);
  console.log(`   Expected: ${testCase.expected}, Detected: ${detected}`);

  // Count results
  if (testCase.expected === "quota") {
    totalQuota++;
    if (isCorrect) correctQuota++;
  } else if (testCase.expected === "connectivity") {
    totalConnectivity++;
    if (isCorrect) correctConnectivity++;
  } else {
    totalUnknown++;
    if (isCorrect) correctUnknown++;
  }

  console.log("");
});

console.log("🎯 Test Results Summary:");
console.log("========================");
console.log(`✅ Quota errors: ${correctQuota}/${totalQuota} correct`);
console.log(
  `✅ Connectivity errors: ${correctConnectivity}/${totalConnectivity} correct`
);
console.log(`✅ Unknown errors: ${correctUnknown}/${totalUnknown} correct`);

const totalCorrect = correctQuota + correctConnectivity + correctUnknown;
const totalTests = totalQuota + totalConnectivity + totalUnknown;
const accuracy = ((totalCorrect / totalTests) * 100).toFixed(1);

console.log(
  `\n🏆 Overall Accuracy: ${totalCorrect}/${totalTests} (${accuracy}%)`
);

if (accuracy >= 90) {
  console.log("🎉 Excellent! Pattern recognition is working correctly.");
} else if (accuracy >= 75) {
  console.log("⚠️  Good, but some patterns may need adjustment.");
} else {
  console.log("❌ Poor accuracy. Pattern recognition needs improvement.");
}

console.log("\n💡 Next Steps:");
console.log("- Deploy the updated code to Render");
console.log("- Monitor logs for proxy error detection");
console.log("- Check email notifications when proxy issues occur");
console.log("- Adjust patterns based on real-world error messages");

console.log("\n🚀 Proxy quota detection system is ready for deployment!");
