// cron/utils/cloudflareBypass.ts
// Enhanced Cloudflare bypass utilities for job scraping

import { chrom<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>er<PERSON><PERSON><PERSON><PERSON>, Page } from "playwright";
import { logger } from "./logger";
import fs from "fs/promises";
import path from "path";

const COOKIE_STORE_DIR = path.resolve(process.cwd(), "storage", "cookies");
const DEFAULT_TIMEOUT = 45000; // 45 seconds for Cloudflare challenges

/**
 * Enhanced browser configuration for Cloudflare bypass
 */
export interface CloudflareBrowserOptions {
  headless?: boolean;
  slowMo?: number;
  proxyServer?: string;
  userAgent?: string;
  viewport?: { width: number; height: number };
  locale?: string;
  timezoneId?: string;
}

/**
 * Get a realistic user agent for bypassing detection
 */
function getRealisticUserAgent(): string {
  const userAgents = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0"
  ];
  return userAgents[Math.floor(Math.random() * userAgents.length)];
}

/**
 * Get a realistic viewport size
 */
function getRealisticViewport(): { width: number; height: number } {
  const viewports = [
    { width: 1920, height: 1080 },
    { width: 1366, height: 768 },
    { width: 1536, height: 864 },
    { width: 1440, height: 900 },
    { width: 1280, height: 720 }
  ];
  return viewports[Math.floor(Math.random() * viewports.length)];
}

/**
 * Load saved cookies for a domain
 */
async function loadCookies(context: BrowserContext, domain: string): Promise<void> {
  try {
    await fs.mkdir(COOKIE_STORE_DIR, { recursive: true });
    const cookieFile = path.join(COOKIE_STORE_DIR, `${domain.replace(/[^a-zA-Z0-9]/g, '_')}.json`);
    const cookiesString = await fs.readFile(cookieFile, "utf8");
    const cookies = JSON.parse(cookiesString);
    
    // Filter cookies to only include valid ones for this domain
    const validCookies = cookies.filter((cookie: any) => 
      cookie.domain && (cookie.domain === domain || cookie.domain.includes(domain))
    );
    
    if (validCookies.length > 0) {
      await context.addCookies(validCookies);
      logger.info(`🍪 Loaded ${validCookies.length} saved cookies for ${domain}`);
    }
  } catch (error) {
    logger.info(`🍪 No existing cookies found for ${domain}, starting fresh`);
  }
}

/**
 * Save cookies for a domain
 */
async function saveCookies(context: BrowserContext, domain: string): Promise<void> {
  try {
    await fs.mkdir(COOKIE_STORE_DIR, { recursive: true });
    const cookies = await context.cookies();
    
    // Filter to only save cookies for this domain
    const domainCookies = cookies.filter(cookie => 
      cookie.domain === domain || cookie.domain.includes(domain)
    );
    
    if (domainCookies.length > 0) {
      const cookieFile = path.join(COOKIE_STORE_DIR, `${domain.replace(/[^a-zA-Z0-9]/g, '_')}.json`);
      await fs.writeFile(cookieFile, JSON.stringify(domainCookies, null, 2), "utf8");
      logger.info(`🍪 Saved ${domainCookies.length} cookies for ${domain}`);
    }
  } catch (error) {
    logger.error(`❌ Failed to save cookies for ${domain}:`, error);
  }
}

/**
 * Launch a browser optimized for Cloudflare bypass
 */
export async function launchCloudflareBypassBrowser(
  options: CloudflareBrowserOptions = {}
): Promise<{ browser: Browser; context: BrowserContext }> {
  const isProduction = process.env.NODE_ENV === "production";
  
  // Force non-headless mode for better Cloudflare bypass
  // In production, we'll use headless but with better stealth
  const headless = options.headless ?? false; // Always use non-headless for better bypass
  const slowMo = options.slowMo ?? (headless ? 100 : 200);
  const userAgent = options.userAgent ?? getRealisticUserAgent();
  const viewport = options.viewport ?? getRealisticViewport();

  logger.info(`🚀 Launching Cloudflare bypass browser (headless: ${headless})`);

  const browser = await chromium.launch({
    headless,
    slowMo,
    args: [
      "--no-sandbox",
      "--disable-setuid-sandbox",
      "--disable-dev-shm-usage",
      "--disable-accelerated-2d-canvas",
      "--no-first-run",
      "--no-zygote",
      "--disable-gpu",
      "--disable-background-timer-throttling",
      "--disable-backgrounding-occluded-windows",
      "--disable-renderer-backgrounding",
      "--disable-features=TranslateUI",
      "--disable-ipc-flooding-protection",
      "--disable-web-security",
      "--disable-features=VizDisplayCompositor"
    ]
  });

  const context = await browser.newContext({
    viewport,
    userAgent,
    locale: options.locale ?? "en-US",
    timezoneId: options.timezoneId ?? "America/New_York",
    deviceScaleFactor: 1,
    javaScriptEnabled: true,
    ignoreHTTPSErrors: true,
    permissions: ["geolocation"],
    colorScheme: "light",
    isMobile: false,
    hasTouch: false,
    // Add extra headers to look more like a real browser
    extraHTTPHeaders: {
      "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
      "Accept-Language": "en-US,en;q=0.9",
      "Accept-Encoding": "gzip, deflate, br",
      "DNT": "1",
      "Connection": "keep-alive",
      "Upgrade-Insecure-Requests": "1",
    }
  });

  return { browser, context };
}

/**
 * Navigate to a URL with Cloudflare bypass handling
 */
export async function navigateWithCloudflareBypass(
  page: Page,
  url: string,
  context: BrowserContext
): Promise<boolean> {
  try {
    const urlObj = new URL(url);
    const domain = urlObj.hostname;

    logger.info(`🌐 Navigating to ${url} with Cloudflare bypass`);

    // Load saved cookies for this domain
    await loadCookies(context, domain);

    // Navigate with extended timeout and wait for network idle
    const response = await page.goto(url, {
      waitUntil: "networkidle",
      timeout: DEFAULT_TIMEOUT
    });

    const status = response?.status() ?? 0;
    logger.info(`📡 Response status: ${status}`);

    // Check for Cloudflare verification
    const pageTitle = await page.title();
    const pageContent = await page.textContent("body") ?? "";

    const cloudflareIndicators = [
      "just a moment",
      "checking your browser",
      "cloudflare",
      "please wait",
      "verifying you are human",
      "ddos protection",
      "security check"
    ];

    const isCloudflareChallenge = cloudflareIndicators.some(indicator =>
      pageTitle.toLowerCase().includes(indicator) ||
      pageContent.toLowerCase().includes(indicator)
    );

    if (isCloudflareChallenge || status === 503) {
      logger.warn(`⚠️ Cloudflare challenge detected on ${domain}`);
      return await handleCloudflareChallenge(page, context, domain);
    }

    // Save cookies if we successfully loaded the page
    await saveCookies(context, domain);
    return true;

  } catch (error) {
    logger.error(`❌ Navigation failed: ${error}`);
    return false;
  }
}

/**
 * Handle Cloudflare verification challenges
 */
async function handleCloudflareChallenge(
  page: Page,
  context: BrowserContext,
  domain: string
): Promise<boolean> {
  logger.info(`⏳ Handling Cloudflare challenge for ${domain}...`);

  try {
    // Wait for the challenge to complete with extended timeout
    await page.waitForFunction(
      () => {
        const title = document.title.toLowerCase();
        const body = document.body?.textContent?.toLowerCase() ?? "";
        
        const challengeIndicators = [
          "just a moment",
          "checking your browser",
          "please wait",
          "verifying you are human"
        ];
        
        return !challengeIndicators.some(indicator =>
          title.includes(indicator) || body.includes(indicator)
        );
      },
      { timeout: DEFAULT_TIMEOUT }
    );

    logger.info(`✅ Cloudflare challenge completed for ${domain}`);
    
    // Save the cookies after successful challenge completion
    await saveCookies(context, domain);
    
    // Add a small delay to ensure page is fully loaded
    await page.waitForTimeout(2000);
    
    return true;

  } catch (error) {
    logger.warn(`⚠️ Cloudflare challenge timed out for ${domain}: ${error}`);
    return false;
  }
}

/**
 * Simulate human-like behavior on the page
 */
export async function simulateHumanBehavior(page: Page): Promise<void> {
  try {
    // Random scroll behavior
    await page.evaluate(async () => {
      const scrollDistance = 200 + Math.random() * 300;
      const scrollDelay = 500 + Math.random() * 1000;
      
      for (let i = 0; i < 3; i++) {
        window.scrollBy(0, scrollDistance);
        await new Promise(resolve => setTimeout(resolve, scrollDelay));
      }
    });

    // Random mouse movements
    const viewport = page.viewportSize();
    if (viewport) {
      await page.mouse.move(
        Math.random() * viewport.width,
        Math.random() * viewport.height
      );
    }

    // Random delay
    await page.waitForTimeout(1000 + Math.random() * 2000);

  } catch (error) {
    logger.warn(`⚠️ Error simulating human behavior: ${error}`);
  }
}
