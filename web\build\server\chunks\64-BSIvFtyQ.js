import { r as redirect, f as fail } from './index-Ddp2AB5f.js';
import { p as prisma } from './prisma-Cit_HrSw.js';
import { s as superValidate, z as zod } from './zod-DfpldWlD.js';
import { o as objectType, b as booleanType, e as enumType, f as dateType, s as stringType } from './types-D78SXuvm.js';
import '@prisma/client';
import './constants-BaiUsPxc.js';
import './_commonjsHelpers-BFTU3MAI.js';

const emailNotificationSchema = objectType({
  enabled: booleanType().default(true),
  digest: enumType(["daily", "weekly", "never"]).default("daily"),
  format: enumType(["html", "text"]).default("html")
});
const jobNotificationSchema = objectType({
  matches: booleanType().default(true),
  matchFrequency: enumType(["realtime", "daily", "weekly"]).default("daily"),
  applicationStatus: booleanType().default(true),
  newJobs: booleanType().default(true),
  newJobsFrequency: enumType(["realtime", "daily", "weekly"]).default("daily"),
  interviewReminders: booleanType().default(true),
  savedJobsUpdates: booleanType().default(true),
  emailNotifications: booleanType().default(true),
  browserNotifications: booleanType().default(true),
  mobileNotifications: booleanType().default(false)
});
const marketingNotificationSchema = objectType({
  enabled: booleanType().default(true),
  productUpdates: booleanType().default(true),
  newsletterSubscription: booleanType().default(false),
  eventInvitations: booleanType().default(false)
});
const platformNotificationSchema = objectType({
  browser: booleanType().default(true),
  desktop: booleanType().default(false),
  mobile: booleanType().default(false),
  push: booleanType().default(true)
});
objectType({
  email: emailNotificationSchema,
  jobs: jobNotificationSchema,
  marketing: marketingNotificationSchema,
  platform: platformNotificationSchema
});
objectType({
  id: stringType().optional(),
  userId: stringType(),
  // Email notifications
  emailEnabled: booleanType().default(true),
  emailDigest: stringType().default("daily"),
  emailFormat: stringType().default("html"),
  // Job notifications
  jobMatchEnabled: booleanType().default(true),
  jobMatchFrequency: stringType().default("daily"),
  applicationStatusEnabled: booleanType().default(true),
  newJobsEnabled: booleanType().default(true),
  newJobsFrequency: stringType().default("daily"),
  interviewRemindersEnabled: booleanType().default(true),
  savedJobsUpdatesEnabled: booleanType().default(true),
  // Automation notifications
  automationEnabled: booleanType().default(true),
  automationFrequency: stringType().default("realtime"),
  // Job notification channels
  jobEmailEnabled: booleanType().default(true),
  jobBrowserEnabled: booleanType().default(true),
  jobMobileEnabled: booleanType().default(false),
  // Marketing notifications
  marketingEnabled: booleanType().default(true),
  productUpdatesEnabled: booleanType().default(true),
  newsletterEnabled: booleanType().default(false),
  eventInvitationsEnabled: booleanType().default(false),
  // Platform notifications
  browserEnabled: booleanType().default(true),
  desktopEnabled: booleanType().default(false),
  mobileEnabled: booleanType().default(false),
  pushEnabled: booleanType().default(true),
  createdAt: dateType().optional(),
  updatedAt: dateType().optional()
});
const notificationFormSchema = objectType({
  // Email notifications
  emailNotifications: booleanType().default(true),
  emailDigest: enumType(["daily", "weekly", "never"]).default("daily"),
  emailFormat: enumType(["html", "text"]).default("html"),
  // Job notifications
  jobMatchNotifications: booleanType().default(true),
  jobMatchFrequency: enumType(["realtime", "daily", "weekly"]).default("daily"),
  applicationStatusNotifications: booleanType().default(true),
  newJobsNotifications: booleanType().default(true),
  newJobsFrequency: enumType(["realtime", "daily", "weekly"]).default("daily"),
  interviewReminders: booleanType().default(true),
  savedJobsUpdates: booleanType().default(true),
  // Automation notifications
  automationNotifications: booleanType().default(true),
  automationFrequency: enumType(["realtime", "daily", "weekly"]).default("realtime"),
  // Job notification channels
  jobEmailNotifications: booleanType().default(true),
  jobBrowserNotifications: booleanType().default(true),
  jobMobileNotifications: booleanType().default(false),
  // Marketing notifications
  marketingEmails: booleanType().default(true),
  productUpdates: booleanType().default(true),
  newsletterSubscription: booleanType().default(false),
  eventInvitations: booleanType().default(false),
  // Platform notifications
  browserNotifications: booleanType().default(true),
  desktopNotifications: booleanType().default(false),
  mobileNotifications: booleanType().default(false),
  pushNotifications: booleanType().default(true)
});
function formToNotificationSettings(formData) {
  return {
    email: {
      enabled: formData.emailNotifications,
      digest: formData.emailDigest,
      format: formData.emailFormat
    },
    jobs: {
      matches: formData.jobMatchNotifications,
      matchFrequency: formData.jobMatchFrequency,
      applicationStatus: formData.applicationStatusNotifications,
      newJobs: formData.newJobsNotifications,
      newJobsFrequency: formData.newJobsFrequency,
      interviewReminders: formData.interviewReminders,
      savedJobsUpdates: formData.savedJobsUpdates,
      emailNotifications: formData.jobEmailNotifications,
      browserNotifications: formData.jobBrowserNotifications,
      mobileNotifications: formData.jobMobileNotifications
    },
    marketing: {
      enabled: formData.marketingEmails,
      productUpdates: formData.productUpdates,
      newsletterSubscription: formData.newsletterSubscription,
      eventInvitations: formData.eventInvitations
    },
    platform: {
      browser: formData.browserNotifications,
      desktop: formData.desktopNotifications,
      mobile: formData.mobileNotifications,
      push: formData.pushNotifications
    }
  };
}
function notificationSettingsToForm(settings) {
  return {
    emailNotifications: settings.email.enabled,
    emailDigest: settings.email.digest,
    emailFormat: settings.email.format,
    jobMatchNotifications: settings.jobs.matches,
    jobMatchFrequency: settings.jobs.matchFrequency,
    applicationStatusNotifications: settings.jobs.applicationStatus,
    newJobsNotifications: settings.jobs.newJobs,
    newJobsFrequency: settings.jobs.newJobsFrequency,
    interviewReminders: settings.jobs.interviewReminders,
    savedJobsUpdates: settings.jobs.savedJobsUpdates,
    jobEmailNotifications: settings.jobs.emailNotifications,
    jobBrowserNotifications: settings.jobs.browserNotifications,
    jobMobileNotifications: settings.jobs.mobileNotifications,
    marketingEmails: settings.marketing.enabled,
    productUpdates: settings.marketing.productUpdates,
    newsletterSubscription: settings.marketing.newsletterSubscription,
    eventInvitations: settings.marketing.eventInvitations,
    browserNotifications: settings.platform.browser,
    desktopNotifications: settings.platform.desktop,
    mobileNotifications: settings.platform.mobile,
    pushNotifications: settings.platform.push
  };
}
function formToDbModel(formData, userId) {
  return {
    userId,
    // Email notifications
    emailEnabled: formData.emailNotifications,
    emailDigest: formData.emailDigest,
    emailFormat: formData.emailFormat,
    // Job notifications
    jobMatchEnabled: formData.jobMatchNotifications,
    jobMatchFrequency: formData.jobMatchFrequency,
    applicationStatusEnabled: formData.applicationStatusNotifications,
    newJobsEnabled: formData.newJobsNotifications,
    newJobsFrequency: formData.newJobsFrequency,
    interviewRemindersEnabled: formData.interviewReminders,
    savedJobsUpdatesEnabled: formData.savedJobsUpdates,
    // Automation notifications
    automationEnabled: formData.automationNotifications,
    automationFrequency: formData.automationFrequency,
    // Job notification channels
    jobEmailEnabled: formData.jobEmailNotifications,
    jobBrowserEnabled: formData.jobBrowserNotifications,
    jobMobileEnabled: formData.jobMobileNotifications,
    // Marketing notifications
    marketingEnabled: formData.marketingEmails,
    productUpdatesEnabled: formData.productUpdates,
    newsletterEnabled: formData.newsletterSubscription,
    eventInvitationsEnabled: formData.eventInvitations,
    // Platform notifications
    browserEnabled: formData.browserNotifications,
    desktopEnabled: formData.desktopNotifications,
    mobileEnabled: formData.mobileNotifications,
    pushEnabled: formData.pushNotifications
  };
}
function dbModelToForm(dbModel) {
  return {
    // Email notifications
    emailNotifications: dbModel.emailEnabled,
    emailDigest: dbModel.emailDigest,
    emailFormat: dbModel.emailFormat,
    // Job notifications
    jobMatchNotifications: dbModel.jobMatchEnabled,
    jobMatchFrequency: dbModel.jobMatchFrequency,
    applicationStatusNotifications: dbModel.applicationStatusEnabled,
    newJobsNotifications: dbModel.newJobsEnabled,
    newJobsFrequency: dbModel.newJobsFrequency,
    interviewReminders: dbModel.interviewRemindersEnabled,
    savedJobsUpdates: dbModel.savedJobsUpdatesEnabled,
    // Automation notifications
    automationNotifications: dbModel.automationEnabled,
    automationFrequency: dbModel.automationFrequency,
    // Job notification channels
    jobEmailNotifications: dbModel.jobEmailEnabled,
    jobBrowserNotifications: dbModel.jobBrowserEnabled,
    jobMobileNotifications: dbModel.jobMobileEnabled,
    // Marketing notifications
    marketingEmails: dbModel.marketingEnabled,
    productUpdates: dbModel.productUpdatesEnabled,
    newsletterSubscription: dbModel.newsletterEnabled,
    eventInvitations: dbModel.eventInvitationsEnabled,
    // Platform notifications
    browserNotifications: dbModel.browserEnabled,
    desktopNotifications: dbModel.desktopEnabled,
    mobileNotifications: dbModel.mobileEnabled,
    pushNotifications: dbModel.pushEnabled
  };
}
const load = async ({ locals }) => {
  const user = locals.user;
  if (!user || !user.email) {
    throw redirect(302, "/auth/sign-in");
  }
  const userData = await prisma.user.findUnique({
    where: { email: user.email },
    include: {
      notifications: true
    }
  });
  if (!userData) {
    throw redirect(302, "/auth/sign-in");
  }
  locals.user = userData;
  let userPreferences;
  if (userData.notifications) {
    userPreferences = dbModelToForm(userData.notifications);
  } else {
    const preferences = userData.preferences || {};
    const notificationPrefs = preferences.notifications || {};
    const defaultSettings = {
      email: {
        enabled: true,
        digest: "daily",
        format: "html"
      },
      jobs: {
        matches: true,
        matchFrequency: "daily",
        applicationStatus: true,
        newJobs: true,
        newJobsFrequency: "daily",
        interviewReminders: true,
        savedJobsUpdates: true,
        emailNotifications: true,
        browserNotifications: true,
        mobileNotifications: false
      },
      marketing: {
        enabled: true,
        productUpdates: true,
        newsletterSubscription: false,
        eventInvitations: false
      },
      platform: {
        browser: true,
        desktop: false,
        mobile: false,
        push: true
      }
    };
    const userSettings = {
      email: { ...defaultSettings.email, ...notificationPrefs.email },
      jobs: { ...defaultSettings.jobs, ...notificationPrefs.jobs },
      marketing: { ...defaultSettings.marketing, ...notificationPrefs.marketing },
      platform: { ...defaultSettings.platform, ...notificationPrefs.platform }
    };
    userPreferences = notificationSettingsToForm(userSettings);
  }
  const form = await superValidate(userPreferences, zod(notificationFormSchema));
  return {
    user: userData,
    form
  };
};
const actions = {
  default: async ({ request, locals }) => {
    const userData = locals.user;
    if (!userData || !userData.email) {
      throw redirect(302, "/auth/sign-in");
    }
    const userWithNotifications = await prisma.user.findUnique({
      where: { email: userData.email },
      include: {
        notifications: true
      }
    });
    if (!userWithNotifications) {
      throw redirect(302, "/auth/sign-in");
    }
    const form = await superValidate(request, zod(notificationFormSchema));
    if (!form.valid) {
      return fail(400, { form });
    }
    try {
      const dbNotificationSettings = formToDbModel(form.data, userWithNotifications.id);
      const preferences = userWithNotifications.preferences || {};
      const notificationSettings = formToNotificationSettings(form.data);
      const updatedPreferences = {
        ...preferences,
        notifications: notificationSettings
      };
      await prisma.$transaction(async (tx) => {
        if (userWithNotifications.notifications) {
          await tx.notificationSettings.update({
            where: { id: userWithNotifications.notifications.id },
            data: {
              // Email notifications
              emailEnabled: dbNotificationSettings.emailEnabled,
              emailDigest: dbNotificationSettings.emailDigest,
              emailFormat: dbNotificationSettings.emailFormat,
              // Job notifications
              jobMatchEnabled: dbNotificationSettings.jobMatchEnabled,
              jobMatchFrequency: dbNotificationSettings.jobMatchFrequency,
              applicationStatusEnabled: dbNotificationSettings.applicationStatusEnabled,
              newJobsEnabled: dbNotificationSettings.newJobsEnabled,
              newJobsFrequency: dbNotificationSettings.newJobsFrequency,
              interviewRemindersEnabled: dbNotificationSettings.interviewRemindersEnabled,
              savedJobsUpdatesEnabled: dbNotificationSettings.savedJobsUpdatesEnabled,
              // Job notification channels
              jobEmailEnabled: dbNotificationSettings.jobEmailEnabled,
              jobBrowserEnabled: dbNotificationSettings.jobBrowserEnabled,
              jobMobileEnabled: dbNotificationSettings.jobMobileEnabled,
              // Marketing notifications
              marketingEnabled: dbNotificationSettings.marketingEnabled,
              productUpdatesEnabled: dbNotificationSettings.productUpdatesEnabled,
              newsletterEnabled: dbNotificationSettings.newsletterEnabled,
              eventInvitationsEnabled: dbNotificationSettings.eventInvitationsEnabled,
              // Platform notifications
              browserEnabled: dbNotificationSettings.browserEnabled,
              desktopEnabled: dbNotificationSettings.desktopEnabled,
              mobileEnabled: dbNotificationSettings.mobileEnabled,
              pushEnabled: dbNotificationSettings.pushEnabled
            }
          });
        } else {
          await tx.notificationSettings.create({
            data: {
              userId: userWithNotifications.id,
              // Email notifications
              emailEnabled: dbNotificationSettings.emailEnabled,
              emailDigest: dbNotificationSettings.emailDigest,
              emailFormat: dbNotificationSettings.emailFormat,
              // Job notifications
              jobMatchEnabled: dbNotificationSettings.jobMatchEnabled,
              jobMatchFrequency: dbNotificationSettings.jobMatchFrequency,
              applicationStatusEnabled: dbNotificationSettings.applicationStatusEnabled,
              newJobsEnabled: dbNotificationSettings.newJobsEnabled,
              newJobsFrequency: dbNotificationSettings.newJobsFrequency,
              interviewRemindersEnabled: dbNotificationSettings.interviewRemindersEnabled,
              savedJobsUpdatesEnabled: dbNotificationSettings.savedJobsUpdatesEnabled,
              // Job notification channels
              jobEmailEnabled: dbNotificationSettings.jobEmailEnabled,
              jobBrowserEnabled: dbNotificationSettings.jobBrowserEnabled,
              jobMobileEnabled: dbNotificationSettings.jobMobileEnabled,
              // Marketing notifications
              marketingEnabled: dbNotificationSettings.marketingEnabled,
              productUpdatesEnabled: dbNotificationSettings.productUpdatesEnabled,
              newsletterEnabled: dbNotificationSettings.newsletterEnabled,
              eventInvitationsEnabled: dbNotificationSettings.eventInvitationsEnabled,
              // Platform notifications
              browserEnabled: dbNotificationSettings.browserEnabled,
              desktopEnabled: dbNotificationSettings.desktopEnabled,
              mobileEnabled: dbNotificationSettings.mobileEnabled,
              pushEnabled: dbNotificationSettings.pushEnabled
            }
          });
        }
        await tx.user.update({
          where: { id: userWithNotifications.id },
          data: {
            preferences: updatedPreferences
          }
        });
      });
      return { form, success: true };
    } catch (error) {
      console.error("Error updating notification settings:", error);
      return fail(500, { form, error: "Failed to update notification settings" });
    }
  }
};

var _page_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  actions: actions,
  load: load
});

const index = 64;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-DJFapMQQ.js')).default;
const server_id = "src/routes/dashboard/settings/notifications/+page.server.ts";
const imports = ["_app/immutable/nodes/64.CBtHtKsK.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/C3w0v0gR.js","_app/immutable/chunks/BvdI7LR8.js","_app/immutable/chunks/DDUgF6Ik.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/FN1sk3P2.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/e3H2jrNf.js","_app/immutable/chunks/BSkrKq6e.js","_app/immutable/chunks/I7hvcB12.js","_app/immutable/chunks/ncUU1dSD.js","_app/immutable/chunks/B-Xjo-Yt.js","_app/immutable/chunks/BfX7a-t9.js","_app/immutable/chunks/BosuxZz1.js","_app/immutable/chunks/CnMg5bH0.js","_app/immutable/chunks/BJIrNhIJ.js","_app/immutable/chunks/DuoUhxYL.js","_app/immutable/chunks/Bd3zs5C6.js","_app/immutable/chunks/OXTnUuEm.js","_app/immutable/chunks/CIOgxH3l.js","_app/immutable/chunks/Bpi49Nrf.js","_app/immutable/chunks/DX6rZLP_.js","_app/immutable/chunks/BnikQ10_.js","_app/immutable/chunks/DMoa_yM9.js","_app/immutable/chunks/XESq6qWN.js","_app/immutable/chunks/OOsIR5sE.js","_app/immutable/chunks/B1K98fMG.js","_app/immutable/chunks/5V1tIHTN.js","_app/immutable/chunks/DM07Bv7T.js","_app/immutable/chunks/BaVT73bJ.js","_app/immutable/chunks/DT9WCdWY.js","_app/immutable/chunks/Cb-3cdbh.js","_app/immutable/chunks/BKLOCbjP.js","_app/immutable/chunks/DjPYYl4Z.js","_app/immutable/chunks/C6g8ubaU.js","_app/immutable/chunks/D9yI7a4E.js","_app/immutable/chunks/BjCTmJLi.js","_app/immutable/chunks/CzsE_FAw.js","_app/immutable/chunks/CGK0g3x_.js","_app/immutable/chunks/D2egQzE8.js","_app/immutable/chunks/Ntteq2n_.js","_app/immutable/chunks/DrQfh6BY.js","_app/immutable/chunks/DxW95yuQ.js","_app/immutable/chunks/w80wGXGd.js","_app/immutable/chunks/D-o7ybA5.js","_app/immutable/chunks/yPulTJ2h.js","_app/immutable/chunks/BBa424ah.js","_app/immutable/chunks/D4f2twK-.js","_app/immutable/chunks/hA0h0kTo.js","_app/immutable/chunks/DSDNnczY.js","_app/immutable/chunks/2KCyzleV.js","_app/immutable/chunks/C88uNE8B.js","_app/immutable/chunks/DmZyh-PW.js"];
const stylesheets = ["_app/immutable/assets/Toaster.DKF17Rty.css","_app/immutable/assets/index.CV-KWLNP.css"];
const fonts = [];

export { component, fonts, imports, index, _page_server_ts as server, server_id, stylesheets };
//# sourceMappingURL=64-BSIvFtyQ.js.map
