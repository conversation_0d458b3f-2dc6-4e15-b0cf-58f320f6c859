{"version": 3, "file": "_page.svelte-D-bAs_0Y.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/system-status/history/_page.svelte.js"], "sourcesContent": ["import { R as attr, V as escape_html, U as ensure_array_like, y as pop, w as push, N as bind_props } from \"../../../../chunks/index3.js\";\nimport { S as SEO } from \"../../../../chunks/SEO.js\";\nimport \"../../../../chunks/client.js\";\nimport { A as Accordion_root } from \"../../../../chunks/accordion-trigger.js\";\nimport { M as MaintenanceAccordion } from \"../../../../chunks/MaintenanceAccordion.js\";\nimport { C as Chevron_left } from \"../../../../chunks/chevron-left.js\";\nimport { C as Chevron_right } from \"../../../../chunks/chevron-right2.js\";\nfunction HistoryComponent($$payload, $$props) {\n  push();\n  const {\n    maintenance,\n    currentMonth,\n    currentYear,\n    hasNextMonth,\n    hasPrevMonth\n  } = $$props;\n  function formatMonth(month, year) {\n    return new Intl.DateTimeFormat(\"en-US\", { month: \"long\", year: \"numeric\" }).format(new Date(year, month - 1, 1));\n  }\n  $$payload.out += `<div class=\"flex flex-col gap-4\"><div class=\"flex items-center justify-between px-4\"><button class=\"flex items-center gap-1 rounded-md p-2 hover:bg-gray-100 dark:hover:bg-gray-800\"${attr(\"disabled\", !hasPrevMonth, true)} aria-label=\"Previous month\">`;\n  Chevron_left($$payload, { class: \"h-4 w-4\" });\n  $$payload.out += `<!----> <span>Previous</span></button> <h2 class=\"text-xl font-semibold\">${escape_html(formatMonth(currentMonth, currentYear))}</h2> <button class=\"flex items-center gap-1 rounded-md p-2 hover:bg-gray-100 dark:hover:bg-gray-800\"${attr(\"disabled\", !hasNextMonth, true)} aria-label=\"Next month\"><span>Next</span> `;\n  Chevron_right($$payload, { class: \"h-4 w-4\" });\n  $$payload.out += `<!----></button></div> <div class=\"px-4 pb-8\">`;\n  if (maintenance && maintenance.length > 0) {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<!---->`;\n    Accordion_root($$payload, {\n      type: \"multiple\",\n      class: \"w-full space-y-4\",\n      children: ($$payload2) => {\n        const each_array = ensure_array_like(maintenance);\n        $$payload2.out += `<!--[-->`;\n        for (let i = 0, $$length = each_array.length; i < $$length; i++) {\n          let incident = each_array[i];\n          MaintenanceAccordion($$payload2, { incident, index: i });\n        }\n        $$payload2.out += `<!--]-->`;\n      },\n      $$slots: { default: true }\n    });\n    $$payload.out += `<!---->`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<div class=\"mt-8 rounded-lg border p-8 text-center\"><p class=\"text-muted-foreground\">No notices or maintenance events for ${escape_html(formatMonth(currentMonth, currentYear))}</p></div>`;\n  }\n  $$payload.out += `<!--]--></div></div>`;\n  pop();\n}\nfunction _page($$payload, $$props) {\n  push();\n  let data = $$props[\"data\"];\n  SEO($$payload, {\n    title: \"System Status History | Hirli\",\n    description: \"View the history of system status notices and maintenance events.\",\n    keywords: \"system status, maintenance history, incident history, Hirli status\"\n  });\n  $$payload.out += `<!----> <div class=\"flex flex-col gap-4\"><div class=\"border-border flex items-center justify-between border-b p-6\"><div class=\"flex flex-col\"><h1 class=\"text-3xl font-bold\">Notice History</h1> <p class=\"text-muted-foreground\">Past system notices and maintenance events</p></div> <div><a href=\"/system-status\" class=\"text-primary hover:underline\">Back to Status Page</a></div></div> `;\n  HistoryComponent($$payload, {\n    maintenance: data.maintenance,\n    currentMonth: data.currentMonth,\n    currentYear: data.currentYear,\n    hasNextMonth: data.hasNextMonth,\n    hasPrevMonth: data.hasPrevMonth\n  });\n  $$payload.out += `<!----></div>`;\n  bind_props($$props, { data });\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,SAAS,gBAAgB,CAAC,SAAS,EAAE,OAAO,EAAE;AAC9C,EAAE,IAAI,EAAE;AACR,EAAE,MAAM;AACR,IAAI,WAAW;AACf,IAAI,YAAY;AAChB,IAAI,WAAW;AACf,IAAI,YAAY;AAChB,IAAI;AACJ,GAAG,GAAG,OAAO;AACb,EAAE,SAAS,WAAW,CAAC,KAAK,EAAE,IAAI,EAAE;AACpC,IAAI,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;AACpH;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,oLAAoL,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,6BAA6B,CAAC;AAC9Q,EAAE,YAAY,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AAC/C,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,yEAAyE,EAAE,WAAW,CAAC,WAAW,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC,CAAC,qGAAqG,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,2CAA2C,CAAC;AAC5U,EAAE,aAAa,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AAChD,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,8CAA8C,CAAC;AACnE,EAAE,IAAI,WAAW,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;AAC7C,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,IAAI,cAAc,CAAC,SAAS,EAAE;AAC9B,MAAM,IAAI,EAAE,UAAU;AACtB,MAAM,KAAK,EAAE,kBAAkB;AAC/B,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,MAAM,UAAU,GAAG,iBAAiB,CAAC,WAAW,CAAC;AACzD,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACpC,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE;AACzE,UAAU,IAAI,QAAQ,GAAG,UAAU,CAAC,CAAC,CAAC;AACtC,UAAU,oBAAoB,CAAC,UAAU,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;AAClE;AACA,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACpC,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B,KAAK,CAAC;AACN,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,0HAA0H,EAAE,WAAW,CAAC,WAAW,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC;AACjN;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC;AACzC,EAAE,GAAG,EAAE;AACP;AACA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC;AAC5B,EAAE,GAAG,CAAC,SAAS,EAAE;AACjB,IAAI,KAAK,EAAE,+BAA+B;AAC1C,IAAI,WAAW,EAAE,mEAAmE;AACpF,IAAI,QAAQ,EAAE;AACd,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,8XAA8X,CAAC;AACnZ,EAAE,gBAAgB,CAAC,SAAS,EAAE;AAC9B,IAAI,WAAW,EAAE,IAAI,CAAC,WAAW;AACjC,IAAI,YAAY,EAAE,IAAI,CAAC,YAAY;AACnC,IAAI,WAAW,EAAE,IAAI,CAAC,WAAW;AACjC,IAAI,YAAY,EAAE,IAAI,CAAC,YAAY;AACnC,IAAI,YAAY,EAAE,IAAI,CAAC;AACvB,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AAClC,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,CAAC;AAC/B,EAAE,GAAG,EAAE;AACP;;;;"}