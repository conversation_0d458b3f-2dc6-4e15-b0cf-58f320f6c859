{"version": 3, "file": "_server.ts-cNLjQKyK.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/feature-check/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../chunks/index.js\";\nimport { g as getFeatureAccessDetails, h as hasReachedLimit } from \"../../../../chunks/feature-check.js\";\nconst GET = async ({ url, locals }) => {\n  const user = locals.user;\n  if (!user) {\n    return json({ hasAccess: false, error: \"Not authenticated\" }, { status: 401 });\n  }\n  const featureId = url.searchParams.get(\"featureId\");\n  if (!featureId) {\n    return json({ hasAccess: false, error: \"Feature ID is required\" }, { status: 400 });\n  }\n  const limitId = url.searchParams.get(\"limitId\");\n  try {\n    const accessDetails = await getFeatureAccessDetails(user.id, featureId);\n    if (limitId && accessDetails.hasAccess) {\n      const limitReached = await hasReachedLimit(user.id, featureId, limitId);\n      return json({\n        ...accessDetails,\n        limitReached,\n        limitId\n      });\n    }\n    return json(accessDetails);\n  } catch (error) {\n    console.error(\"Error checking feature access:\", error);\n    return json(\n      { hasAccess: false, error: \"Failed to check feature access\" },\n      { status: 500 }\n    );\n  }\n};\nconst POST = async ({ request, locals }) => {\n  const user = locals.user;\n  if (!user) {\n    return json({ error: \"Not authenticated\" }, { status: 401 });\n  }\n  try {\n    const body = await request.json();\n    const { features, limits } = body;\n    if (!features || !Array.isArray(features)) {\n      return json({ error: \"Features array is required\" }, { status: 400 });\n    }\n    const results = {};\n    for (const featureId of features) {\n      const limitId = limits?.[featureId];\n      const accessDetails = await getFeatureAccessDetails(user.id, featureId);\n      if (limitId && accessDetails.hasAccess) {\n        const limitReached = await hasReachedLimit(user.id, featureId, limitId);\n        results[featureId] = {\n          ...accessDetails,\n          limitReached,\n          limitId\n        };\n      } else {\n        results[featureId] = accessDetails;\n      }\n    }\n    return json({ results });\n  } catch (error) {\n    console.error(\"Error checking feature access:\", error);\n    return json(\n      { error: \"Failed to check feature access\" },\n      { status: 500 }\n    );\n  }\n};\nexport {\n  GET,\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;AAEK,MAAC,GAAG,GAAG,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK;AACvC,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,IAAI,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,mBAAmB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClF;AACA,EAAE,MAAM,SAAS,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC;AACrD,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,IAAI,OAAO,IAAI,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,wBAAwB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACvF;AACA,EAAE,MAAM,OAAO,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC;AACjD,EAAE,IAAI;AACN,IAAI,MAAM,aAAa,GAAG,MAAM,uBAAuB,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,CAAC;AAC3E,IAAI,IAAI,OAAO,IAAI,aAAa,CAAC,SAAS,EAAE;AAC5C,MAAM,MAAM,YAAY,GAAG,MAAM,eAAe,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC;AAC7E,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,GAAG,aAAa;AACxB,QAAQ,YAAY;AACpB,QAAQ;AACR,OAAO,CAAC;AACR;AACA,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC;AAC9B,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC;AAC1D,IAAI,OAAO,IAAI;AACf,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,gCAAgC,EAAE;AACnE,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;AACK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AAC5C,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAChE;AACA,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,IAAI;AACrC,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AAC/C,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,4BAA4B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3E;AACA,IAAI,MAAM,OAAO,GAAG,EAAE;AACtB,IAAI,KAAK,MAAM,SAAS,IAAI,QAAQ,EAAE;AACtC,MAAM,MAAM,OAAO,GAAG,MAAM,GAAG,SAAS,CAAC;AACzC,MAAM,MAAM,aAAa,GAAG,MAAM,uBAAuB,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,CAAC;AAC7E,MAAM,IAAI,OAAO,IAAI,aAAa,CAAC,SAAS,EAAE;AAC9C,QAAQ,MAAM,YAAY,GAAG,MAAM,eAAe,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC;AAC/E,QAAQ,OAAO,CAAC,SAAS,CAAC,GAAG;AAC7B,UAAU,GAAG,aAAa;AAC1B,UAAU,YAAY;AACtB,UAAU;AACV,SAAS;AACT,OAAO,MAAM;AACb,QAAQ,OAAO,CAAC,SAAS,CAAC,GAAG,aAAa;AAC1C;AACA;AACA,IAAI,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,CAAC;AAC5B,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC;AAC1D,IAAI,OAAO,IAAI;AACf,MAAM,EAAE,KAAK,EAAE,gCAAgC,EAAE;AACjD,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;;;;"}