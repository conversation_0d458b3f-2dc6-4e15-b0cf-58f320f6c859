{"version": 3, "file": "_server.ts-DTma_jhC.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/documents/_id_/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { e as ensureUniqueDocumentName } from \"../../../../../chunks/documentNameUniqueness.js\";\nimport { d as determineDocumentSource } from \"../../../../../chunks/documentSource.js\";\nconst GET = async ({ params }) => {\n  const { id } = params;\n  try {\n    const document = await prisma.document.findUnique({\n      where: { id }\n    });\n    if (!document) {\n      return json({ error: \"Document not found\" }, { status: 404 });\n    }\n    const source = determineDocumentSource(document);\n    return json({\n      ...document,\n      source\n    });\n  } catch (error) {\n    console.error(\"Error fetching document:\", error);\n    return json({ error: \"Failed to fetch document\" }, { status: 500 });\n  }\n};\nconst DELETE = async ({ params }) => {\n  const { id } = params;\n  try {\n    const document = await prisma.document.findUnique({\n      where: { id }\n    });\n    if (!document) {\n      return json({ error: \"Document not found\" }, { status: 404 });\n    }\n    await prisma.document.delete({\n      where: { id }\n    });\n    return json({ success: true, message: \"Document deleted successfully\" });\n  } catch (error) {\n    console.error(\"Error deleting document:\", error);\n    return json({ error: \"Failed to delete document\" }, { status: 500 });\n  }\n};\nconst PATCH = async ({ params, request }) => {\n  const { id } = params;\n  try {\n    const data = await request.json();\n    const document = await prisma.document.findUnique({\n      where: { id }\n    });\n    if (!document) {\n      return json({ error: \"Document not found\" }, { status: 404 });\n    }\n    if (data.label && data.label !== document.label) {\n      data.label = await ensureUniqueDocumentName(data.label, document.userId, document.type, id);\n    }\n    const updatedDocument = await prisma.document.update({\n      where: { id },\n      data\n    });\n    const source = determineDocumentSource(updatedDocument);\n    return json({\n      ...updatedDocument,\n      source\n    });\n  } catch (error) {\n    console.error(\"Error updating document:\", error);\n    return json({ error: \"Failed to update document\" }, { status: 500 });\n  }\n};\nexport {\n  DELETE,\n  GET,\n  PATCH\n};\n"], "names": [], "mappings": ";;;;;;AAIK,MAAC,GAAG,GAAG,OAAO,EAAE,MAAM,EAAE,KAAK;AAClC,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;AACvB,EAAE,IAAI;AACN,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;AACtD,MAAM,KAAK,EAAE,EAAE,EAAE;AACjB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oBAAoB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnE;AACA,IAAI,MAAM,MAAM,GAAG,uBAAuB,CAAC,QAAQ,CAAC;AACpD,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,GAAG,QAAQ;AACjB,MAAM;AACN,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC;AACpD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,0BAA0B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACvE;AACA;AACK,MAAC,MAAM,GAAG,OAAO,EAAE,MAAM,EAAE,KAAK;AACrC,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;AACvB,EAAE,IAAI;AACN,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;AACtD,MAAM,KAAK,EAAE,EAAE,EAAE;AACjB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oBAAoB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnE;AACA,IAAI,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;AACjC,MAAM,KAAK,EAAE,EAAE,EAAE;AACjB,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,+BAA+B,EAAE,CAAC;AAC5E,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC;AACpD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,2BAA2B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA;AACK,MAAC,KAAK,GAAG,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK;AAC7C,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;AACvB,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;AACtD,MAAM,KAAK,EAAE,EAAE,EAAE;AACjB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oBAAoB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnE;AACA,IAAI,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC,KAAK,EAAE;AACrD,MAAM,IAAI,CAAC,KAAK,GAAG,MAAM,wBAAwB,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC;AACjG;AACA,IAAI,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;AACzD,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE;AACnB,MAAM;AACN,KAAK,CAAC;AACN,IAAI,MAAM,MAAM,GAAG,uBAAuB,CAAC,eAAe,CAAC;AAC3D,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,GAAG,eAAe;AACxB,MAAM;AACN,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC;AACpD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,2BAA2B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA;;;;"}