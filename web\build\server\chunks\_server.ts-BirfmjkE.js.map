{"version": 3, "file": "_server.ts-BirfmjkE.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/email/audiences/_id_/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { Resend } from \"resend\";\nimport { l as logger } from \"../../../../../../chunks/logger.js\";\nconst resend = process.env.RESEND_API_KEY ? new Resend(process.env.RESEND_API_KEY) : null;\nasync function DELETE({ params }) {\n  try {\n    const { id } = params;\n    if (!id) {\n      return json({ error: \"Audience ID is required\" }, { status: 400 });\n    }\n    if (!resend) {\n      logger.warn(\"Resend API key not available, returning mock success response\");\n      return json({ success: true });\n    }\n    const response = await resend.audiences.remove(id);\n    if (response.error) {\n      logger.error(\"Error deleting audience:\", response.error);\n      return json({ error: response.error.message }, { status: 500 });\n    }\n    return json({ success: true });\n  } catch (error) {\n    logger.error(\"Error deleting audience:\", error);\n    return json({ error: \"Failed to delete audience\" }, { status: 500 });\n  }\n}\nexport {\n  DELETE\n};\n"], "names": [], "mappings": ";;;;AAGA,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,IAAI;AACzF,eAAe,MAAM,CAAC,EAAE,MAAM,EAAE,EAAE;AAClC,EAAE,IAAI;AACN,IAAI,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;AACzB,IAAI,IAAI,CAAC,EAAE,EAAE;AACb,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,MAAM,CAAC,IAAI,CAAC,+DAA+D,CAAC;AAClF,MAAM,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;AACpC;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;AACtD,IAAI,IAAI,QAAQ,CAAC,KAAK,EAAE;AACxB,MAAM,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,QAAQ,CAAC,KAAK,CAAC;AAC9D,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACrE;AACA,IAAI,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;AAClC,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC;AACnD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,2BAA2B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA;;;;"}