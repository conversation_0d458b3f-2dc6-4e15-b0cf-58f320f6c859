{"version": 3, "file": "_server.ts-kuI6RI4d.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/email/audiences/_id_/import/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../../chunks/index.js\";\nimport { Resend } from \"resend\";\nimport { l as logger } from \"../../../../../../../chunks/logger.js\";\nimport { parse as parse$1 } from \"csv-parse/sync\";\nconst parse = parse$1;\nconst resend = process.env.RESEND_API_KEY ? new Resend(process.env.RESEND_API_KEY) : null;\nasync function POST({ request, params }) {\n  try {\n    const { id } = params;\n    if (!id) {\n      return json({ error: \"Audience ID is required\" }, { status: 400 });\n    }\n    const formData = await request.formData();\n    const file = formData.get(\"file\");\n    if (!file || !(file instanceof File)) {\n      return json({ error: \"CSV file is required\" }, { status: 400 });\n    }\n    const fileContent = await file.text();\n    const records = parse(fileContent, {\n      columns: true,\n      skip_empty_lines: true,\n      trim: true\n    });\n    if (records.length === 0) {\n      return json({ error: \"CSV file is empty\" }, { status: 400 });\n    }\n    const validRecords = records.filter((record) => {\n      return record.email && typeof record.email === \"string\" && record.email.includes(\"@\");\n    });\n    if (validRecords.length === 0) {\n      return json({ error: \"No valid email addresses found in CSV\" }, { status: 400 });\n    }\n    const batchSize = 1e3;\n    let imported = 0;\n    if (!resend) {\n      logger.warn(\"Resend API key not available, returning mock import response\");\n      return json({\n        imported: validRecords.length,\n        total: validRecords.length,\n        message: `Imported ${validRecords.length} out of ${validRecords.length} contacts (mock)`\n      });\n    }\n    for (let i = 0; i < validRecords.length; i += batchSize) {\n      const batch = validRecords.slice(i, i + batchSize);\n      const contacts = batch.map((record) => ({\n        email: record.email,\n        first_name: record.first_name || record.firstName || \"\",\n        last_name: record.last_name || record.lastName || \"\",\n        custom_fields: {}\n      }));\n      const response = await resend.contacts.create({\n        audienceId: id,\n        contacts\n      });\n      if (response.error) {\n        logger.error(`Error importing contacts batch ${i / batchSize + 1}:`, response.error);\n      } else {\n        imported += contacts.length;\n      }\n    }\n    return json({\n      imported,\n      total: validRecords.length,\n      message: `Imported ${imported} out of ${validRecords.length} contacts`\n    });\n  } catch (error) {\n    logger.error(\"Error importing contacts:\", error);\n    return json({ error: \"Failed to import contacts\" }, { status: 500 });\n  }\n}\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;AAIA,MAAM,KAAK,GAAG,OAAO;AACrB,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,IAAI;AACzF,eAAe,IAAI,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE;AACzC,EAAE,IAAI;AACN,IAAI,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;AACzB,IAAI,IAAI,CAAC,EAAE,EAAE;AACb,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,QAAQ,EAAE;AAC7C,IAAI,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC;AACrC,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,YAAY,IAAI,CAAC,EAAE;AAC1C,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,sBAAsB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACrE;AACA,IAAI,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;AACzC,IAAI,MAAM,OAAO,GAAG,KAAK,CAAC,WAAW,EAAE;AACvC,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,gBAAgB,EAAE,IAAI;AAC5B,MAAM,IAAI,EAAE;AACZ,KAAK,CAAC;AACN,IAAI,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;AAC9B,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE;AACA,IAAI,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK;AACpD,MAAM,OAAO,MAAM,CAAC,KAAK,IAAI,OAAO,MAAM,CAAC,KAAK,KAAK,QAAQ,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC;AAC3F,KAAK,CAAC;AACN,IAAI,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;AACnC,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uCAAuC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtF;AACA,IAAI,MAAM,SAAS,GAAG,GAAG;AACzB,IAAI,IAAI,QAAQ,GAAG,CAAC;AACpB,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,MAAM,CAAC,IAAI,CAAC,8DAA8D,CAAC;AACjF,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,QAAQ,EAAE,YAAY,CAAC,MAAM;AACrC,QAAQ,KAAK,EAAE,YAAY,CAAC,MAAM;AAClC,QAAQ,OAAO,EAAE,CAAC,SAAS,EAAE,YAAY,CAAC,MAAM,CAAC,QAAQ,EAAE,YAAY,CAAC,MAAM,CAAC,gBAAgB;AAC/F,OAAO,CAAC;AACR;AACA,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,IAAI,SAAS,EAAE;AAC7D,MAAM,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC;AACxD,MAAM,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,MAAM;AAC9C,QAAQ,KAAK,EAAE,MAAM,CAAC,KAAK;AAC3B,QAAQ,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,SAAS,IAAI,EAAE;AAC/D,QAAQ,SAAS,EAAE,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,QAAQ,IAAI,EAAE;AAC5D,QAAQ,aAAa,EAAE;AACvB,OAAO,CAAC,CAAC;AACT,MAAM,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;AACpD,QAAQ,UAAU,EAAE,EAAE;AACtB,QAAQ;AACR,OAAO,CAAC;AACR,MAAM,IAAI,QAAQ,CAAC,KAAK,EAAE;AAC1B,QAAQ,MAAM,CAAC,KAAK,CAAC,CAAC,+BAA+B,EAAE,CAAC,GAAG,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC;AAC5F,OAAO,MAAM;AACb,QAAQ,QAAQ,IAAI,QAAQ,CAAC,MAAM;AACnC;AACA;AACA,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,QAAQ;AACd,MAAM,KAAK,EAAE,YAAY,CAAC,MAAM;AAChC,MAAM,OAAO,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC,QAAQ,EAAE,YAAY,CAAC,MAAM,CAAC,SAAS;AAC3E,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC;AACpD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,2BAA2B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA;;;;"}