{"version": 3, "file": "_server.ts-CWu15XzX.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/email/config/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { d as private_env } from \"../../../../../chunks/shared-server.js\";\nconst GET = async ({ request }) => {\n  try {\n    const resendApiKey = private_env.RESEND_API_KEY;\n    const resendConfigured = !!resendApiKey;\n    return json({\n      resendConfigured,\n      message: resendConfigured ? \"Resend API is configured\" : \"Resend API key is not configured\"\n    });\n  } catch (error) {\n    console.error(\"Error checking Resend API configuration:\", error);\n    return json(\n      {\n        resendConfigured: false,\n        error: \"Failed to check Resend API configuration\"\n      },\n      { status: 500 }\n    );\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;AAEK,MAAC,GAAG,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK;AACnC,EAAE,IAAI;AACN,IAAI,MAAM,YAAY,GAAG,WAAW,CAAC,cAAc;AACnD,IAAI,MAAM,gBAAgB,GAAG,CAAC,CAAC,YAAY;AAC3C,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,gBAAgB;AACtB,MAAM,OAAO,EAAE,gBAAgB,GAAG,0BAA0B,GAAG;AAC/D,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC;AACpE,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,gBAAgB,EAAE,KAAK;AAC/B,QAAQ,KAAK,EAAE;AACf,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;;;;"}