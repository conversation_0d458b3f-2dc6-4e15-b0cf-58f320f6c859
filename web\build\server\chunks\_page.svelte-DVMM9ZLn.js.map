{"version": 3, "file": "_page.svelte-DVMM9ZLn.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/builder/_page.svelte.js"], "sourcesContent": ["import \"clsx\";\nimport { y as pop, w as push } from \"../../../../chunks/index3.js\";\nimport { R as ResumeTabs, a as Resume, E as EditDesignPanel, b as ResumeForm } from \"../../../../chunks/EditDesignPanel.js\";\nfunction _page($$payload, $$props) {\n  push();\n  let { data } = $$props;\n  $$payload.out += `<div class=\"grid h-screen grid-cols-1 md:grid-cols-[2.5fr_3fr]\"><div class=\"overflow-y-auto border border-b-0 border-l-0 border-t-0 border-zinc-900 p-6 text-white\">`;\n  ResumeTabs($$payload, {\n    $$slots: {\n      content: ($$payload2) => {\n        $$payload2.out += `<div slot=\"content\">`;\n        ResumeForm($$payload2, { data: data.form.resume });\n        $$payload2.out += `<!----></div>`;\n      },\n      design: ($$payload2) => {\n        $$payload2.out += `<div slot=\"design\">`;\n        EditDesignPanel($$payload2, { data: data.form.design });\n        $$payload2.out += `<!----></div>`;\n      }\n    }\n  });\n  $$payload.out += `<!----></div> <div class=\"flex items-center justify-center overflow-y-auto bg-neutral-950 p-4\">`;\n  Resume($$payload, {});\n  $$payload.out += `<!----></div></div>`;\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO;AACxB,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,oKAAoK,CAAC;AACzL,EAAE,UAAU,CAAC,SAAS,EAAE;AACxB,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,CAAC,UAAU,KAAK;AAC/B,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC;AAChD,QAAQ,UAAU,CAAC,UAAU,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1D,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACzC,OAAO;AACP,MAAM,MAAM,EAAE,CAAC,UAAU,KAAK;AAC9B,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC;AAC/C,QAAQ,eAAe,CAAC,UAAU,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC/D,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACzC;AACA;AACA,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,+FAA+F,CAAC;AACpH,EAAE,MAAM,CAAC,SAAS,EAAE,EAAE,CAAC;AACvB,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC;AACxC,EAAE,GAAG,EAAE;AACP;;;;"}