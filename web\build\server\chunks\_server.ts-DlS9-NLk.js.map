{"version": 3, "file": "_server.ts-DlS9-NLk.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/ai/ats/text/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nconst WORKER_API_URL = process.env.WORKER_API_URL ?? \"http://localhost:3002\";\nconst POST = async ({ request, locals }) => {\n  if (!locals.user) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  try {\n    const { resumeText, jobDescription } = await request.json();\n    if (!resumeText) {\n      return json({ error: \"Resume text is required\" }, { status: 400 });\n    }\n    const analysis = await generateATSAnalysis(resumeText, jobDescription);\n    try {\n      const { trackFeatureUsage } = await import(\"../../../../../../chunks/feature-usage.js\");\n      await trackFeatureUsage(locals.user.id, \"ats_optimization\", \"ats_scans_monthly\", 1);\n    } catch (usageError) {\n      console.warn(\"Failed to track feature usage:\", usageError);\n    }\n    return json({ analysis });\n  } catch (error) {\n    console.error(\"Error generating ATS analysis:\", error);\n    return json({ error: \"Failed to generate ATS analysis\" }, { status: 500 });\n  }\n};\nasync function generateATSAnalysis(resumeText, jobDescription) {\n  try {\n    const requestBody = {\n      resumeText\n    };\n    if (jobDescription) {\n      requestBody.jobDescription = jobDescription;\n    }\n    console.log(\"Calling worker API for resume analysis with:\", {\n      resumeTextLength: resumeText.length,\n      hasJobDescription: !!jobDescription\n    });\n    const response = await fetch(`${WORKER_API_URL}/api/ai/resume-analysis`, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\"\n      },\n      body: JSON.stringify(requestBody)\n    });\n    if (!response.ok) {\n      throw new Error(`Worker API returned ${response.status}: ${response.statusText}`);\n    }\n    const data = await response.json();\n    if (!data.success || !data.analysis) {\n      throw new Error(\"Worker API returned invalid response format\");\n    }\n    console.log(\"Received analysis from worker API\");\n    return {\n      overallScore: data.analysis.overallScore ?? 0,\n      keywordScore: data.analysis.keywordScore ?? 0,\n      formatScore: data.analysis.formatScore ?? 0,\n      contentScore: data.analysis.contentScore ?? 0,\n      readabilityScore: data.analysis.readabilityScore ?? 0,\n      keywordMatches: data.analysis.keywordMatches ?? [],\n      missingKeywords: data.analysis.missingKeywords ?? [],\n      formatIssues: data.analysis.formatIssues ?? [],\n      contentSuggestions: data.analysis.contentSuggestions ?? [],\n      readabilitySuggestions: data.analysis.readabilitySuggestions ?? []\n    };\n  } catch (error) {\n    console.error(\"Error in ATS analysis generation:\", error);\n    return {\n      overallScore: 70,\n      keywordScore: 65,\n      formatScore: 75,\n      contentScore: 70,\n      readabilityScore: 80,\n      keywordMatches: [\"resume\", \"experience\", \"skills\"],\n      missingKeywords: [\"specific technical skills\", \"certifications\"],\n      formatIssues: [\"Consider using a more ATS-friendly format\"],\n      contentSuggestions: [\"Add more quantifiable achievements\"],\n      readabilitySuggestions: [\"Use more bullet points for better readability\"]\n    };\n  }\n}\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;AACA,MAAM,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,uBAAuB;AACvE,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AAC5C,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;AACpB,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AAC/D,IAAI,IAAI,CAAC,UAAU,EAAE;AACrB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,mBAAmB,CAAC,UAAU,EAAE,cAAc,CAAC;AAC1E,IAAI,IAAI;AACR,MAAM,MAAM,EAAE,iBAAiB,EAAE,GAAG,MAAM,OAAO,6BAA2C,CAAC;AAC7F,MAAM,MAAM,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,CAAC,CAAC;AACzF,KAAK,CAAC,OAAO,UAAU,EAAE;AACzB,MAAM,OAAO,CAAC,IAAI,CAAC,gCAAgC,EAAE,UAAU,CAAC;AAChE;AACA,IAAI,OAAO,IAAI,CAAC,EAAE,QAAQ,EAAE,CAAC;AAC7B,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC;AAC1D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,iCAAiC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9E;AACA;AACA,eAAe,mBAAmB,CAAC,UAAU,EAAE,cAAc,EAAE;AAC/D,EAAE,IAAI;AACN,IAAI,MAAM,WAAW,GAAG;AACxB,MAAM;AACN,KAAK;AACL,IAAI,IAAI,cAAc,EAAE;AACxB,MAAM,WAAW,CAAC,cAAc,GAAG,cAAc;AACjD;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,8CAA8C,EAAE;AAChE,MAAM,gBAAgB,EAAE,UAAU,CAAC,MAAM;AACzC,MAAM,iBAAiB,EAAE,CAAC,CAAC;AAC3B,KAAK,CAAC;AACN,IAAI,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,uBAAuB,CAAC,EAAE;AAC7E,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,OAAO,EAAE;AACf,QAAQ,cAAc,EAAE;AACxB,OAAO;AACP,MAAM,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW;AACtC,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;AACtB,MAAM,MAAM,IAAI,KAAK,CAAC,CAAC,oBAAoB,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;AACvF;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE;AACtC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AACzC,MAAM,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC;AACpE;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC;AACpD,IAAI,OAAO;AACX,MAAM,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,YAAY,IAAI,CAAC;AACnD,MAAM,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,YAAY,IAAI,CAAC;AACnD,MAAM,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI,CAAC;AACjD,MAAM,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,YAAY,IAAI,CAAC;AACnD,MAAM,gBAAgB,EAAE,IAAI,CAAC,QAAQ,CAAC,gBAAgB,IAAI,CAAC;AAC3D,MAAM,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC,cAAc,IAAI,EAAE;AACxD,MAAM,eAAe,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAe,IAAI,EAAE;AAC1D,MAAM,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,YAAY,IAAI,EAAE;AACpD,MAAM,kBAAkB,EAAE,IAAI,CAAC,QAAQ,CAAC,kBAAkB,IAAI,EAAE;AAChE,MAAM,sBAAsB,EAAE,IAAI,CAAC,QAAQ,CAAC,sBAAsB,IAAI;AACtE,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC;AAC7D,IAAI,OAAO;AACX,MAAM,YAAY,EAAE,EAAE;AACtB,MAAM,YAAY,EAAE,EAAE;AACtB,MAAM,WAAW,EAAE,EAAE;AACrB,MAAM,YAAY,EAAE,EAAE;AACtB,MAAM,gBAAgB,EAAE,EAAE;AAC1B,MAAM,cAAc,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,QAAQ,CAAC;AACxD,MAAM,eAAe,EAAE,CAAC,2BAA2B,EAAE,gBAAgB,CAAC;AACtE,MAAM,YAAY,EAAE,CAAC,2CAA2C,CAAC;AACjE,MAAM,kBAAkB,EAAE,CAAC,oCAAoC,CAAC;AAChE,MAAM,sBAAsB,EAAE,CAAC,+CAA+C;AAC9E,KAAK;AACL;AACA;;;;"}