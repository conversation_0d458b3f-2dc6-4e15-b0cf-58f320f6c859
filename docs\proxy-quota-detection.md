# Proxy Quota Detection and Email Notification System

## Overview
This system automatically detects when your SmartProxy traffic quota has been exhausted and sends email notifications to alert administrators. It distinguishes between quota exhaustion and general connectivity issues to provide appropriate responses.

## Problem Solved
Previously, when proxy traffic quota was exhausted, the system would show generic connectivity errors without identifying the root cause. This led to:
- Unclear error messages
- No automatic notifications
- Manual investigation required to identify quota issues
- Delayed response to service interruptions

## Solution Components

### 1. Proxy Quota Detector (`cron/utils/proxyQuotaDetector.ts`)
**Key Features:**
- **Pattern Recognition:** Detects quota-specific error messages
- **Error Classification:** Distinguishes between quota, connectivity, and unknown errors
- **Smart Notifications:** Sends appropriate email alerts based on error type

**Detected Quota Patterns:**
```javascript
// SmartProxy specific patterns
/traffic.*limit.*exceeded/i
/quota.*exceeded/i
/bandwidth.*limit.*reached/i
/monthly.*limit.*reached/i
/usage.*limit.*exceeded/i
/account.*suspended/i
/insufficient.*traffic/i
/traffic.*depleted/i
/data.*allowance.*exceeded/i

// HTTP status codes
/402.*payment.*required/i
/429.*too.*many.*requests/i
/503.*service.*unavailable/i
```

### 2. Email Notification Types
**New Email Type Added:**
```typescript
PROXY_QUOTA_EXHAUSTED = "proxy-quota-exhausted"
```

**Existing Types Enhanced:**
```typescript
PROXY_DOWN = "proxy-down"  // For general connectivity issues
```

### 3. Worker Pool Integration
**Files Modified:**
- `scraper/workers/workerPool.ts`
- `cron/workers/workerPool.ts`

**Integration Points:**
- Worker creation failures
- Proxy connectivity tests
- Error handling during scraping

## Usage Examples

### Automatic Detection
```javascript
// In worker pool when proxy fails
try {
  await page.goto(testUrl);
} catch (error) {
  // Automatically detects and notifies
  await handleProxyError(error, {
    workerId: id,
    proxyHost: 'smartproxy.com',
    context: 'worker_creation'
  });
}
```

### Manual Testing
```javascript
const { analyzeProxyError } = require('./cron/utils/proxyQuotaDetector');

const error = "Traffic limit exceeded for your account";
const analysis = analyzeProxyError(error);

console.log(analysis);
// Output:
// {
//   type: 'quota',
//   shouldNotify: true,
//   message: 'Proxy quota/traffic limit has been exhausted'
// }
```

## Email Notifications

### Quota Exhausted Email
**Subject:** `🚨 Proxy Quota Exhausted - Action Required`

**Content Includes:**
- Error message details
- Timestamp of occurrence
- Proxy provider information
- Recommended actions
- Current usage status

**Sample Email Data:**
```javascript
{
  error: "Traffic limit exceeded for your account",
  timestamp: "2025-06-03T18:45:00.000Z",
  status: "QUOTA_EXHAUSTED",
  severity: "HIGH",
  action_required: "Increase proxy quota or switch to backup proxy",
  proxy_provider: "SmartProxy",
  current_usage: "Traffic limit exceeded",
  recommendation: "Contact SmartProxy to increase traffic allowance"
}
```

### Connectivity Issues Email
**Subject:** `⚠️ Proxy Connectivity Issues Detected`

**Content Includes:**
- Connection error details
- Network diagnostics
- Troubleshooting steps

## Configuration

### Environment Variables
```bash
# Enable/disable proxy usage
PROXY_ENABLED=false

# Enable email notifications for proxy issues
ENABLE_PROXY_NOTIFICATIONS=true

# SmartProxy credentials (when enabled)
SMARTPROXY_USERNAME=your_username
SMARTPROXY_PASSWORD=your_password
SMARTPROXY_HOST=isp.smartproxy.com
SMARTPROXY_COUNT=100
```

### Render Configuration
Already configured in `render.yaml`:
```yaml
- key: PROXY_ENABLED
  value: "false"
- key: ENABLE_PROXY_NOTIFICATIONS
  value: "true"
```

## Testing

### Run Detection Tests
```bash
# Test pattern recognition
node scripts/test-proxy-quota-detection.js

# Test with email notifications
SEND_TEST_EMAIL=true node scripts/test-proxy-quota-detection.js
```

### Expected Test Results
```
✅ Quota errors correctly identified: 9/9
✅ Connectivity errors correctly identified: 7/7
✅ Unknown errors correctly ignored: 4/4
```

## Monitoring and Alerts

### Log Messages to Watch For

**Quota Exhausted:**
```
🚨 Proxy quota exhausted: Traffic limit exceeded for your account
📧 Proxy quota exhaustion notification sent
```

**Connectivity Issues:**
```
🚨 Proxy connectivity issues: net::ERR_EMPTY_RESPONSE
📧 Proxy down notification sent
```

**System Responses:**
```
🌐 Creating worker #1 without proxy (PROXY_ENABLED=false)
💥 Proxy appears to be down. Shutting down worker pool.
```

## Troubleshooting

### Common Issues

1. **No Email Notifications Received**
   - Check `ENABLE_PROXY_NOTIFICATIONS=true`
   - Verify `RESEND_API_KEY` is configured
   - Check email service logs

2. **False Positive Detections**
   - Review error patterns in `proxyQuotaDetector.ts`
   - Add new patterns if needed
   - Check error message formatting

3. **Missed Quota Exhaustion**
   - Add new error patterns to detection rules
   - Check SmartProxy documentation for error messages
   - Test with actual quota exhaustion scenario

### Manual Notification Test
```javascript
const { sendProxyQuotaNotification } = require('./cron/utils/proxyQuotaDetector');

await sendProxyQuotaNotification(
  'Test: Traffic limit exceeded',
  { test: true, timestamp: new Date().toISOString() }
);
```

## Future Enhancements

1. **Auto-Recovery:** Automatically switch to backup proxy when quota exhausted
2. **Usage Monitoring:** Track proxy usage and predict quota exhaustion
3. **Multiple Providers:** Support for multiple proxy providers with failover
4. **Dashboard Integration:** Real-time proxy status in admin dashboard
5. **Slack Integration:** Send notifications to Slack channels
6. **Quota Alerts:** Proactive alerts at 80%, 90% usage thresholds

## Benefits

- **Immediate Awareness:** Know instantly when proxy quota is exhausted
- **Reduced Downtime:** Faster response to proxy issues
- **Better Debugging:** Clear distinction between quota and connectivity issues
- **Automated Monitoring:** No manual checking required
- **Actionable Alerts:** Specific recommendations for each error type
