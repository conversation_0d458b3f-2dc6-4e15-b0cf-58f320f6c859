{"version": 3, "file": "_server.ts-Bg6qVWPD.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/ai/jobs/match-details/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../../../../chunks/auth.js\";\nconst POST = async ({ request, cookies }) => {\n  try {\n    const token = cookies.get(\"auth_token\");\n    if (!token) {\n      return json({ error: \"Unauthorized\" }, { status: 401 });\n    }\n    const tokenData = verifySessionToken(token);\n    if (!tokenData || !tokenData.id) {\n      return json({ error: \"Invalid token\" }, { status: 401 });\n    }\n    const userId = tokenData.id;\n    const body = await request.json();\n    const { profileId, jobId } = body;\n    if (!profileId || !jobId) {\n      return json(\n        { error: \"Missing required fields\", required: [\"profileId\", \"jobId\"] },\n        { status: 400 }\n      );\n    }\n    const profile = await prisma.profile.findFirst({\n      where: {\n        id: profileId,\n        userId\n      }\n    });\n    if (!profile) {\n      return json({ error: \"Profile not found or access denied\" }, { status: 404 });\n    }\n    const job = await prisma.job.findUnique({\n      where: { id: jobId }\n    });\n    if (!job) {\n      return json({ error: \"Job not found\" }, { status: 404 });\n    }\n    const user = await prisma.user.findUnique({\n      where: { id: userId },\n      include: {\n        subscriptions: {\n          where: { status: \"active\" },\n          include: {\n            plan: {\n              include: {\n                features: {\n                  where: { featureId: \"ai_job_matching\" }\n                }\n              }\n            }\n          }\n        },\n        featureUsage: {\n          where: {\n            featureId: \"ai_job_matching\",\n            limitId: \"job_match_analysis_monthly\"\n          }\n        }\n      }\n    });\n    const hasAccess = user?.subscriptions.some(\n      (sub) => sub.plan.features.some((feature) => feature.featureId === \"ai_job_matching\")\n    );\n    if (!hasAccess && process.env.NODE_ENV === \"production\") {\n      return json({ error: \"Feature not available in your plan\" }, { status: 403 });\n    }\n    const usageLimit = user?.subscriptions.flatMap((sub) => sub.plan.features).find((feature) => feature.featureId === \"ai_job_matching\")?.limits?.find((limit) => limit.limitId === \"job_match_analysis_monthly\")?.value;\n    const currentUsage = user?.featureUsage.find(\n      (usage) => usage.featureId === \"ai_job_matching\" && usage.limitId === \"job_match_analysis_monthly\"\n    )?.usage || 0;\n    if (usageLimit && currentUsage >= parseInt(usageLimit) && process.env.NODE_ENV === \"production\") {\n      return json({ error: \"Monthly usage limit reached\" }, { status: 403 });\n    }\n    let matchDetails = await prisma.enhancedJobMatch.findUnique({\n      where: {\n        profileId_jobId: {\n          profileId,\n          jobId\n        }\n      }\n    });\n    if (!matchDetails) {\n      const mockMatchDetails = generateMockJobMatchDetails(job);\n      matchDetails = await prisma.enhancedJobMatch.create({\n        data: {\n          id: `match-${Date.now()}`,\n          userId,\n          profileId,\n          jobId,\n          overallMatchScore: mockMatchDetails.overallMatchScore,\n          skillsMatchScore: mockMatchDetails.skillsMatchScore,\n          experienceMatchScore: mockMatchDetails.experienceMatchScore,\n          educationMatchScore: mockMatchDetails.educationMatchScore,\n          keywordMatchScore: mockMatchDetails.keywordMatchScore,\n          matchDetails: {\n            matchedSkills: mockMatchDetails.matchedSkills,\n            missingSkills: mockMatchDetails.missingSkills\n          },\n          recommendations: mockMatchDetails.recommendations\n        }\n      });\n    }\n    await prisma.featureUsage.upsert({\n      where: {\n        userId_featureId_limitId: {\n          userId,\n          featureId: \"ai_job_matching\",\n          limitId: \"job_match_analysis_monthly\"\n        }\n      },\n      update: {\n        usage: { increment: 1 }\n      },\n      create: {\n        userId,\n        featureId: \"ai_job_matching\",\n        limitId: \"job_match_analysis_monthly\",\n        usage: 1\n      }\n    });\n    const response = {\n      overallMatchScore: matchDetails.overallMatchScore,\n      skillsMatchScore: matchDetails.skillsMatchScore,\n      experienceMatchScore: matchDetails.experienceMatchScore,\n      educationMatchScore: matchDetails.educationMatchScore,\n      keywordMatchScore: matchDetails.keywordMatchScore,\n      matchedSkills: matchDetails.matchDetails?.matchedSkills || [],\n      missingSkills: matchDetails.matchDetails?.missingSkills || [],\n      recommendations: matchDetails.recommendations || []\n    };\n    return json({ success: true, matchDetails: response });\n  } catch (error) {\n    console.error(\"Error getting job match details:\", error);\n    return json({ error: \"Internal server error\" }, { status: 500 });\n  }\n};\nfunction generateMockJobMatchDetails(job) {\n  const jobSkills = extractSkillsFromJob(job);\n  const matchedSkills = jobSkills.slice(0, Math.floor(jobSkills.length * 0.7));\n  const missingSkills = jobSkills.slice(Math.floor(jobSkills.length * 0.7));\n  const skillsMatchScore = matchedSkills.length / jobSkills.length;\n  const experienceMatchScore = Math.random() * 0.3 + 0.5;\n  const educationMatchScore = Math.random() * 0.3 + 0.6;\n  const keywordMatchScore = Math.random() * 0.3 + 0.5;\n  const overallMatchScore = skillsMatchScore * 0.4 + experienceMatchScore * 0.3 + educationMatchScore * 0.2 + keywordMatchScore * 0.1;\n  const recommendations = generateRecommendations(missingSkills, job);\n  return {\n    overallMatchScore,\n    skillsMatchScore,\n    experienceMatchScore,\n    educationMatchScore,\n    keywordMatchScore,\n    matchedSkills,\n    missingSkills,\n    recommendations\n  };\n}\nfunction extractSkillsFromJob(job) {\n  const commonSkills = [\n    \"Communication\",\n    \"Problem Solving\",\n    \"Teamwork\",\n    \"Time Management\",\n    \"Adaptability\"\n  ];\n  let jobSpecificSkills = [];\n  if (job.title.toLowerCase().includes(\"software\") || job.title.toLowerCase().includes(\"developer\") || job.title.toLowerCase().includes(\"engineer\")) {\n    jobSpecificSkills = [\n      \"JavaScript\",\n      \"TypeScript\",\n      \"React\",\n      \"Node.js\",\n      \"SQL\",\n      \"Git\",\n      \"CI/CD\",\n      \"AWS\",\n      \"Docker\",\n      \"REST API\"\n    ];\n  } else if (job.title.toLowerCase().includes(\"data\")) {\n    jobSpecificSkills = [\n      \"Python\",\n      \"SQL\",\n      \"Data Analysis\",\n      \"Machine Learning\",\n      \"Statistics\",\n      \"Data Visualization\",\n      \"Pandas\",\n      \"NumPy\",\n      \"Tableau\",\n      \"Power BI\"\n    ];\n  } else if (job.title.toLowerCase().includes(\"manager\") || job.title.toLowerCase().includes(\"lead\")) {\n    jobSpecificSkills = [\n      \"Leadership\",\n      \"Project Management\",\n      \"Strategic Planning\",\n      \"Team Building\",\n      \"Budgeting\",\n      \"Performance Management\",\n      \"Conflict Resolution\",\n      \"Decision Making\",\n      \"Delegation\",\n      \"Mentoring\"\n    ];\n  } else if (job.title.toLowerCase().includes(\"marketing\")) {\n    jobSpecificSkills = [\n      \"Digital Marketing\",\n      \"Social Media\",\n      \"Content Creation\",\n      \"SEO\",\n      \"SEM\",\n      \"Analytics\",\n      \"Email Marketing\",\n      \"Brand Management\",\n      \"Market Research\",\n      \"Campaign Management\"\n    ];\n  } else {\n    jobSpecificSkills = [\n      \"Microsoft Office\",\n      \"Customer Service\",\n      \"Research\",\n      \"Analysis\",\n      \"Reporting\",\n      \"Presentation\",\n      \"Organization\",\n      \"Multitasking\",\n      \"Attention to Detail\",\n      \"Critical Thinking\"\n    ];\n  }\n  return [...commonSkills, ...jobSpecificSkills];\n}\nfunction generateRecommendations(missingSkills, job) {\n  const recommendations = [\n    `Add the following skills to your profile: ${missingSkills.slice(0, 3).join(\", \")}`,\n    \"Highlight relevant experience that demonstrates your expertise in the required skills\",\n    \"Tailor your resume summary to match the job description\",\n    \"Include specific achievements that align with the job requirements\"\n  ];\n  if (job.title.toLowerCase().includes(\"senior\") || job.title.toLowerCase().includes(\"lead\")) {\n    recommendations.push(\"Emphasize leadership experience and team management skills\");\n  }\n  if (job.title.toLowerCase().includes(\"remote\")) {\n    recommendations.push(\"Highlight experience working remotely and using collaboration tools\");\n  }\n  return recommendations;\n}\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;AAGK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AAC7C,EAAE,IAAI;AACN,IAAI,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AAC3C,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7D;AACA,IAAI,MAAM,SAAS,GAAG,kBAAkB,CAAC,KAAK,CAAC;AAC/C,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE;AACrC,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9D;AACA,IAAI,MAAM,MAAM,GAAG,SAAS,CAAC,EAAE;AAC/B,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,IAAI;AACrC,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,KAAK,EAAE;AAC9B,MAAM,OAAO,IAAI;AACjB,QAAQ,EAAE,KAAK,EAAE,yBAAyB,EAAE,QAAQ,EAAE,CAAC,WAAW,EAAE,OAAO,CAAC,EAAE;AAC9E,QAAQ,EAAE,MAAM,EAAE,GAAG;AACrB,OAAO;AACP;AACA,IAAI,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;AACnD,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE,SAAS;AACrB,QAAQ;AACR;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,OAAO,EAAE;AAClB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oCAAoC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnF;AACA,IAAI,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC;AAC5C,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,KAAK;AACxB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,GAAG,EAAE;AACd,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9D;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAC9C,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;AAC3B,MAAM,OAAO,EAAE;AACf,QAAQ,aAAa,EAAE;AACvB,UAAU,KAAK,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE;AACrC,UAAU,OAAO,EAAE;AACnB,YAAY,IAAI,EAAE;AAClB,cAAc,OAAO,EAAE;AACvB,gBAAgB,QAAQ,EAAE;AAC1B,kBAAkB,KAAK,EAAE,EAAE,SAAS,EAAE,iBAAiB;AACvD;AACA;AACA;AACA;AACA,SAAS;AACT,QAAQ,YAAY,EAAE;AACtB,UAAU,KAAK,EAAE;AACjB,YAAY,SAAS,EAAE,iBAAiB;AACxC,YAAY,OAAO,EAAE;AACrB;AACA;AACA;AACA,KAAK,CAAC;AACN,IAAI,MAAM,SAAS,GAAG,IAAI,EAAE,aAAa,CAAC,IAAI;AAC9C,MAAM,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,SAAS,KAAK,iBAAiB;AAC1F,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE;AAC7D,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oCAAoC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnF;AACA,IAAI,MAAM,UAAU,GAAG,IAAI,EAAE,aAAa,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,SAAS,KAAK,iBAAiB,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,OAAO,KAAK,4BAA4B,CAAC,EAAE,KAAK;AACzN,IAAI,MAAM,YAAY,GAAG,IAAI,EAAE,YAAY,CAAC,IAAI;AAChD,MAAM,CAAC,KAAK,KAAK,KAAK,CAAC,SAAS,KAAK,iBAAiB,IAAI,KAAK,CAAC,OAAO,KAAK;AAC5E,KAAK,EAAE,KAAK,IAAI,CAAC;AACjB,IAAI,IAAI,UAAU,IAAI,YAAY,IAAI,QAAQ,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE;AACrG,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,6BAA6B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC5E;AACA,IAAI,IAAI,YAAY,GAAG,MAAM,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC;AAChE,MAAM,KAAK,EAAE;AACb,QAAQ,eAAe,EAAE;AACzB,UAAU,SAAS;AACnB,UAAU;AACV;AACA;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,YAAY,EAAE;AACvB,MAAM,MAAM,gBAAgB,GAAG,2BAA2B,CAAC,GAAG,CAAC;AAC/D,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;AAC1D,QAAQ,IAAI,EAAE;AACd,UAAU,EAAE,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;AACnC,UAAU,MAAM;AAChB,UAAU,SAAS;AACnB,UAAU,KAAK;AACf,UAAU,iBAAiB,EAAE,gBAAgB,CAAC,iBAAiB;AAC/D,UAAU,gBAAgB,EAAE,gBAAgB,CAAC,gBAAgB;AAC7D,UAAU,oBAAoB,EAAE,gBAAgB,CAAC,oBAAoB;AACrE,UAAU,mBAAmB,EAAE,gBAAgB,CAAC,mBAAmB;AACnE,UAAU,iBAAiB,EAAE,gBAAgB,CAAC,iBAAiB;AAC/D,UAAU,YAAY,EAAE;AACxB,YAAY,aAAa,EAAE,gBAAgB,CAAC,aAAa;AACzD,YAAY,aAAa,EAAE,gBAAgB,CAAC;AAC5C,WAAW;AACX,UAAU,eAAe,EAAE,gBAAgB,CAAC;AAC5C;AACA,OAAO,CAAC;AACR;AACA,IAAI,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AACrC,MAAM,KAAK,EAAE;AACb,QAAQ,wBAAwB,EAAE;AAClC,UAAU,MAAM;AAChB,UAAU,SAAS,EAAE,iBAAiB;AACtC,UAAU,OAAO,EAAE;AACnB;AACA,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,KAAK,EAAE,EAAE,SAAS,EAAE,CAAC;AAC7B,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,MAAM;AACd,QAAQ,SAAS,EAAE,iBAAiB;AACpC,QAAQ,OAAO,EAAE,4BAA4B;AAC7C,QAAQ,KAAK,EAAE;AACf;AACA,KAAK,CAAC;AACN,IAAI,MAAM,QAAQ,GAAG;AACrB,MAAM,iBAAiB,EAAE,YAAY,CAAC,iBAAiB;AACvD,MAAM,gBAAgB,EAAE,YAAY,CAAC,gBAAgB;AACrD,MAAM,oBAAoB,EAAE,YAAY,CAAC,oBAAoB;AAC7D,MAAM,mBAAmB,EAAE,YAAY,CAAC,mBAAmB;AAC3D,MAAM,iBAAiB,EAAE,YAAY,CAAC,iBAAiB;AACvD,MAAM,aAAa,EAAE,YAAY,CAAC,YAAY,EAAE,aAAa,IAAI,EAAE;AACnE,MAAM,aAAa,EAAE,YAAY,CAAC,YAAY,EAAE,aAAa,IAAI,EAAE;AACnE,MAAM,eAAe,EAAE,YAAY,CAAC,eAAe,IAAI;AACvD,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,CAAC;AAC1D,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC;AAC5D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpE;AACA;AACA,SAAS,2BAA2B,CAAC,GAAG,EAAE;AAC1C,EAAE,MAAM,SAAS,GAAG,oBAAoB,CAAC,GAAG,CAAC;AAC7C,EAAE,MAAM,aAAa,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC;AAC9E,EAAE,MAAM,aAAa,GAAG,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC;AAC3E,EAAE,MAAM,gBAAgB,GAAG,aAAa,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;AAClE,EAAE,MAAM,oBAAoB,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,GAAG;AACxD,EAAE,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,GAAG;AACvD,EAAE,MAAM,iBAAiB,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,GAAG;AACrD,EAAE,MAAM,iBAAiB,GAAG,gBAAgB,GAAG,GAAG,GAAG,oBAAoB,GAAG,GAAG,GAAG,mBAAmB,GAAG,GAAG,GAAG,iBAAiB,GAAG,GAAG;AACrI,EAAE,MAAM,eAAe,GAAG,uBAAuB,CAAC,aAAa,EAAE,GAAG,CAAC;AACrE,EAAE,OAAO;AACT,IAAI,iBAAiB;AACrB,IAAI,gBAAgB;AACpB,IAAI,oBAAoB;AACxB,IAAI,mBAAmB;AACvB,IAAI,iBAAiB;AACrB,IAAI,aAAa;AACjB,IAAI,aAAa;AACjB,IAAI;AACJ,GAAG;AACH;AACA,SAAS,oBAAoB,CAAC,GAAG,EAAE;AACnC,EAAE,MAAM,YAAY,GAAG;AACvB,IAAI,eAAe;AACnB,IAAI,iBAAiB;AACrB,IAAI,UAAU;AACd,IAAI,iBAAiB;AACrB,IAAI;AACJ,GAAG;AACH,EAAE,IAAI,iBAAiB,GAAG,EAAE;AAC5B,EAAE,IAAI,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;AACrJ,IAAI,iBAAiB,GAAG;AACxB,MAAM,YAAY;AAClB,MAAM,YAAY;AAClB,MAAM,OAAO;AACb,MAAM,SAAS;AACf,MAAM,KAAK;AACX,MAAM,KAAK;AACX,MAAM,OAAO;AACb,MAAM,KAAK;AACX,MAAM,QAAQ;AACd,MAAM;AACN,KAAK;AACL,GAAG,MAAM,IAAI,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;AACvD,IAAI,iBAAiB,GAAG;AACxB,MAAM,QAAQ;AACd,MAAM,KAAK;AACX,MAAM,eAAe;AACrB,MAAM,kBAAkB;AACxB,MAAM,YAAY;AAClB,MAAM,oBAAoB;AAC1B,MAAM,QAAQ;AACd,MAAM,OAAO;AACb,MAAM,SAAS;AACf,MAAM;AACN,KAAK;AACL,GAAG,MAAM,IAAI,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;AACtG,IAAI,iBAAiB,GAAG;AACxB,MAAM,YAAY;AAClB,MAAM,oBAAoB;AAC1B,MAAM,oBAAoB;AAC1B,MAAM,eAAe;AACrB,MAAM,WAAW;AACjB,MAAM,wBAAwB;AAC9B,MAAM,qBAAqB;AAC3B,MAAM,iBAAiB;AACvB,MAAM,YAAY;AAClB,MAAM;AACN,KAAK;AACL,GAAG,MAAM,IAAI,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;AAC5D,IAAI,iBAAiB,GAAG;AACxB,MAAM,mBAAmB;AACzB,MAAM,cAAc;AACpB,MAAM,kBAAkB;AACxB,MAAM,KAAK;AACX,MAAM,KAAK;AACX,MAAM,WAAW;AACjB,MAAM,iBAAiB;AACvB,MAAM,kBAAkB;AACxB,MAAM,iBAAiB;AACvB,MAAM;AACN,KAAK;AACL,GAAG,MAAM;AACT,IAAI,iBAAiB,GAAG;AACxB,MAAM,kBAAkB;AACxB,MAAM,kBAAkB;AACxB,MAAM,UAAU;AAChB,MAAM,UAAU;AAChB,MAAM,WAAW;AACjB,MAAM,cAAc;AACpB,MAAM,cAAc;AACpB,MAAM,cAAc;AACpB,MAAM,qBAAqB;AAC3B,MAAM;AACN,KAAK;AACL;AACA,EAAE,OAAO,CAAC,GAAG,YAAY,EAAE,GAAG,iBAAiB,CAAC;AAChD;AACA,SAAS,uBAAuB,CAAC,aAAa,EAAE,GAAG,EAAE;AACrD,EAAE,MAAM,eAAe,GAAG;AAC1B,IAAI,CAAC,0CAA0C,EAAE,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACvF,IAAI,uFAAuF;AAC3F,IAAI,yDAAyD;AAC7D,IAAI;AACJ,GAAG;AACH,EAAE,IAAI,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;AAC9F,IAAI,eAAe,CAAC,IAAI,CAAC,4DAA4D,CAAC;AACtF;AACA,EAAE,IAAI,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;AAClD,IAAI,eAAe,CAAC,IAAI,CAAC,qEAAqE,CAAC;AAC/F;AACA,EAAE,OAAO,eAAe;AACxB;;;;"}