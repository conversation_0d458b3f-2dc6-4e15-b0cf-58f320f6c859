#!/usr/bin/env tsx

/**
 * <PERSON><PERSON><PERSON> to configure CORS policies for all R2 buckets
 * This allows web browsers to access images and files from R2 storage
 */

import { configureAllBucketsCors } from "../lib/storage/r2Storage";
import { logger } from "../utils/logger";

async function main() {
  try {
    logger.info("🚀 Starting R2 CORS configuration...");

    const result = await configureAllBucketsCors();

    if (result.success) {
      logger.info("✅ All R2 buckets configured with CORS successfully!");
      logger.info("📋 Results:", result.results);
    } else {
      logger.error("❌ Some buckets failed CORS configuration");
      logger.error("📋 Results:", result.results);
      process.exit(1);
    }
  } catch (error) {
    logger.error("💥 Fatal error configuring R2 CORS:", error);
    process.exit(1);
  }
}

// Run the script
main();
