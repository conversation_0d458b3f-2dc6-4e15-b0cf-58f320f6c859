{"version": 3, "file": "_server.ts-BE_-2IeC.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/ai/ats/job/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../../chunks/prisma.js\";\nimport { OpenAI } from \"openai\";\nfunction getOpenAIClient() {\n  if (!process.env.OPENAI_API_KEY) {\n    throw new Error(\"OpenAI API key is not configured\");\n  }\n  return new OpenAI({\n    apiKey: process.env.OPENAI_API_KEY\n  });\n}\nconst POST = async ({ request, locals }) => {\n  if (!locals.user) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  try {\n    const { resumeId, jobId } = await request.json();\n    if (!resumeId || !jobId) {\n      return json({ error: \"Resume ID and Job ID are required\" }, { status: 400 });\n    }\n    const resume = await prisma.document.findUnique({\n      where: {\n        id: resumeId,\n        userId: locals.user.id\n      },\n      include: {\n        resume: true\n      }\n    });\n    if (!resume) {\n      return json({ error: \"Resume not found\" }, { status: 404 });\n    }\n    const job = await prisma.jobListing.findUnique({\n      where: {\n        id: jobId\n      },\n      select: {\n        title: true,\n        company: true,\n        description: true,\n        requirements: true\n      }\n    });\n    if (!job) {\n      return json({ error: \"Job not found\" }, { status: 404 });\n    }\n    const analysis = await generateJobSpecificATSAnalysis(resume, job);\n    const savedAnalysis = await prisma.atsAnalysis.create({\n      data: {\n        userId: locals.user.id,\n        resumeId,\n        jobId,\n        overallScore: analysis.overallScore,\n        keywordScore: analysis.keywordScore,\n        formatScore: analysis.formatScore,\n        contentScore: analysis.contentScore,\n        readabilityScore: analysis.readabilityScore,\n        keywordMatches: analysis.keywordMatches,\n        missingKeywords: analysis.missingKeywords,\n        formatIssues: analysis.formatIssues,\n        contentSuggestions: analysis.contentSuggestions,\n        readabilitySuggestions: analysis.readabilitySuggestions,\n        jobSpecific: true,\n        jobTitle: job.title,\n        company: job.company\n      }\n    });\n    await prisma.featureUsage.upsert({\n      where: {\n        userId_featureId_limitId: {\n          userId: locals.user.id,\n          featureId: \"ats_optimization\",\n          limitId: \"ats_job_specific_scans_monthly\"\n        }\n      },\n      update: {\n        usage: {\n          increment: 1\n        }\n      },\n      create: {\n        userId: locals.user.id,\n        featureId: \"ats_optimization\",\n        limitId: \"ats_job_specific_scans_monthly\",\n        usage: 1\n      }\n    });\n    return json({ analysis: savedAnalysis });\n  } catch (error) {\n    console.error(\"Error generating job-specific ATS analysis:\", error);\n    return json({ error: \"Failed to generate job-specific ATS analysis\" }, { status: 500 });\n  }\n};\nconst GET = async ({ url, locals }) => {\n  if (!locals.user) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  try {\n    const resumeId = url.searchParams.get(\"resumeId\");\n    const jobId = url.searchParams.get(\"jobId\");\n    if (!resumeId || !jobId) {\n      return json({ error: \"Resume ID and Job ID are required\" }, { status: 400 });\n    }\n    const analysis = await prisma.atsAnalysis.findFirst({\n      where: {\n        userId: locals.user.id,\n        resumeId,\n        jobId,\n        jobSpecific: true\n      },\n      orderBy: {\n        createdAt: \"desc\"\n      }\n    });\n    if (!analysis) {\n      return json({ error: \"No job-specific analysis found\" }, { status: 404 });\n    }\n    return json({ analysis });\n  } catch (error) {\n    console.error(\"Error fetching job-specific ATS analysis:\", error);\n    return json({ error: \"Failed to fetch job-specific ATS analysis\" }, { status: 500 });\n  }\n};\nasync function generateJobSpecificATSAnalysis(resume, job) {\n  try {\n    const resumeContent = resume.resume ? JSON.stringify(resume.resume) : \"\";\n    const resumeText = resume.content || \"\";\n    const prompt = `Analyze this resume for ATS compatibility specifically for this job:\n\nResume Content:\n${resumeContent}\n\nPlain Text:\n${resumeText}\n\nJob Title: ${job.title}\nCompany: ${job.company}\nJob Description: ${job.description || \"\"}\nRequirements: ${job.requirements || \"\"}\n\nPlease analyze how well this resume matches the job description.\n\nProvide a detailed analysis with the following scores (0-100):\n1. Overall Score\n2. Keyword Score\n3. Format Score\n4. Content Score\n5. Readability Score\n\nAlso provide:\n- Keyword matches found\n- Missing important keywords\n- Format issues\n- Content suggestions\n- Readability suggestions\n\nReturn the analysis as a JSON object with these fields.\n`;\n    const openai = getOpenAIClient();\n    const response = await openai.chat.completions.create({\n      model: \"gpt-4\",\n      messages: [\n        { role: \"system\", content: \"You are an expert ATS (Applicant Tracking System) analyzer.\" },\n        { role: \"user\", content: prompt }\n      ],\n      temperature: 0.7,\n      max_tokens: 2e3,\n      response_format: { type: \"json_object\" }\n    });\n    const content = response.choices[0]?.message?.content || \"\";\n    const analysisResult = JSON.parse(content);\n    return {\n      overallScore: analysisResult.overallScore || 0,\n      keywordScore: analysisResult.keywordScore || 0,\n      formatScore: analysisResult.formatScore || 0,\n      contentScore: analysisResult.contentScore || 0,\n      readabilityScore: analysisResult.readabilityScore || 0,\n      keywordMatches: analysisResult.keywordMatches || [],\n      missingKeywords: analysisResult.missingKeywords || [],\n      formatIssues: analysisResult.formatIssues || [],\n      contentSuggestions: analysisResult.contentSuggestions || [],\n      readabilitySuggestions: analysisResult.readabilitySuggestions || []\n    };\n  } catch (error) {\n    console.error(\"Error in job-specific ATS analysis generation:\", error);\n    return {\n      overallScore: 70,\n      keywordScore: 65,\n      formatScore: 75,\n      contentScore: 70,\n      readabilityScore: 80,\n      keywordMatches: [\"resume\", \"experience\", \"skills\"],\n      missingKeywords: [\"specific technical skills\", \"certifications\"],\n      formatIssues: [\"Consider using a more ATS-friendly format\"],\n      contentSuggestions: [\"Add more quantifiable achievements\"],\n      readabilitySuggestions: [\"Use more bullet points for better readability\"]\n    };\n  }\n}\nexport {\n  GET,\n  POST\n};\n"], "names": [], "mappings": ";;;;;AAGA,SAAS,eAAe,GAAG;AAC3B,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE;AACnC,IAAI,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC;AACvD;AACA,EAAE,OAAO,IAAI,MAAM,CAAC;AACpB,IAAI,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC;AACxB,GAAG,CAAC;AACJ;AACK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AAC5C,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;AACpB,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACpD,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,EAAE;AAC7B,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mCAAmC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClF;AACA,IAAI,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;AACpD,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE,QAAQ;AACpB,QAAQ,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC;AAC5B,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,MAAM,EAAE;AAChB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,kBAAkB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE;AACA,IAAI,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC;AACnD,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE;AACZ,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,KAAK,EAAE,IAAI;AACnB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,WAAW,EAAE,IAAI;AACzB,QAAQ,YAAY,EAAE;AACtB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,GAAG,EAAE;AACd,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9D;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,8BAA8B,CAAC,MAAM,EAAE,GAAG,CAAC;AACtE,IAAI,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;AAC1D,MAAM,IAAI,EAAE;AACZ,QAAQ,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;AAC9B,QAAQ,QAAQ;AAChB,QAAQ,KAAK;AACb,QAAQ,YAAY,EAAE,QAAQ,CAAC,YAAY;AAC3C,QAAQ,YAAY,EAAE,QAAQ,CAAC,YAAY;AAC3C,QAAQ,WAAW,EAAE,QAAQ,CAAC,WAAW;AACzC,QAAQ,YAAY,EAAE,QAAQ,CAAC,YAAY;AAC3C,QAAQ,gBAAgB,EAAE,QAAQ,CAAC,gBAAgB;AACnD,QAAQ,cAAc,EAAE,QAAQ,CAAC,cAAc;AAC/C,QAAQ,eAAe,EAAE,QAAQ,CAAC,eAAe;AACjD,QAAQ,YAAY,EAAE,QAAQ,CAAC,YAAY;AAC3C,QAAQ,kBAAkB,EAAE,QAAQ,CAAC,kBAAkB;AACvD,QAAQ,sBAAsB,EAAE,QAAQ,CAAC,sBAAsB;AAC/D,QAAQ,WAAW,EAAE,IAAI;AACzB,QAAQ,QAAQ,EAAE,GAAG,CAAC,KAAK;AAC3B,QAAQ,OAAO,EAAE,GAAG,CAAC;AACrB;AACA,KAAK,CAAC;AACN,IAAI,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AACrC,MAAM,KAAK,EAAE;AACb,QAAQ,wBAAwB,EAAE;AAClC,UAAU,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;AAChC,UAAU,SAAS,EAAE,kBAAkB;AACvC,UAAU,OAAO,EAAE;AACnB;AACA,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,KAAK,EAAE;AACf,UAAU,SAAS,EAAE;AACrB;AACA,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;AAC9B,QAAQ,SAAS,EAAE,kBAAkB;AACrC,QAAQ,OAAO,EAAE,gCAAgC;AACjD,QAAQ,KAAK,EAAE;AACf;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,EAAE,QAAQ,EAAE,aAAa,EAAE,CAAC;AAC5C,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,6CAA6C,EAAE,KAAK,CAAC;AACvE,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,8CAA8C,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3F;AACA;AACK,MAAC,GAAG,GAAG,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK;AACvC,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;AACpB,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,QAAQ,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC;AACrD,IAAI,MAAM,KAAK,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC;AAC/C,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,EAAE;AAC7B,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mCAAmC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClF;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC;AACxD,MAAM,KAAK,EAAE;AACb,QAAQ,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;AAC9B,QAAQ,QAAQ;AAChB,QAAQ,KAAK;AACb,QAAQ,WAAW,EAAE;AACrB,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,SAAS,EAAE;AACnB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,gCAAgC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC/E;AACA,IAAI,OAAO,IAAI,CAAC,EAAE,QAAQ,EAAE,CAAC;AAC7B,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC;AACrE,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,2CAA2C,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxF;AACA;AACA,eAAe,8BAA8B,CAAC,MAAM,EAAE,GAAG,EAAE;AAC3D,EAAE,IAAI;AACN,IAAI,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE;AAC5E,IAAI,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,IAAI,EAAE;AAC3C,IAAI,MAAM,MAAM,GAAG,CAAC;;AAEpB;AACA,EAAE,aAAa;;AAEf;AACA,EAAE,UAAU;;AAEZ,WAAW,EAAE,GAAG,CAAC,KAAK;AACtB,SAAS,EAAE,GAAG,CAAC,OAAO;AACtB,iBAAiB,EAAE,GAAG,CAAC,WAAW,IAAI,EAAE;AACxC,cAAc,EAAE,GAAG,CAAC,YAAY,IAAI,EAAE;;AAEtC;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,CAAC;AACD,IAAI,MAAM,MAAM,GAAG,eAAe,EAAE;AACpC,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;AAC1D,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,QAAQ,EAAE;AAChB,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,6DAA6D,EAAE;AAClG,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM;AACvC,OAAO;AACP,MAAM,WAAW,EAAE,GAAG;AACtB,MAAM,UAAU,EAAE,GAAG;AACrB,MAAM,eAAe,EAAE,EAAE,IAAI,EAAE,aAAa;AAC5C,KAAK,CAAC;AACN,IAAI,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,IAAI,EAAE;AAC/D,IAAI,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;AAC9C,IAAI,OAAO;AACX,MAAM,YAAY,EAAE,cAAc,CAAC,YAAY,IAAI,CAAC;AACpD,MAAM,YAAY,EAAE,cAAc,CAAC,YAAY,IAAI,CAAC;AACpD,MAAM,WAAW,EAAE,cAAc,CAAC,WAAW,IAAI,CAAC;AAClD,MAAM,YAAY,EAAE,cAAc,CAAC,YAAY,IAAI,CAAC;AACpD,MAAM,gBAAgB,EAAE,cAAc,CAAC,gBAAgB,IAAI,CAAC;AAC5D,MAAM,cAAc,EAAE,cAAc,CAAC,cAAc,IAAI,EAAE;AACzD,MAAM,eAAe,EAAE,cAAc,CAAC,eAAe,IAAI,EAAE;AAC3D,MAAM,YAAY,EAAE,cAAc,CAAC,YAAY,IAAI,EAAE;AACrD,MAAM,kBAAkB,EAAE,cAAc,CAAC,kBAAkB,IAAI,EAAE;AACjE,MAAM,sBAAsB,EAAE,cAAc,CAAC,sBAAsB,IAAI;AACvE,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,gDAAgD,EAAE,KAAK,CAAC;AAC1E,IAAI,OAAO;AACX,MAAM,YAAY,EAAE,EAAE;AACtB,MAAM,YAAY,EAAE,EAAE;AACtB,MAAM,WAAW,EAAE,EAAE;AACrB,MAAM,YAAY,EAAE,EAAE;AACtB,MAAM,gBAAgB,EAAE,EAAE;AAC1B,MAAM,cAAc,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,QAAQ,CAAC;AACxD,MAAM,eAAe,EAAE,CAAC,2BAA2B,EAAE,gBAAgB,CAAC;AACtE,MAAM,YAAY,EAAE,CAAC,2CAA2C,CAAC;AACjE,MAAM,kBAAkB,EAAE,CAAC,oCAAoC,CAAC;AAChE,MAAM,sBAAsB,EAAE,CAAC,+CAA+C;AAC9E,KAAK;AACL;AACA;;;;"}