{"version": 3, "file": "_server.ts-CrkGR-zO.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/applications/_applicationId_/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nasync function GET({ params, locals }) {\n  console.log(\"GET application:\", params.applicationId);\n  console.log(\"User in locals:\", locals.user ? `ID: ${locals.user.id}` : \"Not authenticated\");\n  const isDev = process.env.NODE_ENV === \"development\";\n  if (!locals.user && !isDev) {\n    console.log(\"Unauthorized access attempt - no user in locals\");\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  const { applicationId } = params;\n  try {\n    if (!isDev && locals.user) {\n      const application2 = await prisma.application.findUnique({\n        where: {\n          id: applicationId,\n          userId: locals.user.id\n        }\n      });\n      if (!application2) {\n        console.log(\"Application not found or does not belong to user\");\n        return json({ error: \"Application not found\" }, { status: 404 });\n      }\n    }\n    const application = await prisma.application.findUnique({\n      where: {\n        id: applicationId\n      }\n    });\n    if (!application) {\n      console.log(\"Application not found\");\n      return json({ error: \"Application not found\" }, { status: 404 });\n    }\n    console.log(\"Found application:\", application.id);\n    return json({ application });\n  } catch (error) {\n    console.error(\"Error fetching application:\", error);\n    return json({ error: \"Failed to fetch application\" }, { status: 500 });\n  }\n}\nasync function PATCH({ request, params, locals }) {\n  console.log(\"PATCH application:\", params.applicationId);\n  console.log(\"User in locals:\", locals.user ? `ID: ${locals.user.id}` : \"Not authenticated\");\n  const isDev = process.env.NODE_ENV === \"development\";\n  if (!locals.user && !isDev) {\n    console.log(\"Unauthorized access attempt - no user in locals\");\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  const { applicationId } = params;\n  try {\n    if (!isDev && locals.user) {\n      const application = await prisma.application.findUnique({\n        where: {\n          id: applicationId,\n          userId: locals.user.id\n        }\n      });\n      if (!application) {\n        console.log(\"Application not found or does not belong to user\");\n        return json({ error: \"Application not found\" }, { status: 404 });\n      }\n    }\n    const body = await request.json();\n    console.log(\"Update data:\", body);\n    const updatedApplication = await prisma.application.update({\n      where: {\n        id: applicationId\n      },\n      data: {\n        notes: body.notes !== void 0 ? body.notes : void 0,\n        nextAction: body.nextAction !== void 0 ? body.nextAction : void 0\n      }\n    });\n    console.log(\"Application updated successfully:\", updatedApplication.id);\n    return json({ application: updatedApplication }, { status: 200 });\n  } catch (error) {\n    console.error(\"Error updating application:\", error);\n    if (isDev) {\n      return json(\n        {\n          error: \"Failed to update application\",\n          details: error.message,\n          code: error.code\n        },\n        { status: 500 }\n      );\n    } else {\n      return json({ error: \"Failed to update application\" }, { status: 500 });\n    }\n  }\n}\nexport {\n  GET,\n  PATCH\n};\n"], "names": [], "mappings": ";;;;AAEA,eAAe,GAAG,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;AACvC,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,MAAM,CAAC,aAAa,CAAC;AACvD,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,MAAM,CAAC,IAAI,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,mBAAmB,CAAC;AAC7F,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa;AACtD,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE;AAC9B,IAAI,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC;AAClE,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,MAAM;AAClC,EAAE,IAAI;AACN,IAAI,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,EAAE;AAC/B,MAAM,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;AAC/D,QAAQ,KAAK,EAAE;AACf,UAAU,EAAE,EAAE,aAAa;AAC3B,UAAU,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC;AAC9B;AACA,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,YAAY,EAAE;AACzB,QAAQ,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC;AACvE,QAAQ,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA;AACA,IAAI,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;AAC5D,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE;AACZ;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,WAAW,EAAE;AACtB,MAAM,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC;AAC1C,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtE;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,WAAW,CAAC,EAAE,CAAC;AACrD,IAAI,OAAO,IAAI,CAAC,EAAE,WAAW,EAAE,CAAC;AAChC,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC;AACvD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,6BAA6B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1E;AACA;AACA,eAAe,KAAK,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;AAClD,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,MAAM,CAAC,aAAa,CAAC;AACzD,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,MAAM,CAAC,IAAI,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,mBAAmB,CAAC;AAC7F,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa;AACtD,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE;AAC9B,IAAI,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC;AAClE,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,MAAM;AAClC,EAAE,IAAI;AACN,IAAI,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,EAAE;AAC/B,MAAM,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;AAC9D,QAAQ,KAAK,EAAE;AACf,UAAU,EAAE,EAAE,aAAa;AAC3B,UAAU,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC;AAC9B;AACA,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,WAAW,EAAE;AACxB,QAAQ,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC;AACvE,QAAQ,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC;AACrC,IAAI,MAAM,kBAAkB,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;AAC/D,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE;AACZ,OAAO;AACP,MAAM,IAAI,EAAE;AACZ,QAAQ,KAAK,EAAE,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AAC1D,QAAQ,UAAU,EAAE,IAAI,CAAC,UAAU,KAAK,KAAK,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,KAAK;AACxE;AACA,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,mCAAmC,EAAE,kBAAkB,CAAC,EAAE,CAAC;AAC3E,IAAI,OAAO,IAAI,CAAC,EAAE,WAAW,EAAE,kBAAkB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACrE,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC;AACvD,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,OAAO,IAAI;AACjB,QAAQ;AACR,UAAU,KAAK,EAAE,8BAA8B;AAC/C,UAAU,OAAO,EAAE,KAAK,CAAC,OAAO;AAChC,UAAU,IAAI,EAAE,KAAK,CAAC;AACtB,SAAS;AACT,QAAQ,EAAE,MAAM,EAAE,GAAG;AACrB,OAAO;AACP,KAAK,MAAM;AACX,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,8BAA8B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7E;AACA;AACA;;;;"}