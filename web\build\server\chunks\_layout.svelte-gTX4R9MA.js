import 'clsx';
import { p as push, q as pop, J as attr_class, K as fallback, M as ensure_array_like, N as attr, O as escape_html, P as stringify, Q as bind_props, R as spread_props, S as store_get, T as unsubscribe_stores, U as head, V as copy_payload, W as assign_payload, X as sanitize_props, Y as rest_props, Z as spread_attributes, _ as clsx, $ as attr_style, a0 as slot, a1 as derived, a2 as run } from './index3-CqUPEnZw.js';
import { L as Logo, S as ShortcutPage, g as getShortcutsForPage } from './shortcuts-registry-C7HymBtd.js';
import { B as Button } from './button-CrucCo1G.js';
import { c as cn } from './utils-pWl1tgmi.js';
import { b as box, m as mergeProps, u as useRefById, S as SvelteMap, w as watch } from './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import { g as getTabbableCandidates, P as Portal, D as Dismissible_layer, C as CustomEventDispatcher, E as Escape_layer, a as afterSleep } from './scroll-lock-BkBz2nVp.js';
import { a as afterTick } from './after-tick-BHyS0ZjN.js';
import { u as useDebounce } from './use-debounce.svelte-gxToHznJ.js';
import { C as Context } from './context-oepKpCf5.js';
import { g as getDataOrientation, a as getDataOpenClosed, A as ARROW_LEFT, b as ARROW_RIGHT, c as ARROW_DOWN, d as getAriaExpanded, e as getDataDisabled, T as TAB } from './kbd-constants-Ch6RKbNZ.js';
import { n as noop } from './noop-n4I-x7yK.js';
import { u as useRovingFocus } from './use-roving-focus.svelte-BzQ2WziA.js';
import { b as boxAutoReset } from './box-auto-reset.svelte-BDripiF0.js';
import { i as isElement } from './is-mzPc4wSG.js';
import { u as useId } from './use-id-CcFpwo20.js';
import { P as Presence_layer } from './presence-layer-B0FVaAYL.js';
import { M as Mounted } from './mounted-BL5aWRUY.js';
import { c as ce } from './index-DjwFQdT_.js';
import { C as Chevron_down } from './chevron-down-xGjWLrZH.js';
import { g as goto } from './client-dNyMPa8V.js';
import { A as Arrow_right } from './arrow-right-8SE89OuT.js';
import { Z as Zap } from './zap-XN6hrXDp.js';
import { T as Target } from './target-VMK77SRs.js';
import { F as File_text } from './file-text-HttY5S5h.js';
import { B as Bot } from './bot-Be-vDUqI.js';
import { T as Trending_up } from './trending-up-BKR_Sbhj.js';
import { o as onDestroy } from './index-server-CezSOnuG.js';
import { R as Root, D as Dropdown_menu_trigger, a as Dropdown_menu_content } from './index6-D2_psKnf.js';
import { s as setInitialMode, m as modeStorageKey, t as themeStorageKey, d as darkClassNames, l as lightClassNames, a as disableTransitions, b as themeColors, c as defineConfig, e as derivedMode, f as setMode } from './mode-CboidaPj.js';
import { S as Sun, M as Moon } from './sun-B8mCPuDt.js';
import { L as Laptop } from './laptop-CJiJM9e7.js';
import { D as Dropdown_menu_item } from './dropdown-menu-item-DwivDmnZ.js';
import { t as toastState, c as cn$1, u as useEffect } from './Toaster.svelte_svelte_type_style_lang-C29KBcns.js';
import { R as Root$1, D as Dialog_content } from './index7-BURUpWjT.js';
import { w as writable, d as derived$1 } from './index2-Cut0V_vU.js';
import { P as PlanFeaturesList } from './PlanFeaturesList-C6rEbMbF.js';
import { D as Dialog_header, a as Dialog_title, b as Dialog_description, c as Dialog_footer } from './dialog-description-CxPAHL_4.js';
import { C as Check } from './check-WP_4Msti.js';
import { L as Loader_circle } from './loader-circle-BG7dEt2I.js';
import { c as closePricingModal, p as pricingModalStore } from './pricing-D13CEnfk.js';
import { h as html } from './html-FW6Ia4bL.js';
import './false-CRHihH2U.js';
import 'tailwind-merge';
import 'date-fns';
import './_commonjsHelpers-BFTU3MAI.js';
import './events-CUVXVLW9.js';
import './Icon-A4vzmk-O.js';
import './popper-layer-force-mount-GhIXXB9T.js';
import './use-grace-area.svelte-CrXiOQDy.js';
import './dialog-overlay-CspOQRJq.js';
import './x-DwZgpWRG.js';
import './dialog-description2-rfr-pd9k.js';

const ignoredElement = ["INPUT", "TEXTAREA"];
function useArrowNavigation(e, currentElement, parentElement, options) {
  if (!currentElement || ignoredElement.includes(currentElement.nodeName)) {
    return null;
  }
  const { arrowKeyOptions = "both", itemsArray = [], loop = true, dir = "ltr", preventScroll = true, focus = false } = options;
  const [right, left, up, down, home, end] = [
    e.key === "ArrowRight",
    e.key === "ArrowLeft",
    e.key === "ArrowUp",
    e.key === "ArrowDown",
    e.key === "Home",
    e.key === "End"
  ];
  const goingVertical = up || down;
  const goingHorizontal = right || left;
  if (!home && !end && (!goingVertical && !goingHorizontal || arrowKeyOptions === "vertical" && goingHorizontal || arrowKeyOptions === "horizontal" && goingVertical))
    return null;
  const allCollectionItems = itemsArray;
  if (!allCollectionItems.length)
    return null;
  if (preventScroll)
    e.preventDefault();
  let item = null;
  if (goingHorizontal || goingVertical) {
    const goForward = goingVertical ? down : dir === "ltr" ? right : left;
    item = findNextFocusableElement(allCollectionItems, currentElement, {
      goForward,
      loop
    });
  } else if (home) {
    item = allCollectionItems.at(0) || null;
  } else if (end) {
    item = allCollectionItems.at(-1) || null;
  }
  if (focus)
    item?.focus();
  return item;
}
function findNextFocusableElement(elements, currentElement, { goForward, loop }, iterations = elements.length) {
  if (--iterations === 0)
    return null;
  const index = elements.indexOf(currentElement);
  const newIndex = goForward ? index + 1 : index - 1;
  if (!loop && (newIndex < 0 || newIndex >= elements.length))
    return null;
  const adjustedNewIndex = (newIndex + elements.length) % elements.length;
  const candidate = elements[adjustedNewIndex];
  if (!candidate)
    return null;
  const isDisabled = candidate.hasAttribute("disabled") && candidate.getAttribute("disabled") !== "false";
  if (isDisabled) {
    return findNextFocusableElement(elements, candidate, { goForward, loop }, iterations);
  }
  return candidate;
}
const NAVIGATION_MENU_ROOT_ATTR = "data-navigation-menu-root";
const NAVIGATION_MENU_ATTR = "data-navigation-menu";
const NAVIGATION_MENU_ITEM_ATTR = "data-navigation-menu-item";
const NAVIGATION_MENU_LIST_ATTR = "data-navigation-menu-list";
const NAVIGATION_MENU_TRIGGER_ATTR = "data-navigation-menu-trigger";
const NAVIGATION_MENU_CONTENT_ATTR = "data-navigation-menu-content";
const NAVIGATION_MENU_LINK_ATTR = "data-navigation-menu-link";
const NAVIGATION_MENU_VIEWPORT_ATTR = "data-navigation-menu-viewport";
class NavigationMenuProviderState {
  opts;
  indicatorTrackRef = box(null);
  viewportRef = box(null);
  viewportContent = new SvelteMap();
  onTriggerEnter;
  onTriggerLeave = noop;
  onContentEnter = noop;
  onContentLeave = noop;
  onItemSelect;
  onItemDismiss;
  activeItem = null;
  prevActiveItem = null;
  constructor(opts) {
    this.opts = opts;
    this.onTriggerEnter = opts.onTriggerEnter;
    this.onTriggerLeave = opts.onTriggerLeave ?? noop;
    this.onContentEnter = opts.onContentEnter ?? noop;
    this.onContentLeave = opts.onContentLeave ?? noop;
    this.onItemDismiss = opts.onItemDismiss;
    this.onItemSelect = opts.onItemSelect;
  }
  setActiveItem = (item) => {
    this.prevActiveItem = this.activeItem;
    this.activeItem = item;
  };
}
class NavigationMenuRootState {
  opts;
  provider;
  previousValue = box("");
  isDelaySkipped;
  #derivedDelay = derived(() => {
    const isOpen = this.opts?.value?.current !== "";
    if (isOpen || this.isDelaySkipped.current) {
      return 100;
    } else {
      return this.opts.delayDuration.current;
    }
  });
  constructor(opts) {
    this.opts = opts;
    this.isDelaySkipped = boxAutoReset(false, this.opts.skipDelayDuration.current);
    useRefById(opts);
    this.provider = useNavigationMenuProvider({
      value: this.opts.value,
      previousValue: this.previousValue,
      dir: this.opts.dir,
      orientation: this.opts.orientation,
      rootNavigationMenuRef: this.opts.ref,
      isRootMenu: true,
      onTriggerEnter: (itemValue, itemState) => {
        this.#onTriggerEnter(itemValue, itemState);
      },
      onTriggerLeave: this.#onTriggerLeave,
      onContentEnter: this.#onContentEnter,
      onContentLeave: this.#onContentLeave,
      onItemSelect: this.#onItemSelect,
      onItemDismiss: this.#onItemDismiss
    });
  }
  #debouncedFn = useDebounce(
    (val, itemState) => {
      if (typeof val === "string") {
        this.setValue(val, itemState);
      }
    },
    () => this.#derivedDelay()
  );
  #onTriggerEnter = (itemValue, itemState) => {
    this.#debouncedFn(itemValue, itemState);
  };
  #onTriggerLeave = () => {
    this.isDelaySkipped.current = false;
    this.#debouncedFn("", null);
  };
  #onContentEnter = () => {
    this.#debouncedFn(void 0, null);
  };
  #onContentLeave = () => {
    if (this.provider.activeItem && this.provider.activeItem.opts.openOnHover.current === false) {
      return;
    }
    this.#debouncedFn("", null);
  };
  #onItemSelect = (itemValue, itemState) => {
    this.setValue(itemValue, itemState);
  };
  #onItemDismiss = () => {
    this.setValue("", null);
  };
  setValue = (newValue, itemState) => {
    this.previousValue.current = this.opts.value.current;
    this.opts.value.current = newValue;
    this.provider.setActiveItem(itemState);
    if (newValue === "") {
      this.previousValue.current = "";
    }
  };
  #props = derived(() => ({
    id: this.opts.id.current,
    "data-orientation": getDataOrientation(this.opts.orientation.current),
    dir: this.opts.dir.current,
    [NAVIGATION_MENU_ROOT_ATTR]: "",
    [NAVIGATION_MENU_ATTR]: ""
  }));
  get props() {
    return this.#props();
  }
  set props($$value) {
    return this.#props($$value);
  }
}
class NavigationMenuListState {
  opts;
  context;
  wrapperId = box(useId());
  wrapperRef = box(null);
  listTriggers = [];
  rovingFocusGroup;
  wrapperMounted = false;
  constructor(opts, context) {
    this.opts = opts;
    this.context = context;
    useRefById(opts);
    useRefById({
      id: this.wrapperId,
      ref: this.wrapperRef,
      onRefChange: (node) => {
        this.context.indicatorTrackRef.current = node;
      },
      deps: () => this.wrapperMounted
    });
    this.rovingFocusGroup = useRovingFocus({
      rootNodeId: opts.id,
      candidateSelector: `[${NAVIGATION_MENU_TRIGGER_ATTR}]:not([data-disabled]), [${NAVIGATION_MENU_LINK_ATTR}]:not([data-disabled])`,
      loop: box.with(() => false),
      orientation: this.context.opts.orientation
    });
  }
  registerTrigger(trigger) {
    if (trigger) this.listTriggers.push(trigger);
    return () => {
      this.listTriggers = this.listTriggers.filter((t) => t.id !== trigger.id);
    };
  }
  #wrapperProps = derived(() => ({ id: this.wrapperId.current }));
  get wrapperProps() {
    return this.#wrapperProps();
  }
  set wrapperProps($$value) {
    return this.#wrapperProps($$value);
  }
  #props = derived(() => ({
    id: this.opts.id.current,
    "data-orientation": getDataOrientation(this.context.opts.orientation.current),
    [NAVIGATION_MENU_LIST_ATTR]: ""
  }));
  get props() {
    return this.#props();
  }
  set props($$value) {
    return this.#props($$value);
  }
}
class NavigationMenuItemState {
  opts;
  listContext;
  contentNode = null;
  triggerNode = null;
  focusProxyNode = null;
  restoreContentTabOrder = noop;
  wasEscapeClose = false;
  #contentId = derived(() => this.contentNode?.id);
  get contentId() {
    return this.#contentId();
  }
  set contentId($$value) {
    return this.#contentId($$value);
  }
  #triggerId = derived(() => this.triggerNode?.id);
  get triggerId() {
    return this.#triggerId();
  }
  set triggerId($$value) {
    return this.#triggerId($$value);
  }
  contentChildren = box(void 0);
  contentChild = box(void 0);
  contentProps = box({});
  constructor(opts, listContext) {
    this.opts = opts;
    this.listContext = listContext;
  }
  #handleContentEntry = (side = "start") => {
    if (!this.contentNode) return;
    this.restoreContentTabOrder();
    const candidates = getTabbableCandidates(this.contentNode);
    if (candidates.length) focusFirst(side === "start" ? candidates : candidates.reverse());
  };
  #handleContentExit = () => {
    if (!this.contentNode) return;
    const candidates = getTabbableCandidates(this.contentNode);
    if (candidates.length) this.restoreContentTabOrder = removeFromTabOrder(candidates);
  };
  onEntryKeydown = this.#handleContentEntry;
  onFocusProxyEnter = this.#handleContentEntry;
  onRootContentClose = this.#handleContentExit;
  onContentFocusOutside = this.#handleContentExit;
  #props = derived(() => ({
    id: this.opts.id.current,
    [NAVIGATION_MENU_ITEM_ATTR]: ""
  }));
  get props() {
    return this.#props();
  }
  set props($$value) {
    return this.#props($$value);
  }
}
class NavigationMenuTriggerState {
  opts;
  focusProxyId = box(useId());
  focusProxyRef = box(null);
  context;
  itemContext;
  listContext;
  hasPointerMoveOpened = box(false);
  wasClickClose = false;
  #open = derived(() => this.itemContext.opts.value.current === this.context.opts.value.current);
  get open() {
    return this.#open();
  }
  set open($$value) {
    return this.#open($$value);
  }
  focusProxyMounted = false;
  constructor(opts, context) {
    this.opts = opts;
    this.hasPointerMoveOpened = boxAutoReset(false, 300);
    this.context = context.provider;
    this.itemContext = context.item;
    this.listContext = context.list;
    useRefById({
      ...opts,
      onRefChange: (node) => {
        this.itemContext.triggerNode = node;
      }
    });
    useRefById({
      id: this.focusProxyId,
      ref: this.focusProxyRef,
      onRefChange: (node) => {
        this.itemContext.focusProxyNode = node;
      },
      deps: () => this.focusProxyMounted
    });
    watch(() => this.opts.ref.current, () => {
      const node = this.opts.ref.current;
      if (!node) return;
      return this.listContext.registerTrigger(node);
    });
  }
  onpointerenter = (_) => {
    this.wasClickClose = false;
    this.itemContext.wasEscapeClose = false;
  };
  onpointermove = whenMouse(() => {
    if (this.opts.disabled.current || this.wasClickClose || this.itemContext.wasEscapeClose || this.hasPointerMoveOpened.current || !this.itemContext.opts.openOnHover.current) {
      return;
    }
    this.context.onTriggerEnter(this.itemContext.opts.value.current, this.itemContext);
    this.hasPointerMoveOpened.current = true;
  });
  onpointerleave = whenMouse(() => {
    if (this.opts.disabled.current || !this.itemContext.opts.openOnHover.current) return;
    this.context.onTriggerLeave();
    this.hasPointerMoveOpened.current = false;
  });
  onclick = () => {
    if (this.hasPointerMoveOpened.current) return;
    const shouldClose = this.open && (!this.itemContext.opts.openOnHover.current || this.context.opts.isRootMenu);
    if (shouldClose) {
      this.context.onItemSelect("", null);
    } else if (!this.open) {
      this.context.onItemSelect(this.itemContext.opts.value.current, this.itemContext);
    }
    this.wasClickClose = shouldClose;
  };
  onkeydown = (e) => {
    const verticalEntryKey = this.context.opts.dir.current === "rtl" ? ARROW_LEFT : ARROW_RIGHT;
    const entryKey = {
      horizontal: ARROW_DOWN,
      vertical: verticalEntryKey
    }[this.context.opts.orientation.current];
    if (this.open && e.key === entryKey) {
      this.itemContext.onEntryKeydown();
      e.preventDefault();
      return;
    }
    this.itemContext.listContext.rovingFocusGroup.handleKeydown(this.opts.ref.current, e);
  };
  focusProxyOnFocus = (e) => {
    const content = this.itemContext.contentNode;
    const prevFocusedElement = e.relatedTarget;
    const wasTriggerFocused = this.opts.ref.current && prevFocusedElement === this.opts.ref.current;
    const wasFocusFromContent = content?.contains(prevFocusedElement);
    if (wasTriggerFocused || !wasFocusFromContent) {
      this.itemContext.onFocusProxyEnter(wasTriggerFocused ? "start" : "end");
    }
  };
  #props = derived(() => ({
    id: this.opts.id.current,
    disabled: this.opts.disabled.current,
    "data-disabled": getDataDisabled(Boolean(this.opts.disabled.current)),
    "data-state": getDataOpenClosed(this.open),
    "data-value": this.itemContext.opts.value.current,
    "aria-expanded": getAriaExpanded(this.open),
    "aria-controls": this.itemContext.contentId,
    [NAVIGATION_MENU_TRIGGER_ATTR]: "",
    onpointermove: this.onpointermove,
    onpointerleave: this.onpointerleave,
    onpointerenter: this.onpointerenter,
    onclick: this.onclick,
    onkeydown: this.onkeydown
  }));
  get props() {
    return this.#props();
  }
  set props($$value) {
    return this.#props($$value);
  }
  #focusProxyProps = derived(() => ({
    id: this.focusProxyId.current,
    tabindex: 0,
    onfocus: this.focusProxyOnFocus
  }));
  get focusProxyProps() {
    return this.#focusProxyProps();
  }
  set focusProxyProps($$value) {
    return this.#focusProxyProps($$value);
  }
  #restructureSpanProps = derived(() => ({ "aria-owns": this.itemContext.contentId }));
  get restructureSpanProps() {
    return this.#restructureSpanProps();
  }
  set restructureSpanProps($$value) {
    return this.#restructureSpanProps($$value);
  }
}
const LINK_SELECT_EVENT = new CustomEventDispatcher("bitsLinkSelect", { bubbles: true, cancelable: true });
const ROOT_CONTENT_DISMISS_EVENT = new CustomEventDispatcher("bitsRootContentDismiss", { cancelable: true, bubbles: true });
class NavigationMenuLinkState {
  opts;
  context;
  isFocused = false;
  constructor(opts, context) {
    this.opts = opts;
    this.context = context;
    useRefById(opts);
  }
  onclick = (e) => {
    const currTarget = e.currentTarget;
    LINK_SELECT_EVENT.listen(currTarget, (e2) => this.opts.onSelect.current(e2), { once: true });
    const linkSelectEvent = LINK_SELECT_EVENT.dispatch(currTarget);
    if (!linkSelectEvent.defaultPrevented && !e.metaKey) {
      ROOT_CONTENT_DISMISS_EVENT.dispatch(currTarget);
    }
  };
  onkeydown = (e) => {
    if (this.context.item.contentNode) return;
    this.context.item.listContext.rovingFocusGroup.handleKeydown(this.opts.ref.current, e);
  };
  onfocus = (_) => {
    this.isFocused = true;
  };
  onblur = (_) => {
    this.isFocused = false;
  };
  #handlePointerDismiss = () => {
    const currentlyOpenValue = this.context.provider.opts.value.current;
    const isInsideOpenSubmenu = this.context.item.opts.value.current === currentlyOpenValue;
    const activeItem = this.context.item.listContext.context.activeItem;
    if (activeItem && !activeItem.opts.openOnHover.current) return;
    if (currentlyOpenValue && !isInsideOpenSubmenu) {
      this.context.provider.onItemDismiss();
    }
  };
  onpointerenter = () => {
    this.#handlePointerDismiss();
  };
  onpointermove = whenMouse(() => {
    this.#handlePointerDismiss();
  });
  #props = derived(() => ({
    id: this.opts.id.current,
    "data-active": this.opts.active.current ? "" : void 0,
    "aria-current": this.opts.active.current ? "page" : void 0,
    "data-focused": this.isFocused ? "" : void 0,
    onclick: this.onclick,
    onkeydown: this.onkeydown,
    onfocus: this.onfocus,
    onblur: this.onblur,
    onpointerenter: this.onpointerenter,
    onpointermove: this.onpointermove,
    [NAVIGATION_MENU_LINK_ATTR]: ""
  }));
  get props() {
    return this.#props();
  }
  set props($$value) {
    return this.#props($$value);
  }
}
class NavigationMenuContentState {
  opts;
  context;
  itemContext;
  listContext;
  #open = derived(() => this.itemContext.opts.value.current === this.context.opts.value.current);
  get open() {
    return this.#open();
  }
  set open($$value) {
    return this.#open($$value);
  }
  mounted = false;
  #value = derived(() => this.itemContext.opts.value.current);
  get value() {
    return this.#value();
  }
  set value($$value) {
    return this.#value($$value);
  }
  #isLastActiveValue = derived(() => {
    if (this.context.viewportRef.current) {
      if (!this.context.opts.value.current && this.context.opts.previousValue.current) {
        return this.context.opts.previousValue.current === this.itemContext.opts.value.current;
      }
    }
    return false;
  });
  get isLastActiveValue() {
    return this.#isLastActiveValue();
  }
  set isLastActiveValue($$value) {
    return this.#isLastActiveValue($$value);
  }
  constructor(opts, context) {
    this.opts = opts;
    this.context = context.provider;
    this.itemContext = context.item;
    this.listContext = context.list;
    useRefById({
      ...opts,
      onRefChange: (node) => {
        this.itemContext.contentNode = node;
      },
      deps: () => this.mounted
    });
  }
  onpointerenter = (_) => {
    this.context.onContentEnter();
  };
  onpointerleave = whenMouse(() => {
    if (!this.itemContext.opts.openOnHover.current) return;
    this.context.onContentLeave();
  });
  #props = derived(() => ({
    id: this.opts.id.current,
    onpointerenter: this.onpointerenter,
    onpointerleave: this.onpointerleave
  }));
  get props() {
    return this.#props();
  }
  set props($$value) {
    return this.#props($$value);
  }
}
class NavigationMenuContentImplState {
  opts;
  itemContext;
  context;
  listContext;
  prevMotionAttribute = null;
  #motionAttribute = derived(() => {
    const items = this.listContext.listTriggers;
    const values = items.map((item) => item.getAttribute("data-value")).filter(Boolean);
    if (this.context.opts.dir.current === "rtl") values.reverse();
    const index = values.indexOf(this.context.opts.value.current);
    const prevIndex = values.indexOf(this.context.opts.previousValue.current);
    const isSelected = this.itemContext.opts.value.current === this.context.opts.value.current;
    const wasSelected = prevIndex === values.indexOf(this.itemContext.opts.value.current);
    if (!this.context.opts.value.current && !this.context.opts.previousValue.current) {
      run(() => this.prevMotionAttribute = null);
      return null;
    }
    if (!isSelected && !wasSelected) return run(() => this.prevMotionAttribute);
    const attribute = (() => {
      if (index !== prevIndex) {
        if (isSelected && prevIndex !== -1) return index > prevIndex ? "from-end" : "from-start";
        if (wasSelected && index !== -1) return index > prevIndex ? "to-start" : "to-end";
      }
      return null;
    })();
    run(() => this.prevMotionAttribute = attribute);
    return attribute;
  });
  get motionAttribute() {
    return this.#motionAttribute();
  }
  set motionAttribute($$value) {
    return this.#motionAttribute($$value);
  }
  constructor(opts, itemContext) {
    this.opts = opts;
    this.itemContext = itemContext;
    this.listContext = itemContext.listContext;
    this.context = itemContext.listContext.context;
    useRefById({
      ...opts,
      deps: () => this.context.opts.value.current
    });
    watch(
      [
        () => this.itemContext.opts.value.current,
        () => this.itemContext.triggerNode,
        () => this.opts.ref.current
      ],
      () => {
        const content = this.opts.ref.current;
        if (!(content && this.context.opts.isRootMenu)) return;
        const handleClose = () => {
          this.context.onItemDismiss();
          this.itemContext.onRootContentClose();
          if (content.contains(document.activeElement)) {
            this.itemContext.triggerNode?.focus();
          }
        };
        const removeListener = ROOT_CONTENT_DISMISS_EVENT.listen(content, handleClose);
        return () => {
          removeListener();
        };
      }
    );
  }
  onFocusOutside = (e) => {
    this.itemContext.onContentFocusOutside();
    const target = e.target;
    if (this.context.opts.rootNavigationMenuRef.current?.contains(target)) {
      e.preventDefault();
      return;
    }
    this.context.onItemDismiss();
  };
  onInteractOutside = (e) => {
    const target = e.target;
    const isTrigger = this.listContext.listTriggers.some((trigger) => trigger.contains(target));
    const isRootViewport = this.context.opts.isRootMenu && this.context.viewportRef.current?.contains(target);
    if (!this.context.opts.isRootMenu && !isTrigger) {
      this.context.onItemDismiss();
      return;
    }
    if (isTrigger || isRootViewport) {
      e.preventDefault();
      return;
    }
    if (!this.itemContext.opts.openOnHover.current) {
      this.context.onItemSelect("", null);
    }
  };
  onkeydown = (e) => {
    const target = e.target;
    if (!isElement(target)) return;
    if (target.closest(`[${NAVIGATION_MENU_ATTR}]`) !== this.context.opts.rootNavigationMenuRef.current) return;
    const isMetaKey = e.altKey || e.ctrlKey || e.metaKey;
    const isTabKey = e.key === TAB && !isMetaKey;
    const candidates = getTabbableCandidates(e.currentTarget);
    if (isTabKey) {
      const focusedElement = document.activeElement;
      const index = candidates.findIndex((candidate) => candidate === focusedElement);
      const isMovingBackwards = e.shiftKey;
      const nextCandidates = isMovingBackwards ? candidates.slice(0, index).reverse() : candidates.slice(index + 1, candidates.length);
      if (focusFirst(nextCandidates)) {
        e.preventDefault();
        return;
      } else {
        handleProxyFocus(this.itemContext.focusProxyNode);
        return;
      }
    }
    let activeEl = document.activeElement;
    if (this.itemContext.contentNode) {
      const focusedNode = this.itemContext.contentNode.querySelector("[data-focused]");
      if (focusedNode) {
        activeEl = focusedNode;
      }
    }
    if (activeEl === this.itemContext.triggerNode) return;
    const newSelectedElement = useArrowNavigation(e, activeEl, void 0, {
      itemsArray: candidates,
      loop: false
    });
    newSelectedElement?.focus();
  };
  onEscapeKeydown = (_) => {
    this.context.onItemDismiss();
    this.itemContext.triggerNode?.focus();
    this.itemContext.wasEscapeClose = true;
  };
  #props = derived(() => ({
    id: this.opts.id.current,
    "aria-labelledby": this.itemContext.triggerId,
    "data-motion": this.motionAttribute ?? void 0,
    "data-orientation": getDataOrientation(this.context.opts.orientation.current),
    "data-state": getDataOpenClosed(this.context.opts.value.current === this.itemContext.opts.value.current),
    onkeydown: this.onkeydown,
    [NAVIGATION_MENU_CONTENT_ATTR]: ""
  }));
  get props() {
    return this.#props();
  }
  set props($$value) {
    return this.#props($$value);
  }
}
class NavigationMenuViewportState {
  opts;
  context;
  #open = derived(() => !!this.context.opts.value.current);
  get open() {
    return this.#open();
  }
  set open($$value) {
    return this.#open($$value);
  }
  size = null;
  contentNode = null;
  #viewportWidth = derived(() => this.size ? `${this.size.width}px` : void 0);
  get viewportWidth() {
    return this.#viewportWidth();
  }
  set viewportWidth($$value) {
    return this.#viewportWidth($$value);
  }
  #viewportHeight = derived(() => this.size ? `${this.size.height}px` : void 0);
  get viewportHeight() {
    return this.#viewportHeight();
  }
  set viewportHeight($$value) {
    return this.#viewportHeight($$value);
  }
  #activeContentValue = derived(() => this.context.opts.value.current);
  get activeContentValue() {
    return this.#activeContentValue();
  }
  set activeContentValue($$value) {
    return this.#activeContentValue($$value);
  }
  mounted = false;
  constructor(opts, context) {
    this.opts = opts;
    this.context = context;
    useRefById({
      ...opts,
      onRefChange: (node) => {
        this.context.viewportRef.current = node;
      },
      deps: () => this.open
    });
    watch(
      [
        () => this.activeContentValue,
        () => this.open
      ],
      () => {
        afterTick(() => {
          const currNode = this.context.viewportRef.current;
          if (!currNode) return;
          const el = currNode.querySelector("[data-state=open]")?.children?.[0] ?? null;
          this.contentNode = el;
        });
      }
    );
    watch(() => this.mounted, () => {
      if (!this.mounted && this.size) {
        this.size = null;
      }
    });
  }
  #props = derived(() => ({
    id: this.opts.id.current,
    "data-state": getDataOpenClosed(this.open),
    "data-orientation": getDataOrientation(this.context.opts.orientation.current),
    style: {
      pointerEvents: !this.open && this.context.opts.isRootMenu ? "none" : void 0,
      "--bits-navigation-menu-viewport-width": this.viewportWidth,
      "--bits-navigation-menu-viewport-height": this.viewportHeight
    },
    [NAVIGATION_MENU_VIEWPORT_ATTR]: "",
    onpointerenter: this.context.onContentEnter,
    onpointerleave: this.context.onContentLeave
  }));
  get props() {
    return this.#props();
  }
  set props($$value) {
    return this.#props($$value);
  }
}
const NavigationMenuProviderContext = new Context("NavigationMenu.Root");
const NavigationMenuItemContext = new Context("NavigationMenu.Item");
const NavigationMenuListContext = new Context("NavigationMenu.List");
const NavigationMenuContentContext = new Context("NavigationMenu.Content");
const NavigationMenuSubContext = new Context("NavigationMenu.Sub");
function useNavigationMenuRoot(props) {
  return new NavigationMenuRootState(props);
}
function useNavigationMenuProvider(props) {
  return NavigationMenuProviderContext.set(new NavigationMenuProviderState(props));
}
function useNavigationMenuList(props) {
  return NavigationMenuListContext.set(new NavigationMenuListState(props, NavigationMenuProviderContext.get()));
}
function useNavigationMenuItem(props) {
  return NavigationMenuItemContext.set(new NavigationMenuItemState(props, NavigationMenuListContext.get()));
}
function useNavigationMenuTrigger(props) {
  return new NavigationMenuTriggerState(props, {
    provider: NavigationMenuProviderContext.get(),
    item: NavigationMenuItemContext.get(),
    list: NavigationMenuListContext.get(),
    sub: NavigationMenuSubContext.getOr(null)
  });
}
function useNavigationMenuContent(props) {
  return NavigationMenuContentContext.set(new NavigationMenuContentState(props, {
    provider: NavigationMenuProviderContext.get(),
    item: NavigationMenuItemContext.get(),
    list: NavigationMenuListContext.get()
  }));
}
function useNavigationMenuLink(props) {
  return new NavigationMenuLinkState(props, {
    provider: NavigationMenuProviderContext.get(),
    item: NavigationMenuItemContext.get()
  });
}
function useNavigationMenuContentImpl(props, itemState) {
  return new NavigationMenuContentImplState(props, itemState ?? NavigationMenuItemContext.get());
}
function useNavigationMenuViewport(props) {
  return new NavigationMenuViewportState(props, NavigationMenuProviderContext.get());
}
function focusFirst(candidates) {
  const previouslyFocusedElement = document.activeElement;
  return candidates.some((candidate) => {
    if (candidate === previouslyFocusedElement) return true;
    candidate.focus();
    return document.activeElement !== previouslyFocusedElement;
  });
}
function removeFromTabOrder(candidates) {
  candidates.forEach((candidate) => {
    candidate.dataset.tabindex = candidate.getAttribute("tabindex") || "";
    candidate.setAttribute("tabindex", "-1");
  });
  return () => {
    candidates.forEach((candidate) => {
      const prevTabIndex = candidate.dataset.tabindex;
      candidate.setAttribute("tabindex", prevTabIndex);
    });
  };
}
function whenMouse(handler) {
  return (e) => e.pointerType === "mouse" ? handler(e) : void 0;
}
function handleProxyFocus(guard, focusOptions) {
  if (!guard) return;
  const ariaHidden = guard.getAttribute("aria-hidden");
  guard.removeAttribute("aria-hidden");
  guard.focus(focusOptions);
  afterSleep(0, () => {
    if (ariaHidden === null) {
      guard.setAttribute("aria-hidden", "");
    } else {
      guard.setAttribute("aria-hidden", ariaHidden);
    }
  });
}
function Navigation_menu$1($$payload, $$props) {
  push();
  let {
    child,
    children,
    id = useId(),
    ref = null,
    value = "",
    onValueChange = noop,
    delayDuration = 200,
    skipDelayDuration = 300,
    dir = "ltr",
    orientation = "horizontal",
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const rootState = useNavigationMenuRoot({
    id: box.with(() => id),
    value: box.with(() => value, (v) => {
      value = v;
      onValueChange(v);
    }),
    delayDuration: box.with(() => delayDuration),
    skipDelayDuration: box.with(() => skipDelayDuration),
    dir: box.with(() => dir),
    orientation: box.with(() => orientation),
    ref: box.with(() => ref, (v) => ref = v)
  });
  const mergedProps = mergeProps({ "aria-label": "main" }, restProps, rootState.props);
  if (child) {
    $$payload.out += "<!--[-->";
    child($$payload, { props: mergedProps });
    $$payload.out += `<!---->`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<nav${spread_attributes({ ...mergedProps }, null)}>`;
    children?.($$payload);
    $$payload.out += `<!----></nav>`;
  }
  $$payload.out += `<!--]-->`;
  bind_props($$props, { ref, value });
  pop();
}
function Navigation_menu_content_impl($$payload, $$props) {
  push();
  let {
    ref = null,
    id = useId(),
    child: childProp,
    children: childrenProp,
    onInteractOutside = noop,
    onFocusOutside = noop,
    onEscapeKeydown = noop,
    escapeKeydownBehavior = "close",
    interactOutsideBehavior = "close",
    itemState,
    onRefChange,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const contentImplState = useNavigationMenuContentImpl(
    {
      id: box.with(() => id),
      ref: box.with(() => ref, (v) => {
        ref = v;
        run(() => onRefChange?.(v));
      })
    },
    itemState
  );
  if (itemState) {
    NavigationMenuItemContext.set(itemState);
  }
  const mergedProps = mergeProps(restProps, contentImplState.props);
  {
    let children = function($$payload2, { props: dismissibleProps }) {
      Escape_layer($$payload2, {
        enabled: true,
        onEscapeKeydown: (e) => {
          onEscapeKeydown(e);
          if (e.defaultPrevented) return;
          contentImplState.onEscapeKeydown(e);
        },
        escapeKeydownBehavior,
        children: ($$payload3) => {
          const finalProps = mergeProps(mergedProps, dismissibleProps);
          if (childProp) {
            $$payload3.out += "<!--[-->";
            childProp($$payload3, { props: finalProps });
            $$payload3.out += `<!---->`;
          } else {
            $$payload3.out += "<!--[!-->";
            $$payload3.out += `<div${spread_attributes({ ...finalProps }, null)}>`;
            childrenProp?.($$payload3);
            $$payload3.out += `<!----></div>`;
          }
          $$payload3.out += `<!--]-->`;
        }
      });
    };
    Dismissible_layer($$payload, {
      id,
      enabled: true,
      onInteractOutside: (e) => {
        onInteractOutside(e);
        if (e.defaultPrevented) return;
        contentImplState.onInteractOutside(e);
      },
      onFocusOutside: (e) => {
        onFocusOutside(e);
        if (e.defaultPrevented) return;
        contentImplState.onFocusOutside(e);
      },
      interactOutsideBehavior,
      children
    });
  }
  bind_props($$props, { ref });
  pop();
}
function Navigation_menu_content$1($$payload, $$props) {
  push();
  let {
    ref = null,
    id = useId(),
    children,
    child,
    forceMount = false,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const contentState = useNavigationMenuContent({
    id: box.with(() => id),
    ref: box.with(() => ref, (v) => ref = v)
  });
  const mergedProps = mergeProps(restProps, contentState.props);
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    Portal($$payload2, {
      to: contentState.context.viewportRef.current || void 0,
      disabled: !contentState.context.viewportRef.current,
      children: ($$payload3) => {
        {
          let presence = function($$payload4) {
            Navigation_menu_content_impl($$payload4, spread_props([mergedProps, { children, child }]));
            $$payload4.out += `<!----> `;
            Mounted($$payload4, {
              get mounted() {
                return contentState.mounted;
              },
              set mounted($$value) {
                contentState.mounted = $$value;
                $$settled = false;
              }
            });
            $$payload4.out += `<!---->`;
          };
          Presence_layer($$payload3, {
            id,
            present: forceMount || contentState.open || contentState.isLastActiveValue,
            presence
          });
        }
      }
    });
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function Navigation_menu_item$1($$payload, $$props) {
  push();
  let {
    id = useId(),
    value = useId(),
    ref = null,
    child,
    children,
    openOnHover = true,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const itemState = useNavigationMenuItem({
    id: box.with(() => id),
    ref: box.with(() => ref, (v) => ref = v),
    value: box.with(() => value),
    openOnHover: box.with(() => openOnHover)
  });
  const mergedProps = mergeProps(restProps, itemState.props);
  if (child) {
    $$payload.out += "<!--[-->";
    child($$payload, { props: mergedProps });
    $$payload.out += `<!---->`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<li${spread_attributes({ ...mergedProps }, null)}>`;
    children?.($$payload);
    $$payload.out += `<!----></li>`;
  }
  $$payload.out += `<!--]-->`;
  bind_props($$props, { ref });
  pop();
}
function Navigation_menu_link$1($$payload, $$props) {
  push();
  let {
    id = useId(),
    ref = null,
    child,
    children,
    active = false,
    onSelect = noop,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const linkState = useNavigationMenuLink({
    id: box.with(() => id),
    ref: box.with(() => ref, (v) => ref = v),
    active: box.with(() => active),
    onSelect: box.with(() => onSelect)
  });
  const mergedProps = mergeProps(restProps, linkState.props);
  if (child) {
    $$payload.out += "<!--[-->";
    child($$payload, { props: mergedProps });
    $$payload.out += `<!---->`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<a${spread_attributes({ ...mergedProps }, null)}>`;
    children?.($$payload);
    $$payload.out += `<!----></a>`;
  }
  $$payload.out += `<!--]-->`;
  bind_props($$props, { ref });
  pop();
}
function Navigation_menu_list$1($$payload, $$props) {
  push();
  let {
    id = useId(),
    children,
    child,
    ref = null,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const listState = useNavigationMenuList({
    id: box.with(() => id),
    ref: box.with(() => ref, (v) => ref = v)
  });
  const mergedProps = mergeProps(restProps, listState.props);
  const wrapperProps = mergeProps(listState.wrapperProps);
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    if (child) {
      $$payload2.out += "<!--[-->";
      child($$payload2, { props: mergedProps, wrapperProps });
      $$payload2.out += `<!----> `;
      Mounted($$payload2, {
        get mounted() {
          return listState.wrapperMounted;
        },
        set mounted($$value) {
          listState.wrapperMounted = $$value;
          $$settled = false;
        }
      });
      $$payload2.out += `<!---->`;
    } else {
      $$payload2.out += "<!--[!-->";
      $$payload2.out += `<div${spread_attributes({ ...wrapperProps }, null)}><ul${spread_attributes({ ...mergedProps }, null)}>`;
      children?.($$payload2);
      $$payload2.out += `<!----></ul></div> `;
      Mounted($$payload2, {
        get mounted() {
          return listState.wrapperMounted;
        },
        set mounted($$value) {
          listState.wrapperMounted = $$value;
          $$settled = false;
        }
      });
      $$payload2.out += `<!---->`;
    }
    $$payload2.out += `<!--]-->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function Visually_hidden($$payload, $$props) {
  push();
  let {
    children,
    child,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const style = {
    position: "absolute",
    border: 0,
    width: "1px",
    display: "inline-block",
    height: "1px",
    padding: 0,
    margin: "-1px",
    overflow: "hidden",
    clip: "rect(0 0 0 0)",
    whiteSpace: "nowrap",
    wordWrap: "normal"
  };
  const mergedProps = mergeProps(restProps, { style });
  if (child) {
    $$payload.out += "<!--[-->";
    child($$payload, { props: mergedProps });
    $$payload.out += `<!---->`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<span${spread_attributes({ ...mergedProps }, null)}>`;
    children?.($$payload);
    $$payload.out += `<!----></span>`;
  }
  $$payload.out += `<!--]-->`;
  pop();
}
function Navigation_menu_trigger$1($$payload, $$props) {
  push();
  let {
    id = useId(),
    disabled = false,
    children,
    child,
    ref = null,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const triggerState = useNavigationMenuTrigger({
    id: box.with(() => id),
    disabled: box.with(() => disabled ?? false),
    ref: box.with(() => ref, (v) => ref = v)
  });
  const mergedProps = mergeProps(restProps, triggerState.props);
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    if (child) {
      $$payload2.out += "<!--[-->";
      child($$payload2, { props: mergedProps });
      $$payload2.out += `<!---->`;
    } else {
      $$payload2.out += "<!--[!-->";
      $$payload2.out += `<button${spread_attributes({ ...mergedProps }, null)}>`;
      children?.($$payload2);
      $$payload2.out += `<!----></button>`;
    }
    $$payload2.out += `<!--]--> `;
    if (triggerState.open) {
      $$payload2.out += "<!--[-->";
      Visually_hidden($$payload2, spread_props([triggerState.focusProxyProps]));
      $$payload2.out += `<!----> `;
      Mounted($$payload2, {
        get mounted() {
          return triggerState.focusProxyMounted;
        },
        set mounted($$value) {
          triggerState.focusProxyMounted = $$value;
          $$settled = false;
        }
      });
      $$payload2.out += `<!----> `;
      if (triggerState.context.viewportRef.current) {
        $$payload2.out += "<!--[-->";
        $$payload2.out += `<span${attr("aria-owns", triggerState.itemContext.contentId ?? void 0)}></span>`;
      } else {
        $$payload2.out += "<!--[!-->";
      }
      $$payload2.out += `<!--]-->`;
    } else {
      $$payload2.out += "<!--[!-->";
    }
    $$payload2.out += `<!--]-->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function Navigation_menu_viewport$1($$payload, $$props) {
  push();
  let {
    id = useId(),
    ref = null,
    forceMount = false,
    child,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  const viewportState = useNavigationMenuViewport({
    id: box.with(() => id),
    ref: box.with(() => ref, (v) => ref = v)
  });
  const mergedProps = mergeProps(restProps, viewportState.props);
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    {
      let presence = function($$payload3) {
        if (child) {
          $$payload3.out += "<!--[-->";
          child($$payload3, { props: mergedProps });
          $$payload3.out += `<!---->`;
        } else {
          $$payload3.out += "<!--[!-->";
          $$payload3.out += `<div${spread_attributes({ ...mergedProps }, null)}>`;
          children?.($$payload3);
          $$payload3.out += `<!----></div>`;
        }
        $$payload3.out += `<!--]--> `;
        Mounted($$payload3, {
          get mounted() {
            return viewportState.mounted;
          },
          set mounted($$value) {
            viewportState.mounted = $$value;
            $$settled = false;
          }
        });
        $$payload3.out += `<!---->`;
      };
      Presence_layer($$payload2, {
        id,
        present: forceMount || viewportState.open,
        presence
      });
    }
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function Navigation_menu_viewport($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<div${attr_class(clsx(cn("absolute left-0 top-full isolate z-50 flex justify-center")))}><!---->`;
    Navigation_menu_viewport$1($$payload2, spread_props([
      {
        "data-slot": "navigation-menu-viewport",
        class: cn("origin-top-center bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 relative mt-1.5 h-[var(--bits-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border shadow md:w-[var(--bits-navigation-menu-viewport-width)]", className)
      },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        }
      }
    ]));
    $$payload2.out += `<!----></div>`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function Navigation_menu($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    viewport = false,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<!---->`;
    Navigation_menu$1($$payload2, spread_props([
      {
        "data-slot": "navigation-menu",
        "data-viewport": viewport,
        class: cn("group/navigation-menu relative flex max-w-max flex-1 items-center justify-center", className)
      },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        },
        children: ($$payload3) => {
          children?.($$payload3);
          $$payload3.out += `<!----> `;
          if (viewport) {
            $$payload3.out += "<!--[-->";
            Navigation_menu_viewport($$payload3, {});
          } else {
            $$payload3.out += "<!--[!-->";
          }
          $$payload3.out += `<!--]-->`;
        },
        $$slots: { default: true }
      }
    ]));
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function Navigation_menu_content($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<!---->`;
    Navigation_menu_content$1($$payload2, spread_props([
      {
        "data-slot": "navigation-menu-content",
        class: cn("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=open]:fade-in-0 data-[state=closed]:fade-out-0 border-border absolute top-full z-50 mt-1.5 w-auto overflow-hidden rounded-md border shadow-sm", className)
      },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        }
      }
    ]));
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function Navigation_menu_item($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<!---->`;
    Navigation_menu_item$1($$payload2, spread_props([
      {
        "data-slot": "navigation-menu-item",
        class: cn("relative", className)
      },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        }
      }
    ]));
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function Navigation_menu_link($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<!---->`;
    Navigation_menu_link$1($$payload2, spread_props([
      {
        "data-slot": "navigation-menu-link",
        class: cn("data-[active=true]:focus:bg-accent data-[active=true]:hover:bg-accent data-[active=true]:bg-accent/50 data-[active=true]:text-accent-foreground hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus-visible:ring-ring/50 [&_svg:not([class*='text-'])]:text-muted-foreground flex flex-col gap-1 rounded-sm p-2 text-sm outline-none transition-all focus-visible:outline-1 focus-visible:ring-[3px] [&_svg:not([class*='size-'])]:size-4", className)
      },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        }
      }
    ]));
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function Navigation_menu_list($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<!---->`;
    Navigation_menu_list$1($$payload2, spread_props([
      {
        "data-slot": "navigation-menu-list",
        class: cn("group flex flex-1 list-none items-center justify-center gap-1", className)
      },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        }
      }
    ]));
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
const navigationMenuTriggerStyle = ce({
  base: "bg-background hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground data-[state=open]:hover:bg-accent data-[state=open]:text-accent-foreground data-[state=open]:focus:bg-accent data-[state=open]:bg-accent/50 focus-visible:ring-ring/50 group inline-flex h-9 w-max items-center justify-center rounded-md px-4 py-2 text-sm font-medium outline-none transition-[color,box-shadow] focus-visible:outline-1 focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50"
});
function Navigation_menu_trigger($$payload, $$props) {
  push();
  let {
    ref = null,
    class: className,
    children,
    $$slots,
    $$events,
    ...restProps
  } = $$props;
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    $$payload2.out += `<!---->`;
    Navigation_menu_trigger$1($$payload2, spread_props([
      {
        "data-slot": "navigation-menu-trigger",
        class: cn(navigationMenuTriggerStyle(), "group", className)
      },
      restProps,
      {
        get ref() {
          return ref;
        },
        set ref($$value) {
          ref = $$value;
          $$settled = false;
        },
        children: ($$payload3) => {
          children?.($$payload3);
          $$payload3.out += `<!----> `;
          Chevron_down($$payload3, {
            class: "relative ml-1 size-3 transition duration-300 group-data-[state=open]:rotate-180",
            "aria-hidden": "true"
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      }
    ]));
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { ref });
  pop();
}
function Header($$payload, $$props) {
  push();
  let topCollections = [];
  const productLinks = [
    {
      title: "Auto Apply",
      href: "/auto-apply",
      description: "Automatic application processing",
      icon: Zap
    },
    {
      title: "Job Tracker",
      href: "/job-tracker",
      description: "Track your applications and interviews",
      icon: Target
    },
    {
      title: "Resume Builder",
      href: "/resume-builder",
      description: "Resume builder with AI/ATS assistance",
      icon: File_text
    },
    {
      title: "AI Co-Pilot",
      href: "/co-pilot",
      description: "Browser extension for job search",
      icon: Bot
    },
    {
      title: "Matches",
      href: "/matches",
      description: "Personalized automated job matches",
      icon: Trending_up
    }
  ];
  const newsItems = [
    {
      title: "Resume is now compatible with Figma Sites",
      date: "May 21, 2025",
      description: "Build better resumes with our new Figma integration",
      image: "/news/figma-integration.jpg"
    }
  ];
  function isActive(href, exact = false) {
    return false;
  }
  function isCollectionActive(collectionSlug) {
    return false;
  }
  function handleLaunchClick() {
    setTimeout(
      () => {
        goto();
      },
      100
    );
  }
  $$payload.out += `<div${attr_class(`top-4.5 fixed left-10 z-50 flex transform items-center transition-transform duration-500 ease-in-out ${stringify("translate-y-9")}`)}><button${attr_class(`rounded-sm bg-gradient-to-r from-orange-500 to-purple-600 p-0.5 ${stringify("")}`)} aria-label="Scroll to top">`;
  Logo($$payload, {
    fill: "white",
    stroke: "black",
    class: "h-6 w-6"
  });
  $$payload.out += `<!----></button></div> <div${attr_class(`z-100 fixed right-10 top-3 transform transition-transform duration-500 ease-in-out ${stringify("translate-y-9")}`)}>`;
  Button($$payload, {
    variant: "default",
    size: "lg",
    onclick: handleLaunchClick,
    class: " bg-purple-500 hover:bg-purple-600",
    children: ($$payload2) => {
      $$payload2.out += `<!---->Launch `;
      Arrow_right($$payload2, { class: "h-4 w-4" });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div> <div class="bg-primary text-secondary font-lighter w-full p-2 text-center text-xs">We're currently in private beta. Apply for early access today!</div> <header class="relative z-50 mt-1"><div class="mx-auto px-4 sm:px-6 lg:px-8"><div class="flex h-16 items-center justify-between"><a href="/" class="absolute ml-11 text-xl font-bold text-gray-900">Hirli</a> <div></div> <!---->`;
  Navigation_menu($$payload, {
    children: ($$payload2) => {
      $$payload2.out += `<!---->`;
      Navigation_menu_list($$payload2, {
        class: "gap-1",
        children: ($$payload3) => {
          $$payload3.out += `<!---->`;
          Navigation_menu_item($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->`;
              Navigation_menu_trigger($$payload4, {
                class: "font-roboto text-foreground/80 bg-transparent text-[15px]",
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Products`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> <!---->`;
              Navigation_menu_content($$payload4, {
                class: "-left-14",
                children: ($$payload5) => {
                  const each_array = ensure_array_like(productLinks);
                  const each_array_1 = ensure_array_like(newsItems);
                  $$payload5.out += `<div class="grid w-[600px] grid-cols-2 gap-4 p-3"><ul class="space-y-2 p-1"><!--[-->`;
                  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
                    let product = each_array[$$index];
                    const Icon2 = product.icon;
                    $$payload5.out += `<li><!---->`;
                    Navigation_menu_link($$payload5, {
                      href: product.href,
                      class: cn("flex flex-row items-center gap-3 rounded-md", isActive(product.href) ? "bg-accent text-accent-foreground" : ""),
                      children: ($$payload6) => {
                        $$payload6.out += `<div class="rounded-lg bg-gradient-to-r from-orange-500 to-purple-600 p-0.5"><div class="border-primary bg-secondary flex items-center justify-center rounded-md border-2 p-1.5"><!---->`;
                        Icon2($$payload6, { class: "text-primary h-10 w-10" });
                        $$payload6.out += `<!----></div></div> <div class="flex flex-1 flex-col align-middle"><div class="text-primary/90 text-sm font-medium">${escape_html(product.title)}</div> <p class="text-primary/90 text-[11px]">${escape_html(product.description)}</p></div>`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!----></li>`;
                  }
                  $$payload5.out += `<!--]--></ul> <div class="bg-secondary rounded-md p-4"><div class="mb-4 flex items-center justify-between"><h3 class="font-lighter text-primary text-sm">What's New</h3> <a href="/blog" class="text-primary/80 text-xs">View all</a></div> <ul class="space-y-2"><!--[-->`;
                  for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
                    let news = each_array_1[$$index_1];
                    $$payload5.out += `<li><!---->`;
                    Navigation_menu_link($$payload5, {
                      href: `/news/${stringify(news.title.toLowerCase().replace(/\s+/g, "-"))}`,
                      class: "flex flex-col gap-2 transition-colors",
                      children: ($$payload6) => {
                        $$payload6.out += `<div class="border-border h-20 w-full rounded-md border bg-gradient-to-br from-blue-100 to-purple-100"></div> <div class="flex flex-col gap-0"><div class="text-primary mb-1 text-[10px]">${escape_html(news.date)}</div> <div class="font-lighter text-primary text-sm">${escape_html(news.title)}</div></div>`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!----></li>`;
                  }
                  $$payload5.out += `<!--]--></ul></div></div>`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!---->`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <!---->`;
          Navigation_menu_item($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->`;
              Navigation_menu_trigger($$payload4, {
                class: "font-roboto text-foreground/80 bg-transparent text-[15px]",
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Sectors`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!----> <!---->`;
              Navigation_menu_content($$payload4, {
                class: "w-[200px]",
                children: ($$payload5) => {
                  const each_array_2 = ensure_array_like(topCollections);
                  $$payload5.out += `<ul class="grid gap-0 p-2"><!--[-->`;
                  for (let $$index_2 = 0, $$length = each_array_2.length; $$index_2 < $$length; $$index_2++) {
                    let collection = each_array_2[$$index_2];
                    $$payload5.out += `<li><!---->`;
                    Navigation_menu_link($$payload5, {
                      href: `/jobs?collection=${stringify(collection.slug)}`,
                      class: cn("hover:bg-accent hover:text-accent-foreground block select-none rounded-md p-2 leading-none no-underline outline-none transition-colors", isCollectionActive(collection.slug) ? "bg-accent text-accent-foreground" : ""),
                      children: ($$payload6) => {
                        $$payload6.out += `<!---->${escape_html(collection.name)}`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload5.out += `<!----></li>`;
                  }
                  $$payload5.out += `<!--]--> <li><!---->`;
                  Navigation_menu_link($$payload5, {
                    href: "/jobs",
                    class: cn("hover:bg-accent hover:text-accent-foreground block select-none rounded-md p-2 leading-none no-underline outline-none transition-colors", ""),
                    children: ($$payload6) => {
                      $$payload6.out += `<!---->Browse All Jobs`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload5.out += `<!----></li></ul>`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!---->`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <!---->`;
          Navigation_menu_item($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->`;
              Navigation_menu_link($$payload4, {
                href: "/pricing",
                class: cn(navigationMenuTriggerStyle(), "font-roboto text-foreground/80 bg-transparent text-[15px]"),
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Pricing`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!---->`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <!---->`;
          Navigation_menu_item($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->`;
              Navigation_menu_link($$payload4, {
                href: "/resources",
                class: cn(navigationMenuTriggerStyle(), "font-roboto text-foreground/80 bg-transparent text-[15px]"),
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Learn`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!---->`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <!---->`;
          Navigation_menu_item($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->`;
              Navigation_menu_link($$payload4, {
                href: "/recruiters",
                class: cn(navigationMenuTriggerStyle(), "font-roboto text-foreground/80 bg-transparent text-[15px]"),
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Recruiters`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!---->`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <!---->`;
          Navigation_menu_item($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->`;
              Navigation_menu_link($$payload4, {
                href: "/employers",
                class: cn(navigationMenuTriggerStyle(), "font-roboto text-foreground/80 bg-transparent text-[15px]"),
                children: ($$payload5) => {
                  $$payload5.out += `<!---->Employers`;
                },
                $$slots: { default: true }
              });
              $$payload4.out += `<!---->`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> <div></div></div></div></header>`;
  pop();
}
function Mode_watcher_lite($$payload, $$props) {
  push();
  let { themeColors: themeColors2 } = $$props;
  if (themeColors2) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<meta name="theme-color"${attr("content", themeColors2.dark)}/>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]-->`;
  pop();
}
function Mode_watcher_full($$payload, $$props) {
  push();
  let { trueNonce = "", initConfig, themeColors: themeColors2 } = $$props;
  head($$payload, ($$payload2) => {
    if (themeColors2) {
      $$payload2.out += "<!--[-->";
      $$payload2.out += `<meta name="theme-color"${attr("content", themeColors2.dark)}/>`;
    } else {
      $$payload2.out += "<!--[!-->";
    }
    $$payload2.out += `<!--]--> ${html(`<script${trueNonce ? ` nonce=${trueNonce}` : ""}>(` + setInitialMode.toString() + `)(` + JSON.stringify(initConfig) + `);<\/script>`)}`;
  });
  pop();
}
function Mode_watcher($$payload, $$props) {
  push();
  let {
    defaultMode = "system",
    themeColors: themeColorsProp,
    disableTransitions: disableTransitionsProp = true,
    darkClassNames: darkClassNamesProp = ["dark"],
    lightClassNames: lightClassNamesProp = [],
    defaultTheme = "",
    nonce = "",
    themeStorageKey: themeStorageKeyProp = "mode-watcher-theme",
    modeStorageKey: modeStorageKeyProp = "mode-watcher-mode",
    disableHeadScriptInjection = false
  } = $$props;
  modeStorageKey.current = modeStorageKeyProp;
  themeStorageKey.current = themeStorageKeyProp;
  darkClassNames.current = darkClassNamesProp;
  lightClassNames.current = lightClassNamesProp;
  disableTransitions.current = disableTransitionsProp;
  themeColors.current = themeColorsProp;
  const initConfig = defineConfig({
    defaultMode,
    themeColors: themeColorsProp,
    darkClassNames: darkClassNamesProp,
    lightClassNames: lightClassNamesProp,
    defaultTheme,
    modeStorageKey: modeStorageKeyProp,
    themeStorageKey: themeStorageKeyProp
  });
  const trueNonce = typeof window === "undefined" ? nonce : "";
  if (disableHeadScriptInjection) {
    $$payload.out += "<!--[-->";
    Mode_watcher_lite($$payload, { themeColors: themeColors.current });
  } else {
    $$payload.out += "<!--[!-->";
    Mode_watcher_full($$payload, {
      trueNonce,
      initConfig,
      themeColors: themeColors.current
    });
  }
  $$payload.out += `<!--]-->`;
  pop();
}
function ThemeToggle($$payload, $$props) {
  push();
  const { variant = "default", size = "default" } = $$props;
  const currentTheme = derivedMode.current || "system";
  function handleThemeChange(theme) {
    setMode(theme);
  }
  Root($$payload, {
    children: ($$payload2) => {
      Dropdown_menu_trigger($$payload2, {
        children: ($$payload3) => {
          Button($$payload3, {
            variant,
            size,
            class: "gap-2",
            children: ($$payload4) => {
              if (currentTheme === "light") {
                $$payload4.out += "<!--[-->";
                Sun($$payload4, { class: "h-4 w-4" });
                $$payload4.out += `<!----> `;
                if (size !== "icon") {
                  $$payload4.out += "<!--[-->";
                  $$payload4.out += `<span>Light</span>`;
                } else {
                  $$payload4.out += "<!--[!-->";
                }
                $$payload4.out += `<!--]-->`;
              } else if (currentTheme === "dark") {
                $$payload4.out += "<!--[1-->";
                Moon($$payload4, { class: "h-4 w-4" });
                $$payload4.out += `<!----> `;
                if (size !== "icon") {
                  $$payload4.out += "<!--[-->";
                  $$payload4.out += `<span>Dark</span>`;
                } else {
                  $$payload4.out += "<!--[!-->";
                }
                $$payload4.out += `<!--]-->`;
              } else {
                $$payload4.out += "<!--[!-->";
                Laptop($$payload4, { class: "h-4 w-4" });
                $$payload4.out += `<!----> `;
                if (size !== "icon") {
                  $$payload4.out += "<!--[-->";
                  $$payload4.out += `<span>System</span>`;
                } else {
                  $$payload4.out += "<!--[!-->";
                }
                $$payload4.out += `<!--]-->`;
              }
              $$payload4.out += `<!--]--> <span class="sr-only">Toggle theme</span>`;
            },
            $$slots: { default: true }
          });
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> `;
      Dropdown_menu_content($$payload2, {
        align: "end",
        children: ($$payload3) => {
          Dropdown_menu_item($$payload3, {
            onclick: () => handleThemeChange("light"),
            children: ($$payload4) => {
              Sun($$payload4, { class: "mr-2 h-4 w-4" });
              $$payload4.out += `<!----> <span>Light</span>`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> `;
          Dropdown_menu_item($$payload3, {
            onclick: () => handleThemeChange("dark"),
            children: ($$payload4) => {
              Moon($$payload4, { class: "mr-2 h-4 w-4" });
              $$payload4.out += `<!----> <span>Dark</span>`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> `;
          Dropdown_menu_item($$payload3, {
            onclick: () => handleThemeChange("system"),
            children: ($$payload4) => {
              Laptop($$payload4, { class: "mr-2 h-4 w-4" });
              $$payload4.out += `<!----> <span>System</span>`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  pop();
}
function Footer($$payload, $$props) {
  push();
  let data = fallback($$props["data"], () => ({}), true);
  const currentYear = (/* @__PURE__ */ new Date()).getFullYear();
  let topCollections = [];
  const productLinks = [
    { href: "/auto-apply", label: "Auto Apply" },
    { href: "/job-tracker", label: "Job Tracker" },
    {
      href: "/resume-builder",
      label: "Resume Builder"
    },
    { href: "/co-pilot", label: "AI Co-Pilot" },
    { href: "/pricing", label: "Pricing" }
  ];
  const fallbackCategoryLinks = [
    { href: "/tech-jobs", label: "Tech Jobs" },
    { href: "/remote-jobs", label: "Remote Jobs" },
    { href: "/entry-level", label: "Entry Level" },
    { href: "/jobs", label: "Browse All Jobs" }
  ];
  const supportLinks = [
    { href: "/help", label: "Help Center" },
    {
      href: "/system-status",
      label: "System Status"
    },
    {
      href: "https://autoapply.featurebase.app/",
      label: "Submit Feedback"
    },
    {
      href: "https://autoapply.featurebase.app/roadmap",
      label: "Roadmap"
    },
    { href: "/contact", label: "Contact Us" }
  ];
  const resourceLinks = [
    { href: "/resources", label: "Free Tools" },
    {
      href: "/resources/resume-templates",
      label: "Resume Templates"
    },
    {
      href: "/resources/cover-letters",
      label: "Cover Letter Templates"
    },
    {
      href: "/resources/ats-optimization/checker",
      label: "ATS Resume Checker"
    },
    {
      href: "/resources/interview-prep/question-database",
      label: "Interview Questions"
    },
    {
      href: "/resources/salary-tools",
      label: "Salary Tools"
    }
  ];
  const companyLinks = [
    { href: "/about", label: "About" },
    { href: "/blog", label: "Blog" },
    { href: "/press", label: "Press & Media" }
  ];
  const socialLinks = [
    {
      href: "https://tiktok.com/hirli",
      label: "Tiktok"
    },
    {
      href: "https://linkedin.com/company/hirli",
      label: "LinkedIn"
    },
    { href: "https://x.com/hirliapp", label: "X" },
    {
      href: "https://instagram.com/hirliapp",
      label: "Instagram"
    }
  ];
  const legalLinks = [
    { href: "/legal", label: "Legal" },
    { href: "/legal/terms", label: "Terms" },
    {
      href: "/legal/privacy-policy",
      label: "Privacy"
    },
    {
      href: "/legal/cookie-policy",
      label: "Cookies"
    },
    {
      href: "/legal/accessibility",
      label: "Accessibility"
    },
    { href: "/sitemap.xml", label: "Sitemap" }
  ];
  const each_array = ensure_array_like(productLinks);
  const each_array_3 = ensure_array_like(supportLinks);
  const each_array_4 = ensure_array_like(resourceLinks);
  const each_array_5 = ensure_array_like(companyLinks);
  const each_array_6 = ensure_array_like(socialLinks);
  const each_array_7 = ensure_array_like(legalLinks);
  $$payload.out += `<footer class="border-border w-full border border-b border-l border-r"><div class="py-18 grid w-full grid-cols-2 px-8"><div class="mb-12"><a href="/" class="inline-block" aria-label="Hirli Home">`;
  Logo($$payload, {
    fill: "white",
    stroke: "black",
    class: "h-10 w-10"
  });
  $$payload.out += `<!----></a> <h2 class="mt-4 text-2xl font-light">Automate your <br/> job search.</h2></div> <div class="grid grid-cols-2 gap-8 md:grid-cols-4"><div><h5 class="text-muted-foreground mb-4 text-xs font-medium uppercase tracking-wider">Products</h5> <ul class="space-y-1.5"><!--[-->`;
  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
    let link = each_array[$$index];
    $$payload.out += `<li><a${attr("href", link.href)} class="text-muted-foreground hover:text-muted-foreground/80 text-sm">${escape_html(link.label)}</a></li>`;
  }
  $$payload.out += `<!--]--></ul></div> <div><h5 class="text-muted-foreground mb-4 text-xs font-medium uppercase tracking-wider">Categories</h5> <ul class="space-y-1.5">`;
  if (topCollections.length > 0) {
    $$payload.out += "<!--[-->";
    const each_array_1 = ensure_array_like(topCollections);
    $$payload.out += `<!--[-->`;
    for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
      let collection = each_array_1[$$index_1];
      $$payload.out += `<li><a${attr("href", `/jobs?collection=${stringify(collection.slug)}`)} class="text-muted-foreground hover:text-muted-foreground/80 text-sm">${escape_html(collection.name)}</a></li>`;
    }
    $$payload.out += `<!--]-->`;
  } else {
    $$payload.out += "<!--[!-->";
    const each_array_2 = ensure_array_like(fallbackCategoryLinks.slice(0, 3));
    $$payload.out += `<!--[-->`;
    for (let $$index_2 = 0, $$length = each_array_2.length; $$index_2 < $$length; $$index_2++) {
      let link = each_array_2[$$index_2];
      $$payload.out += `<li><a${attr("href", link.href)} class="text-muted-foreground hover:text-muted-foreground/80 text-sm">${escape_html(link.label)}</a></li>`;
    }
    $$payload.out += `<!--]-->`;
  }
  $$payload.out += `<!--]--> <li><a href="/jobs" class="text-muted-foreground hover:text-muted-foreground/80 text-sm">Browse All Jobs</a></li></ul></div> <div><h5 class="text-muted-foreground mb-4 text-xs font-medium uppercase tracking-wider">Support</h5> <ul class="mb-6 space-y-1.5"><!--[-->`;
  for (let $$index_3 = 0, $$length = each_array_3.length; $$index_3 < $$length; $$index_3++) {
    let link = each_array_3[$$index_3];
    $$payload.out += `<li><a${attr("href", link.href)} class="text-muted-foreground hover:text-muted-foreground/80 text-sm">${escape_html(link.label)}</a></li>`;
  }
  $$payload.out += `<!--]--></ul> <h5 class="text-muted-foreground mb-4 text-xs font-medium uppercase tracking-wider">Resources</h5> <ul class="space-y-1.5"><!--[-->`;
  for (let $$index_4 = 0, $$length = each_array_4.length; $$index_4 < $$length; $$index_4++) {
    let link = each_array_4[$$index_4];
    $$payload.out += `<li><a${attr("href", link.href)} class="text-muted-foreground hover:text-muted-foreground/80 text-sm">${escape_html(link.label)}</a></li>`;
  }
  $$payload.out += `<!--]--></ul></div> <div class="space-y-8"><div><h5 class="text-muted-foreground mb-4 text-xs font-medium uppercase tracking-wider">Company</h5> <ul class="space-y-1.5"><!--[-->`;
  for (let $$index_5 = 0, $$length = each_array_5.length; $$index_5 < $$length; $$index_5++) {
    let link = each_array_5[$$index_5];
    $$payload.out += `<li><a${attr("href", link.href)} class="text-muted-foreground hover:text-muted-foreground/80 text-sm">${escape_html(link.label)}</a></li>`;
  }
  $$payload.out += `<!--]--></ul></div> <div class="space-y-8"><h5 class="text-muted-foreground mb-4 text-xs font-medium uppercase tracking-wider">Follow</h5> <ul class="space-y-1.5"><!--[-->`;
  for (let $$index_6 = 0, $$length = each_array_6.length; $$index_6 < $$length; $$index_6++) {
    let link = each_array_6[$$index_6];
    $$payload.out += `<li><a${attr("href", link.href)} class="text-muted-foreground hover:text-muted-foreground/80 text-sm"${attr("aria-label", link.label)}>${escape_html(link.label)}</a></li>`;
  }
  $$payload.out += `<!--]--></ul></div></div></div></div> <div class="mx-8 py-6"><div class="border-border flex w-full flex-col items-center justify-between border-t pt-6 md:flex-row"><div class="mb-4 md:mb-0">`;
  ThemeToggle($$payload, { variant: "outline", size: "icon" });
  $$payload.out += `<!----></div> <div class="text-muted-foreground flex flex-wrap justify-center gap-4 text-sm"><!--[-->`;
  for (let $$index_7 = 0, $$length = each_array_7.length; $$index_7 < $$length; $$index_7++) {
    let link = each_array_7[$$index_7];
    $$payload.out += `<a${attr("href", link.href)} class="hover:text-foreground">${escape_html(link.label)}</a>`;
  }
  $$payload.out += `<!--]--> <p class="ml-4">© ${escape_html(currentYear)} Hirli, Inc.</p></div></div></div></footer>`;
  bind_props($$props, { data });
  pop();
}
function Icon($$payload, $$props) {
  let type = fallback($$props["type"], "success");
  if (type === "success") {
    $$payload.out += "<!--[-->";
    $$payload.out += `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" height="20" width="20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd"></path></svg>`;
  } else if (type === "error") {
    $$payload.out += "<!--[1-->";
    $$payload.out += `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" height="20" width="20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path></svg>`;
  } else if (type === "info") {
    $$payload.out += "<!--[2-->";
    $$payload.out += `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" height="20" width="20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z" clip-rule="evenodd"></path></svg>`;
  } else if (type === "warning") {
    $$payload.out += "<!--[3-->";
    $$payload.out += `<svg viewBox="0 0 64 64" fill="currentColor" height="20" width="20" xmlns="http://www.w3.org/2000/svg"><path d="M32.427,7.987c2.183,0.124 4,1.165 5.096,3.281l17.936,36.208c1.739,3.66 -0.954,8.585 -5.373,8.656l-36.119,0c-4.022,-0.064 -7.322,-4.631 -5.352,-8.696l18.271,-36.207c0.342,-0.65 0.498,-0.838 0.793,-1.179c1.186,-1.375 2.483,-2.111 4.748,-2.063Zm-0.295,3.997c-0.687,0.034 -1.316,0.419 -1.659,1.017c-6.312,11.979 -12.397,24.081 -18.301,36.267c-0.546,1.225 0.391,2.797 1.762,2.863c12.06,0.195 24.125,0.195 36.185,0c1.325,-0.064 2.321,-1.584 1.769,-2.85c-5.793,-12.184 -11.765,-24.286 -17.966,-36.267c-0.366,-0.651 -0.903,-1.042 -1.79,-1.03Z"></path><path d="M33.631,40.581l-3.348,0l-0.368,-16.449l4.1,0l-0.384,16.449Zm-3.828,5.03c0,-0.609 0.197,-1.113 0.592,-1.514c0.396,-0.4 0.935,-0.601 1.618,-0.601c0.684,0 1.223,0.201 1.618,0.601c0.395,0.401 0.593,0.905 0.593,1.514c0,0.587 -0.193,1.078 -0.577,1.473c-0.385,0.395 -0.929,0.593 -1.634,0.593c-0.705,0 -1.249,-0.198 -1.634,-0.593c-0.384,-0.395 -0.576,-0.886 -0.576,-1.473Z"></path></svg>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]-->`;
  bind_props($$props, { type });
}
function Loader($$payload, $$props) {
  push();
  let visible = $$props["visible"];
  const bars = Array(12).fill(0);
  const each_array = ensure_array_like(bars);
  $$payload.out += `<div class="sonner-loading-wrapper"${attr("data-visible", visible)}><div class="sonner-spinner"><!--[-->`;
  for (let i = 0, $$length = each_array.length; i < $$length; i++) {
    each_array[i];
    $$payload.out += `<div class="sonner-loading-bar"></div>`;
  }
  $$payload.out += `<!--]--></div></div>`;
  bind_props($$props, { visible });
  pop();
}
function Toast($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  push();
  var $$store_subs;
  let isFront, isVisible, toastType, toastClass, toastDescriptionClass, heightIndex, coords, toastsHeightBefore, disabled, isPromiseLoadingOrInfiniteDuration;
  const TOAST_LIFETIME = 4e3;
  const GAP = 14;
  const TIME_BEFORE_UNMOUNT = 200;
  const defaultClasses = {
    toast: "",
    title: "",
    description: "",
    loader: "",
    closeButton: "",
    cancelButton: "",
    actionButton: "",
    action: "",
    warning: "",
    error: "",
    success: "",
    default: "",
    info: "",
    loading: ""
  };
  const {
    toasts,
    heights,
    removeHeight,
    remove
  } = toastState;
  let toast = $$props["toast"];
  let index = $$props["index"];
  let expanded = $$props["expanded"];
  let invert = $$props["invert"];
  let position = $$props["position"];
  let visibleToasts = $$props["visibleToasts"];
  let expandByDefault = $$props["expandByDefault"];
  let closeButton = $$props["closeButton"];
  let interacting = $$props["interacting"];
  let cancelButtonStyle = fallback($$props["cancelButtonStyle"], "");
  let actionButtonStyle = fallback($$props["actionButtonStyle"], "");
  let duration = fallback($$props["duration"], 4e3);
  let descriptionClass = fallback($$props["descriptionClass"], "");
  let classes = fallback($$props["classes"], () => ({}), true);
  let unstyled = fallback($$props["unstyled"], false);
  let mounted = false;
  let removed = false;
  let swiping = false;
  let swipeOut = false;
  let offsetBeforeRemove = 0;
  let initialHeight = 0;
  let offset = 0;
  let closeTimerStartTimeRef = 0;
  let lastCloseTimerStartTimeRef = 0;
  async function updateHeights() {
    {
      return;
    }
  }
  function deleteToast() {
    removed = true;
    offsetBeforeRemove = offset;
    removeHeight(toast.id);
    setTimeout(
      () => {
        remove(toast.id);
      },
      TIME_BEFORE_UNMOUNT
    );
  }
  let timeoutId;
  let remainingTime = toast.duration || duration || TOAST_LIFETIME;
  function pauseTimer() {
    if (lastCloseTimerStartTimeRef < closeTimerStartTimeRef) {
      const elapsedTime = (/* @__PURE__ */ new Date()).getTime() - closeTimerStartTimeRef;
      remainingTime = remainingTime - elapsedTime;
    }
    lastCloseTimerStartTimeRef = (/* @__PURE__ */ new Date()).getTime();
  }
  function startTimer() {
    closeTimerStartTimeRef = (/* @__PURE__ */ new Date()).getTime();
    timeoutId = setTimeout(
      () => {
        toast.onAutoClose?.(toast);
        deleteToast();
      },
      remainingTime
    );
  }
  let effect;
  classes = { ...defaultClasses, ...classes };
  isFront = index === 0;
  isVisible = index + 1 <= visibleToasts;
  toast.title;
  toast.description;
  toastType = toast.type;
  toastClass = toast.class || "";
  toastDescriptionClass = toast.descriptionClass || "";
  heightIndex = store_get($$store_subs ??= {}, "$heights", heights).findIndex((height) => height.toastId === toast.id) || 0;
  coords = position.split("-");
  toastsHeightBefore = store_get($$store_subs ??= {}, "$heights", heights).reduce(
    (prev, curr, reducerIndex) => {
      if (reducerIndex >= heightIndex) return prev;
      return prev + curr.height;
    },
    0
  );
  invert = toast.invert || invert;
  disabled = toastType === "loading";
  offset = Math.round(heightIndex * GAP + toastsHeightBefore);
  updateHeights();
  if (toast.updated) {
    clearTimeout(timeoutId);
    remainingTime = toast.duration || duration || TOAST_LIFETIME;
    startTimer();
  }
  isPromiseLoadingOrInfiniteDuration = toast.promise && toastType === "loading" || toast.duration === Number.POSITIVE_INFINITY;
  effect = useEffect(() => {
    if (!isPromiseLoadingOrInfiniteDuration) {
      if (expanded || interacting) {
        pauseTimer();
      } else {
        startTimer();
      }
    }
    return () => clearTimeout(timeoutId);
  });
  store_get($$store_subs ??= {}, "$effect", effect);
  if (toast.delete) {
    deleteToast();
  }
  $$payload.out += `<li${attr("aria-live", toast.important ? "assertive" : "polite")} aria-atomic="true" role="status"${attr("tabindex", 0)}${attr_class(clsx(cn$1($$sanitized_props.class, toastClass, classes?.toast, toast?.classes?.toast, classes?.[toastType], toast?.classes?.[toastType])))} data-sonner-toast=""${attr("data-styled", !(toast.component || toast?.unstyled || unstyled))}${attr("data-mounted", mounted)}${attr("data-promise", Boolean(toast.promise))}${attr("data-removed", removed)}${attr("data-visible", isVisible)}${attr("data-y-position", coords[0])}${attr("data-x-position", coords[1])}${attr("data-index", index)}${attr("data-front", isFront)}${attr("data-swiping", swiping)}${attr("data-type", toastType)}${attr("data-invert", invert)}${attr("data-swipe-out", swipeOut)}${attr("data-expanded", Boolean(expanded || expandByDefault && mounted))}${attr_style(`${$$sanitized_props.style} ${toast.style}`, {
    "--index": index,
    "--toasts-before": index,
    "--z-index": store_get($$store_subs ??= {}, "$toasts", toasts).length - index,
    "--offset": `${removed ? offsetBeforeRemove : offset}px`,
    "--initial-height": `${initialHeight}px`
  })}>`;
  if (closeButton && !toast.component) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<button aria-label="Close toast"${attr("data-disabled", disabled)} data-close-button=""${attr_class(clsx(cn$1(classes?.closeButton, toast?.classes?.closeButton)))}><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg></button>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (toast.component) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<!---->`;
    toast.component?.($$payload, spread_props([toast.componentProps]));
    $$payload.out += `<!---->`;
  } else {
    $$payload.out += "<!--[!-->";
    if (toastType !== "default" || toast.icon || toast.promise) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<div data-icon="">`;
      if ((toast.promise || toastType === "loading") && !toast.icon) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<!---->`;
        slot($$payload, $$props, "loading-icon", {}, null);
        $$payload.out += `<!---->`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--> `;
      if (toast.icon) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<!---->`;
        toast.icon?.($$payload, {});
        $$payload.out += `<!---->`;
      } else if (toastType === "success") {
        $$payload.out += "<!--[1-->";
        $$payload.out += `<!---->`;
        slot($$payload, $$props, "success-icon", {}, null);
        $$payload.out += `<!---->`;
      } else if (toastType === "error") {
        $$payload.out += "<!--[2-->";
        $$payload.out += `<!---->`;
        slot($$payload, $$props, "error-icon", {}, null);
        $$payload.out += `<!---->`;
      } else if (toastType === "warning") {
        $$payload.out += "<!--[3-->";
        $$payload.out += `<!---->`;
        slot($$payload, $$props, "warning-icon", {}, null);
        $$payload.out += `<!---->`;
      } else if (toastType === "info") {
        $$payload.out += "<!--[4-->";
        $$payload.out += `<!---->`;
        slot($$payload, $$props, "info-icon", {}, null);
        $$payload.out += `<!---->`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></div>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--> <div data-content="">`;
    if (toast.title) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<div data-title=""${attr_class(clsx(cn$1(classes?.title, toast?.classes?.title)))}>`;
      if (typeof toast.title !== "string") {
        $$payload.out += "<!--[-->";
        $$payload.out += `<!---->`;
        toast.title?.($$payload, spread_props([toast.componentProps]));
        $$payload.out += `<!---->`;
      } else {
        $$payload.out += "<!--[!-->";
        $$payload.out += `${escape_html(toast.title)}`;
      }
      $$payload.out += `<!--]--></div>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--> `;
    if (toast.description) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<div data-description=""${attr_class(clsx(cn$1(descriptionClass, toastDescriptionClass, classes?.description, toast.classes?.description)))}>`;
      if (typeof toast.description !== "string") {
        $$payload.out += "<!--[-->";
        $$payload.out += `<!---->`;
        toast.description?.($$payload, spread_props([toast.componentProps]));
        $$payload.out += `<!---->`;
      } else {
        $$payload.out += "<!--[!-->";
        $$payload.out += `${escape_html(toast.description)}`;
      }
      $$payload.out += `<!--]--></div>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--></div> `;
    if (toast.cancel) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<button data-button="" data-cancel=""${attr_style(cancelButtonStyle)}${attr_class(clsx(cn$1(classes?.cancelButton, toast?.classes?.cancelButton)))}>${escape_html(toast.cancel.label)}</button>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--> `;
    if (toast.action) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<button data-button=""${attr_style(actionButtonStyle)}${attr_class(clsx(cn$1(classes?.actionButton, toast?.classes?.actionButton)))}>${escape_html(toast.action.label)}</button>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]-->`;
  }
  $$payload.out += `<!--]--></li>`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, {
    toast,
    index,
    expanded,
    invert,
    position,
    visibleToasts,
    expandByDefault,
    closeButton,
    interacting,
    cancelButtonStyle,
    actionButtonStyle,
    duration,
    descriptionClass,
    classes,
    unstyled
  });
  pop();
}
function Toaster($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const $$restProps = rest_props($$sanitized_props, [
    "invert",
    "theme",
    "position",
    "hotkey",
    "containerAriaLabel",
    "richColors",
    "expand",
    "duration",
    "visibleToasts",
    "closeButton",
    "toastOptions",
    "offset",
    "dir"
  ]);
  push();
  var $$store_subs;
  let possiblePositions, hotkeyLabel;
  const VISIBLE_TOASTS_AMOUNT = 3;
  const VIEWPORT_OFFSET = "32px";
  const TOAST_WIDTH = 356;
  const GAP = 14;
  const DARK = "dark";
  const LIGHT = "light";
  function getInitialTheme(t) {
    if (t !== "system") {
      return t;
    }
    if (typeof window !== "undefined") {
      if (window.matchMedia && window.matchMedia("(prefers-color-scheme: dark)").matches) {
        return DARK;
      }
      return LIGHT;
    }
    return LIGHT;
  }
  function getDocumentDirection() {
    if (typeof window === "undefined") return "ltr";
    if (typeof document === "undefined") return "ltr";
    const dirAttribute = document.documentElement.getAttribute("dir");
    if (dirAttribute === "auto" || !dirAttribute) {
      return window.getComputedStyle(document.documentElement).direction;
    }
    return dirAttribute;
  }
  let invert = fallback($$props["invert"], false);
  let theme = fallback($$props["theme"], "light");
  let position = fallback($$props["position"], "bottom-right");
  let hotkey = fallback($$props["hotkey"], () => ["altKey", "KeyT"], true);
  let containerAriaLabel = fallback($$props["containerAriaLabel"], "Notifications");
  let richColors = fallback($$props["richColors"], false);
  let expand = fallback($$props["expand"], false);
  let duration = fallback($$props["duration"], 4e3);
  let visibleToasts = fallback($$props["visibleToasts"], VISIBLE_TOASTS_AMOUNT);
  let closeButton = fallback($$props["closeButton"], false);
  let toastOptions = fallback($$props["toastOptions"], () => ({}), true);
  let offset = fallback($$props["offset"], null);
  let dir = fallback($$props["dir"], getDocumentDirection, true);
  const { toasts, heights } = toastState;
  let expanded = false;
  let interacting = false;
  let actualTheme = getInitialTheme(theme);
  onDestroy(() => {
  });
  possiblePositions = Array.from(new Set([
    position,
    ...store_get($$store_subs ??= {}, "$toasts", toasts).filter((toast) => toast.position).map((toast) => toast.position)
  ].filter(Boolean)));
  hotkeyLabel = hotkey.join("+").replace(/Key/g, "").replace(/Digit/g, "");
  if (store_get($$store_subs ??= {}, "$toasts", toasts).length <= 1) {
    expanded = false;
  }
  {
    const toastsToDismiss = store_get($$store_subs ??= {}, "$toasts", toasts).filter((toast) => toast.dismiss && !toast.delete);
    if (toastsToDismiss.length > 0) {
      const updatedToasts = store_get($$store_subs ??= {}, "$toasts", toasts).map((toast) => {
        const matchingToast = toastsToDismiss.find((dismissToast) => dismissToast.id === toast.id);
        if (matchingToast) {
          return { ...toast, delete: true };
        }
        return toast;
      });
      toasts.set(updatedToasts);
    }
  }
  {
    if (theme !== "system") {
      actualTheme = theme;
    }
    if (typeof window !== "undefined") {
      if (theme === "system") {
        if (window.matchMedia && window.matchMedia("(prefers-color-scheme: dark)").matches) {
          actualTheme = DARK;
        } else {
          actualTheme = LIGHT;
        }
      }
      const mediaQueryList = window.matchMedia("(prefers-color-scheme: dark)");
      const changeHandler = ({ matches }) => {
        actualTheme = matches ? DARK : LIGHT;
      };
      if ("addEventListener" in mediaQueryList) {
        mediaQueryList.addEventListener("change", changeHandler);
      } else {
        mediaQueryList.addListener(changeHandler);
      }
    }
  }
  if (store_get($$store_subs ??= {}, "$toasts", toasts).length > 0) {
    $$payload.out += "<!--[-->";
    const each_array = ensure_array_like(possiblePositions);
    $$payload.out += `<section${attr("aria-label", `${containerAriaLabel} ${hotkeyLabel}`)}${attr("tabindex", -1)} class="svelte-1fo5d1m"><!--[-->`;
    for (let index = 0, $$length = each_array.length; index < $$length; index++) {
      let position2 = each_array[index];
      const each_array_1 = ensure_array_like(store_get($$store_subs ??= {}, "$toasts", toasts).filter((toast) => !toast.position && index === 0 || toast.position === position2));
      $$payload.out += `<ol${spread_attributes(
        {
          tabindex: -1,
          class: clsx($$sanitized_props.class),
          "data-sonner-toaster": true,
          "data-theme": actualTheme,
          "data-rich-colors": richColors,
          dir: dir === "auto" ? getDocumentDirection() : dir,
          "data-y-position": position2.split("-")[0],
          "data-x-position": position2.split("-")[1],
          style: $$sanitized_props.style,
          ...$$restProps
        },
        "svelte-1fo5d1m",
        void 0,
        {
          "--front-toast-height": `${store_get($$store_subs ??= {}, "$heights", heights)[0]?.height}px`,
          "--offset": typeof offset === "number" ? `${offset}px` : offset || VIEWPORT_OFFSET,
          "--width": `${TOAST_WIDTH}px`,
          "--gap": `${GAP}px`
        }
      )}><!--[-->`;
      for (let index2 = 0, $$length2 = each_array_1.length; index2 < $$length2; index2++) {
        let toast = each_array_1[index2];
        Toast($$payload, {
          index: index2,
          toast,
          invert,
          visibleToasts,
          closeButton,
          interacting,
          position: position2,
          expandByDefault: expand,
          expanded,
          actionButtonStyle: toastOptions?.actionButtonStyle || "",
          cancelButtonStyle: toastOptions?.cancelButtonStyle || "",
          class: toastOptions?.class || "",
          descriptionClass: toastOptions?.descriptionClass || "",
          classes: toastOptions.classes || {},
          duration: toastOptions?.duration ?? duration,
          unstyled: toastOptions.unstyled || false,
          $$slots: {
            "loading-icon": ($$payload2) => {
              $$payload2.out += `<!---->`;
              slot($$payload2, $$props, "loading-icon", {}, () => {
                Loader($$payload2, { visible: toast.type === "loading" });
              });
              $$payload2.out += `<!---->`;
            },
            "success-icon": ($$payload2) => {
              $$payload2.out += `<!---->`;
              slot($$payload2, $$props, "success-icon", {}, () => {
                Icon($$payload2, { type: "success" });
              });
              $$payload2.out += `<!---->`;
            },
            "error-icon": ($$payload2) => {
              $$payload2.out += `<!---->`;
              slot($$payload2, $$props, "error-icon", {}, () => {
                Icon($$payload2, { type: "error" });
              });
              $$payload2.out += `<!---->`;
            },
            "warning-icon": ($$payload2) => {
              $$payload2.out += `<!---->`;
              slot($$payload2, $$props, "warning-icon", {}, () => {
                Icon($$payload2, { type: "warning" });
              });
              $$payload2.out += `<!---->`;
            },
            "info-icon": ($$payload2) => {
              $$payload2.out += `<!---->`;
              slot($$payload2, $$props, "info-icon", {}, () => {
                Icon($$payload2, { type: "info" });
              });
              $$payload2.out += `<!---->`;
            }
          }
        });
      }
      $$payload.out += `<!--]--></ol>`;
    }
    $$payload.out += `<!--]--></section>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]-->`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, {
    invert,
    theme,
    position,
    hotkey,
    containerAriaLabel,
    richColors,
    expand,
    duration,
    visibleToasts,
    closeButton,
    toastOptions,
    offset,
    dir
  });
  pop();
}
function Sonner_1($$payload, $$props) {
  push();
  let { $$slots, $$events, ...restProps } = $$props;
  Toaster($$payload, spread_props([
    {
      theme: derivedMode.current,
      class: "toaster group",
      toastOptions: {
        style: "background: hsl(var(--popover)); color: hsl(var(--popover-foreground)); border: 1px solid hsl(var(--border));"
      }
    },
    restProps
  ]));
  pop();
}
function PricingModal($$payload, $$props) {
  push();
  var $$store_subs;
  let billingCycle, filteredPlans;
  let isOpen = fallback($$props["isOpen"], false);
  let onClose = fallback($$props["onClose"], () => {
  });
  let onSelectPlan = fallback($$props["onSelectPlan"], (planId, billingCycle2) => {
  });
  let currentPlanId = fallback($$props["currentPlanId"], null);
  let isLoading = fallback($$props["isLoading"], false);
  let section = fallback($$props["section"], "pro");
  let billingCycleStore = writable("monthly");
  let plans = [];
  function getFeatureLimit(plan, featureId, limitId) {
    const feature = plan.features.find((f) => f.featureId === featureId);
    if (!feature || !feature.limits) return null;
    const limit = feature.limits.find((l) => l.limitId === limitId);
    return limit ? typeof limit.value === "number" ? limit.value : null : null;
  }
  function getCurrentPlan() {
    return currentPlanId ? plans.find((p) => p.id === currentPlanId) : void 0;
  }
  function formatPrice(cents) {
    return (cents / 100).toFixed(0);
  }
  billingCycle = store_get($$store_subs ??= {}, "$billingCycleStore", billingCycleStore);
  {
    billingCycleStore.set("monthly");
  }
  filteredPlans = plans.filter((plan) => plan.section === section);
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    Root$1($$payload2, {
      onOpenChange: onClose,
      get open() {
        return isOpen;
      },
      set open($$value) {
        isOpen = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        Dialog_content($$payload3, {
          class: "sm:max-w-[900px]",
          children: ($$payload4) => {
            const each_array = ensure_array_like(filteredPlans);
            Dialog_header($$payload4, {
              children: ($$payload5) => {
                Dialog_title($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Choose a Plan`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Dialog_description($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Select the plan that best fits your needs.`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <div class="flex justify-center py-4"><div class="bg-muted flex items-center gap-2 rounded-lg p-1"><button${attr_class(`rounded-md px-3 py-1 text-sm font-medium ${"bg-background text-foreground"}`)}>Monthly</button> <button${attr_class(`rounded-md px-3 py-1 text-sm font-medium ${"text-muted-foreground"}`)}>Annual `;
            {
              $$payload4.out += "<!--[!-->";
            }
            $$payload4.out += `<!--]--></button></div></div> <div class="grid gap-6 md:grid-cols-3"><!--[-->`;
            for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
              let plan = each_array[$$index];
              if (plan.id !== "free" || section === "pro") {
                $$payload4.out += "<!--[-->";
                $$payload4.out += `<div${attr_class(`rounded-lg border p-6 ${plan.id === "power" || plan.id === "startup" ? "border-primary relative" : ""}`)}>`;
                if (plan.id === "power" || plan.id === "startup") {
                  $$payload4.out += "<!--[-->";
                  $$payload4.out += `<div class="bg-primary absolute -top-3 left-1/2 -translate-x-1/2 rounded-full px-3 py-1 text-xs font-medium text-white">Popular</div>`;
                } else {
                  $$payload4.out += "<!--[!-->";
                }
                $$payload4.out += `<!--]--> <h3 class="mb-2 text-lg font-medium">${escape_html(plan.name)}</h3> <p class="text-3xl font-bold">$${escape_html(formatPrice(plan.monthlyPrice))}</p> <p class="text-muted-foreground">per month${escape_html("")}</p> `;
                {
                  $$payload4.out += "<!--[!-->";
                }
                $$payload4.out += `<!--]--> <p class="text-muted-foreground mt-4 text-sm">${escape_html(plan.description)}</p> <div class="mt-4"><ul class="space-y-2 text-sm"><li class="flex items-center">`;
                Check($$payload4, { class: "mr-2 h-4 w-4 text-green-500" });
                $$payload4.out += `<!----> ${escape_html(getFeatureLimit(plan, "resume_scanner", "resume_scans_per_month") || 10)} resumes per
                  month</li> `;
                if (getFeatureLimit(plan, "team", "seats")) {
                  $$payload4.out += "<!--[-->";
                  $$payload4.out += `<li class="flex items-center">`;
                  Check($$payload4, { class: "mr-2 h-4 w-4 text-green-500" });
                  $$payload4.out += `<!----> ${escape_html(getFeatureLimit(plan, "team", "seats"))}
                    ${escape_html(getFeatureLimit(plan, "team", "seats") === 1 ? "seat" : "seats")}</li>`;
                } else {
                  $$payload4.out += "<!--[!-->";
                }
                $$payload4.out += `<!--]--></ul> <div class="mt-2">`;
                PlanFeaturesList($$payload4, { plan, compact: true });
                $$payload4.out += `<!----></div></div> `;
                Button($$payload4, {
                  variant: plan.id === "power" || plan.id === "startup" ? "default" : "outline",
                  class: "mt-6 w-full",
                  disabled: currentPlanId === plan.id || isLoading,
                  onclick: () => onSelectPlan(plan.id, billingCycle),
                  children: ($$payload5) => {
                    if (isLoading) {
                      $$payload5.out += "<!--[-->";
                      Loader_circle($$payload5, { class: "mr-2 h-4 w-4 animate-spin" });
                      $$payload5.out += `<!----> Loading...`;
                    } else if (currentPlanId === plan.id) {
                      $$payload5.out += "<!--[1-->";
                      $$payload5.out += `Current Plan`;
                    } else if (plan.id === "free" || currentPlanId && getCurrentPlan() && plan.monthlyPrice < getCurrentPlan()?.monthlyPrice) {
                      $$payload5.out += "<!--[2-->";
                      $$payload5.out += `Downgrade`;
                    } else {
                      $$payload5.out += "<!--[!-->";
                      $$payload5.out += `Upgrade`;
                    }
                    $$payload5.out += `<!--]-->`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----></div>`;
              } else {
                $$payload4.out += "<!--[!-->";
              }
              $$payload4.out += `<!--]-->`;
            }
            $$payload4.out += `<!--]--></div> `;
            Dialog_footer($$payload4, {
              children: ($$payload5) => {
                Button($$payload5, {
                  variant: "outline",
                  onclick: onClose,
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Cancel`;
                  },
                  $$slots: { default: true }
                });
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          },
          $$slots: { default: true }
        });
      },
      $$slots: { default: true }
    });
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, {
    isOpen,
    onClose,
    onSelectPlan,
    currentPlanId,
    isLoading,
    section
  });
  pop();
}
function GlobalPricingModal($$payload, $$props) {
  push();
  var $$store_subs;
  let isLoading = false;
  async function handlePlanChange(planId, billingCycle) {
    if (isLoading || !store_get($$store_subs ??= {}, "$pricingModalStore", pricingModalStore).onSelectPlan) return;
    isLoading = true;
    try {
      await store_get($$store_subs ??= {}, "$pricingModalStore", pricingModalStore).onSelectPlan(planId, billingCycle);
    } finally {
      isLoading = false;
    }
  }
  PricingModal($$payload, {
    isOpen: store_get($$store_subs ??= {}, "$pricingModalStore", pricingModalStore).isOpen,
    onClose: closePricingModal,
    onSelectPlan: handlePlanChange,
    currentPlanId: store_get($$store_subs ??= {}, "$pricingModalStore", pricingModalStore).currentPlanId,
    isLoading,
    section: store_get($$store_subs ??= {}, "$pricingModalStore", pricingModalStore).section
  });
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
const currentShortcutPage = writable(ShortcutPage.GLOBAL);
const activeShortcutGroups = derived$1(
  [currentShortcutPage],
  ([$currentShortcutPage]) => getShortcutsForPage($currentShortcutPage)
);
derived$1(
  [activeShortcutGroups],
  ([$activeShortcutGroups]) => $activeShortcutGroups.flatMap((group) => group.shortcuts)
);
function Keyboard_shortcuts_initializer($$payload, $$props) {
  push();
  pop();
}
function _layout($$payload, $$props) {
  push();
  const { data, children } = $$props;
  data?.user;
  onDestroy(() => {
  });
  Mode_watcher($$payload, {});
  $$payload.out += `<!----> `;
  {
    $$payload.out += "<!--[!-->";
    {
      $$payload.out += "<!--[-->";
      Header($$payload);
    }
    $$payload.out += `<!--]--> <main>`;
    children($$payload);
    $$payload.out += `<!----></main> `;
    {
      $$payload.out += "<!--[-->";
      Footer($$payload, { data });
    }
    $$payload.out += `<!--]-->`;
  }
  $$payload.out += `<!--]--> `;
  Sonner_1($$payload, {});
  $$payload.out += `<!----> `;
  {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  GlobalPricingModal($$payload);
  $$payload.out += `<!----> `;
  Keyboard_shortcuts_initializer();
  $$payload.out += `<!---->`;
  pop();
}

export { _layout as default };
//# sourceMappingURL=_layout.svelte-gTX4R9MA.js.map
