{"version": 3, "file": "_server.ts-COhtlpez.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/search/users/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { v as verifySessionToken } from \"../../../../../chunks/auth.js\";\nimport { s as searchUsers } from \"../../../../../chunks/prisma-search-service.js\";\nconst POST = async ({ request, cookies }) => {\n  const token = cookies.get(\"auth_token\");\n  token ? await verifySessionToken(token) : null;\n  const { query, limit = 10 } = await request.json();\n  if (!query || query.length < 2) {\n    return json({ hits: [] });\n  }\n  try {\n    const results = await searchUsers(query, { limit });\n    return json(results);\n  } catch (error) {\n    console.error(\"User search error:\", error);\n    return json({ error: \"Failed to perform user search\" }, { status: 500 });\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAGK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AAC7C,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,KAAK,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC,GAAG,IAAI;AAChD,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,GAAG,EAAE,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACpD,EAAE,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AAClC,IAAI,OAAO,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;AAC7B;AACA,EAAE,IAAI;AACN,IAAI,MAAM,OAAO,GAAG,MAAM,WAAW,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,CAAC;AACvD,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC;AACxB,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC;AAC9C,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,+BAA+B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC5E;AACA;;;;"}