{"version": 3, "file": "_server.ts-BB5fCgZ5.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/resume/_id_/data/_server.ts.js"], "sourcesContent": ["import { p as prisma } from \"../../../../../../chunks/prisma.js\";\nimport { j as json } from \"../../../../../../chunks/index.js\";\nconst PUT = async ({ params, request, locals }) => {\n  try {\n    console.log(\"Updating resume data for ID:\", params.id);\n    const data = await request.json();\n    console.log(\"Received data:\", JSON.stringify(data.parsedData));\n    const parsedData = data.parsedData || {};\n    if (!parsedData.header) {\n      console.log(\"Adding missing header to parsed data\");\n      parsedData.header = { name: \"\", email: \"\", phone: \"\" };\n    }\n    if (!parsedData.summary) {\n      console.log(\"Adding missing summary to parsed data\");\n      parsedData.summary = { content: \"\" };\n    }\n    if (!parsedData.experience) {\n      console.log(\"Adding missing experience to parsed data\");\n      parsedData.experience = [];\n    }\n    if (!parsedData.education) {\n      console.log(\"Adding missing education to parsed data\");\n      parsedData.education = [];\n    }\n    if (!parsedData.skills) {\n      console.log(\"Adding missing skills to parsed data\");\n      parsedData.skills = [];\n    }\n    if (!parsedData.projects) {\n      console.log(\"Adding missing projects to parsed data\");\n      parsedData.projects = [];\n    }\n    if (!parsedData.certifications) {\n      console.log(\"Adding missing certifications to parsed data\");\n      parsedData.certifications = [];\n    }\n    console.log(\"Final parsed data structure:\", JSON.stringify(parsedData));\n    const resume = await prisma.resume.update({\n      where: { id: params.id },\n      data: {\n        parsedData,\n        updatedAt: /* @__PURE__ */ new Date()\n      }\n    });\n    if (data.label) {\n      await prisma.document.update({\n        where: { id: resume.documentId },\n        data: {\n          label: data.label,\n          updatedAt: /* @__PURE__ */ new Date()\n        }\n      });\n    }\n    console.log(\"Resume data updated successfully\");\n    return json({\n      success: true,\n      message: \"Resume data updated successfully\",\n      data: parsedData\n    });\n  } catch (error) {\n    console.error(\"Error updating resume data:\", error);\n    return json({ error: \"Failed to update resume data\", details: String(error) }, { status: 500 });\n  }\n};\nexport {\n  PUT\n};\n"], "names": [], "mappings": ";;;;AAEK,MAAC,GAAG,GAAG,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AACnD,EAAE,IAAI;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,MAAM,CAAC,EAAE,CAAC;AAC1D,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAClE,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,EAAE;AAC5C,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;AAC5B,MAAM,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC;AACzD,MAAM,UAAU,CAAC,MAAM,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE;AAC5D;AACA,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;AAC7B,MAAM,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC;AAC1D,MAAM,UAAU,CAAC,OAAO,GAAG,EAAE,OAAO,EAAE,EAAE,EAAE;AAC1C;AACA,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE;AAChC,MAAM,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC;AAC7D,MAAM,UAAU,CAAC,UAAU,GAAG,EAAE;AAChC;AACA,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE;AAC/B,MAAM,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC;AAC5D,MAAM,UAAU,CAAC,SAAS,GAAG,EAAE;AAC/B;AACA,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;AAC5B,MAAM,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC;AACzD,MAAM,UAAU,CAAC,MAAM,GAAG,EAAE;AAC5B;AACA,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;AAC9B,MAAM,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC;AAC3D,MAAM,UAAU,CAAC,QAAQ,GAAG,EAAE;AAC9B;AACA,IAAI,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE;AACpC,MAAM,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC;AACjE,MAAM,UAAU,CAAC,cAAc,GAAG,EAAE;AACpC;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;AAC3E,IAAI,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;AAC9C,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE;AAC9B,MAAM,IAAI,EAAE;AACZ,QAAQ,UAAU;AAClB,QAAQ,SAAS,kBAAkB,IAAI,IAAI;AAC3C;AACA,KAAK,CAAC;AACN,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;AACpB,MAAM,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;AACnC,QAAQ,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,UAAU,EAAE;AACxC,QAAQ,IAAI,EAAE;AACd,UAAU,KAAK,EAAE,IAAI,CAAC,KAAK;AAC3B,UAAU,SAAS,kBAAkB,IAAI,IAAI;AAC7C;AACA,OAAO,CAAC;AACR;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC;AACnD,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,OAAO,EAAE,kCAAkC;AACjD,MAAM,IAAI,EAAE;AACZ,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC;AACvD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,8BAA8B,EAAE,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnG;AACA;;;;"}