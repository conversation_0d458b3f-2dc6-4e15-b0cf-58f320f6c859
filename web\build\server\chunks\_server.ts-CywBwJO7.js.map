{"version": 3, "file": "_server.ts-CywBwJO7.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/jobs/_id_/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { v as verifySessionToken, g as getUserFromToken } from \"../../../../../chunks/auth.js\";\nconst GET = async ({ params, cookies }) => {\n  try {\n    let user = null;\n    const authToken = cookies.get(\"auth_token\");\n    const token = cookies.get(\"token\");\n    if (authToken) {\n      user = await verifySessionToken(authToken);\n    } else if (token) {\n      user = await getUserFromToken(token);\n    }\n    if (!user) {\n      return json({ error: \"Authentication required\" }, { status: 401 });\n    }\n    const { id } = params;\n    if (!id) {\n      return json({ error: \"Job ID is required\" }, { status: 400 });\n    }\n    const job = await prisma.job_listing.findUnique({\n      where: { id }\n    });\n    if (!job) {\n      return json({ error: \"Job not found\" }, { status: 404 });\n    }\n    const jobMatch = await prisma.job_match_result.findFirst({\n      where: {\n        jobId: id,\n        userId: user.id\n      }\n    });\n    const similarJobs = await prisma.job_listing.findMany({\n      where: {\n        id: { not: id },\n        OR: [\n          { title: { contains: job.title.split(\" \")[0] } },\n          { company: { equals: job.company } }\n        ],\n        isActive: true\n      },\n      take: 5\n    });\n    return json({\n      success: true,\n      job,\n      matchScore: jobMatch?.matchScore || null,\n      similarJobs\n    });\n  } catch (error) {\n    console.error(\"Error getting job details:\", error);\n    return json({ error: \"Failed to get job details\" }, { status: 500 });\n  }\n};\nconst POST = async ({ params, request, cookies }) => {\n  try {\n    let user = null;\n    const authToken = cookies.get(\"auth_token\");\n    const token = cookies.get(\"token\");\n    if (authToken) {\n      user = await verifySessionToken(authToken);\n    } else if (token) {\n      user = await getUserFromToken(token);\n    }\n    if (!user) {\n      return json({ error: \"Authentication required\" }, { status: 401 });\n    }\n    const { id } = params;\n    const { action } = await request.json();\n    if (!id) {\n      return json({ error: \"Job ID is required\" }, { status: 400 });\n    }\n    const job = await prisma.job_listing.findUnique({\n      where: { id }\n    });\n    if (!job) {\n      return json({ error: \"Job not found\" }, { status: 404 });\n    }\n    if (action === \"save\") {\n      const existingSavedJob = await prisma.savedJob.findFirst({\n        where: {\n          userId: user.id,\n          jobId: id\n        }\n      });\n      if (existingSavedJob) {\n        return json({\n          success: true,\n          message: \"Job already saved\",\n          savedJob: existingSavedJob\n        });\n      }\n      const savedJob = await prisma.savedJob.create({\n        data: {\n          userId: user.id,\n          jobId: id\n        }\n      });\n      return json({\n        success: true,\n        message: \"Job saved successfully\",\n        savedJob\n      });\n    } else if (action === \"unsave\") {\n      await prisma.savedJob.deleteMany({\n        where: {\n          userId: user.id,\n          jobId: id\n        }\n      });\n      return json({\n        success: true,\n        message: \"Job removed from saved jobs\"\n      });\n    } else {\n      return json({ error: \"Invalid action\" }, { status: 400 });\n    }\n  } catch (error) {\n    console.error(\"Error saving job:\", error);\n    return json({ error: \"Failed to save job\" }, { status: 500 });\n  }\n};\nexport {\n  GET,\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;AAGK,MAAC,GAAG,GAAG,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK;AAC3C,EAAE,IAAI;AACN,IAAI,IAAI,IAAI,GAAG,IAAI;AACnB,IAAI,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AAC/C,IAAI,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC;AACtC,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,IAAI,GAAG,MAAM,kBAAkB,CAAC,SAAS,CAAC;AAChD,KAAK,MAAM,IAAI,KAAK,EAAE;AACtB,MAAM,IAAI,GAAG,MAAM,gBAAgB,CAAC,KAAK,CAAC;AAC1C;AACA,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA,IAAI,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;AACzB,IAAI,IAAI,CAAC,EAAE,EAAE;AACb,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oBAAoB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnE;AACA,IAAI,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;AACpD,MAAM,KAAK,EAAE,EAAE,EAAE;AACjB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,GAAG,EAAE;AACd,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9D;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC;AAC7D,MAAM,KAAK,EAAE;AACb,QAAQ,KAAK,EAAE,EAAE;AACjB,QAAQ,MAAM,EAAE,IAAI,CAAC;AACrB;AACA,KAAK,CAAC;AACN,IAAI,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;AAC1D,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE;AACvB,QAAQ,EAAE,EAAE;AACZ,UAAU,EAAE,KAAK,EAAE,EAAE,QAAQ,EAAE,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;AAC1D,UAAU,EAAE,OAAO,EAAE,EAAE,MAAM,EAAE,GAAG,CAAC,OAAO,EAAE;AAC5C,SAAS;AACT,QAAQ,QAAQ,EAAE;AAClB,OAAO;AACP,MAAM,IAAI,EAAE;AACZ,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,GAAG;AACT,MAAM,UAAU,EAAE,QAAQ,EAAE,UAAU,IAAI,IAAI;AAC9C,MAAM;AACN,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC;AACtD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,2BAA2B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA;AACK,MAAC,IAAI,GAAG,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AACrD,EAAE,IAAI;AACN,IAAI,IAAI,IAAI,GAAG,IAAI;AACnB,IAAI,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AAC/C,IAAI,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC;AACtC,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,IAAI,GAAG,MAAM,kBAAkB,CAAC,SAAS,CAAC;AAChD,KAAK,MAAM,IAAI,KAAK,EAAE;AACtB,MAAM,IAAI,GAAG,MAAM,gBAAgB,CAAC,KAAK,CAAC;AAC1C;AACA,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA,IAAI,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;AACzB,IAAI,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AAC3C,IAAI,IAAI,CAAC,EAAE,EAAE;AACb,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oBAAoB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnE;AACA,IAAI,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;AACpD,MAAM,KAAK,EAAE,EAAE,EAAE;AACjB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,GAAG,EAAE;AACd,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9D;AACA,IAAI,IAAI,MAAM,KAAK,MAAM,EAAE;AAC3B,MAAM,MAAM,gBAAgB,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;AAC/D,QAAQ,KAAK,EAAE;AACf,UAAU,MAAM,EAAE,IAAI,CAAC,EAAE;AACzB,UAAU,KAAK,EAAE;AACjB;AACA,OAAO,CAAC;AACR,MAAM,IAAI,gBAAgB,EAAE;AAC5B,QAAQ,OAAO,IAAI,CAAC;AACpB,UAAU,OAAO,EAAE,IAAI;AACvB,UAAU,OAAO,EAAE,mBAAmB;AACtC,UAAU,QAAQ,EAAE;AACpB,SAAS,CAAC;AACV;AACA,MAAM,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;AACpD,QAAQ,IAAI,EAAE;AACd,UAAU,MAAM,EAAE,IAAI,CAAC,EAAE;AACzB,UAAU,KAAK,EAAE;AACjB;AACA,OAAO,CAAC;AACR,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,OAAO,EAAE,wBAAwB;AACzC,QAAQ;AACR,OAAO,CAAC;AACR,KAAK,MAAM,IAAI,MAAM,KAAK,QAAQ,EAAE;AACpC,MAAM,MAAM,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;AACvC,QAAQ,KAAK,EAAE;AACf,UAAU,MAAM,EAAE,IAAI,CAAC,EAAE;AACzB,UAAU,KAAK,EAAE;AACjB;AACA,OAAO,CAAC;AACR,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,OAAO,EAAE;AACjB,OAAO,CAAC;AACR,KAAK,MAAM;AACX,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,gBAAgB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC/D;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC;AAC7C,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oBAAoB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE;AACA;;;;"}