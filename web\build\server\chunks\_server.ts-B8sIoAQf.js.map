{"version": 3, "file": "_server.ts-B8sIoAQf.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/resume/_id_/status/_server.ts.js"], "sourcesContent": ["import { p as prisma } from \"../../../../../../chunks/prisma.js\";\nconst GET = async ({ params, locals }) => {\n  const user = locals.user;\n  if (!user) return new Response(\"Unauthorized\", { status: 401 });\n  const resumeId = params.id;\n  if (!resumeId) {\n    return new Response(\"Resume ID is required\", { status: 400 });\n  }\n  try {\n    const resume = await prisma.resume.findUnique({\n      where: { id: resumeId },\n      include: {\n        document: true\n      }\n    });\n    if (!resume) {\n      return new Response(\"Resume not found\", { status: 404 });\n    }\n    if (resume.document.userId !== user.id) {\n      return new Response(\"Unauthorized\", { status: 401 });\n    }\n    return new Response(\n      JSON.stringify({\n        isParsed: resume.isParsed,\n        parsedAt: resume.parsedAt,\n        parsedData: resume.parsedData,\n        error: resume.isParsed === false && resume.parsedAt !== null ? \"Failed to parse resume\" : null\n      }),\n      {\n        headers: { \"Content-Type\": \"application/json\" }\n      }\n    );\n  } catch (error) {\n    console.error(\"Error checking resume parsing status:\", error);\n    return new Response(\"Internal server error\", { status: 500 });\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;AACK,MAAC,GAAG,GAAG,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK;AAC1C,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE,EAAE,MAAM,QAAQ,GAAG,MAAM,CAAC,EAAE;AAC5B,EAAE,IAAI,CAAC,QAAQ,EAAE;AACjB,IAAI,OAAO,IAAI,QAAQ,CAAC,uBAAuB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE;AACA,EAAE,IAAI;AACN,IAAI,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;AAClD,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE;AAC7B,MAAM,OAAO,EAAE;AACf,QAAQ,QAAQ,EAAE;AAClB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,OAAO,IAAI,QAAQ,CAAC,kBAAkB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9D;AACA,IAAI,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,KAAK,IAAI,CAAC,EAAE,EAAE;AAC5C,MAAM,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1D;AACA,IAAI,OAAO,IAAI,QAAQ;AACvB,MAAM,IAAI,CAAC,SAAS,CAAC;AACrB,QAAQ,QAAQ,EAAE,MAAM,CAAC,QAAQ;AACjC,QAAQ,QAAQ,EAAE,MAAM,CAAC,QAAQ;AACjC,QAAQ,UAAU,EAAE,MAAM,CAAC,UAAU;AACrC,QAAQ,KAAK,EAAE,MAAM,CAAC,QAAQ,KAAK,KAAK,IAAI,MAAM,CAAC,QAAQ,KAAK,IAAI,GAAG,wBAAwB,GAAG;AAClG,OAAO,CAAC;AACR,MAAM;AACN,QAAQ,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB;AACrD;AACA,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC;AACjE,IAAI,OAAO,IAAI,QAAQ,CAAC,uBAAuB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE;AACA;;;;"}