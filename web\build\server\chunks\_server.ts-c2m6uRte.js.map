{"version": 3, "file": "_server.ts-c2m6uRte.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/occupations/resolve/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nconst POST = async ({ request }) => {\n  try {\n    const { ids } = await request.json();\n    if (!Array.isArray(ids) || ids.length === 0) {\n      return json([]);\n    }\n    const occupations = await prisma.occupation.findMany({\n      where: {\n        id: {\n          in: ids\n        }\n      },\n      select: {\n        id: true,\n        title: true,\n        shortTitle: true\n      }\n    });\n    return json(occupations);\n  } catch (error) {\n    console.error(\"Error resolving occupation IDs:\", error);\n    return json({ error: \"Failed to resolve occupation IDs\" }, { status: 500 });\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;AAEK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK;AACpC,EAAE,IAAI;AACN,IAAI,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACxC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;AACjD,MAAM,OAAO,IAAI,CAAC,EAAE,CAAC;AACrB;AACA,IAAI,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;AACzD,MAAM,KAAK,EAAE;AACb,QAAQ,EAAE,EAAE;AACZ,UAAU,EAAE,EAAE;AACd;AACA,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,EAAE,EAAE,IAAI;AAChB,QAAQ,KAAK,EAAE,IAAI;AACnB,QAAQ,UAAU,EAAE;AACpB;AACA,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC;AAC5B,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC;AAC3D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,kCAAkC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC/E;AACA;;;;"}