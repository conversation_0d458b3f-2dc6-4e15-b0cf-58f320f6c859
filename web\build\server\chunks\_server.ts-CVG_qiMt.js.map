{"version": 3, "file": "_server.ts-CVG_qiMt.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/schools/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../chunks/prisma.js\";\nconst GET = async ({ url }) => {\n  try {\n    if (!prisma) {\n      console.log(\"Prisma client not initialized during build\");\n      return json([]);\n    }\n    const search = url.searchParams.get(\"search\") || \"\";\n    const countryId = url.searchParams.get(\"countryId\") || void 0;\n    const stateId = url.searchParams.get(\"stateId\") || void 0;\n    const limit = parseInt(url.searchParams.get(\"limit\") || \"20\");\n    const filters = {};\n    if (search) {\n      filters.institution = {\n        contains: search,\n        mode: \"insensitive\"\n      };\n    }\n    if (countryId) {\n      filters.countryId = countryId;\n    }\n    if (stateId) {\n      filters.stateId = stateId;\n    }\n    const schools = await prisma.school.findMany({\n      where: filters,\n      include: {\n        state: {\n          include: {\n            country: true\n          }\n        },\n        country: true\n      },\n      orderBy: {\n        institution: \"asc\"\n      },\n      take: limit\n    });\n    const formattedSchools = schools.map((school) => ({\n      id: school.id,\n      institution: school.institution,\n      state: school.state ? {\n        id: school.state.id,\n        name: school.state.name,\n        code: school.state.code\n      } : null,\n      country: school.country ? {\n        id: school.country.id,\n        name: school.country.name,\n        isoCode: school.country.isoCode\n      } : null\n    }));\n    return json(formattedSchools);\n  } catch (error) {\n    console.error(\"Error fetching schools:\", error);\n    return json({ error: \"Failed to fetch schools\" }, { status: 500 });\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;AAEK,MAAC,GAAG,GAAG,OAAO,EAAE,GAAG,EAAE,KAAK;AAC/B,EAAE,IAAI;AACN,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC;AAC/D,MAAM,OAAO,IAAI,CAAC,EAAE,CAAC;AACrB;AACA,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE;AACvD,IAAI,MAAM,SAAS,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC;AACjE,IAAI,MAAM,OAAO,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC;AAC7D,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC;AACjE,IAAI,MAAM,OAAO,GAAG,EAAE;AACtB,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,OAAO,CAAC,WAAW,GAAG;AAC5B,QAAQ,QAAQ,EAAE,MAAM;AACxB,QAAQ,IAAI,EAAE;AACd,OAAO;AACP;AACA,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,OAAO,CAAC,SAAS,GAAG,SAAS;AACnC;AACA,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,OAAO,CAAC,OAAO,GAAG,OAAO;AAC/B;AACA,IAAI,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;AACjD,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,OAAO,EAAE;AACf,QAAQ,KAAK,EAAE;AACf,UAAU,OAAO,EAAE;AACnB,YAAY,OAAO,EAAE;AACrB;AACA,SAAS;AACT,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,WAAW,EAAE;AACrB,OAAO;AACP,MAAM,IAAI,EAAE;AACZ,KAAK,CAAC;AACN,IAAI,MAAM,gBAAgB,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,MAAM;AACtD,MAAM,EAAE,EAAE,MAAM,CAAC,EAAE;AACnB,MAAM,WAAW,EAAE,MAAM,CAAC,WAAW;AACrC,MAAM,KAAK,EAAE,MAAM,CAAC,KAAK,GAAG;AAC5B,QAAQ,EAAE,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;AAC3B,QAAQ,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI;AAC/B,QAAQ,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC;AAC3B,OAAO,GAAG,IAAI;AACd,MAAM,OAAO,EAAE,MAAM,CAAC,OAAO,GAAG;AAChC,QAAQ,EAAE,EAAE,MAAM,CAAC,OAAO,CAAC,EAAE;AAC7B,QAAQ,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC,IAAI;AACjC,QAAQ,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC;AAChC,OAAO,GAAG;AACV,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,IAAI,CAAC,gBAAgB,CAAC;AACjC,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC;AACnD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACtE;AACA;;;;"}