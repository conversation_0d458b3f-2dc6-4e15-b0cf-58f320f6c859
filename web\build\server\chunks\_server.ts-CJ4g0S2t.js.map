{"version": 3, "file": "_server.ts-CJ4g0S2t.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/health/collector/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { l as logger } from \"../../../../../chunks/logger.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nclass HealthMonitor {\n  static instance;\n  healthCache = /* @__PURE__ */ new Map();\n  lastUpdate = /* @__PURE__ */ new Map();\n  CACHE_TTL = 3e4;\n  // 30 seconds\n  static getInstance() {\n    if (!HealthMonitor.instance) {\n      HealthMonitor.instance = new HealthMonitor();\n    }\n    return HealthMonitor.instance;\n  }\n  /**\n   * Check health of a specific consumer-facing service\n   */\n  async checkServiceHealth(serviceName, fetch) {\n    const startTime = performance.now();\n    try {\n      switch (serviceName.toLowerCase()) {\n        case \"matches\":\n          return await this.checkMatchesService(fetch, startTime);\n        case \"jobs\":\n          return await this.checkJobsService(fetch, startTime);\n        case \"tracker\":\n          return await this.checkTrackerService(fetch, startTime);\n        case \"documents\":\n          return await this.checkDocumentsService(fetch, startTime);\n        case \"automation\":\n          return await this.checkAutomationService(fetch, startTime);\n        case \"system\":\n          return await this.checkSystemService(fetch, startTime);\n        case \"website\":\n          return await this.checkWebsiteService(fetch, startTime);\n        default:\n          throw new Error(`Unknown service: ${serviceName}`);\n      }\n    } catch (error) {\n      const responseTime = Math.round(performance.now() - startTime);\n      logger.error(`Health check failed for ${serviceName}:`, error);\n      return {\n        service: serviceName,\n        status: \"outage\",\n        responseTime,\n        details: {\n          error: error instanceof Error ? error.message : \"Unknown error\",\n          statusCode: 503\n        }\n      };\n    }\n  }\n  /**\n   * Check Matches service (job matching and recommendations)\n   */\n  async checkMatchesService(fetch, startTime) {\n    try {\n      const response = await fetch(\"/api/jobs/matches\", {\n        method: \"GET\",\n        headers: { \"Content-Type\": \"application/json\" }\n      });\n      const responseTime = Math.round(performance.now() - startTime);\n      const isHealthy = response.ok;\n      return {\n        service: \"matches\",\n        status: isHealthy ? \"operational\" : \"degraded\",\n        responseTime,\n        details: {\n          statusCode: response.status,\n          availability: isHealthy ? 100 : 0,\n          endpoint: \"/api/jobs/matches\"\n        }\n      };\n    } catch (error) {\n      const responseTime = Math.round(performance.now() - startTime);\n      return {\n        service: \"matches\",\n        status: \"outage\",\n        responseTime,\n        details: {\n          error: error instanceof Error ? error.message : \"Service unavailable\",\n          statusCode: 503\n        }\n      };\n    }\n  }\n  /**\n   * Check Jobs service (job search and listings)\n   */\n  async checkJobsService(fetch, startTime) {\n    try {\n      const response = await fetch(\"/api/jobs?limit=1\", {\n        method: \"GET\",\n        headers: { \"Content-Type\": \"application/json\" }\n      });\n      const responseTime = Math.round(performance.now() - startTime);\n      const isHealthy = response.ok;\n      return {\n        service: \"jobs\",\n        status: isHealthy ? \"operational\" : \"degraded\",\n        responseTime,\n        details: {\n          statusCode: response.status,\n          availability: isHealthy ? 100 : 0,\n          endpoint: \"/api/jobs\"\n        }\n      };\n    } catch (error) {\n      const responseTime = Math.round(performance.now() - startTime);\n      return {\n        service: \"jobs\",\n        status: \"outage\",\n        responseTime,\n        details: {\n          error: error instanceof Error ? error.message : \"Service unavailable\",\n          statusCode: 503\n        }\n      };\n    }\n  }\n  /**\n   * Check Tracker service (application tracking)\n   */\n  async checkTrackerService(fetch, startTime) {\n    try {\n      const response = await fetch(\"/api/applications/status\", {\n        method: \"GET\",\n        headers: { \"Content-Type\": \"application/json\" }\n      });\n      const responseTime = Math.round(performance.now() - startTime);\n      const isHealthy = response.ok;\n      let details = {\n        statusCode: response.status,\n        availability: isHealthy ? 100 : 0,\n        endpoint: \"/api/applications/status\"\n      };\n      if (isHealthy) {\n        const data = await response.json();\n        details = {\n          ...details,\n          dailyApplications: data.dailyApplications || 0,\n          successRate: data.successRate || 0,\n          averageProcessingTime: data.averageProcessingTime || 0\n        };\n      }\n      return {\n        service: \"tracker\",\n        status: isHealthy ? \"operational\" : \"degraded\",\n        responseTime,\n        details\n      };\n    } catch (error) {\n      const responseTime = Math.round(performance.now() - startTime);\n      return {\n        service: \"tracker\",\n        status: \"outage\",\n        responseTime,\n        details: {\n          error: error instanceof Error ? error.message : \"Service unavailable\",\n          statusCode: 503\n        }\n      };\n    }\n  }\n  /**\n   * Check Documents service (resume and document management)\n   */\n  async checkDocumentsService(fetch, startTime) {\n    try {\n      const response = await fetch(\"/api/resume/templates\", {\n        method: \"GET\",\n        headers: { \"Content-Type\": \"application/json\" }\n      });\n      const responseTime = Math.round(performance.now() - startTime);\n      const isHealthy = response.ok;\n      return {\n        service: \"documents\",\n        status: isHealthy ? \"operational\" : \"degraded\",\n        responseTime,\n        details: {\n          statusCode: response.status,\n          availability: isHealthy ? 100 : 0,\n          endpoint: \"/api/resume/templates\"\n        }\n      };\n    } catch (error) {\n      const responseTime = Math.round(performance.now() - startTime);\n      return {\n        service: \"documents\",\n        status: \"outage\",\n        responseTime,\n        details: {\n          error: error instanceof Error ? error.message : \"Service unavailable\",\n          statusCode: 503\n        }\n      };\n    }\n  }\n  /**\n   * Check Automation service (automated job application tools)\n   */\n  async checkAutomationService(fetch, startTime) {\n    try {\n      const workerUrl = process.env.WORKER_API_URL || \"https://auto-apply-worker.onrender.com\";\n      const response = await fetch(`${workerUrl}/health`, {\n        method: \"GET\",\n        headers: { \"Content-Type\": \"application/json\" }\n      });\n      const responseTime = Math.round(performance.now() - startTime);\n      const isHealthy = response.ok;\n      return {\n        service: \"automation\",\n        status: isHealthy ? \"operational\" : \"degraded\",\n        responseTime,\n        details: {\n          statusCode: response.status,\n          availability: isHealthy ? 100 : 0,\n          endpoint: `${workerUrl}/health`\n        }\n      };\n    } catch (error) {\n      const responseTime = Math.round(performance.now() - startTime);\n      return {\n        service: \"automation\",\n        status: \"outage\",\n        responseTime,\n        details: {\n          error: error instanceof Error ? error.message : \"Service unavailable\",\n          statusCode: 503\n        }\n      };\n    }\n  }\n  /**\n   * Check System service (core system services)\n   */\n  async checkSystemService(fetch, startTime) {\n    try {\n      await prisma.$queryRaw`SELECT 1 as ping`;\n      const responseTime = Math.round(performance.now() - startTime);\n      return {\n        service: \"system\",\n        status: \"operational\",\n        responseTime,\n        details: {\n          database: \"connected\",\n          availability: 100\n        }\n      };\n    } catch (error) {\n      const responseTime = Math.round(performance.now() - startTime);\n      return {\n        service: \"system\",\n        status: \"outage\",\n        responseTime,\n        details: {\n          error: error instanceof Error ? error.message : \"Database unavailable\",\n          database: \"disconnected\",\n          availability: 0\n        }\n      };\n    }\n  }\n  /**\n   * Check Website service (website and user interface)\n   */\n  async checkWebsiteService(fetch, startTime) {\n    try {\n      const response = await fetch(\"/\", {\n        method: \"GET\",\n        headers: { \"Content-Type\": \"text/html\" }\n      });\n      const responseTime = Math.round(performance.now() - startTime);\n      const isHealthy = response.ok;\n      return {\n        service: \"website\",\n        status: isHealthy ? \"operational\" : \"degraded\",\n        responseTime,\n        details: {\n          statusCode: response.status,\n          availability: isHealthy ? 100 : 0,\n          endpoint: \"/\"\n        }\n      };\n    } catch (error) {\n      const responseTime = Math.round(performance.now() - startTime);\n      return {\n        service: \"website\",\n        status: \"outage\",\n        responseTime,\n        details: {\n          error: error instanceof Error ? error.message : \"Website unavailable\",\n          statusCode: 503\n        }\n      };\n    }\n  }\n  /**\n   * Update service status in database\n   */\n  async updateServiceStatus(serviceName, healthData) {\n    try {\n      const existingService = await prisma.serviceStatus.findUnique({\n        where: { name: serviceName }\n      });\n      if (existingService) {\n        if (existingService.status !== healthData.status) {\n          await prisma.serviceStatus.update({\n            where: { id: existingService.id },\n            data: {\n              status: healthData.status,\n              lastCheckedAt: /* @__PURE__ */ new Date()\n            }\n          });\n          await prisma.serviceStatusHistory.create({\n            data: {\n              serviceId: existingService.id,\n              status: healthData.status\n            }\n          });\n          logger.info(`Updated status for ${serviceName} to ${healthData.status}`);\n        } else {\n          await prisma.serviceStatus.update({\n            where: { id: existingService.id },\n            data: {\n              lastCheckedAt: /* @__PURE__ */ new Date()\n            }\n          });\n        }\n      }\n    } catch (error) {\n      logger.error(`Error updating service status for ${serviceName}:`, error);\n    }\n  }\n  /**\n   * Get cached health data or fetch fresh data\n   */\n  async getServiceHealth(serviceName, fetch) {\n    const now = Date.now();\n    const lastUpdateTime = this.lastUpdate.get(serviceName) || 0;\n    const cachedData = this.healthCache.get(serviceName);\n    if (cachedData && now - lastUpdateTime < this.CACHE_TTL) {\n      return cachedData;\n    }\n    const healthResult = await this.checkServiceHealth(serviceName, fetch);\n    const healthData = {\n      service: serviceName,\n      status: healthResult.status,\n      responseTime: healthResult.responseTime,\n      details: healthResult.details,\n      timestamp: (/* @__PURE__ */ new Date()).toISOString()\n    };\n    this.healthCache.set(serviceName, healthData);\n    this.lastUpdate.set(serviceName, now);\n    await this.updateServiceStatus(serviceName, healthResult);\n    return healthData;\n  }\n  /**\n   * Get health data for all services\n   */\n  async getAllServicesHealth(fetch) {\n    const services = [\"matches\", \"jobs\", \"tracker\", \"documents\", \"automation\", \"system\", \"website\"];\n    const healthData = {};\n    await Promise.all(\n      services.map(async (service) => {\n        try {\n          healthData[service] = await this.getServiceHealth(service, fetch);\n        } catch (error) {\n          logger.error(`Error getting health for ${service}:`, error);\n          healthData[service] = {\n            service,\n            status: \"unknown\",\n            responseTime: 0,\n            details: { error: \"Health check failed\" },\n            timestamp: (/* @__PURE__ */ new Date()).toISOString()\n          };\n        }\n      })\n    );\n    return healthData;\n  }\n}\nconst GET = async ({ fetch, url }) => {\n  const startTime = performance.now();\n  try {\n    const healthMonitor = HealthMonitor.getInstance();\n    const service = url.searchParams.get(\"service\");\n    const includeDetails = url.searchParams.get(\"details\") === \"true\";\n    if (service) {\n      const healthData = await healthMonitor.getServiceHealth(service, fetch);\n      const responseTime = Math.round(performance.now() - startTime);\n      return json({\n        service: healthData.service,\n        status: healthData.status,\n        responseTime: healthData.responseTime,\n        ...includeDetails && { details: healthData.details },\n        timestamp: healthData.timestamp,\n        collectorResponseTime: responseTime\n      });\n    } else {\n      const allHealthData = await healthMonitor.getAllServicesHealth(fetch);\n      const responseTime = Math.round(performance.now() - startTime);\n      const services = Object.values(allHealthData);\n      let overallStatus = \"operational\";\n      if (services.some((s) => s.status === \"outage\")) {\n        overallStatus = \"outage\";\n      } else if (services.some((s) => s.status === \"degraded\")) {\n        overallStatus = \"degraded\";\n      } else if (services.some((s) => s.status === \"maintenance\")) {\n        overallStatus = \"maintenance\";\n      } else if (services.some((s) => s.status === \"unknown\")) {\n        overallStatus = \"degraded\";\n      }\n      const avgResponseTime = services.length > 0 ? Math.round(services.reduce((sum, s) => sum + s.responseTime, 0) / services.length) : 0;\n      const statusCounts = services.reduce(\n        (counts, service2) => {\n          counts[service2.status] = (counts[service2.status] || 0) + 1;\n          return counts;\n        },\n        {}\n      );\n      const response = {\n        overallStatus,\n        totalServices: services.length,\n        statusCounts,\n        averageResponseTime: avgResponseTime,\n        services: includeDetails ? allHealthData : Object.fromEntries(\n          Object.entries(allHealthData).map(([key, value]) => [\n            key,\n            {\n              service: value.service,\n              status: value.status,\n              responseTime: value.responseTime,\n              timestamp: value.timestamp\n            }\n          ])\n        ),\n        collectorResponseTime: responseTime,\n        timestamp: (/* @__PURE__ */ new Date()).toISOString()\n      };\n      logger.info(\n        `Health collector: ${overallStatus} (${services.length} services, avg ${avgResponseTime}ms)`\n      );\n      return json(response);\n    }\n  } catch (error) {\n    const responseTime = Math.round(performance.now() - startTime);\n    logger.error(\"Health collector error:\", error);\n    return json(\n      {\n        error: \"Health collection failed\",\n        message: error instanceof Error ? error.message : \"Unknown error\",\n        collectorResponseTime: responseTime,\n        timestamp: (/* @__PURE__ */ new Date()).toISOString()\n      },\n      { status: 500 }\n    );\n  }\n};\nconst POST = async ({ request, fetch }) => {\n  try {\n    const body = await request.json();\n    const { services, forceRefresh } = body;\n    const healthMonitor = HealthMonitor.getInstance();\n    if (forceRefresh) {\n      logger.info(\"Forcing health data refresh for all services\");\n    }\n    let servicesToCheck = services;\n    if (!servicesToCheck || !Array.isArray(servicesToCheck)) {\n      servicesToCheck = [\n        \"matches\",\n        \"jobs\",\n        \"tracker\",\n        \"documents\",\n        \"automation\",\n        \"system\",\n        \"website\"\n      ];\n    }\n    const healthData = {};\n    const errors = [];\n    await Promise.all(\n      servicesToCheck.map(async (serviceName) => {\n        try {\n          healthData[serviceName] = await healthMonitor.getServiceHealth(serviceName, fetch);\n        } catch (error) {\n          const errorMessage = `Failed to check ${serviceName}: ${error instanceof Error ? error.message : \"Unknown error\"}`;\n          errors.push(errorMessage);\n          logger.error(errorMessage);\n          healthData[serviceName] = {\n            service: serviceName,\n            status: \"unknown\",\n            responseTime: 0,\n            details: { error: errorMessage },\n            timestamp: (/* @__PURE__ */ new Date()).toISOString()\n          };\n        }\n      })\n    );\n    const response = {\n      success: errors.length === 0,\n      servicesChecked: servicesToCheck.length,\n      healthData,\n      errors: errors.length > 0 ? errors : void 0,\n      timestamp: (/* @__PURE__ */ new Date()).toISOString()\n    };\n    logger.info(\n      `Health collection completed: ${servicesToCheck.length} services checked, ${errors.length} errors`\n    );\n    return json(response);\n  } catch (error) {\n    logger.error(\"Health collector POST error:\", error);\n    return json(\n      {\n        success: false,\n        error: \"Health collection request failed\",\n        message: error instanceof Error ? error.message : \"Unknown error\",\n        timestamp: (/* @__PURE__ */ new Date()).toISOString()\n      },\n      { status: 500 }\n    );\n  }\n};\nconst OPTIONS = async () => {\n  return new Response(null, {\n    status: 200,\n    headers: {\n      \"Access-Control-Allow-Origin\": \"*\",\n      \"Access-Control-Allow-Methods\": \"GET, POST, OPTIONS\",\n      \"Access-Control-Allow-Headers\": \"Content-Type, Authorization\"\n    }\n  });\n};\nexport {\n  GET,\n  OPTIONS,\n  POST\n};\n"], "names": [], "mappings": ";;;;;AAGA,MAAM,aAAa,CAAC;AACpB,EAAE,OAAO,QAAQ;AACjB,EAAE,WAAW,mBAAmB,IAAI,GAAG,EAAE;AACzC,EAAE,UAAU,mBAAmB,IAAI,GAAG,EAAE;AACxC,EAAE,SAAS,GAAG,GAAG;AACjB;AACA,EAAE,OAAO,WAAW,GAAG;AACvB,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;AACjC,MAAM,aAAa,CAAC,QAAQ,GAAG,IAAI,aAAa,EAAE;AAClD;AACA,IAAI,OAAO,aAAa,CAAC,QAAQ;AACjC;AACA;AACA;AACA;AACA,EAAE,MAAM,kBAAkB,CAAC,WAAW,EAAE,KAAK,EAAE;AAC/C,IAAI,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE;AACvC,IAAI,IAAI;AACR,MAAM,QAAQ,WAAW,CAAC,WAAW,EAAE;AACvC,QAAQ,KAAK,SAAS;AACtB,UAAU,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,SAAS,CAAC;AACjE,QAAQ,KAAK,MAAM;AACnB,UAAU,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,SAAS,CAAC;AAC9D,QAAQ,KAAK,SAAS;AACtB,UAAU,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,SAAS,CAAC;AACjE,QAAQ,KAAK,WAAW;AACxB,UAAU,OAAO,MAAM,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,SAAS,CAAC;AACnE,QAAQ,KAAK,YAAY;AACzB,UAAU,OAAO,MAAM,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,SAAS,CAAC;AACpE,QAAQ,KAAK,QAAQ;AACrB,UAAU,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,SAAS,CAAC;AAChE,QAAQ,KAAK,SAAS;AACtB,UAAU,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,SAAS,CAAC;AACjE,QAAQ;AACR,UAAU,MAAM,IAAI,KAAK,CAAC,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAC,CAAC;AAC5D;AACA,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;AACpE,MAAM,MAAM,CAAC,KAAK,CAAC,CAAC,wBAAwB,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;AACpE,MAAM,OAAO;AACb,QAAQ,OAAO,EAAE,WAAW;AAC5B,QAAQ,MAAM,EAAE,QAAQ;AACxB,QAAQ,YAAY;AACpB,QAAQ,OAAO,EAAE;AACjB,UAAU,KAAK,EAAE,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,eAAe;AACzE,UAAU,UAAU,EAAE;AACtB;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,mBAAmB,CAAC,KAAK,EAAE,SAAS,EAAE;AAC9C,IAAI,IAAI;AACR,MAAM,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,mBAAmB,EAAE;AACxD,QAAQ,MAAM,EAAE,KAAK;AACrB,QAAQ,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB;AACrD,OAAO,CAAC;AACR,MAAM,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;AACpE,MAAM,MAAM,SAAS,GAAG,QAAQ,CAAC,EAAE;AACnC,MAAM,OAAO;AACb,QAAQ,OAAO,EAAE,SAAS;AAC1B,QAAQ,MAAM,EAAE,SAAS,GAAG,aAAa,GAAG,UAAU;AACtD,QAAQ,YAAY;AACpB,QAAQ,OAAO,EAAE;AACjB,UAAU,UAAU,EAAE,QAAQ,CAAC,MAAM;AACrC,UAAU,YAAY,EAAE,SAAS,GAAG,GAAG,GAAG,CAAC;AAC3C,UAAU,QAAQ,EAAE;AACpB;AACA,OAAO;AACP,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;AACpE,MAAM,OAAO;AACb,QAAQ,OAAO,EAAE,SAAS;AAC1B,QAAQ,MAAM,EAAE,QAAQ;AACxB,QAAQ,YAAY;AACpB,QAAQ,OAAO,EAAE;AACjB,UAAU,KAAK,EAAE,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,qBAAqB;AAC/E,UAAU,UAAU,EAAE;AACtB;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,gBAAgB,CAAC,KAAK,EAAE,SAAS,EAAE;AAC3C,IAAI,IAAI;AACR,MAAM,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,mBAAmB,EAAE;AACxD,QAAQ,MAAM,EAAE,KAAK;AACrB,QAAQ,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB;AACrD,OAAO,CAAC;AACR,MAAM,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;AACpE,MAAM,MAAM,SAAS,GAAG,QAAQ,CAAC,EAAE;AACnC,MAAM,OAAO;AACb,QAAQ,OAAO,EAAE,MAAM;AACvB,QAAQ,MAAM,EAAE,SAAS,GAAG,aAAa,GAAG,UAAU;AACtD,QAAQ,YAAY;AACpB,QAAQ,OAAO,EAAE;AACjB,UAAU,UAAU,EAAE,QAAQ,CAAC,MAAM;AACrC,UAAU,YAAY,EAAE,SAAS,GAAG,GAAG,GAAG,CAAC;AAC3C,UAAU,QAAQ,EAAE;AACpB;AACA,OAAO;AACP,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;AACpE,MAAM,OAAO;AACb,QAAQ,OAAO,EAAE,MAAM;AACvB,QAAQ,MAAM,EAAE,QAAQ;AACxB,QAAQ,YAAY;AACpB,QAAQ,OAAO,EAAE;AACjB,UAAU,KAAK,EAAE,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,qBAAqB;AAC/E,UAAU,UAAU,EAAE;AACtB;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,mBAAmB,CAAC,KAAK,EAAE,SAAS,EAAE;AAC9C,IAAI,IAAI;AACR,MAAM,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,0BAA0B,EAAE;AAC/D,QAAQ,MAAM,EAAE,KAAK;AACrB,QAAQ,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB;AACrD,OAAO,CAAC;AACR,MAAM,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;AACpE,MAAM,MAAM,SAAS,GAAG,QAAQ,CAAC,EAAE;AACnC,MAAM,IAAI,OAAO,GAAG;AACpB,QAAQ,UAAU,EAAE,QAAQ,CAAC,MAAM;AACnC,QAAQ,YAAY,EAAE,SAAS,GAAG,GAAG,GAAG,CAAC;AACzC,QAAQ,QAAQ,EAAE;AAClB,OAAO;AACP,MAAM,IAAI,SAAS,EAAE;AACrB,QAAQ,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE;AAC1C,QAAQ,OAAO,GAAG;AAClB,UAAU,GAAG,OAAO;AACpB,UAAU,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,IAAI,CAAC;AACxD,UAAU,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,CAAC;AAC5C,UAAU,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,IAAI;AAC/D,SAAS;AACT;AACA,MAAM,OAAO;AACb,QAAQ,OAAO,EAAE,SAAS;AAC1B,QAAQ,MAAM,EAAE,SAAS,GAAG,aAAa,GAAG,UAAU;AACtD,QAAQ,YAAY;AACpB,QAAQ;AACR,OAAO;AACP,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;AACpE,MAAM,OAAO;AACb,QAAQ,OAAO,EAAE,SAAS;AAC1B,QAAQ,MAAM,EAAE,QAAQ;AACxB,QAAQ,YAAY;AACpB,QAAQ,OAAO,EAAE;AACjB,UAAU,KAAK,EAAE,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,qBAAqB;AAC/E,UAAU,UAAU,EAAE;AACtB;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,qBAAqB,CAAC,KAAK,EAAE,SAAS,EAAE;AAChD,IAAI,IAAI;AACR,MAAM,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,uBAAuB,EAAE;AAC5D,QAAQ,MAAM,EAAE,KAAK;AACrB,QAAQ,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB;AACrD,OAAO,CAAC;AACR,MAAM,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;AACpE,MAAM,MAAM,SAAS,GAAG,QAAQ,CAAC,EAAE;AACnC,MAAM,OAAO;AACb,QAAQ,OAAO,EAAE,WAAW;AAC5B,QAAQ,MAAM,EAAE,SAAS,GAAG,aAAa,GAAG,UAAU;AACtD,QAAQ,YAAY;AACpB,QAAQ,OAAO,EAAE;AACjB,UAAU,UAAU,EAAE,QAAQ,CAAC,MAAM;AACrC,UAAU,YAAY,EAAE,SAAS,GAAG,GAAG,GAAG,CAAC;AAC3C,UAAU,QAAQ,EAAE;AACpB;AACA,OAAO;AACP,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;AACpE,MAAM,OAAO;AACb,QAAQ,OAAO,EAAE,WAAW;AAC5B,QAAQ,MAAM,EAAE,QAAQ;AACxB,QAAQ,YAAY;AACpB,QAAQ,OAAO,EAAE;AACjB,UAAU,KAAK,EAAE,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,qBAAqB;AAC/E,UAAU,UAAU,EAAE;AACtB;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,sBAAsB,CAAC,KAAK,EAAE,SAAS,EAAE;AACjD,IAAI,IAAI;AACR,MAAM,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,wCAAwC;AAC9F,MAAM,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,OAAO,CAAC,EAAE;AAC1D,QAAQ,MAAM,EAAE,KAAK;AACrB,QAAQ,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB;AACrD,OAAO,CAAC;AACR,MAAM,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;AACpE,MAAM,MAAM,SAAS,GAAG,QAAQ,CAAC,EAAE;AACnC,MAAM,OAAO;AACb,QAAQ,OAAO,EAAE,YAAY;AAC7B,QAAQ,MAAM,EAAE,SAAS,GAAG,aAAa,GAAG,UAAU;AACtD,QAAQ,YAAY;AACpB,QAAQ,OAAO,EAAE;AACjB,UAAU,UAAU,EAAE,QAAQ,CAAC,MAAM;AACrC,UAAU,YAAY,EAAE,SAAS,GAAG,GAAG,GAAG,CAAC;AAC3C,UAAU,QAAQ,EAAE,CAAC,EAAE,SAAS,CAAC,OAAO;AACxC;AACA,OAAO;AACP,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;AACpE,MAAM,OAAO;AACb,QAAQ,OAAO,EAAE,YAAY;AAC7B,QAAQ,MAAM,EAAE,QAAQ;AACxB,QAAQ,YAAY;AACpB,QAAQ,OAAO,EAAE;AACjB,UAAU,KAAK,EAAE,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,qBAAqB;AAC/E,UAAU,UAAU,EAAE;AACtB;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,kBAAkB,CAAC,KAAK,EAAE,SAAS,EAAE;AAC7C,IAAI,IAAI;AACR,MAAM,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;AAC9C,MAAM,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;AACpE,MAAM,OAAO;AACb,QAAQ,OAAO,EAAE,QAAQ;AACzB,QAAQ,MAAM,EAAE,aAAa;AAC7B,QAAQ,YAAY;AACpB,QAAQ,OAAO,EAAE;AACjB,UAAU,QAAQ,EAAE,WAAW;AAC/B,UAAU,YAAY,EAAE;AACxB;AACA,OAAO;AACP,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;AACpE,MAAM,OAAO;AACb,QAAQ,OAAO,EAAE,QAAQ;AACzB,QAAQ,MAAM,EAAE,QAAQ;AACxB,QAAQ,YAAY;AACpB,QAAQ,OAAO,EAAE;AACjB,UAAU,KAAK,EAAE,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,sBAAsB;AAChF,UAAU,QAAQ,EAAE,cAAc;AAClC,UAAU,YAAY,EAAE;AACxB;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,mBAAmB,CAAC,KAAK,EAAE,SAAS,EAAE;AAC9C,IAAI,IAAI;AACR,MAAM,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE;AACxC,QAAQ,MAAM,EAAE,KAAK;AACrB,QAAQ,OAAO,EAAE,EAAE,cAAc,EAAE,WAAW;AAC9C,OAAO,CAAC;AACR,MAAM,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;AACpE,MAAM,MAAM,SAAS,GAAG,QAAQ,CAAC,EAAE;AACnC,MAAM,OAAO;AACb,QAAQ,OAAO,EAAE,SAAS;AAC1B,QAAQ,MAAM,EAAE,SAAS,GAAG,aAAa,GAAG,UAAU;AACtD,QAAQ,YAAY;AACpB,QAAQ,OAAO,EAAE;AACjB,UAAU,UAAU,EAAE,QAAQ,CAAC,MAAM;AACrC,UAAU,YAAY,EAAE,SAAS,GAAG,GAAG,GAAG,CAAC;AAC3C,UAAU,QAAQ,EAAE;AACpB;AACA,OAAO;AACP,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;AACpE,MAAM,OAAO;AACb,QAAQ,OAAO,EAAE,SAAS;AAC1B,QAAQ,MAAM,EAAE,QAAQ;AACxB,QAAQ,YAAY;AACpB,QAAQ,OAAO,EAAE;AACjB,UAAU,KAAK,EAAE,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,qBAAqB;AAC/E,UAAU,UAAU,EAAE;AACtB;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,mBAAmB,CAAC,WAAW,EAAE,UAAU,EAAE;AACrD,IAAI,IAAI;AACR,MAAM,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC;AACpE,QAAQ,KAAK,EAAE,EAAE,IAAI,EAAE,WAAW;AAClC,OAAO,CAAC;AACR,MAAM,IAAI,eAAe,EAAE;AAC3B,QAAQ,IAAI,eAAe,CAAC,MAAM,KAAK,UAAU,CAAC,MAAM,EAAE;AAC1D,UAAU,MAAM,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;AAC5C,YAAY,KAAK,EAAE,EAAE,EAAE,EAAE,eAAe,CAAC,EAAE,EAAE;AAC7C,YAAY,IAAI,EAAE;AAClB,cAAc,MAAM,EAAE,UAAU,CAAC,MAAM;AACvC,cAAc,aAAa,kBAAkB,IAAI,IAAI;AACrD;AACA,WAAW,CAAC;AACZ,UAAU,MAAM,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC;AACnD,YAAY,IAAI,EAAE;AAClB,cAAc,SAAS,EAAE,eAAe,CAAC,EAAE;AAC3C,cAAc,MAAM,EAAE,UAAU,CAAC;AACjC;AACA,WAAW,CAAC;AACZ,UAAU,MAAM,CAAC,IAAI,CAAC,CAAC,mBAAmB,EAAE,WAAW,CAAC,IAAI,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;AAClF,SAAS,MAAM;AACf,UAAU,MAAM,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;AAC5C,YAAY,KAAK,EAAE,EAAE,EAAE,EAAE,eAAe,CAAC,EAAE,EAAE;AAC7C,YAAY,IAAI,EAAE;AAClB,cAAc,aAAa,kBAAkB,IAAI,IAAI;AACrD;AACA,WAAW,CAAC;AACZ;AACA;AACA,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,MAAM,CAAC,KAAK,CAAC,CAAC,kCAAkC,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;AAC9E;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,gBAAgB,CAAC,WAAW,EAAE,KAAK,EAAE;AAC7C,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE;AAC1B,IAAI,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC;AAChE,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,CAAC;AACxD,IAAI,IAAI,UAAU,IAAI,GAAG,GAAG,cAAc,GAAG,IAAI,CAAC,SAAS,EAAE;AAC7D,MAAM,OAAO,UAAU;AACvB;AACA,IAAI,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,KAAK,CAAC;AAC1E,IAAI,MAAM,UAAU,GAAG;AACvB,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,MAAM,EAAE,YAAY,CAAC,MAAM;AACjC,MAAM,YAAY,EAAE,YAAY,CAAC,YAAY;AAC7C,MAAM,OAAO,EAAE,YAAY,CAAC,OAAO;AACnC,MAAM,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AACzD,KAAK;AACL,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,EAAE,UAAU,CAAC;AACjD,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC;AACzC,IAAI,MAAM,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,YAAY,CAAC;AAC7D,IAAI,OAAO,UAAU;AACrB;AACA;AACA;AACA;AACA,EAAE,MAAM,oBAAoB,CAAC,KAAK,EAAE;AACpC,IAAI,MAAM,QAAQ,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,YAAY,EAAE,QAAQ,EAAE,SAAS,CAAC;AACnG,IAAI,MAAM,UAAU,GAAG,EAAE;AACzB,IAAI,MAAM,OAAO,CAAC,GAAG;AACrB,MAAM,QAAQ,CAAC,GAAG,CAAC,OAAO,OAAO,KAAK;AACtC,QAAQ,IAAI;AACZ,UAAU,UAAU,CAAC,OAAO,CAAC,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,KAAK,CAAC;AAC3E,SAAS,CAAC,OAAO,KAAK,EAAE;AACxB,UAAU,MAAM,CAAC,KAAK,CAAC,CAAC,yBAAyB,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;AACrE,UAAU,UAAU,CAAC,OAAO,CAAC,GAAG;AAChC,YAAY,OAAO;AACnB,YAAY,MAAM,EAAE,SAAS;AAC7B,YAAY,YAAY,EAAE,CAAC;AAC3B,YAAY,OAAO,EAAE,EAAE,KAAK,EAAE,qBAAqB,EAAE;AACrD,YAAY,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC/D,WAAW;AACX;AACA,OAAO;AACP,KAAK;AACL,IAAI,OAAO,UAAU;AACrB;AACA;AACK,MAAC,GAAG,GAAG,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK;AACtC,EAAE,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE;AACrC,EAAE,IAAI;AACN,IAAI,MAAM,aAAa,GAAG,aAAa,CAAC,WAAW,EAAE;AACrD,IAAI,MAAM,OAAO,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC;AACnD,IAAI,MAAM,cAAc,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,MAAM;AACrE,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,MAAM,UAAU,GAAG,MAAM,aAAa,CAAC,gBAAgB,CAAC,OAAO,EAAE,KAAK,CAAC;AAC7E,MAAM,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;AACpE,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,OAAO,EAAE,UAAU,CAAC,OAAO;AACnC,QAAQ,MAAM,EAAE,UAAU,CAAC,MAAM;AACjC,QAAQ,YAAY,EAAE,UAAU,CAAC,YAAY;AAC7C,QAAQ,GAAG,cAAc,IAAI,EAAE,OAAO,EAAE,UAAU,CAAC,OAAO,EAAE;AAC5D,QAAQ,SAAS,EAAE,UAAU,CAAC,SAAS;AACvC,QAAQ,qBAAqB,EAAE;AAC/B,OAAO,CAAC;AACR,KAAK,MAAM;AACX,MAAM,MAAM,aAAa,GAAG,MAAM,aAAa,CAAC,oBAAoB,CAAC,KAAK,CAAC;AAC3E,MAAM,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;AACpE,MAAM,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC;AACnD,MAAM,IAAI,aAAa,GAAG,aAAa;AACvC,MAAM,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,QAAQ,CAAC,EAAE;AACvD,QAAQ,aAAa,GAAG,QAAQ;AAChC,OAAO,MAAM,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,UAAU,CAAC,EAAE;AAChE,QAAQ,aAAa,GAAG,UAAU;AAClC,OAAO,MAAM,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,aAAa,CAAC,EAAE;AACnE,QAAQ,aAAa,GAAG,aAAa;AACrC,OAAO,MAAM,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,SAAS,CAAC,EAAE;AAC/D,QAAQ,aAAa,GAAG,UAAU;AAClC;AACA,MAAM,MAAM,eAAe,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,YAAY,EAAE,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC;AAC1I,MAAM,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM;AAC1C,QAAQ,CAAC,MAAM,EAAE,QAAQ,KAAK;AAC9B,UAAU,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;AACtE,UAAU,OAAO,MAAM;AACvB,SAAS;AACT,QAAQ;AACR,OAAO;AACP,MAAM,MAAM,QAAQ,GAAG;AACvB,QAAQ,aAAa;AACrB,QAAQ,aAAa,EAAE,QAAQ,CAAC,MAAM;AACtC,QAAQ,YAAY;AACpB,QAAQ,mBAAmB,EAAE,eAAe;AAC5C,QAAQ,QAAQ,EAAE,cAAc,GAAG,aAAa,GAAG,MAAM,CAAC,WAAW;AACrE,UAAU,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK;AAC9D,YAAY,GAAG;AACf,YAAY;AACZ,cAAc,OAAO,EAAE,KAAK,CAAC,OAAO;AACpC,cAAc,MAAM,EAAE,KAAK,CAAC,MAAM;AAClC,cAAc,YAAY,EAAE,KAAK,CAAC,YAAY;AAC9C,cAAc,SAAS,EAAE,KAAK,CAAC;AAC/B;AACA,WAAW;AACX,SAAS;AACT,QAAQ,qBAAqB,EAAE,YAAY;AAC3C,QAAQ,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC3D,OAAO;AACP,MAAM,MAAM,CAAC,IAAI;AACjB,QAAQ,CAAC,kBAAkB,EAAE,aAAa,CAAC,EAAE,EAAE,QAAQ,CAAC,MAAM,CAAC,eAAe,EAAE,eAAe,CAAC,GAAG;AACnG,OAAO;AACP,MAAM,OAAO,IAAI,CAAC,QAAQ,CAAC;AAC3B;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;AAClE,IAAI,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC;AAClD,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,KAAK,EAAE,0BAA0B;AACzC,QAAQ,OAAO,EAAE,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,eAAe;AACzE,QAAQ,qBAAqB,EAAE,YAAY;AAC3C,QAAQ,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC3D,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;AACK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK;AAC3C,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG,IAAI;AAC3C,IAAI,MAAM,aAAa,GAAG,aAAa,CAAC,WAAW,EAAE;AACrD,IAAI,IAAI,YAAY,EAAE;AACtB,MAAM,MAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC;AACjE;AACA,IAAI,IAAI,eAAe,GAAG,QAAQ;AAClC,IAAI,IAAI,CAAC,eAAe,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,EAAE;AAC7D,MAAM,eAAe,GAAG;AACxB,QAAQ,SAAS;AACjB,QAAQ,MAAM;AACd,QAAQ,SAAS;AACjB,QAAQ,WAAW;AACnB,QAAQ,YAAY;AACpB,QAAQ,QAAQ;AAChB,QAAQ;AACR,OAAO;AACP;AACA,IAAI,MAAM,UAAU,GAAG,EAAE;AACzB,IAAI,MAAM,MAAM,GAAG,EAAE;AACrB,IAAI,MAAM,OAAO,CAAC,GAAG;AACrB,MAAM,eAAe,CAAC,GAAG,CAAC,OAAO,WAAW,KAAK;AACjD,QAAQ,IAAI;AACZ,UAAU,UAAU,CAAC,WAAW,CAAC,GAAG,MAAM,aAAa,CAAC,gBAAgB,CAAC,WAAW,EAAE,KAAK,CAAC;AAC5F,SAAS,CAAC,OAAO,KAAK,EAAE;AACxB,UAAU,MAAM,YAAY,GAAG,CAAC,gBAAgB,EAAE,WAAW,CAAC,EAAE,EAAE,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,eAAe,CAAC,CAAC;AAC5H,UAAU,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC;AACnC,UAAU,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC;AACpC,UAAU,UAAU,CAAC,WAAW,CAAC,GAAG;AACpC,YAAY,OAAO,EAAE,WAAW;AAChC,YAAY,MAAM,EAAE,SAAS;AAC7B,YAAY,YAAY,EAAE,CAAC;AAC3B,YAAY,OAAO,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE;AAC5C,YAAY,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC/D,WAAW;AACX;AACA,OAAO;AACP,KAAK;AACL,IAAI,MAAM,QAAQ,GAAG;AACrB,MAAM,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;AAClC,MAAM,eAAe,EAAE,eAAe,CAAC,MAAM;AAC7C,MAAM,UAAU;AAChB,MAAM,MAAM,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,KAAK,CAAC;AACjD,MAAM,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AACzD,KAAK;AACL,IAAI,MAAM,CAAC,IAAI;AACf,MAAM,CAAC,6BAA6B,EAAE,eAAe,CAAC,MAAM,CAAC,mBAAmB,EAAE,MAAM,CAAC,MAAM,CAAC,OAAO;AACvG,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC;AACzB,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC;AACvD,IAAI,OAAO,IAAI;AACf,MAAM;AACN,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,KAAK,EAAE,kCAAkC;AACjD,QAAQ,OAAO,EAAE,KAAK,YAAY,KAAK,GAAG,KAAK,CAAC,OAAO,GAAG,eAAe;AACzE,QAAQ,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC3D,OAAO;AACP,MAAM,EAAE,MAAM,EAAE,GAAG;AACnB,KAAK;AACL;AACA;AACK,MAAC,OAAO,GAAG,YAAY;AAC5B,EAAE,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE;AAC5B,IAAI,MAAM,EAAE,GAAG;AACf,IAAI,OAAO,EAAE;AACb,MAAM,6BAA6B,EAAE,GAAG;AACxC,MAAM,8BAA8B,EAAE,oBAAoB;AAC1D,MAAM,8BAA8B,EAAE;AACtC;AACA,GAAG,CAAC;AACJ;;;;"}