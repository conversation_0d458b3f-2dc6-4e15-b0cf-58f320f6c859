import { v as verifySessionToken } from './auth-BPad-IlN.js';
import './prisma-Cit_HrSw.js';
import '@prisma/client';
import 'jsonwebtoken';
import 'ua-parser-js';
import './index4-HpJcNJHQ.js';
import './false-CRHihH2U.js';

const csr = true;
const ssr = true;
function load$1({ data }) {
  return data;
}

var _layout = /*#__PURE__*/Object.freeze({
  __proto__: null,
  csr: csr,
  load: load$1,
  ssr: ssr
});

const load = async ({ cookies, locals, fetch }) => {
  const session = await locals.getSession();
  const token = cookies.get("auth_token");
  const user = token ? await verifySessionToken(token) : null;
  return {
    user,
    session
  };
};

var _layout_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 0;
let component_cache;
const component = async () => component_cache ??= (await import('./_layout.svelte-gTX4R9MA.js')).default;
const universal_id = "src/routes/+layout.js";
const server_id = "src/routes/+layout.server.ts";
const imports = ["_app/immutable/nodes/0.jeVIX5KT.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/ncUU1dSD.js","_app/immutable/chunks/B-Xjo-Yt.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/C3w0v0gR.js","_app/immutable/chunks/BvdI7LR8.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/FDtb8v4a.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/FN1sk3P2.js","_app/immutable/chunks/B1K98fMG.js","_app/immutable/chunks/5V1tIHTN.js","_app/immutable/chunks/DM07Bv7T.js","_app/immutable/chunks/BfX7a-t9.js","_app/immutable/chunks/BosuxZz1.js","_app/immutable/chunks/BaVT73bJ.js","_app/immutable/chunks/DT9WCdWY.js","_app/immutable/chunks/Bpi49Nrf.js","_app/immutable/chunks/OOsIR5sE.js","_app/immutable/chunks/Cb-3cdbh.js","_app/immutable/chunks/DX6rZLP_.js","_app/immutable/chunks/CIOgxH3l.js","_app/immutable/chunks/DuoUhxYL.js","_app/immutable/chunks/CnMg5bH0.js","_app/immutable/chunks/BJIrNhIJ.js","_app/immutable/chunks/hQ6uUXJy.js","_app/immutable/chunks/Bd3zs5C6.js","_app/immutable/chunks/OXTnUuEm.js","_app/immutable/chunks/Ntteq2n_.js","_app/immutable/chunks/XESq6qWN.js","_app/immutable/chunks/D2egQzE8.js","_app/immutable/chunks/BwkAotBa.js","_app/immutable/chunks/BBa424ah.js","_app/immutable/chunks/D4f2twK-.js","_app/immutable/chunks/w80wGXGd.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/9r-6KH_O.js","_app/immutable/chunks/BSkrKq6e.js","_app/immutable/chunks/Cs0qIT7f.js","_app/immutable/chunks/1zwBog76.js","_app/immutable/chunks/CZ8wIJN8.js","_app/immutable/chunks/ChqRiddM.js","_app/immutable/chunks/iTqMWrIH.js","_app/immutable/chunks/Csk_I0QV.js","_app/immutable/chunks/WD4kvFhR.js","_app/immutable/chunks/D-o7ybA5.js","_app/immutable/chunks/hrXlVaSN.js","_app/immutable/chunks/BlYzNxlg.js","_app/immutable/chunks/BQ5jqT_2.js","_app/immutable/chunks/aemnuA_0.js","_app/immutable/chunks/w9xFoQXV.js","_app/immutable/chunks/Z6UAQTuv.js","_app/immutable/chunks/BA1W9HJN.js","_app/immutable/chunks/Dc4vaUpe.js","_app/immutable/chunks/DjPYYl4Z.js","_app/immutable/chunks/BYB878do.js","_app/immutable/chunks/tdzGgazS.js","_app/immutable/chunks/DMoa_yM9.js","_app/immutable/chunks/CnpHcmx3.js","_app/immutable/chunks/I7hvcB12.js","_app/immutable/chunks/D9yI7a4E.js","_app/immutable/chunks/BjCTmJLi.js","_app/immutable/chunks/CzsE_FAw.js","_app/immutable/chunks/Dq03aqGn.js","_app/immutable/chunks/CKh8VGVX.js","_app/immutable/chunks/BKLOCbjP.js","_app/immutable/chunks/C88uNE8B.js","_app/immutable/chunks/rNI1Perp.js","_app/immutable/chunks/DLZV8qTT.js","_app/immutable/chunks/B-l1ubNa.js","_app/immutable/chunks/DmZyh-PW.js","_app/immutable/chunks/NEMeLqAU.js","_app/immutable/chunks/CdkBcXOf.js","_app/immutable/chunks/BniYvUIG.js","_app/immutable/chunks/BNEH2jqx.js","_app/immutable/chunks/BhzFx1Wy.js","_app/immutable/chunks/DrHxToS6.js","_app/immutable/chunks/DYwWIJ9y.js"];
const stylesheets = ["_app/immutable/assets/Toaster.DKF17Rty.css","_app/immutable/assets/0.DJ2ztns0.css"];
const fonts = [];

export { component, fonts, imports, index, _layout_server_ts as server, server_id, stylesheets, _layout as universal, universal_id };
//# sourceMappingURL=0-C_UlMq9D.js.map
