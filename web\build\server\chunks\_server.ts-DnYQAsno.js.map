{"version": 3, "file": "_server.ts-DnYQAsno.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/resume/_id_/update-status/_server.ts.js"], "sourcesContent": ["import { p as prisma } from \"../../../../../../chunks/prisma.js\";\nimport { j as json } from \"../../../../../../chunks/index.js\";\nconst POST = async ({ params, request, locals }) => {\n  const user = locals.user;\n  if (!user) return new Response(\"Unauthorized\", { status: 401 });\n  const resumeId = params.id;\n  if (!resumeId) {\n    return json({ error: \"Resume ID is required\" }, { status: 400 });\n  }\n  try {\n    const body = await request.json();\n    const { isParsed, parsedData, forceUpdate } = body;\n    const resume = await prisma.resume.findUnique({\n      where: { id: resumeId },\n      include: {\n        document: true\n      }\n    });\n    if (!resume) {\n      return json({ error: \"Resume not found\" }, { status: 404 });\n    }\n    if (resume.document.userId !== user.id) {\n      return json({ error: \"Unauthorized access to resume\" }, { status: 403 });\n    }\n    if (forceUpdate && isParsed) {\n      try {\n        const parsedResume = await prisma.$queryRaw`\n          SELECT * FROM \"workers\".\"ParsedResume\" WHERE \"resumeId\" = ${resumeId} LIMIT 1\n        `;\n        if (parsedResume && Array.isArray(parsedResume) && parsedResume.length > 0) {\n          console.log(\n            `Found parsed resume data in workers schema for ${resumeId}, using it for force update`\n          );\n          const updatedResume2 = await prisma.resume.update({\n            where: { id: resumeId },\n            data: {\n              isParsed: true,\n              parsedAt: /* @__PURE__ */ new Date(),\n              parsedData: parsedResume[0],\n              updatedAt: /* @__PURE__ */ new Date()\n            }\n          });\n          console.log(`Force updated resume ${resumeId} with data from workers schema`);\n          return json({\n            success: true,\n            message: \"Resume status force updated successfully with data from workers schema\",\n            data: {\n              isParsed: updatedResume2.isParsed,\n              parsedAt: updatedResume2.parsedAt\n            }\n          });\n        }\n      } catch (queryError) {\n        console.error(`Error querying workers schema for parsed resume:`, queryError);\n      }\n    }\n    const updatedResume = await prisma.resume.update({\n      where: { id: resumeId },\n      data: {\n        isParsed: isParsed ?? resume.isParsed,\n        parsedAt: isParsed ? /* @__PURE__ */ new Date() : resume.parsedAt,\n        parsedData: parsedData ?? resume.parsedData,\n        updatedAt: /* @__PURE__ */ new Date()\n      }\n    });\n    console.log(`Updated resume ${resumeId} status: isParsed=${isParsed}`);\n    return json({\n      success: true,\n      message: forceUpdate ? \"Resume status force updated successfully\" : \"Resume status updated successfully\",\n      data: {\n        isParsed: updatedResume.isParsed,\n        parsedAt: updatedResume.parsedAt\n      }\n    });\n  } catch (error) {\n    console.error(\"Error updating resume status:\", error);\n    return json({ error: \"Failed to update resume status\" }, { status: 500 });\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;AAEK,MAAC,IAAI,GAAG,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AACpD,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE,EAAE,MAAM,QAAQ,GAAG,MAAM,CAAC,EAAE;AAC5B,EAAE,IAAI,CAAC,QAAQ,EAAE;AACjB,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpE;AACA,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACrC,IAAI,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,GAAG,IAAI;AACtD,IAAI,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC;AAClD,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE;AAC7B,MAAM,OAAO,EAAE;AACf,QAAQ,QAAQ,EAAE;AAClB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,kBAAkB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE;AACA,IAAI,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,KAAK,IAAI,CAAC,EAAE,EAAE;AAC5C,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,+BAA+B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC9E;AACA,IAAI,IAAI,WAAW,IAAI,QAAQ,EAAE;AACjC,MAAM,IAAI;AACV,QAAQ,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,SAAS;AACnD,oEAAoE,EAAE,QAAQ,CAAC;AAC/E,QAAQ,CAAC;AACT,QAAQ,IAAI,YAAY,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;AACpF,UAAU,OAAO,CAAC,GAAG;AACrB,YAAY,CAAC,+CAA+C,EAAE,QAAQ,CAAC,2BAA2B;AAClG,WAAW;AACX,UAAU,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;AAC5D,YAAY,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE;AACnC,YAAY,IAAI,EAAE;AAClB,cAAc,QAAQ,EAAE,IAAI;AAC5B,cAAc,QAAQ,kBAAkB,IAAI,IAAI,EAAE;AAClD,cAAc,UAAU,EAAE,YAAY,CAAC,CAAC,CAAC;AACzC,cAAc,SAAS,kBAAkB,IAAI,IAAI;AACjD;AACA,WAAW,CAAC;AACZ,UAAU,OAAO,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,QAAQ,CAAC,8BAA8B,CAAC,CAAC;AACvF,UAAU,OAAO,IAAI,CAAC;AACtB,YAAY,OAAO,EAAE,IAAI;AACzB,YAAY,OAAO,EAAE,wEAAwE;AAC7F,YAAY,IAAI,EAAE;AAClB,cAAc,QAAQ,EAAE,cAAc,CAAC,QAAQ;AAC/C,cAAc,QAAQ,EAAE,cAAc,CAAC;AACvC;AACA,WAAW,CAAC;AACZ;AACA,OAAO,CAAC,OAAO,UAAU,EAAE;AAC3B,QAAQ,OAAO,CAAC,KAAK,CAAC,CAAC,gDAAgD,CAAC,EAAE,UAAU,CAAC;AACrF;AACA;AACA,IAAI,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;AACrD,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE;AAC7B,MAAM,IAAI,EAAE;AACZ,QAAQ,QAAQ,EAAE,QAAQ,IAAI,MAAM,CAAC,QAAQ;AAC7C,QAAQ,QAAQ,EAAE,QAAQ,mBAAmB,IAAI,IAAI,EAAE,GAAG,MAAM,CAAC,QAAQ;AACzE,QAAQ,UAAU,EAAE,UAAU,IAAI,MAAM,CAAC,UAAU;AACnD,QAAQ,SAAS,kBAAkB,IAAI,IAAI;AAC3C;AACA,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,QAAQ,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC,CAAC;AAC1E,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,OAAO,EAAE,WAAW,GAAG,0CAA0C,GAAG,oCAAoC;AAC9G,MAAM,IAAI,EAAE;AACZ,QAAQ,QAAQ,EAAE,aAAa,CAAC,QAAQ;AACxC,QAAQ,QAAQ,EAAE,aAAa,CAAC;AAChC;AACA,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC;AACzD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,gCAAgC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7E;AACA;;;;"}