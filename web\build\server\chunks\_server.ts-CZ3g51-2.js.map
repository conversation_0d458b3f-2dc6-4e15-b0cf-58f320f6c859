{"version": 3, "file": "_server.ts-CZ3g51-2.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/feature-usage/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../chunks/index.js\";\nimport { v as verifySessionToken } from \"../../../../chunks/auth.js\";\nimport { featureTablesExist, getFeatureUsage, hasReachedLimit, trackFeatureUsage } from \"../../../../chunks/feature-usage.js\";\nconst GET = async ({ cookies, url }) => {\n  const token = cookies.get(\"auth_token\");\n  if (!token) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  const userData = verifySessionToken(token);\n  if (!userData?.id) {\n    return json({ error: \"Unauthorized\" }, { status: 401 });\n  }\n  try {\n    const tablesExist = await featureTablesExist().catch(() => false);\n    if (!tablesExist) {\n      console.warn(\"Feature usage tables do not exist yet\");\n      return json([]);\n    }\n    const featureId = url.searchParams.get(\"featureId\");\n    const limitId = url.searchParams.get(\"limitId\");\n    const period = url.searchParams.get(\"period\");\n    try {\n      let usageData = await getFeatureUsage(userData.id);\n      if (featureId && featureId !== \"undefined\") {\n        usageData = usageData.filter((usage) => usage.featureId === featureId);\n      }\n      if (limitId && limitId !== \"undefined\") {\n        usageData = usageData.filter((usage) => usage.limitId === limitId);\n      }\n      if (period && period !== \"undefined\") {\n        usageData = usageData.filter((usage) => usage.period === period);\n      }\n      return json(usageData);\n    } catch (usageError) {\n      console.error(\"Error getting feature usage data:\", usageError);\n      return json([]);\n    }\n  } catch (error) {\n    console.error(\"Error in feature usage API:\", error);\n    return json([]);\n  }\n};\nconst POST = async ({ cookies, request }) => {\n  const token = cookies.get(\"auth_token\");\n  if (!token) return new Response(\"Unauthorized\", { status: 401 });\n  const userData = verifySessionToken(token);\n  if (!userData?.id) return new Response(\"Unauthorized\", { status: 401 });\n  try {\n    const { featureId, limitId, amount = 1, checkLimit = true } = await request.json();\n    if (!featureId || !limitId) {\n      return json({ error: \"Missing required fields\" }, { status: 400 });\n    }\n    try {\n      if (checkLimit) {\n        try {\n          const reachedLimit = await hasReachedLimit(userData.id, featureId, limitId);\n          if (reachedLimit) {\n            return json(\n              {\n                error: \"Feature limit reached\",\n                limitReached: true\n              },\n              { status: 403 }\n            );\n          }\n        } catch (limitError) {\n          console.error(\"Error checking feature limit:\", limitError);\n        }\n      }\n      const usage = await trackFeatureUsage(userData.id, featureId, limitId, amount);\n      try {\n        const usageData = await getFeatureUsage(userData.id);\n        const formattedUsage = usageData.find((u) => u.id === usage.id);\n        if (formattedUsage) {\n          return json(formattedUsage);\n        }\n      } catch (formatError) {\n        console.error(\"Error getting formatted usage data:\", formatError);\n      }\n      return json({\n        id: usage.id || \"temp\",\n        featureId: usage.featureId,\n        limitId: usage.limitId,\n        used: usage.used,\n        period: usage.period,\n        updatedAt: usage.updatedAt\n      });\n    } catch (trackingError) {\n      console.error(\"Error in feature usage tracking:\", trackingError);\n      return json({\n        id: \"temp\",\n        featureId,\n        limitId,\n        used: amount,\n        success: false,\n        error: trackingError.message || \"Failed to track feature usage\"\n      });\n    }\n  } catch (error) {\n    console.error(\"Error parsing request in feature usage API:\", error);\n    return json({ error: \"Failed to track feature usage\" }, { status: 500 });\n  }\n};\nexport {\n  GET,\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAGK,MAAC,GAAG,GAAG,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK;AACxC,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE;AACd,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,MAAM,QAAQ,GAAG,kBAAkB,CAAC,KAAK,CAAC;AAC5C,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,WAAW,GAAG,MAAM,kBAAkB,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC;AACrE,IAAI,IAAI,CAAC,WAAW,EAAE;AACtB,MAAM,OAAO,CAAC,IAAI,CAAC,uCAAuC,CAAC;AAC3D,MAAM,OAAO,IAAI,CAAC,EAAE,CAAC;AACrB;AACA,IAAI,MAAM,SAAS,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC;AACvD,IAAI,MAAM,OAAO,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC;AACnD,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC;AACjD,IAAI,IAAI;AACR,MAAM,IAAI,SAAS,GAAG,MAAM,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAC;AACxD,MAAM,IAAI,SAAS,IAAI,SAAS,KAAK,WAAW,EAAE;AAClD,QAAQ,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,SAAS,KAAK,SAAS,CAAC;AAC9E;AACA,MAAM,IAAI,OAAO,IAAI,OAAO,KAAK,WAAW,EAAE;AAC9C,QAAQ,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,OAAO,KAAK,OAAO,CAAC;AAC1E;AACA,MAAM,IAAI,MAAM,IAAI,MAAM,KAAK,WAAW,EAAE;AAC5C,QAAQ,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC;AACxE;AACA,MAAM,OAAO,IAAI,CAAC,SAAS,CAAC;AAC5B,KAAK,CAAC,OAAO,UAAU,EAAE;AACzB,MAAM,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,UAAU,CAAC;AACpE,MAAM,OAAO,IAAI,CAAC,EAAE,CAAC;AACrB;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC;AACvD,IAAI,OAAO,IAAI,CAAC,EAAE,CAAC;AACnB;AACA;AACK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK;AAC7C,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClE,EAAE,MAAM,QAAQ,GAAG,kBAAkB,CAAC,KAAK,CAAC;AAC5C,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACzE,EAAE,IAAI;AACN,IAAI,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,GAAG,CAAC,EAAE,UAAU,GAAG,IAAI,EAAE,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACtF,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,OAAO,EAAE;AAChC,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA,IAAI,IAAI;AACR,MAAM,IAAI,UAAU,EAAE;AACtB,QAAQ,IAAI;AACZ,UAAU,MAAM,YAAY,GAAG,MAAM,eAAe,CAAC,QAAQ,CAAC,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC;AACrF,UAAU,IAAI,YAAY,EAAE;AAC5B,YAAY,OAAO,IAAI;AACvB,cAAc;AACd,gBAAgB,KAAK,EAAE,uBAAuB;AAC9C,gBAAgB,YAAY,EAAE;AAC9B,eAAe;AACf,cAAc,EAAE,MAAM,EAAE,GAAG;AAC3B,aAAa;AACb;AACA,SAAS,CAAC,OAAO,UAAU,EAAE;AAC7B,UAAU,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,UAAU,CAAC;AACpE;AACA;AACA,MAAM,MAAM,KAAK,GAAG,MAAM,iBAAiB,CAAC,QAAQ,CAAC,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC;AACpF,MAAM,IAAI;AACV,QAAQ,MAAM,SAAS,GAAG,MAAM,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAC;AAC5D,QAAQ,MAAM,cAAc,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,CAAC;AACvE,QAAQ,IAAI,cAAc,EAAE;AAC5B,UAAU,OAAO,IAAI,CAAC,cAAc,CAAC;AACrC;AACA,OAAO,CAAC,OAAO,WAAW,EAAE;AAC5B,QAAQ,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,WAAW,CAAC;AACzE;AACA,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,EAAE,EAAE,KAAK,CAAC,EAAE,IAAI,MAAM;AAC9B,QAAQ,SAAS,EAAE,KAAK,CAAC,SAAS;AAClC,QAAQ,OAAO,EAAE,KAAK,CAAC,OAAO;AAC9B,QAAQ,IAAI,EAAE,KAAK,CAAC,IAAI;AACxB,QAAQ,MAAM,EAAE,KAAK,CAAC,MAAM;AAC5B,QAAQ,SAAS,EAAE,KAAK,CAAC;AACzB,OAAO,CAAC;AACR,KAAK,CAAC,OAAO,aAAa,EAAE;AAC5B,MAAM,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,aAAa,CAAC;AACtE,MAAM,OAAO,IAAI,CAAC;AAClB,QAAQ,EAAE,EAAE,MAAM;AAClB,QAAQ,SAAS;AACjB,QAAQ,OAAO;AACf,QAAQ,IAAI,EAAE,MAAM;AACpB,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,KAAK,EAAE,aAAa,CAAC,OAAO,IAAI;AACxC,OAAO,CAAC;AACR;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,6CAA6C,EAAE,KAAK,CAAC;AACvE,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,+BAA+B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC5E;AACA;;;;"}