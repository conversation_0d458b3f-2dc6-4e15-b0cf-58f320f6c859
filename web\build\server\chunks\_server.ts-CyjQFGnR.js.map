{"version": 3, "file": "_server.ts-CyjQFGnR.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/maintenance/_id_/history/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../../chunks/prisma.js\";\nimport { l as logger } from \"../../../../../../chunks/logger.js\";\nconst GET = async ({ params, locals }) => {\n  try {\n    const user = locals.user;\n    if (!user || !user.isAdmin) {\n      return json({ error: \"Unauthorized\" }, { status: 401 });\n    }\n    const { id } = params;\n    if (!id) {\n      return json({ error: \"Missing event ID\" }, { status: 400 });\n    }\n    const event = await prisma.maintenanceEvent.findUnique({\n      where: { id }\n    });\n    if (!event) {\n      return json({ error: \"Maintenance event not found\" }, { status: 404 });\n    }\n    const history = await prisma.maintenanceEventHistory.findMany({\n      where: { eventId: id },\n      orderBy: { createdAt: \"desc\" }\n    });\n    return json(history);\n  } catch (error) {\n    logger.error(\"Error fetching maintenance event history:\", error);\n    return json({ error: \"Failed to fetch maintenance event history\" }, { status: 500 });\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;;AAGK,MAAC,GAAG,GAAG,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK;AAC1C,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC5B,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AAChC,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC7D;AACA,IAAI,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;AACzB,IAAI,IAAI,CAAC,EAAE,EAAE;AACb,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,kBAAkB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE;AACA,IAAI,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC;AAC3D,MAAM,KAAK,EAAE,EAAE,EAAE;AACjB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,6BAA6B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC5E;AACA,IAAI,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,uBAAuB,CAAC,QAAQ,CAAC;AAClE,MAAM,KAAK,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE;AAC5B,MAAM,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM;AAClC,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC;AACxB,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC;AACpE,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,2CAA2C,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxF;AACA;;;;"}