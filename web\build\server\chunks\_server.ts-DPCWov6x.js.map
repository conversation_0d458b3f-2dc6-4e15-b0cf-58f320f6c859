{"version": 3, "file": "_server.ts-DPCWov6x.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/document/upload/_server.ts.js"], "sourcesContent": ["import { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { u as uploadDocument } from \"../../../../../chunks/documentUpload.js\";\nimport { e as ensureUniqueDocumentName } from \"../../../../../chunks/documentNameUniqueness.js\";\nimport { d as determineDocumentSource } from \"../../../../../chunks/documentSource.js\";\nimport { c as canCreateResume, t as trackDocumentUpload } from \"../../../../../chunks/resume-usage.js\";\nconst POST = async ({ request, locals }) => {\n  const user = locals.user;\n  console.log(\"User in document upload:\", user);\n  if (!user) return new Response(\"Unauthorized\", { status: 401 });\n  if (!user.id) {\n    console.error(\"User missing ID:\", user);\n    return new Response(\"User ID missing\", { status: 400 });\n  }\n  try {\n    const formData = await request.formData();\n    const file = formData.get(\"file\");\n    const profileId = formData.get(\"profileId\");\n    let label = formData.get(\"label\") || file.name;\n    const documentType = formData.get(\"documentType\") || \"document\";\n    if (!file) {\n      return new Response(\"Missing file\", { status: 400 });\n    }\n    if (profileId) {\n      const profile = await prisma.profile.findUnique({ where: { id: profileId } });\n      if (!profile) {\n        return new Response(\"Invalid profileId\", { status: 404 });\n      }\n    }\n    label = await ensureUniqueDocumentName(label, user.id, documentType);\n    const isDev = process.env.NODE_ENV === \"development\" || process.env.VITE_DISABLE_FEATURE_LIMITS === \"true\";\n    if (!isDev) {\n      const canCreate = await canCreateResume(user.id);\n      if (!canCreate) {\n        return new Response(\n          JSON.stringify({\n            error: \"Document limit reached\",\n            limitReached: true,\n            message: \"You have reached your document upload limit. Please upgrade your plan to upload more documents.\"\n          }),\n          {\n            status: 403,\n            headers: { \"Content-Type\": \"application/json\" }\n          }\n        );\n      }\n    } else {\n      console.log(\"Development mode: Bypassing document limit check in document upload API\");\n    }\n    try {\n      console.log(\"Uploading document:\", {\n        fileName: file.name,\n        fileType: file.type,\n        fileSize: file.size,\n        documentType\n      });\n      const uploadResult = await uploadDocument(file, documentType);\n      console.log(\"Upload result:\", uploadResult);\n      console.log(\"Creating document with data:\", {\n        label,\n        fileUrl: uploadResult.publicPath,\n        userId: user.id,\n        profileId: profileId || null,\n        type: documentType\n      });\n      console.log(\"User ID:\", user.id);\n      console.log(\"Document data for Prisma:\", {\n        label,\n        fileUrl: uploadResult.publicPath,\n        filePath: uploadResult.filePath,\n        fileName: uploadResult.originalFileName,\n        type: documentType,\n        contentType: uploadResult.contentType,\n        fileSize: uploadResult.fileSize,\n        storageType: \"local\",\n        storageLocation: documentType,\n        userId: user.id,\n        profileId: profileId || void 0\n      });\n      let document;\n      try {\n        document = await prisma.document.create({\n          data: {\n            label,\n            fileUrl: uploadResult.publicPath,\n            filePath: uploadResult.filePath,\n            fileName: uploadResult.originalFileName,\n            type: documentType,\n            contentType: uploadResult.contentType,\n            fileSize: uploadResult.fileSize,\n            storageType: \"local\",\n            storageLocation: documentType,\n            // Note: 'source' field is not in the Prisma schema, so we can't set it here\n            userId: user.id,\n            ...profileId ? { profileId } : {}\n          }\n        });\n        console.log(\"Document created successfully:\", document);\n        await trackDocumentUpload(user.id);\n        console.log(\"Document upload tracked for feature usage\");\n      } catch (dbError) {\n        console.error(\"Error creating document in database:\", dbError);\n        throw new Error(`Database error: ${dbError.message}`);\n      }\n      const source = determineDocumentSource(document);\n      return new Response(\n        JSON.stringify({\n          document: {\n            ...document,\n            source\n            // Add the determined source information for the frontend\n          },\n          message: \"Document uploaded successfully.\"\n        }),\n        {\n          headers: { \"Content-Type\": \"application/json\" }\n        }\n      );\n    } catch (error) {\n      if (error.message && error.message.includes(\"File type\")) {\n        return new Response(\n          JSON.stringify({\n            error: \"Invalid file type\",\n            details: error.message\n          }),\n          {\n            status: 400,\n            headers: { \"Content-Type\": \"application/json\" }\n          }\n        );\n      }\n      throw error;\n    }\n  } catch (error) {\n    console.error(\"Error creating document:\", error);\n    return new Response(\n      JSON.stringify({\n        error: \"Failed to create document\",\n        details: error.message\n      }),\n      {\n        status: 500,\n        headers: { \"Content-Type\": \"application/json\" }\n      }\n    );\n  }\n};\nexport {\n  POST\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAKK,MAAC,IAAI,GAAG,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AAC5C,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAC;AAC/C,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;AAChB,IAAI,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAE,IAAI,CAAC;AAC3C,IAAI,OAAO,IAAI,QAAQ,CAAC,iBAAiB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3D;AACA,EAAE,IAAI;AACN,IAAI,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,QAAQ,EAAE;AAC7C,IAAI,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC;AACrC,IAAI,MAAM,SAAS,GAAG,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC;AAC/C,IAAI,IAAI,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,IAAI;AAClD,IAAI,MAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,UAAU;AACnE,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,OAAO,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC1D;AACA,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,CAAC;AACnF,MAAM,IAAI,CAAC,OAAO,EAAE;AACpB,QAAQ,OAAO,IAAI,QAAQ,CAAC,mBAAmB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjE;AACA;AACA,IAAI,KAAK,GAAG,MAAM,wBAAwB,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE,YAAY,CAAC;AACxE,IAAI,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,IAAI,OAAO,CAAC,GAAG,CAAC,2BAA2B,KAAK,MAAM;AAC9G,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,MAAM,SAAS,GAAG,MAAM,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC;AACtD,MAAM,IAAI,CAAC,SAAS,EAAE;AACtB,QAAQ,OAAO,IAAI,QAAQ;AAC3B,UAAU,IAAI,CAAC,SAAS,CAAC;AACzB,YAAY,KAAK,EAAE,wBAAwB;AAC3C,YAAY,YAAY,EAAE,IAAI;AAC9B,YAAY,OAAO,EAAE;AACrB,WAAW,CAAC;AACZ,UAAU;AACV,YAAY,MAAM,EAAE,GAAG;AACvB,YAAY,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB;AACzD;AACA,SAAS;AACT;AACA,KAAK,MAAM;AACX,MAAM,OAAO,CAAC,GAAG,CAAC,yEAAyE,CAAC;AAC5F;AACA,IAAI,IAAI;AACR,MAAM,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE;AACzC,QAAQ,QAAQ,EAAE,IAAI,CAAC,IAAI;AAC3B,QAAQ,QAAQ,EAAE,IAAI,CAAC,IAAI;AAC3B,QAAQ,QAAQ,EAAE,IAAI,CAAC,IAAI;AAC3B,QAAQ;AACR,OAAO,CAAC;AACR,MAAM,MAAM,YAAY,GAAG,MAAM,cAAc,CAAC,IAAI,EAAE,YAAY,CAAC;AACnE,MAAM,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,YAAY,CAAC;AACjD,MAAM,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE;AAClD,QAAQ,KAAK;AACb,QAAQ,OAAO,EAAE,YAAY,CAAC,UAAU;AACxC,QAAQ,MAAM,EAAE,IAAI,CAAC,EAAE;AACvB,QAAQ,SAAS,EAAE,SAAS,IAAI,IAAI;AACpC,QAAQ,IAAI,EAAE;AACd,OAAO,CAAC;AACR,MAAM,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,CAAC;AACtC,MAAM,OAAO,CAAC,GAAG,CAAC,2BAA2B,EAAE;AAC/C,QAAQ,KAAK;AACb,QAAQ,OAAO,EAAE,YAAY,CAAC,UAAU;AACxC,QAAQ,QAAQ,EAAE,YAAY,CAAC,QAAQ;AACvC,QAAQ,QAAQ,EAAE,YAAY,CAAC,gBAAgB;AAC/C,QAAQ,IAAI,EAAE,YAAY;AAC1B,QAAQ,WAAW,EAAE,YAAY,CAAC,WAAW;AAC7C,QAAQ,QAAQ,EAAE,YAAY,CAAC,QAAQ;AACvC,QAAQ,WAAW,EAAE,OAAO;AAC5B,QAAQ,eAAe,EAAE,YAAY;AACrC,QAAQ,MAAM,EAAE,IAAI,CAAC,EAAE;AACvB,QAAQ,SAAS,EAAE,SAAS,IAAI,KAAK;AACrC,OAAO,CAAC;AACR,MAAM,IAAI,QAAQ;AAClB,MAAM,IAAI;AACV,QAAQ,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;AAChD,UAAU,IAAI,EAAE;AAChB,YAAY,KAAK;AACjB,YAAY,OAAO,EAAE,YAAY,CAAC,UAAU;AAC5C,YAAY,QAAQ,EAAE,YAAY,CAAC,QAAQ;AAC3C,YAAY,QAAQ,EAAE,YAAY,CAAC,gBAAgB;AACnD,YAAY,IAAI,EAAE,YAAY;AAC9B,YAAY,WAAW,EAAE,YAAY,CAAC,WAAW;AACjD,YAAY,QAAQ,EAAE,YAAY,CAAC,QAAQ;AAC3C,YAAY,WAAW,EAAE,OAAO;AAChC,YAAY,eAAe,EAAE,YAAY;AACzC;AACA,YAAY,MAAM,EAAE,IAAI,CAAC,EAAE;AAC3B,YAAY,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,GAAG;AAC3C;AACA,SAAS,CAAC;AACV,QAAQ,OAAO,CAAC,GAAG,CAAC,gCAAgC,EAAE,QAAQ,CAAC;AAC/D,QAAQ,MAAM,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC;AAC1C,QAAQ,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC;AAChE,OAAO,CAAC,OAAO,OAAO,EAAE;AACxB,QAAQ,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,OAAO,CAAC;AACtE,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,gBAAgB,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;AAC7D;AACA,MAAM,MAAM,MAAM,GAAG,uBAAuB,CAAC,QAAQ,CAAC;AACtD,MAAM,OAAO,IAAI,QAAQ;AACzB,QAAQ,IAAI,CAAC,SAAS,CAAC;AACvB,UAAU,QAAQ,EAAE;AACpB,YAAY,GAAG,QAAQ;AACvB,YAAY;AACZ;AACA,WAAW;AACX,UAAU,OAAO,EAAE;AACnB,SAAS,CAAC;AACV,QAAQ;AACR,UAAU,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB;AACvD;AACA,OAAO;AACP,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,IAAI,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;AAChE,QAAQ,OAAO,IAAI,QAAQ;AAC3B,UAAU,IAAI,CAAC,SAAS,CAAC;AACzB,YAAY,KAAK,EAAE,mBAAmB;AACtC,YAAY,OAAO,EAAE,KAAK,CAAC;AAC3B,WAAW,CAAC;AACZ,UAAU;AACV,YAAY,MAAM,EAAE,GAAG;AACvB,YAAY,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB;AACzD;AACA,SAAS;AACT;AACA,MAAM,MAAM,KAAK;AACjB;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC;AACpD,IAAI,OAAO,IAAI,QAAQ;AACvB,MAAM,IAAI,CAAC,SAAS,CAAC;AACrB,QAAQ,KAAK,EAAE,2BAA2B;AAC1C,QAAQ,OAAO,EAAE,KAAK,CAAC;AACvB,OAAO,CAAC;AACR,MAAM;AACN,QAAQ,MAAM,EAAE,GAAG;AACnB,QAAQ,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB;AACrD;AACA,KAAK;AACL;AACA;;;;"}