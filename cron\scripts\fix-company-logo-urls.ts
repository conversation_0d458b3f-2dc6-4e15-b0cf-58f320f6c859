#!/usr/bin/env tsx
// cron/scripts/fix-company-logo-urls.ts - Fix company logo URLs to use correct R2 format

import { getPrismaClient } from "../utils/prismaClient";
import { logger } from "../utils/logger";
import dotenv from "dotenv";
import path from "path";

// Load environment variables
dotenv.config({ path: path.join(process.cwd(), ".env") });

interface UrlFixResult {
  companyId: string;
  companyName: string;
  oldUrl: string;
  newUrl: string;
  updated: boolean;
  error?: string;
}

/**
 * Convert old R2 URL format to new public development URL format
 */
function convertToCorrectR2Url(oldUrl: string): string | null {
  if (!oldUrl || !oldUrl.includes(".r2.dev/")) {
    return null;
  }

  const R2_ACCOUNT_ID = process.env.R2_ACCOUNT_ID;
  if (!R2_ACCOUNT_ID) {
    logger.error("R2_ACCOUNT_ID environment variable is not set");
    return null;
  }

  // Extract the filename from various old URL formats
  let filename: string | null = null;

  // Pattern 1: https://hirli-company-logos.7efc1bf67e7d23f5683e06d0227c883f.r2.dev/filename.webp
  const bucketPattern = /https:\/\/[^.]+\.[^.]+\.r2\.dev\/(.+)$/;
  const bucketMatch = oldUrl.match(bucketPattern);
  if (bucketMatch) {
    filename = bucketMatch[1];
  }

  // Pattern 2: https://pub-{accountId}.r2.dev/filename.webp (already correct)
  const pubPattern = /https:\/\/pub-[^.]+\.r2\.dev\/(.+)$/;
  const pubMatch = oldUrl.match(pubPattern);
  if (pubMatch) {
    // Already in correct format
    return oldUrl;
  }

  // Pattern 3: Any other R2 URL format
  if (!filename) {
    const generalPattern = /https:\/\/[^\/]+\.r2\.dev\/(.+)$/;
    const generalMatch = oldUrl.match(generalPattern);
    if (generalMatch) {
      filename = generalMatch[1];
    }
  }

  if (!filename) {
    logger.warn(`Could not extract filename from URL: ${oldUrl}`);
    return null;
  }

  // Generate the correct public development URL
  const newUrl = `https://pub-${R2_ACCOUNT_ID}.r2.dev/${filename}`;
  return newUrl;
}

async function fixCompanyLogoUrls() {
  logger.info("🚀 Starting company logo URL fix");

  const prisma = await getPrismaClient("cron");

  try {
    // Get all companies with logo URLs that need fixing
    const companies = await prisma.company.findMany({
      where: {
        logoUrl: {
          not: null,
          contains: ".r2.dev/",
        },
      },
      select: {
        id: true,
        name: true,
        logoUrl: true,
      },
    });

    logger.info(
      `📊 Found ${companies.length} companies with R2 logo URLs to check`
    );

    if (companies.length === 0) {
      logger.info("✅ No companies need logo URL updates");
      return;
    }

    const results: UrlFixResult[] = [];
    let updated = 0;
    let skipped = 0;
    let errors = 0;

    for (const company of companies) {
      try {
        if (!company.logoUrl) {
          skipped++;
          continue;
        }

        const newUrl = convertToCorrectR2Url(company.logoUrl);

        const result: UrlFixResult = {
          companyId: company.id,
          companyName: company.name,
          oldUrl: company.logoUrl,
          newUrl: newUrl || company.logoUrl,
          updated: false,
        };

        if (!newUrl) {
          logger.warn(
            `⚠️ Could not convert URL for ${company.name}: ${company.logoUrl}`
          );
          skipped++;
          result.error = "Could not convert URL format";
          results.push(result);
          continue;
        }

        if (newUrl === company.logoUrl) {
          logger.info(
            `⏭️ URL already correct for ${company.name}: ${company.logoUrl}`
          );
          skipped++;
          results.push(result);
          continue;
        }

        // Update the company with the new URL
        await prisma.company.update({
          where: { id: company.id },
          data: { logoUrl: newUrl },
        });

        result.updated = true;
        updated++;

        logger.info(`✅ Updated logo URL for "${company.name}"`);
        logger.info(`   Old: ${company.logoUrl}`);
        logger.info(`   New: ${newUrl}`);

        results.push(result);

        // Add a small delay to avoid overwhelming the database
        await new Promise((resolve) => setTimeout(resolve, 10));
      } catch (error) {
        errors++;
        logger.error(`❌ Error processing company ${company.name}:`, error);

        results.push({
          companyId: company.id,
          companyName: company.name,
          oldUrl: company.logoUrl || "",
          newUrl: company.logoUrl || "",
          updated: false,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }

    // Summary
    logger.info(`📊 Company logo URL fix completed:`);
    logger.info(`   • Companies processed: ${companies.length}`);
    logger.info(`   • URLs updated: ${updated}`);
    logger.info(`   • URLs skipped: ${skipped}`);
    logger.info(`   • Errors: ${errors}`);

    // Log some examples of the changes
    const updatedResults = results.filter((r) => r.updated);
    if (updatedResults.length > 0) {
      logger.info(`📝 Examples of URL changes:`);
      updatedResults.slice(0, 5).forEach((result) => {
        logger.info(`   • ${result.companyName}:`);
        logger.info(`     Old: ${result.oldUrl}`);
        logger.info(`     New: ${result.newUrl}`);
      });
    }

    // Log any errors
    const errorResults = results.filter((r) => r.error);
    if (errorResults.length > 0) {
      logger.warn(`⚠️ Companies with errors:`);
      errorResults.forEach((result) => {
        logger.warn(`   • ${result.companyName}: ${result.error}`);
      });
    }
  } catch (error) {
    logger.error("❌ Error in company logo URL fix:", error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
fixCompanyLogoUrls()
  .then(() => {
    logger.info("✅ Company logo URL fix completed successfully");
    process.exit(0);
  })
  .catch((error) => {
    logger.error("❌ Company logo URL fix failed:", error);
    process.exit(1);
  });

export { fixCompanyLogoUrls };
