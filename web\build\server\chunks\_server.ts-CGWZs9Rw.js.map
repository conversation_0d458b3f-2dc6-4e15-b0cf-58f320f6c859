{"version": 3, "file": "_server.ts-CGWZs9Rw.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/email/webhook/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { l as logger } from \"../../../../../chunks/logger.js\";\nimport { d as private_env } from \"../../../../../chunks/shared-server.js\";\nimport crypto from \"crypto\";\nasync function processWebhookEvent(event) {\n  try {\n    logger.info(`Processing email webhook event: ${event.type}`);\n    return {\n      type: event.type,\n      processed: true,\n      timestamp: (/* @__PURE__ */ new Date()).toISOString()\n    };\n  } catch (error) {\n    logger.error(\"Error processing webhook event:\", error);\n    return null;\n  }\n}\nfunction verifySignature(payload, signature, secret) {\n  if (!signature || !secret) return false;\n  try {\n    const hmac = crypto.createHmac(\"sha256\", secret);\n    const expectedSignature = hmac.update(payload).digest(\"hex\");\n    return crypto.timingSafeEqual(Buffer.from(signature), Buffer.from(expectedSignature));\n  } catch (error) {\n    logger.error(\"Error verifying webhook signature:\", error);\n    return false;\n  }\n}\nasync function POST({ request }) {\n  try {\n    const rawBody = await request.text();\n    const body = JSON.parse(rawBody);\n    const signature = request.headers.get(\"resend-signature\");\n    const webhookSecret = private_env.RESEND_WEBHOOK_SECRET;\n    if (private_env.NODE_ENV === \"production\" && webhookSecret) {\n      if (!signature) {\n        logger.warn(\"Missing Resend signature header\");\n        return json({ error: \"Missing signature header\" }, { status: 401 });\n      }\n      const isValid = verifySignature(rawBody, signature, webhookSecret);\n      if (!isValid) {\n        logger.warn(\"Invalid Resend webhook signature\");\n        return json({ error: \"Invalid signature\" }, { status: 401 });\n      }\n      logger.info(\"✅ Resend webhook signature verified\");\n    } else {\n      logger.info(\n        \"⚠️ Webhook signature verification skipped (not in production or missing secret)\"\n      );\n    }\n    const event = await processWebhookEvent(body);\n    if (!event) {\n      return json({ error: \"Failed to process webhook event\" }, { status: 400 });\n    }\n    return json({ success: true, event });\n  } catch (error) {\n    logger.error(\"Error processing webhook:\", error);\n    return json({ error: \"Failed to process webhook\" }, { status: 500 });\n  }\n}\nexport {\n  POST\n};\n"], "names": ["crypto"], "mappings": ";;;;;AAIA,eAAe,mBAAmB,CAAC,KAAK,EAAE;AAC1C,EAAE,IAAI;AACN,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,gCAAgC,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;AAChE,IAAI,OAAO;AACX,MAAM,IAAI,EAAE,KAAK,CAAC,IAAI;AACtB,MAAM,SAAS,EAAE,IAAI;AACrB,MAAM,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AACzD,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC;AAC1D,IAAI,OAAO,IAAI;AACf;AACA;AACA,SAAS,eAAe,CAAC,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE;AACrD,EAAE,IAAI,CAAC,SAAS,IAAI,CAAC,MAAM,EAAE,OAAO,KAAK;AACzC,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAGA,eAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC;AACpD,IAAI,MAAM,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;AAChE,IAAI,OAAOA,eAAM,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;AACzF,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC;AAC7D,IAAI,OAAO,KAAK;AAChB;AACA;AACA,eAAe,IAAI,CAAC,EAAE,OAAO,EAAE,EAAE;AACjC,EAAE,IAAI;AACN,IAAI,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE;AACxC,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;AACpC,IAAI,MAAM,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC;AAC7D,IAAI,MAAM,aAAa,GAAG,WAAW,CAAC,qBAAqB;AAC3D,IAAI,IAAI,WAAW,CAAC,QAAQ,KAAK,YAAY,IAAI,aAAa,EAAE;AAChE,MAAM,IAAI,CAAC,SAAS,EAAE;AACtB,QAAQ,MAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC;AACtD,QAAQ,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,0BAA0B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAC3E;AACA,MAAM,MAAM,OAAO,GAAG,eAAe,CAAC,OAAO,EAAE,SAAS,EAAE,aAAa,CAAC;AACxE,MAAM,IAAI,CAAC,OAAO,EAAE;AACpB,QAAQ,MAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC;AACvD,QAAQ,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACpE;AACA,MAAM,MAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC;AACxD,KAAK,MAAM;AACX,MAAM,MAAM,CAAC,IAAI;AACjB,QAAQ;AACR,OAAO;AACP;AACA,IAAI,MAAM,KAAK,GAAG,MAAM,mBAAmB,CAAC,IAAI,CAAC;AACjD,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,iCAAiC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAChF;AACA,IAAI,OAAO,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;AACzC,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC;AACpD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,2BAA2B,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACxE;AACA;;;;"}