# Base image with <PERSON><PERSON> installed (updated to match package.json version)
FROM mcr.microsoft.com/playwright:v1.43.1-jammy

# Set working directory
WORKDIR /app/cron

# Copy package files first for better caching
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy the rest of the application
COPY . .

# Generate Prisma client and install tsx globally
RUN npx prisma generate --schema=./prisma/schema.prisma && npm install -g tsx

# Set environment variables
ENV NODE_ENV=production
ENV TZ=America/New_York
ENV HEALTH_CHECK_PORT=8080

# Expose health check port
EXPOSE 8080

# Add health check
HEALTHCHECK --interval=30000s --timeout=10s --start-period=60000s --retries=3 \
  CMD curl -f http://localhost:8080/health || exit 1

# Install curl for health checks
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# Start the scheduled jobs service as a background worker
C<PERSON> ["npm", "start"]
